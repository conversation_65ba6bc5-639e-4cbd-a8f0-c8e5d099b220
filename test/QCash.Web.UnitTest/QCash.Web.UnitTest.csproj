<Project Sdk="Microsoft.NET.Sdk">
    <ItemGroup>
      <PackageReference Include="AutoFixture.AutoNSubstitute" Version="4.18.1" />
      <PackageReference Include="AutoFixture.Xunit2" Version="4.18.1" />
      <PackageReference Include="FluentAssertions" Version="6.12.2" />
      <PackageReference Include="log4net" Version="3.1.0" />
      <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
      <PackageReference Include="MockQueryable.NSubstitute" Version="7.0.3" />
      <PackageReference Include="NSubstitute" Version="5.1.0" />
      <PackageReference Include="NSubstitute.Analyzers.CSharp" Version="1.0.17">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="System.Net.Http" Version="4.3.4" />
      <PackageReference Include="xunit" Version="2.9.3" />
      <PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\QCash.Service.UnitTest\QCash.Service.UnitTest.csproj" />
      <ProjectReference Include="..\..\src\QCash.Web\QCash.Web.csproj" />
    </ItemGroup>

</Project>
