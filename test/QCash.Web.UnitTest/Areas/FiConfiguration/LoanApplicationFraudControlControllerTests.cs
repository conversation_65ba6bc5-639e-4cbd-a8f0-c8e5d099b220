using AutoFixture;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using QCash.Service.Core;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.UnitTest;
using QCash.Web.Areas.FIConfiguration.Controllers;
using QCash.Web.Areas.FIConfiguration.Models;
using Xunit;

namespace QCash.Web.UnitTest.Areas.FiConfiguration;

public class LoanApplicationFraudControlControllerTests
{
    private class TestRig(IFixture fixture)
    {
        private readonly Guid _financialInstitutionId = Guid.NewGuid();
        private readonly Guid _fraudControlSettingsId = Guid.NewGuid();
        public readonly IUnitOfWork UnitOfWork = fixture.Freeze<IUnitOfWork>();
        public readonly IFraudControlService FraudControlService = fixture.Freeze<IFraudControlService>();
        private readonly ILookupService _lookupService = fixture.Freeze<ILookupService>();
        public LoanApplicationFraudControlController Controller { get; set; } = fixture.Freeze<LoanApplicationFraudControlController>();


        public Task<IActionResult> EditGetAsync()
        {
            FraudControlService.GetSettingsAsync()
                .Returns(new FraudControlSettingsDto
                {
                    Id = _fraudControlSettingsId,
                    TimeStamp = [0, 1, 2],
                    FinancialInstitutionId = _financialInstitutionId,
                    FiSlug = IFixtureExtensions.FiSlug,
                    IsEnabled = false,
                    PreferredDelivery = "",
                    ResendThreshold = 0,
                    SubmitThreshold = 0,
                    CodeValidityMinutesThreshold = 0,
                });

            _lookupService.GetItemsFromEnum<Enums.FraudControlPreferredDeliveryMethod>()
                .Returns([
                    new QListItem<Enums.FraudControlPreferredDeliveryMethod>()
                    {
                        Text = "EmailItem1", Value = Enums.FraudControlPreferredDeliveryMethod.Email,
                    },

                    new QListItem<Enums.FraudControlPreferredDeliveryMethod>()
                    {
                        Text = "SmsItem1", Value = Enums.FraudControlPreferredDeliveryMethod.SMS,
                    },

                ]);
            
            return Controller.EditAsync((string?)null, null);
        }

        public Task<IActionResult> EditSaveAsync()
        {
            var model = new FraudControlSettingPageViewModel
            {
                FraudControlSettingId = _fraudControlSettingsId,
                FraudControlSettingTimeStamp = "",
                FinancialInstitutionId = _financialInstitutionId,
                IsEnabled = false,
                PreferredDelivery = "",
                ResendThreshold = 0,
                SubmitThreshold = 0,
                CodeValidityMinutesThreshold = 0,
                FiSlug = IFixtureExtensions.FiSlug,
            }; 
            return Controller.EditAsync(model, IFixtureExtensions.FiSlug);

        }
    }

    [Theory, AutoSubstituteData]
    public async Task EditAsyncGetTest(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var iActionResult = await rig.EditGetAsync();
        var viewResult = iActionResult as ViewResult;
        Assert.NotNull(viewResult);
        var model = viewResult.Model as FraudControlSettingPageViewModel;
        Assert.NotNull(model?.NavHeader);
        model.NavHeader.SelectedTab.Should().Be(Constants.LoanApplicationHeaderTabEnum.FraudControlTab);

        await rig.FraudControlService.Received().GetSettingsAsync();
        
        model.PreferredDeliveryChoices.Count.Should().Be(2);
        model.PreferredDeliveryChoices.Any(a => a.Text == "EmailItem1").Should().BeTrue();
    }
    
    [Theory, AutoSubstituteData]
    public async Task EditAsyncSaveSuccessTest(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var iActionResult = await rig.EditSaveAsync();
        Assert.NotNull(iActionResult);
        var redirectToActionResult = iActionResult as RedirectToActionResult;
        Assert.NotNull(redirectToActionResult);
        redirectToActionResult.ActionName.Should().Be("Edit");
        redirectToActionResult.ControllerName.Should().Be("LoanApplicationFraudControl");
        
        await rig.FraudControlService.Received()
            .SaveSettingsAsync(Arg.Any<FraudControlSettingsDto>(), IFixtureExtensions.FinancialInstitutionId);

        await rig.UnitOfWork.Received()
            .CommitAsync();
    }
    
    [Theory, AutoSubstituteData]
    public async Task EditAsyncSaveInvalidModelTest(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        rig.Controller.ModelState.AddModelError("Test", "Test Error");
        var iActionResult = await rig.EditSaveAsync();
        Assert.NotNull(iActionResult);
        var viewResult = iActionResult as ViewResult;
        Assert.NotNull(viewResult);
        // redirectToActionResult.ActionName.Should().Be("Edit");
        // redirectToActionResult.ControllerName.Should().Be("LoanApplicationGeneralSettings");
        
        await rig.FraudControlService.DidNotReceive()
            .SaveSettingsAsync(Arg.Any<FraudControlSettingsDto>(), IFixtureExtensions.FinancialInstitutionId);

        await rig.UnitOfWork.DidNotReceive()
            .CommitAsync();
    }
}