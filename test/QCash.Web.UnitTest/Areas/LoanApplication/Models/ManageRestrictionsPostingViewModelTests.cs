using FluentAssertions;
using QCash.Service.UnitTest;
using QCash.Web.Areas.LoanApplication.Models;
using QCash.Web.UnitTest.Utils;
using Xunit;

namespace QCash.Web.UnitTest.Areas.LoanApplication.Models;

public class ManageRestrictionsPostingViewModelTests
{
    [Fact]
    public void ValidateSimpleGood()
    {
        var model = new ManageRestrictionsPostingViewModel
        {
            FiSlug = IFixtureExtensions.FiSlug,
            Products =
            [
                new ManageRestrictionsProductViewModel()
                {
                    ProductId = Guid.NewGuid(),
                    Rates =
                    [
                        new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = 1,
                            RestrictedStateAbbreviations = ["AL"],
                        },

                        new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = 2,
                            RestrictedStateAbbreviations = ["CA"],
                        },

                    ],
                },
            ],
            MilitaryAnnualPercentageRate = 0,
            SettingTimeStamp = null,
            SettingId = Guid.Empty,
        };
        var validationResult = ModelValidationHelper.ValidateModel(model);
        validationResult.Should().BeEmpty();
    }
    
    [Fact]
    public void ValidateDuplicateStateBad()
    {
        var model = new ManageRestrictionsPostingViewModel
        {
            FiSlug = IFixtureExtensions.FiSlug,
            Products =
            [
                new ManageRestrictionsProductViewModel()
                {
                    ProductId = Guid.NewGuid(),
                    Rates =
                    [
                        new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = 1,
                            RestrictedStateAbbreviations = ["AL"],
                        },

                        new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = 2,
                            RestrictedStateAbbreviations = ["AL"],
                        },

                    ],
                },
            ],
            MilitaryAnnualPercentageRate = 0,
            SettingTimeStamp = null,
            SettingId = Guid.Empty,
        };
        var validationResult = ModelValidationHelper.ValidateModel(model);
        validationResult.Should().NotBeEmpty();
    }
    
    [Fact]
    public void ValidateDuplicateStateOkIfSameRate()
    {
        var model = new ManageRestrictionsPostingViewModel
        {
            FiSlug = IFixtureExtensions.FiSlug,
            Products =
            [
                new ManageRestrictionsProductViewModel()
                {
                    ProductId = Guid.NewGuid(),
                    Rates =
                    [
                        new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = 1,
                            RestrictedStateAbbreviations = ["AL"],
                        },

                        new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = 1,
                            RestrictedStateAbbreviations = ["AL"],
                        },

                    ],
                },
            ],
            MilitaryAnnualPercentageRate = 0,
            SettingTimeStamp = null,
            SettingId = Guid.Empty,
        };
        var validationResult = ModelValidationHelper.ValidateModel(model);
        validationResult.Should().BeEmpty();
    }
    
    [Fact]
    public void ToDtoMergeOnCreate()
    {
        var model = new ManageRestrictionsPostingViewModel
        {
            FiSlug = IFixtureExtensions.FiSlug,
            Products =
            [
                new ManageRestrictionsProductViewModel()
                {
                    ProductId = Guid.NewGuid(),
                    Rates =
                    [
                        new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = 1,
                            RestrictedStateAbbreviations = ["AL"],
                        },

                        new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = 1,
                            RestrictedStateAbbreviations = ["AL"],
                        },

                    ],
                },
            ],
            MilitaryAnnualPercentageRate = 0,
            SettingTimeStamp = null,
            SettingId = Guid.Empty,
        };
        var dto = model.ToDto();
        dto.RestrictedRates.Count.Should().Be(1);
        var rate = dto.RestrictedRates.Single();
        rate.Id.Should().Be(Guid.Empty);
        rate.TimeStamp.Should().BeEquivalentTo<byte[]>([]);
        rate.State.Should().Be("AL");
        rate.Rate.Should().Be(1);
        rate.UpdateRequested.Should().BeFalse();
        rate.CreateRequested.Should().BeTrue();
        rate.DeleteRequested.Should().BeFalse();
    }
    
    [Fact]
    public void ToDtoMergeOnUpdate()
    {
        Guid existingRecordId = Guid.NewGuid();
        byte[] existingTimeStamp = [1, 2, 3, 4];
        var model = new ManageRestrictionsPostingViewModel
        {
            FiSlug = IFixtureExtensions.FiSlug,
            Products =
            [
                new ManageRestrictionsProductViewModel()
                {
                    ProductId = Guid.NewGuid(),
                    ExcludedStateOriginalDetails = [],
                    Rates =
                    [
                        new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = 1,
                            RestrictedStateAbbreviations = ["AL"],
                            RestrictedStateOriginalDetails =
                            [
                                new ManageRestrictionsStateRateViewModel()
                                {
                                    Id = existingRecordId, State = "AL",
                                    TimeStamp = Convert.ToBase64String(existingTimeStamp), Rate = 20,
                                },
                            ],
                        },

                        new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = 1,
                            RestrictedStateAbbreviations = ["AL"],
                        },

                    ],
                },
            ],
            MilitaryAnnualPercentageRate = 0,
            SettingTimeStamp = null,
            SettingId = Guid.Empty,
        };
        var dto = model.ToDto();
        dto.RestrictedRates.Count.Should().Be(1);
        var rate = dto.RestrictedRates.Single();

        rate.Id.Should().Be(existingRecordId);
        rate.TimeStamp.Should().BeEquivalentTo(existingTimeStamp);
        rate.State.Should().Be("AL");
        rate.Rate.Should().Be(1);
        rate.UpdateRequested.Should().BeTrue();
        rate.CreateRequested.Should().BeFalse();
        rate.DeleteRequested.Should().BeFalse();
    }
    
    [Fact]
    public void ToDtoDelete()
    {
        Guid existingRecordId = Guid.NewGuid();
        byte[] existingTimeStamp = [1, 2, 3, 4];
        var model = new ManageRestrictionsPostingViewModel
        {
            FiSlug = IFixtureExtensions.FiSlug,
            Products =
            [
                new ManageRestrictionsProductViewModel()
                {
                    ProductId = Guid.NewGuid(),
                    ExcludedStateOriginalDetails = [],
                    Rates =
                    [
                        new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = 1,
                            RestrictedStateAbbreviations = ["ZZ"],
                            RestrictedStateOriginalDetails =
                            [
                                new ManageRestrictionsStateRateViewModel()
                                {
                                    Id = existingRecordId, State = "AL",
                                    TimeStamp = Convert.ToBase64String(existingTimeStamp), Rate = 20,
                                },
                            ],
                        },

                        new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = 1,
                            RestrictedStateAbbreviations = ["ZY"],
                        },

                    ],
                },
            ],
            MilitaryAnnualPercentageRate = 0,
            SettingTimeStamp = null,
            SettingId = Guid.Empty,
        };
        var dto = model.ToDto();
        dto.RestrictedRates.Count.Should().Be(3);
        var rate = dto.RestrictedRates.Single(a => a.State == "AL");

        rate.Id.Should().Be(existingRecordId);
        rate.TimeStamp.Should().BeEquivalentTo(existingTimeStamp);
        rate.State.Should().Be("AL");
        rate.Rate.Should().Be(20);
        rate.UpdateRequested.Should().BeFalse();
        rate.CreateRequested.Should().BeFalse();
        rate.DeleteRequested.Should().BeTrue();

    }
    
}