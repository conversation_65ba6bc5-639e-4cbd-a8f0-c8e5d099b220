using System.Diagnostics.CodeAnalysis;
using AutoFixture;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using NSubstitute;
using QCash.Service.Core;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.UnitTest;
using QCash.Web.Areas.FIConfiguration.Controllers;
using QCash.Web.Areas.FIConfiguration.Models;
using QCash.Web.Models;
using Xunit;

namespace QCash.Web.UnitTest.Controllers;

public class FiInfoControllerTests
{
    private class FiInfoControllerTestRig
    {
        public IFiService FiService { get; set; }
        public IErrorNotificationService ErrorNotificationService { get; set; }
        public IUnitOfWork UnitOfWork { get; set; }
        private ILookupService LookupService { get; set; }
        private IAuthUserService AuthUserService { get; set; }
        private ILogger<FiInfoController> Logger { get; set; }
        public FiInfoController Controller { get; set; }
        public Guid SettingId { get; set; } = Guid.NewGuid();

        public FiInfoControllerTestRig(IFixture fixture)
        {
            FiService = fixture.Freeze<IFiService>();
            ErrorNotificationService = fixture.Freeze<IErrorNotificationService>();
            UnitOfWork = fixture.Freeze<IUnitOfWork>();
            LookupService = fixture.Freeze<ILookupService>();
            AuthUserService = fixture.Freeze<IAuthUserService>();
            Logger = fixture.Freeze<ILogger<FiInfoController>>();
            Controller = fixture.Create<FiInfoController>();

            FiService.GetPageDataAsync().Returns(Task.FromResult<FiInfoDto?>(new FiInfoDto
            {
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Name = "FI New",
                Address = "100 Main Street",
                City = "Las Vegas",
                State = "NV",
                Zip = "88901",
                NoReplySubdomain = "No reply Subdomain Test",
                NoReplyDisplayName = "No reply Display Name Test",
                FinancialInstitutionTimeStamp = [ ],
                ContactName = "Bob Jones",
                ContactPhone = "************",
                ContactEmail = "<EMAIL>",
                SupportEmail = "<EMAIL>",
                TimeZone = "Central Time Zone",
                SettingId = SettingId,
                SettingTimeStamp = [ ],
            }));
            LookupService.GetTimeZoneChoices().Returns(
                [new QListItem<string>() { Value = "Eastern Time Zone", Text = "Eastern Time Zone" },
                    new QListItem<string>() { Value = "Some Other Time Zone", Text = "Some Other Time Zone" }]
            );
            LookupService.GetStatesAsync().Returns(Task.FromResult(new List<QListItem<string>>() {
                new() { Value = "TX", Text = "Texas" },
                new() { Value = "PA", Text = "Pennsylvania" },
                new() { Value = "FL", Text = "Florida" },
                new() { Value = "NV", Text = "Nevada" },
            }));
            AuthUserService.IsQcfFinance().Returns(true);
        }

        public async Task<IActionResult> EditGetAsync() =>
            await Controller.EditAsync((string?)null, (ToastMsgTypeEnum?)null);

        public async Task<IActionResult> EditPostAsync(FiInfoPostingViewModel model, string fiSlug, bool saveIsSuccessful = true)
        {
            FiService.SaveAsync(IFixtureExtensions.FinancialInstitutionId, Arg.Any<FiInfoDto>())
                .Returns(new GenericActionResult() { IsSuccessful = saveIsSuccessful, ErrorMessage = "Test Message", });
            return await Controller.EditAsync(model, fiSlug);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task EditGetAsyncSuccess(IFixture fixture)
    {
        var rig = new FiInfoControllerTestRig(fixture);
        var result = await rig.EditGetAsync() as ViewResult;
        ValidateFiInfoPageViewModel(result?.Model as FiInfoPageViewModel, rig);
        result.ViewName.Should().Be("Edit");
    }

    private static void ValidateFiInfoPageViewModel([NotNull]FiInfoPageViewModel? model, FiInfoControllerTestRig rig, string? submissionNotification = null)
    {
        Assert.NotNull(model);
        model.FinancialInstitutionId.Should().Be(IFixtureExtensions.FinancialInstitutionId);
        model.SettingId.Should().Be(rig.SettingId);
        model.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        model.IsQcfFinance.Should().BeTrue();
        model.Name.Should().Be("FI New");
        model.Address.Should().Be("100 Main Street");
        model.City.Should().Be("Las Vegas");
        model.State.Should().Be("NV");
        model.Zip.Should().Be("88901");
        model.NoReplySubdomain.Should().Be("No reply Subdomain Test");
        model.NoReplyDisplayName.Should().Be("No reply Display Name Test");
        model.ContactName.Should().Be("Bob Jones");
        model.ContactPhone.Should().Be("************");
        model.TimeZone.Should().Be("Central Time Zone");
        model.TimeZoneChoices.Count.Should().Be(2);
        model.TimeZoneChoices.Any(a => a.Value == "Eastern Time Zone").Should().BeTrue();
        model.StateChoices.Count.Should().Be(4);
        model.StateChoices.Any(a => a.Value == "NV").Should().BeTrue();
        model.ContactEmail.Should().Be("<EMAIL>");
        model.SupportEmail.Should().Be("<EMAIL>");
        model.SubmissionNotifications?.Message.Should().Be(submissionNotification);
    }

    private FiInfoPostingViewModel GetViewModelForPostback(FiInfoControllerTestRig rig)
    {
        var model = new FiInfoPostingViewModel
        {
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            SettingId = rig.SettingId,
            Name = "FI New",
            Address = "100 Main Street",
            City = "Las Vegas",
            State = "NV",
            Zip = "88901",
            NoReplySubdomain = "No reply Subdomain Test",
            NoReplyDisplayName = "No reply Display Name Test",
            SettingTimeStamp = "",
            FiSlug = "FiSlug",
            FinancialInstitutionTimeStamp = "",
            SupportEmail = "<EMAIL>",
            ContactEmail = "<EMAIL>",
            ContactPhone = "************",
            ContactName = "Bob Jones",
            TimeZone = "Central Time Zone",
        };
        return model;
    }

    [Theory, AutoSubstituteData]
    public async Task EditPostAsyncSuccess(IFixture fixture)
    {
        var rig = new FiInfoControllerTestRig(fixture);
        var result = await rig.EditPostAsync(GetViewModelForPostback(rig), "FiSlug") as RedirectToActionResult;
        Assert.NotNull(result);
        rig.Controller.ModelState.IsValid.Should().BeTrue();
        await rig.FiService.Received().SaveAsync(IFixtureExtensions.FinancialInstitutionId, Arg.Any<FiInfoDto>());
        await rig.UnitOfWork.Received().CommitAsync();
    }

    [Theory, AutoSubstituteData]
    public async Task EditPostAsyncModelValidationFail(IFixture fixture)
    {
        var rig = new FiInfoControllerTestRig(fixture);
        rig.Controller.ModelState.AddModelError("Test", "Test Error");
        var result = await rig.EditPostAsync(GetViewModelForPostback(rig), IFixtureExtensions.FiSlug) as ViewResult;
        ValidateFiInfoPageViewModel(result?.Model as FiInfoPageViewModel, rig);
        result.ViewName.Should().Be("Edit");
        rig.Controller.ModelState.IsValid.Should().BeFalse();
        await rig.FiService.DidNotReceive().SaveAsync(IFixtureExtensions.FinancialInstitutionId, Arg.Any<FiInfoDto>());
        await rig.UnitOfWork.DidNotReceive().CommitAsync();
    }

    [Theory, AutoSubstituteData]
    public async Task EditPostAsyncModelSaveFail(IFixture fixture)
    {
        var rig = new FiInfoControllerTestRig(fixture);
        rig.ErrorNotificationService.GetNotyErrorNotification(Arg.Any<string>())
            .Returns("This is the error message");
        var result = await rig.EditPostAsync(GetViewModelForPostback(rig), IFixtureExtensions.FiSlug, saveIsSuccessful:false) as ViewResult;
        ValidateFiInfoPageViewModel(result?.Model as FiInfoPageViewModel, rig, "This is the error message");
        result.ViewName.Should().Be("Edit");
        rig.Controller.ModelState.IsValid.Should().BeTrue();
        await rig.FiService.Received().SaveAsync(IFixtureExtensions.FinancialInstitutionId, Arg.Any<FiInfoDto>());
        await rig.UnitOfWork.DidNotReceive().CommitAsync();
    }
}
