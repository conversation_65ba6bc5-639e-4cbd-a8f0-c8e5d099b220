using AutoFixture;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using QCash.Service.Core;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.UnitTest;
using QCash.Web.Controllers;
using QCash.Web.Models;
using QCash.Web.Models.Lookup;
using Xunit;
using ViewResult = Microsoft.AspNetCore.Mvc.ViewResult;

namespace QCash.Web.UnitTest.Controllers;

public class LookupControllerTests
{
    private class LookupControllerTestRig
    {
        public LookupController Controller { get; set; }
        public IDecisionEngineSettingsService DecisionEngineSettingsServiceMock { get; set; }
        public IErrorNotificationService ErrorNotificationServiceMock { get; set; }
        public ILookupItemService LookupItemService { get; set; }
        public IUnitOfWork UnitOfWork { get; set; }

        public LookupControllerTestRig(IFixture fixture)
        {
            {
                ErrorNotificationServiceMock = fixture.Freeze<IErrorNotificationService>();
                ErrorNotificationServiceMock.GetNotyErrorNotification("Sample Error Message")
                    .Returns("<div>Sample Error Message Formatted</div>");
            }
            LookupItemService = fixture.Freeze<ILookupItemService>();
            UnitOfWork = fixture.Freeze<IUnitOfWork>();
            {
                DecisionEngineSettingsServiceMock = Substitute.For<IDecisionEngineSettingsService>();
                DecisionEngineSettingsServiceMock.GetLookupTypeEnum("LoanCode")
                    .Returns(Enums.DecisionEngineLookupTypeEnum.LoanCode);
                DecisionEngineSettingsServiceMock.GetLookupTypeEnum("DecisionEngineTrackingRecordDataMapping")
                    .Returns(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping);
                DecisionEngineSettingsServiceMock.GetLookup(Enums.DecisionEngineLookupTypeEnum.LoanCode)
                    .Returns(new DecisionEngineLookupListType()
                    {
                        LinkText = "Loan Code",
                        LookupType = Enums.DecisionEngineLookupTypeEnum.LoanCode,
                        CssClass = "loan-code",
                        Title = "Loan Codes lookup list",
                        CreateTitle = "Loan Code",
                        NameHeader = "Code",
                        ShowInUi = true,
                        UIGroupEnum = Enums.DecisionEngineUIGroupEnum.CoreApiLookups,
                        UIOrder = 3,
                    });
                DecisionEngineSettingsServiceMock.GetLookup(Enums.DecisionEngineLookupTypeEnum.CheckingAccountType)
                    .Returns(new DecisionEngineLookupListType()
                    {
                        LinkText = "Checking Account Types",
                        LookupType = Enums.DecisionEngineLookupTypeEnum.CheckingAccountType,
                        CssClass = "",
                        Title = "Checking Account Type List",
                        CreateTitle = "Checking Account Type",
                        ListHelpTextId = Guid.Empty,
                        ShowInUi = true,
                        UIGroupEnum = Enums.DecisionEngineUIGroupEnum.AccountsAndTransactions,
                        UIOrder = 2,
                        HasIndividualEditUi = true,
                        HasIndividualListUi = true,
                    });
                DecisionEngineSettingsServiceMock.GetLookup(Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode)
                    .Returns(new DecisionEngineLookupListType()
                    {
                        LinkText = "NSF Transaction Codes",
                        LookupType = Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode,
                        CssClass = "",
                        Title = "NSF Transaction Code List",
                        CreateTitle = "NSF Transaction Code",
                        ListHelpTextId = Guid.Empty,
                        ShowInUi = true,
                        UIGroupEnum = Enums.DecisionEngineUIGroupEnum.AccountsAndTransactions,
                        UIOrder = 4,
                        HasIndividualEditUi = true,
                        HasIndividualListUi = true,
                    });
                DecisionEngineSettingsServiceMock.GetLookup(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping)
                    .Returns(new DecisionEngineLookupListType()
                    {
                        LinkText = "DE Tracking Record Data Mappings",
                        LookupType = Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping,
                        CssClass = "",
                        Title = "DE Tracking Record Data Mappings",
                        CreateTitle = "Create DE Tracking Record Data Mapping",
                        NameHeader = "Value",
                        ShowInUi = true,
                        HasIndividualListUi = true,
                        HasIndividualEditUi = true,
                        UIGroupEnum = Enums.DecisionEngineUIGroupEnum.EligibilityAttributes,
                        UIOrder = 11,
                    });

                DecisionEngineSettingsServiceMock.IsDvLookup(Enums.DecisionEngineLookupTypeEnum.CheckingAccountType)
                    .Returns(true);

                DecisionEngineSettingsServiceMock.GetScoreTypeFromLookupType(Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode)
                    .Returns(Enums.ScoreTypesEnum.NSFTransactionCode);

                DecisionEngineSettingsServiceMock.GetNameHeader(Enums.DecisionEngineLookupTypeEnum.LoanCode)
                    .Returns("Sample Loan Code Name Header");
                fixture.Register(() => DecisionEngineSettingsServiceMock);
            }

            Controller = fixture.Create<LookupController>();
        }


        public IActionResult List(string fiSlug, string lookupTypeName, string? msg, ToastMsgTypeEnum msgType) =>
            Controller.List(fiSlug, lookupTypeName, msg, msgType);

        public async Task<IActionResult> DeleteAsync(string fiSlug, string lookupTypeName, DeleteLookupViewModel model, bool saveIsSuccessful = true)
        {
            DecisionEngineSettingsServiceMock
                .DeleteItemAsync(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping, Arg.Any<DeleteItemDto>())
                .Returns(Task.FromResult(new GenericActionResult()
                {
                    IsSuccessful = saveIsSuccessful,
                    ErrorMessage = saveIsSuccessful ? null : "Sample Error Saving Message",
                }));
            return await Controller.DeleteAsync(fiSlug, lookupTypeName, model);
        }

        public async Task<IActionResult> EditAsyncGet(string lookupTypeName, string lookupItemSlug)
        {
            LookupItemService.CreateBlankItem(IFixtureExtensions.FiSlug, lookupTypeName, "Item1")
                .Returns(new EFPocoLookupDto
                {
                    Name = "Name1",
                    Description = "Desc1",
                    Code = "Code1",
                    Slug = "Slug1",
                    FiSlug = IFixtureExtensions.FiSlug,
                    LookupTypeName = "", 
                });

            LookupItemService.GetItemAsync(IFixtureExtensions.FiSlug, lookupTypeName, "Item1")
                .Returns(new EFPocoLookupDto
                {
                    Name = "Name2",
                    Description = "Desc2",
                    Code = "Code2",
                    Slug = "Slug2",
                    FiSlug = IFixtureExtensions.FiSlug,
                    LookupTypeName = "", 
                });

            return await Controller.EditAsync(lookupTypeName, lookupItemSlug, null, null);
        }

        public async Task<IActionResult> EditAsyncPost(string lookupItemSlug, bool saveIsSuccessful = true)
        {
            var lookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping);
            LookupItemService.SaveAsync(IFixtureExtensions.FinancialInstitutionId, lookupTypeName, Arg.Any<EFPocoLookupDto>())
                .Returns(new GetOrCreateRecordResult
                {
                    IsSuccessful = saveIsSuccessful,
                    ErrorMessage = saveIsSuccessful
                        ? null
                        : "Sample Error Saving Message",
                    Record = null,
                    FoundExistingRecord = false,
                    CreatingNewRecord = false,
                    EditingExistingRecord = false,
                });
            ErrorNotificationServiceMock.GetNotyErrorNotification("Invalid route parameters.")
                .Returns("<div>Invalid route parameters.</div>");
            ErrorNotificationServiceMock.GetNotyErrorNotification("Sample Error Saving Message")
                .Returns("<div>Sample Error Saving Message</div>");

            var model = new EditPostingViewModel()
            {
                Slug = "ItemSlug1",
                FiSlug = IFixtureExtensions.FiSlug,
                LookupTypeName = lookupTypeName,
            };
            return await Controller.EditAsync(model, IFixtureExtensions.FiSlug, lookupTypeName, lookupItemSlug);
        }
    }

    [Theory, AutoSubstituteData]
    public void ListTest1(IFixture fixture)
    {
        var rig = new LookupControllerTestRig(fixture);
        var lookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.LoanCode);
        var result = rig.List("DOES NOT MATTER", lookupTypeName, "Sample Error Message", ToastMsgTypeEnum.Error);
        var viewResult = result as ViewResult;
        var model = viewResult?.Model as ListPageViewModel;
        Assert.NotNull(model);
        model.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        model.LookupTypeName.Should().Be("LoanCode");
        model.LookupTypeEnum.Should().Be(Enums.DecisionEngineLookupTypeEnum.LoanCode);
        model.NameHeader.Should().Be("Sample Loan Code Name Header");
        model.SubmissionNotifications.Should().NotBeNull();
        model.SubmissionNotifications?.Message.Should().Be("<div>Sample Error Message Formatted</div>");
        model.SubmissionNotifications?.MsgType.Should().Be(ToastMsgTypeEnum.Error);
    }

    [Theory, AutoSubstituteData]
    public void ListWithIndividualListViewDv(IFixture fixture)
    {
        var rig = new LookupControllerTestRig(fixture);
        var lookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.CheckingAccountType);
        var result = rig.List("DOES NOT MATTER", lookupTypeName, "Sample Error Message", ToastMsgTypeEnum.Error);
        var redirectToActionResult = result as RedirectToActionResult;
        Assert.NotNull(redirectToActionResult);
        redirectToActionResult.ActionName.Should().Be("List");
        redirectToActionResult.ControllerName.Should().Be("DvLookup");
        Assert.NotNull(redirectToActionResult.RouteValues);
        redirectToActionResult.RouteValues.Any(a =>
            a.Key == "lookupTypeName" && a.Value as string == "CheckingAccountType").Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public void ListWithIndividualListViewScoreType(IFixture fixture)
    {
        var rig = new LookupControllerTestRig(fixture);
        var lookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode);
        var result = rig.List("DOES NOT MATTER", lookupTypeName, "Sample Error Message", ToastMsgTypeEnum.Error);
        var redirectToActionResult = result as RedirectToActionResult;
        Assert.NotNull(redirectToActionResult);
        redirectToActionResult.ActionName.Should().Be("ListScoreTypes");
        redirectToActionResult.ControllerName.Should().Be("DecisionEngineSettings");
        Assert.NotNull(redirectToActionResult.RouteValues);
        redirectToActionResult.RouteValues.Any(a =>
            a.Key == "scoreTypeSlug" && a.Value as string == "nsf_transaction_code").Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public void ListWithIndividualListViewOther(IFixture fixture)
    {
        var rig = new LookupControllerTestRig(fixture);
        var lookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping);
        var result = rig.List("DOES NOT MATTER", lookupTypeName, "Sample Error Message", ToastMsgTypeEnum.Error);
        var viewResult = result as ViewResult;
        var model = viewResult?.Model as ListPageViewModel;
        Assert.NotNull(model);
        model.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        model.LookupTypeName.Should().Be("DecisionEngineTrackingRecordDataMapping");
        model.LookupTypeEnum.Should().Be(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping);
        model.NameHeader.Should().Be("");
        model.SubmissionNotifications.Should().NotBeNull();
        model.SubmissionNotifications?.Message.Should().Be("<div>Sample Error Message Formatted</div>");
        model.SubmissionNotifications?.MsgType.Should().Be(ToastMsgTypeEnum.Error);
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteActionSuccess(IFixture fixture)
    {
        var rig = new LookupControllerTestRig(fixture);
        var lookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping);
        var postModel = new DeleteLookupViewModel
        {
            FiSlug = IFixtureExtensions.FiSlug,
            LookupTypeName = lookupTypeName,
            DeleteItemId = Guid.NewGuid(),
            TimeStamp = "AAEC",
        };
        var result = await rig.DeleteAsync(IFixtureExtensions.FiSlug, lookupTypeName, postModel) as RedirectToActionResult;
        Assert.NotNull(result?.RouteValues);
        result.ActionName.Should().Be("List");
        result.RouteValues.Any(a => a.Key.Equals("LookupTypeName") && a.Value as string == lookupTypeName).Should().BeTrue();
        result.RouteValues.Any(a => a.Key == "msg" && a.Value as string == "Item has been deleted.").Should().BeTrue();

        await rig.DecisionEngineSettingsServiceMock.Received()
            .DeleteItemAsync(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping, Arg.Any<DeleteItemDto>());
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteActionNoItem(IFixture fixture)
    {
        var rig = new LookupControllerTestRig(fixture);
        var lookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping);
        var postModel = new DeleteLookupViewModel
        {
            FiSlug = IFixtureExtensions.FiSlug,
            LookupTypeName = lookupTypeName,
            DeleteItemId = Guid.Empty,
            TimeStamp = "AAEC",
        };
        var result = await rig.DeleteAsync(IFixtureExtensions.FiSlug, lookupTypeName, postModel) as RedirectToActionResult;
        Assert.NotNull(result?.RouteValues);
        result.ActionName.Should().Be("List");
        result.RouteValues.Any(a => a.Key.Equals("LookupTypeName") && a.Value as string == lookupTypeName).Should().BeTrue();
        result.RouteValues.Any(a => a.Key == "msg" && a.Value as string == "Item Id is missing.").Should().BeTrue();

        await rig.DecisionEngineSettingsServiceMock.DidNotReceive()
            .DeleteItemAsync(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping, Arg.Any<DeleteItemDto>());
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteActionErrorSaving(IFixture fixture)
    {
        var rig = new LookupControllerTestRig(fixture);
        var lookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping);
        var postModel = new DeleteLookupViewModel
        {
            FiSlug = IFixtureExtensions.FiSlug,
            LookupTypeName = lookupTypeName,
            DeleteItemId = Guid.NewGuid(),
            TimeStamp = "AAEC",
        };
        var result = await rig.DeleteAsync(IFixtureExtensions.FiSlug, lookupTypeName, postModel, false) as RedirectToActionResult;
        Assert.NotNull(result?.RouteValues);
        result.ActionName.Should().Be("List");
        result.RouteValues.Any(a => a.Key.Equals("LookupTypeName") && a.Value as string == lookupTypeName).Should().BeTrue();
        result.RouteValues.Any(a => a.Key == "msg" && a.Value as string == "Sample Error Saving Message").Should().BeTrue();

        await rig.DecisionEngineSettingsServiceMock.Received()
            .DeleteItemAsync(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping, Arg.Any<DeleteItemDto>());
    }

    [Theory, AutoSubstituteData]
    public async Task EditGetCreateItem(IFixture fixture)
    {
        var rig = new LookupControllerTestRig(fixture);
        var lookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping);
        var viewResult = await rig.EditAsyncGet(lookupTypeName, Service.Utilities.ItemConstants.CreateItemTag) as ViewResult;
        Assert.NotNull(viewResult);
        var model = viewResult.Model as EditPageViewModel;
        Assert.NotNull(model);
        model.SubmissionNotifications.Should().BeNull();
        rig.LookupItemService.Received().CreateBlankItem(IFixtureExtensions.FiSlug, lookupTypeName, Service.Utilities.ItemConstants.CreateItemTag);
        await rig.LookupItemService.DidNotReceive().GetItemAsync(IFixtureExtensions.FiSlug, lookupTypeName, Service.Utilities.ItemConstants.CreateItemTag);
    }

    [Theory, AutoSubstituteData]
    public async Task EditGetExistingItem(IFixture fixture)
    {
        var itemId = Guid.NewGuid();
        var rig = new LookupControllerTestRig(fixture);
        var lookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping);
        var viewResult = await rig.EditAsyncGet(lookupTypeName, itemId.ToString()) as ViewResult;
        Assert.NotNull(viewResult);
        var model = viewResult.Model as EditPageViewModel;
        Assert.NotNull(model);
        model.SubmissionNotifications.Should().BeNull();
        rig.LookupItemService.DidNotReceive().CreateBlankItem(IFixtureExtensions.FiSlug, lookupTypeName, itemId.ToString());
        await rig.LookupItemService.Received().GetItemAsync(IFixtureExtensions.FiSlug, lookupTypeName, itemId.ToString());
    }

    [Theory, AutoSubstituteData]
    public async Task EditPostSuccess(IFixture fixture)
    {
        var rig = new LookupControllerTestRig(fixture);
        var lookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping);
        var redirectToActionResult = await rig.EditAsyncPost("ItemSlug1") as RedirectToActionResult;
        Assert.NotNull(redirectToActionResult?.RouteValues);
        redirectToActionResult.ActionName.Should().Be("List");
        redirectToActionResult.RouteValues.Any(a => a.Key.Equals("LookupTypeName") && a.Value as string == lookupTypeName).Should().BeTrue();
        await rig.UnitOfWork.Received().CommitAsync();
        await rig.LookupItemService.Received().SaveAsync(IFixtureExtensions.FinancialInstitutionId, lookupTypeName, Arg.Any<EFPocoLookupDto>());
    }

    [Theory, AutoSubstituteData]
    public async Task EditPostInvalidModel(IFixture fixture)
    {
        var rig = new LookupControllerTestRig(fixture);
        rig.Controller.ModelState.AddModelError("Test", "Test Error");
        var redirectToActionResult = await rig.EditAsyncPost("ItemSlug1") as ViewResult;
        Assert.NotNull(redirectToActionResult);
        var model = redirectToActionResult.Model as EditPageViewModel;
        Assert.NotNull(model);
        model.SubmissionNotifications.Should().BeNull();
        await rig.UnitOfWork.DidNotReceive().CommitAsync();
        await rig.LookupItemService.DidNotReceive().SaveAsync(IFixtureExtensions.FinancialInstitutionId, Arg.Any<string>(), Arg.Any<EFPocoLookupDto>());
    }

    [Theory, AutoSubstituteData]
    public async Task EditPostFailedToSave(IFixture fixture)
    {
        var rig = new LookupControllerTestRig(fixture);
        var redirectToActionResult = await rig.EditAsyncPost("ItemSlug1", false) as ViewResult;
        Assert.NotNull(redirectToActionResult);
        var model = redirectToActionResult.Model as EditPageViewModel;
        Assert.NotNull(model);
        model.SubmissionNotifications!.Message.Should().Be("<div>Sample Error Saving Message</div>");
        model.SubmissionNotifications!.MsgType.Should().Be(ToastMsgTypeEnum.Error);
        await rig.UnitOfWork.DidNotReceive().CommitAsync();
        await rig.LookupItemService.Received().SaveAsync(IFixtureExtensions.FinancialInstitutionId, Arg.Any<string>(), Arg.Any<EFPocoLookupDto>());
        rig.ErrorNotificationServiceMock.Received().GetNotyErrorNotification("Sample Error Saving Message");
    }
}
