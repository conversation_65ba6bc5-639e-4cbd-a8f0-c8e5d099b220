using AutoFixture;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.UnitTest;
using QCash.Web.Areas.FIConfiguration.Controllers;
using Xunit;
using NSubstitute;
using QCash.Service.Core;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Services.General.Interfaces;
using QCash.Web.Areas.FIConfiguration.Models;


namespace QCash.Web.UnitTest.Controllers;

public class GeneralControllerTests
{
    private class TestRig(IFixture fixture)
    {
        public IFiService FiService { get; set; } = fixture.Freeze<IFiService>();
        public IAuthUserService AuthUserService { get; set; } = fixture.Freeze<IAuthUserService>();
        public IUnitOfWork UnitOfWork { get; set; } = fixture.Freeze<IUnitOfWork>();
        public GeneralController Controller { get; set; } = fixture.Freeze<GeneralController>();
        public Guid SettingId = Guid.NewGuid();
        public Guid FinancialInstitutionId = Guid.NewGuid();

        public async Task<IActionResult> EditGetAsync()
        {
            FiGeneralInfoDto? dto = new FiGeneralInfoDto
            {
                SettingId = SettingId,
                SettingTimeStamp = [1, 2, 3],
                FinancialInstitutionId = FinancialInstitutionId,
                MonitorAccount = "MonitorAccount1",
                FiSlug = IFixtureExtensions.FiSlug,
                CoreCheckInterval = 123,
                CacheFailedCoreResponses = true,
                UseTwoFactorAuthentication = true,
            };
            FiService.GetGeneralInfoAsync().Returns(dto);
            AuthUserService.IsSuperUser().Returns(true);
            AuthUserService.IsSystemAdmin().Returns(true);
            return await Controller.EditAsync(null);
        }

        public async Task<IActionResult> EditPostAsync()
        {
            var userSubmittedModel = new FiGeneralInfoPostingViewModel
            {
                SettingTimeStamp = Convert.ToBase64String([1,2,3]),
                MonitorAccount = "MonitorAccount1EDITED",
                FiSlug = IFixtureExtensions.FiSlug,
                UseTwoFactorAuthentication = false,
                CacheFailedCoreResponses = false,
                CoreCheckInterval = null,
            };
            FiService.SaveGeneralInfoAsync(IFixtureExtensions.FinancialInstitutionId, Arg.Any<FiGeneralInfoDto>())
                .Returns(new GenericActionResult() { IsSuccessful = true, ErrorMessage = "", });

            return await Controller.EditAsync(userSubmittedModel, IFixtureExtensions.FiSlug);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task EditGetAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.EditGetAsync() as ViewResult;
        var model = result?.Model as FiGeneralInfoPageViewModel;
        Assert.NotNull(model);
        model.SettingId.Should().Be(rig.SettingId);
        model.FinancialInstitutionId.Should().Be(rig.FinancialInstitutionId);
        model.MonitorAccount.Should().Be("MonitorAccount1");
        model.CoreCheckInterval.Should().Be(123);
        model.CacheFailedCoreResponses.Should().BeTrue();
        model.UseTwoFactorAuthentication.Should().BeTrue();
        model.IsSuperUser.Should().BeTrue();
        model.IsSystemAdmin.Should().BeTrue();
        await rig.FiService.Received().GetGeneralInfoAsync();
    }

    [Theory, AutoSubstituteData]
    public async Task EditPostSuccessAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var iActionResult = await rig.EditPostAsync();
        var redirectToActionResult = iActionResult as RedirectToActionResult;
        Assert.NotNull(redirectToActionResult);
        redirectToActionResult.ActionName.Should().Be("Edit");
        redirectToActionResult.ControllerName.Should().Be("General");

        await rig.FiService.Received()
            .SaveGeneralInfoAsync(IFixtureExtensions.FinancialInstitutionId, Arg.Any<FiGeneralInfoDto>());
        await rig.UnitOfWork.Received()
            .CommitAsync();


    }
}
