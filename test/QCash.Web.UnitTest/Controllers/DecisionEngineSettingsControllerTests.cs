using AutoFixture;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using QCash.Service.Core;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.UnitTest;
using QCash.Service.Utilities.Extensions;
using QCash.Web.Controllers;
using QCash.Web.Models.DecisionEngine;
using QCash.Web.Models.Lookup;
using Xunit;

namespace QCash.Web.UnitTest.Controllers;

public class DecisionEngineSettingsControllerTests
{
    private class DecisionEngineSettingsControllerTestRig
    {
        public DecisionEngineSettingsController Controller { get; set; }
        public IDecisionEngineSettingsService DecisionEngineSettingsServiceMock { get; set; }
        private IErrorNotificationService ErrorNotificationServiceMock { get; set; }
        public IUnitOfWork UnitOfWorkMock { get; set; }
        private ILogger<DecisionEngineSettingsController> LoggerMock { get; set; }
        public readonly Enums.ScoreTypesEnum ScoreTypeEnum = Enums.ScoreTypesEnum.NSFTransactionCode;
        public BusinessRuleViewModel NewRecordModel { get; set; }
        public BusinessRuleDto NewRecordDto { get; set; }
        public DeleteLookupViewModel DeleteLookupViewModel { get; set; }

        public DecisionEngineSettingsControllerTestRig(IFixture fixture)
        {
            DecisionEngineSettingsServiceMock = fixture.Freeze<IDecisionEngineSettingsService>();
            ErrorNotificationServiceMock = fixture.Freeze<IErrorNotificationService>();
            UnitOfWorkMock = fixture.Freeze<IUnitOfWork>();
            LoggerMock = fixture.Freeze<ILogger<DecisionEngineSettingsController>>();
            Controller = fixture.Create<DecisionEngineSettingsController>();
            // Controller = new DecisionEngineSettingsController(DecisionEngineSettingsServiceMock, ErrorNotificationServiceMock,
            //     UnitOfWorkMock, LoggerMock);

            NewRecordModel = new BusinessRuleViewModel
            {
                Id = Guid.NewGuid(),
                ScoreTypeSlug = ScoreTypeEnum.ToDescriptionString() ?? "",
                FiSlug = IFixtureExtensions.FiSlug,
                Name = "Test Name",
                Detail = "Test Detail",
                Score = 10,
            };
            NewRecordDto = new BusinessRuleDto
            {
                Id = Guid.NewGuid(),
                TimeStamp = [1, 2, 3],
                FiSlug = IFixtureExtensions.FiSlug,
                Name = "Test Name 1",
                Detail = "Test Detail 1",
                Score = 0,
                ScoreTypeSlug = ScoreTypeEnum.ToDescriptionString() ?? "",
            };
            DeleteLookupViewModel = new DeleteLookupViewModel()
            {
                DeleteItemId = Guid.NewGuid(),
                TimeStamp =  Convert.ToBase64String([0, 1, 2]),
                FiSlug = IFixtureExtensions.FiSlug,
                LookupTypeName = Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode.ToString() ?? "",
            };
        }

        public async Task<IActionResult> CreateBusinessRuleAsync(string? msg)
        {
            DecisionEngineSettingsServiceMock.ShouldHideScore(ScoreTypeEnum)
                .Returns(true);
            return await Controller.CreateBusinessRuleAsync(IFixtureExtensions.FiSlug, ScoreTypeEnum.ToDescriptionString() ?? "", msg);
        }

        public async Task<IActionResult> SaveNewRecord(BusinessRuleViewModel model, Guid id, string? fiSlug = null, string? msg = null,
            bool saveIsSuccessful = true, bool saveCausesException = false)
        {
            DecisionEngineSettingsServiceMock.ShouldHideScore(ScoreTypeEnum)
                .Returns(true);

            if (saveCausesException)
            {
                DecisionEngineSettingsServiceMock.SaveAsync(IFixtureExtensions.FinancialInstitutionId, Arg.Any<BusinessRuleDto>())
                    .ThrowsAsync<Exception>();

                ErrorNotificationServiceMock.GetNotyErrorNotification(Arg.Any<string>())
                    .Returns("Sample Error Message");
            }
            else
            {
                DecisionEngineSettingsServiceMock.SaveAsync(IFixtureExtensions.FinancialInstitutionId, Arg.Any<BusinessRuleDto>())
                    .Returns(Task.FromResult(new GenericActionResult()
                    {
                        IsSuccessful = saveIsSuccessful, ErrorMessage = "Test Message",
                    }));
            }


            fiSlug ??= IFixtureExtensions.FiSlug;
            var scoreTypeSlug = ScoreTypeEnum.ToDescriptionString() ?? "";
            return await Controller.EditAsync(model, fiSlug, scoreTypeSlug, id);
        }

        public async Task<IActionResult> Delete(string? fiSlug = null, string? scoreTypeSlug = null, bool deleteIsSuccessful = true)
        {
            scoreTypeSlug ??= ScoreTypeEnum.ToDescriptionString() ?? "";

            fiSlug ??= IFixtureExtensions.FiSlug;

            DecisionEngineSettingsServiceMock
                .DeleteItemAsync(Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode, Arg.Any<DeleteItemDto>())
                .Returns(Task.FromResult(new GenericActionResult() { IsSuccessful = deleteIsSuccessful, ErrorMessage = "Test Message",}));

            DecisionEngineSettingsServiceMock.GetLookupTypeEnumFromScoreType(ScoreTypeEnum)
                .Returns(Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode);

            return await Controller.DeleteScoreTypeAsync( fiSlug, scoreTypeSlug, DeleteLookupViewModel);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task CreateBusinessRuleGetAsync(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsControllerTestRig(fixture);
        var viewResult = await rig.CreateBusinessRuleAsync(null) as ViewResult;
        Assert.NotNull(viewResult);
        viewResult.ViewName.Should().Be("Edit");
        var model = viewResult.Model as BusinessRuleViewModel;
        Assert.NotNull(model);
        model.ShouldHideScore.Should().BeTrue();
        model.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        model.Id.Should().Be(Guid.Empty);
        model.ScoreTypeSlug.Should().Be("nsf_transaction_code");

        rig.DecisionEngineSettingsServiceMock.Received()
            .ShouldHideScore(rig.ScoreTypeEnum);
    }

    [Theory, AutoSubstituteData]
    public async Task SaveNewRecordSuccess(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsControllerTestRig(fixture);
        var userEditModel = rig.NewRecordModel;
        var result = await rig.SaveNewRecord(userEditModel, userEditModel.Id, null) as RedirectResult;
        Assert.NotNull(result);

        rig.Controller.ModelState.IsValid.Should().BeTrue();

        await rig.DecisionEngineSettingsServiceMock
            .Received()
            .ValidateModelAsync(Arg.Any<BusinessRuleDto>(), Arg.Any<ModelStateDictionary>());

        await rig.DecisionEngineSettingsServiceMock
            .Received()
            .SaveAsync(IFixtureExtensions.FinancialInstitutionId, Arg.Is<BusinessRuleDto>(b =>
                        b.Id.Equals(userEditModel.Id)
                        && b.FiSlug.Equals(userEditModel.FiSlug)
                        && b.Name.Equals(userEditModel.Name)
                        && b.Detail.Equals(userEditModel.Detail)
                        && b.Score.Equals(userEditModel.Score)
                        && b.ScoreTypeSlug.Equals(userEditModel.ScoreTypeSlug)
                    )
                );

        await rig.UnitOfWorkMock.Received().CommitAsync();
    }

    [Theory, AutoSubstituteData]
    public async Task SaveNewRecordBadRoute(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsControllerTestRig(fixture);
        var userEditModel = rig.NewRecordModel;
        await Assert.ThrowsAsync<Exception>(() => rig.SaveNewRecord(userEditModel, userEditModel.Id, "WrongFiSlug", null));

        await rig.DecisionEngineSettingsServiceMock
            .DidNotReceive().ValidateModelAsync(Arg.Any<BusinessRuleDto>(), Arg.Any<ModelStateDictionary>());

        await rig.DecisionEngineSettingsServiceMock
            .DidNotReceive().SaveAsync(Arg.Any<Guid>(), Arg.Any<BusinessRuleDto>());

        await rig.UnitOfWorkMock.DidNotReceive().CommitAsync();
    }

    [Theory, AutoSubstituteData]
    public async Task SaveNewRecordInvalidModel(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsControllerTestRig(fixture);
        rig.Controller.ModelState.AddModelError("Test", "Test Error");
        var userEditModel = rig.NewRecordModel;
        var result = await rig.SaveNewRecord(userEditModel, userEditModel.Id, null) as ViewResult;
        Assert.NotNull(result);
        result.ViewName.Should().Be("Edit");
        var model = result.Model as BusinessRuleViewModel;
        Assert.NotNull(model);
        model.SubmissionNotifications.Should().BeNull();
        rig.Controller.ModelState.IsValid.Should().BeFalse();

        await rig.DecisionEngineSettingsServiceMock
            .Received()
            .ValidateModelAsync(Arg.Any<BusinessRuleDto>(), Arg.Any<ModelStateDictionary>());

        await rig.DecisionEngineSettingsServiceMock
            .DidNotReceive().SaveAsync(Arg.Any<Guid>(), Arg.Any<BusinessRuleDto>());

        await rig.UnitOfWorkMock.DidNotReceive().CommitAsync();
    }

    [Theory, AutoSubstituteData]
    public async Task SaveNewRecordFailedToSave(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsControllerTestRig(fixture);
        var userEditModel = rig.NewRecordModel;
        var result = await rig.SaveNewRecord(userEditModel, userEditModel.Id, null, saveIsSuccessful:false) as ViewResult;
        Assert.NotNull(result);
        result.ViewName.Should().Be("Edit");
        rig.Controller.ModelState.IsValid.Should().BeTrue();

        await rig.DecisionEngineSettingsServiceMock
            .Received()
            .ValidateModelAsync(Arg.Any<BusinessRuleDto>(), Arg.Any<ModelStateDictionary>());

        await rig.DecisionEngineSettingsServiceMock
            .Received().SaveAsync(Arg.Any<Guid>(), Arg.Any<BusinessRuleDto>());

        await rig.UnitOfWorkMock.DidNotReceive().CommitAsync();
    }

    [Theory, AutoSubstituteData]
    public async Task SaveNewRecordExceptionWhileSaving(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsControllerTestRig(fixture);
        var userEditModel = rig.NewRecordModel;
        var viewResult = await rig.SaveNewRecord(userEditModel, userEditModel.Id, null, saveCausesException:true) as ViewResult;
        Assert.NotNull(viewResult);
        viewResult.ViewName.Should().Be("Edit");
        var model = viewResult.Model as BusinessRuleViewModel;
        Assert.NotNull(model);
        model.SubmissionNotifications.Should().Be("Sample Error Message");
        rig.Controller.ModelState.IsValid.Should().BeTrue();

        await rig.DecisionEngineSettingsServiceMock
            .Received()
            .ValidateModelAsync(Arg.Any<BusinessRuleDto>(), Arg.Any<ModelStateDictionary>());

        await rig.DecisionEngineSettingsServiceMock
            .Received().SaveAsync(Arg.Any<Guid>(), Arg.Any<BusinessRuleDto>());

        await rig.UnitOfWorkMock.DidNotReceive().CommitAsync();
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteSuccess(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsControllerTestRig(fixture);
        var result = await rig.Delete() as RedirectResult;
        Assert.NotNull(result);

        rig.Controller.ModelState.IsValid.Should().BeTrue();

        await rig.DecisionEngineSettingsServiceMock
            .Received()
            .DeleteItemAsync(Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode, Arg.Is<DeleteItemDto>(b =>
                    b.DeleteItemId.Equals(rig.DeleteLookupViewModel.DeleteItemId)
                )
            );

        await rig.UnitOfWorkMock.Received().CommitAsync();
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteFailEmptyId(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsControllerTestRig(fixture) { DeleteLookupViewModel = { DeleteItemId = Guid.Empty } };
        var result = await rig.Delete() as RedirectResult;
        Assert.NotNull(result);

        rig.Controller.ModelState.IsValid.Should().BeTrue();

        await rig.DecisionEngineSettingsServiceMock
            .DidNotReceive()
            .DeleteItemAsync(Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode, Arg.Any<DeleteItemDto>());

        await rig.UnitOfWorkMock.DidNotReceive().CommitAsync();
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteFailOnSave(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsControllerTestRig(fixture);
        var result = await rig.Delete(deleteIsSuccessful:false) as RedirectResult;
        Assert.NotNull(result);

        rig.Controller.ModelState.IsValid.Should().BeTrue();

        await rig.DecisionEngineSettingsServiceMock
            .Received()
            .DeleteItemAsync(Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode, Arg.Is<DeleteItemDto>(b =>
                    b.DeleteItemId.Equals(rig.DeleteLookupViewModel.DeleteItemId)
                )
            );

        await rig.UnitOfWorkMock.DidNotReceive().CommitAsync();
    }
}
