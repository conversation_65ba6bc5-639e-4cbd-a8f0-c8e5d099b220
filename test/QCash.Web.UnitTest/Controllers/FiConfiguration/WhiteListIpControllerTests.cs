using AutoFixture;
using FluentAssertions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using QCash.Data.Models;
using QCash.Service.Core;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.UnitTest;
using QCash.Service.Utilities.Extensions;
using QCash.Web.Areas.FIConfiguration.Controllers;
using QCash.Web.Areas.FIConfiguration.Models;
using QCash.Web.Models.General;
using Xunit;
using Enums = QCash.Service.Models.Core.Enums;

namespace QCash.Web.UnitTest.Controllers.FiConfiguration;

public class WhiteListIpControllerTests
{
    private class TestRig(IFixture fixture)
    {
        public IWhiteListIpService WhiteListIpService { get; } = fixture.Freeze<IWhiteListIpService>();
        public readonly IErrorNotificationService ErrorNotificationService = fixture.Freeze<IErrorNotificationService>();
        public IUnitOfWork UnitOfWork { get; } = fixture.Freeze<IUnitOfWork>();
        public WhiteListIpController Controller { get; } = fixture.Freeze<WhiteListIpController>();
        private readonly Guid _itemId = Guid.NewGuid();

        public IActionResult List(string? msg = null)
        {
            ErrorNotificationService.GetNotyErrorNotification("dummy error")
                .Returns("formatted dummy error");
            return Controller.List(Enums.WhiteListIpTypeEnum.AdminUI, msg);
        }

        public async Task<IActionResult> GetGridDataAsync(DataSourceRequest dataSourceRequest)
        {
            WhiteListIpService.GetAllAsync( Enums.WhiteListIpTypeEnum.AdminUI)
                .Returns([
                    new WhiteListIpDto
                    {
                        WhiteListIpType = Enums.WhiteListIpTypeEnum.AdminUI, Port = null,
                        Id = Guid.NewGuid(), IpAddress = "127.0.0.1", Description = "Test1", TimeStamp = [0, 1, 2],
                    },

                    new WhiteListIpDto
                    {
                        WhiteListIpType = Enums.WhiteListIpTypeEnum.AdminUI, Port = null,
                        Id = Guid.NewGuid(), IpAddress = "*********", Description = "Test2", TimeStamp = [0, 1, 2],
                    },
                ]);

            return await Controller.GetGridDataAsync(dataSourceRequest, Enums.WhiteListIpTypeEnum.AdminUI);
        }

        public IActionResult Create()
        {
            ErrorNotificationService.GetNotyErrorNotification("dummy error")
                .Returns("formatted dummy error");
            return Controller.Create(Enums.WhiteListIpTypeEnum.AdminUI, null);
        }

        public async Task<IActionResult> CreateAsync(bool saveIsSuccessful = true, bool saveThrowsException = false)
        {
            if (saveThrowsException)
            {
                WhiteListIpService.SaveAsync(Arg.Any<WhiteListIpDto>(), Arg.Any<Guid>())
                    .ThrowsAsync(new Exception("Sample Error Saving Message"));
                ErrorNotificationService.GetNotyErrorNotification("Error while saving financial institution information.")
                    .Returns("Formatted Error while saving financial institution information.");
            }
            else
            {
                WhiteListIpService.SaveAsync(Arg.Any<WhiteListIpDto>(), Arg.Any<Guid>())
                    .Returns(new GetOrCreateRecordResult
                    {
                        CreatingNewRecord = false,
                        Record = new WhiteListIp()
                        {
                            Id = Guid.NewGuid(), IpAddress = "***********", Description = "Test1", TimeStamp = [0, 1, 2],
                        },
                        FoundExistingRecord = true,
                        EditingExistingRecord = false,
                        IsSuccessful = saveIsSuccessful,
                        ErrorMessage = saveIsSuccessful ? null : "Sample Error Saving Message",
                    });
                if (!saveIsSuccessful)
                {
                    ErrorNotificationService.GetNotyErrorNotification("Sample Error Saving Message")
                        .Returns("save failed");
                }
            }

            var model = new WhiteListIpCreatePageViewModel {
                FiSlug = IFixtureExtensions.FiSlug,
                Id = Guid.NewGuid(),
                IPAddress = "***********",
                Description = "",
                WhiteListIpType = Enums.WhiteListIpTypeEnum.AdminUI, Port = null,
            };
            return await Controller.CreateAsync(model);
        }

        public async Task<IActionResult> DeleteAsync()
        {
            WhiteListIpService.DeleteItemAsync(Arg.Any<Enums.WhiteListIpTypeEnum>(),Arg.Any<DeleteItemDto>())
                .Returns(new GenericActionResult() { IsSuccessful = true, ErrorMessage = "", });
            var vm = new DeleteItemViewModel
                {
                    FiSlug = IFixtureExtensions.FiSlug,
                    DeleteItemId = _itemId,
                    TimeStamp = Convert.ToBase64String([0, 1, 2]),
                };
            return await Controller.DeleteAsync(IFixtureExtensions.FiSlug, Enums.WhiteListIpTypeEnum.AdminUI, vm);
        }
    }

    [Theory, AutoSubstituteData]
    private void List(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = rig.List("dummy error") as ViewResult;
        result.ThrowIfNull();
        result.ViewName.Should().Be("List");
        var model = result.Model as WhiteListIpListPageViewModel;
        model.ThrowIfNull();
        model.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        model.SubmissionNotifications!.Message.Should().Be("formatted dummy error");
    }

    [Theory, AutoSubstituteData]
    private async Task GetGridDataAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var rawResult = await rig.GetGridDataAsync(new DataSourceRequest());
        var jsonResult = rawResult as JsonResult;
        var dsr = jsonResult?.Value as DataSourceResult;
        Assert.NotNull(dsr);
        var dataEnum = dsr.Data as IEnumerable<WhiteListIpListItemViewModel>;
        dsr.Total.Should().Be(2);
        var list = dataEnum?.ToList();
        Assert.NotNull(list);
        list.Count.Should().Be(2);
        await rig.WhiteListIpService.Received().GetAllAsync(Enums.WhiteListIpTypeEnum.AdminUI);
    }

    [Theory, AutoSubstituteData]
    private void GetCreatePage(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = rig.Create() as ViewResult;
        result.ThrowIfNull();
        result.ViewName.Should().Be("Edit");
        var model = result.Model as WhiteListIpCreatePageViewModel;
        model.ThrowIfNull();
        model.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
    }

    [Theory, AutoSubstituteData]
    private async Task CreateAsyncWithSuccessAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var redirectResult = await rig.CreateAsync() as RedirectToActionResult;
        await rig.UnitOfWork.Received().CommitAsync();
        await rig.WhiteListIpService.Received().SaveAsync(Arg.Any<WhiteListIpDto>(), Arg.Any<Guid>());
        rig.ErrorNotificationService.DidNotReceive().GetNotyErrorNotification(Arg.Any<string>());
        redirectResult.ThrowIfNull();
        redirectResult.ActionName.Should().Be("List");
        redirectResult.ControllerName.Should().Be("WhiteListIp");
        redirectResult.RouteValues?.Any(a =>
            a.Key == "area" && a.Value as string == Constants.FiConfigurationArea).Should().BeTrue();
        redirectResult.RouteValues?.Any(a =>
            a.Key == nameof(IFixtureExtensions.FiSlug) && a.Value as string == IFixtureExtensions.FiSlug).Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    private async Task CreateAsyncWithModelValidationFailAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        rig.Controller.ModelState.AddModelError("Test", "Test Error");
        var viewResult = await rig.CreateAsync() as ViewResult;
        await rig.UnitOfWork.DidNotReceive().CommitAsync();
        await rig.WhiteListIpService.DidNotReceive().SaveAsync(Arg.Any<WhiteListIpDto>(), Arg.Any<Guid>());
        rig.ErrorNotificationService.DidNotReceive().GetNotyErrorNotification("dummy error");

        viewResult.ThrowIfNull();
        viewResult.ViewName.Should().Be("Edit");
        var model = viewResult.Model as WhiteListIpCreatePageViewModel;
        model.ThrowIfNull();
        model.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        model.SubmissionNotifications.Should().Be(null);
        rig.Controller.ModelState.IsValid.Should().BeFalse();
    }

    [Theory, AutoSubstituteData]
    private async Task CreateAsyncWithSaveFailureAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var viewResult = await rig.CreateAsync(saveIsSuccessful:false) as ViewResult;
        await rig.UnitOfWork.DidNotReceive().CommitAsync();
        await rig.WhiteListIpService.Received().SaveAsync(Arg.Any<WhiteListIpDto>(), Arg.Any<Guid>());
        rig.ErrorNotificationService.Received().GetNotyErrorNotification("Sample Error Saving Message");

        viewResult.ThrowIfNull();
        viewResult.ViewName.Should().Be("Edit");
        var model = viewResult.Model as WhiteListIpCreatePageViewModel;
        model.ThrowIfNull();
        model.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        model.SubmissionNotifications.Should().Be("save failed");
    }

    [Theory, AutoSubstituteData]
    private async Task CreateAsyncWithSaveExceptionAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var viewResult = await rig.CreateAsync(saveThrowsException:true) as ViewResult;
        await rig.UnitOfWork.DidNotReceive().CommitAsync();
        await rig.WhiteListIpService.Received().SaveAsync(Arg.Any<WhiteListIpDto>(), Arg.Any<Guid>());
        rig.ErrorNotificationService.Received().GetNotyErrorNotification("Error while saving financial institution information.");

        viewResult.ThrowIfNull();
        viewResult.ViewName.Should().Be("Edit");
        var model = viewResult.Model as WhiteListIpCreatePageViewModel;
        model.ThrowIfNull();
        model.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        model.SubmissionNotifications.Should().Be("Formatted Error while saving financial institution information.");
    }

    [Theory, AutoSubstituteData]
    private async Task DeleteAsyncSuccessAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var viewResult = await rig.DeleteAsync() as RedirectToActionResult;
        await rig.UnitOfWork.Received().CommitAsync();
        await rig.WhiteListIpService.Received().DeleteItemAsync(Arg.Any<Enums.WhiteListIpTypeEnum>(),Arg.Any<DeleteItemDto>());
        viewResult.ThrowIfNull();
        viewResult.ActionName.Should().Be("List");
        viewResult.ControllerName.Should().Be("WhiteListIp");
        viewResult.RouteValues?.Any(a =>
            a.Key == "area" && a.Value as string == Constants.FiConfigurationArea).Should().BeTrue();
        viewResult.RouteValues?.Any(a =>
            a.Key == nameof(IFixtureExtensions.FiSlug) && a.Value as string == IFixtureExtensions.FiSlug).Should().BeTrue();
    }
}
