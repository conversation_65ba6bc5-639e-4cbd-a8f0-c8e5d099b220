using AutoFixture;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using NSubstitute;
using QCash.Data.Models;
using QCash.Service.Core;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.UnitTest;
using QCash.Web.Areas.FIConfiguration.Controllers;
using QCash.Web.Areas.FIConfiguration.Models;
using QCash.Web.Models;
using Xunit;
using Enums = QCash.Service.Models.DecisionEngine.Enums;

namespace QCash.Web.UnitTest.Controllers.FiConfiguration;

public class LoanApplicationCalcEngineControllerTests
{
    private class TestRig(IFixture fixture)
    {
        private readonly ILoanApplicationSettingsService _loanApplicationSettingsService = fixture.Freeze<ILoanApplicationSettingsService>();
        private readonly IErrorNotificationService _errorNotificationService = fixture.Freeze<IErrorNotificationService>();
        public readonly IUnitOfWork UnitOfWork = fixture.Freeze<IUnitOfWork>();
        private readonly ILookupService _lookupService = fixture.Freeze<ILookupService>();
        private readonly IAuthUserService _authUserService = fixture.Freeze<IAuthUserService>();
        private readonly ICalcEngineService _calcEngineService = fixture.Freeze<ICalcEngineService>();
        private LoanApplicationCalcEngineController Controller { get; set; } = fixture.Freeze<LoanApplicationCalcEngineController>();
        private readonly Guid _calcSettingsId = Guid.NewGuid();
        private readonly Guid _financialInstitutionId = Guid.NewGuid();

        private void SetupStandardMocks(bool isSuperUser = true)
        {
            _authUserService.IsSuperUser().Returns(isSuperUser);
            _authUserService.IsSystemAdmin().Returns(false);

            _loanApplicationSettingsService.GetNavHeader(IFixtureExtensions.FiSlug)
                .Returns(new LoanApplicationSettingsNavHeaderDto
                {
                    FiSlug = IFixtureExtensions.FiSlug,
                    IsSystemAdmin = false,
                    IsSuperUser = isSuperUser,
                    ShowCalcEngineSettings = isSuperUser,
                    ShowNSFSettings = false,
                    ShowFinancialCoachingSettings = false,
                    ShowInsuranceProductSettings = false,
                });

            _errorNotificationService.GetNotyErrorNotification(Arg.Any<string>())
                .Returns("formatted dummy error");

            _lookupService.GetItemsFromEnum<Enums.CalcEngineAmortMethodEnum>()
                .Returns(new List<QListItem<Enums.CalcEngineAmortMethodEnum>>()
                {
                    new() { Value = Enums.CalcEngineAmortMethodEnum.ACTUALDAY, Text = "A1", },
                    new() { Value = Enums.CalcEngineAmortMethodEnum.ACTUALDAY365, Text = "A2", },
                });

            _lookupService.GetItemsFromEnum<Enums.CalcEngineRoundToIndicatorEnum>()
                .Returns(new List<QListItem<Enums.CalcEngineRoundToIndicatorEnum>>()
                {
                    new() { Value = Enums.CalcEngineRoundToIndicatorEnum.Down, Text = "A1", },
                    new() { Value = Enums.CalcEngineRoundToIndicatorEnum.Up, Text = "A2", },
                });
        }

        public async Task<IActionResult> GetEditPageAsync(bool isSuperUser = true)
        {
            SetupStandardMocks(isSuperUser);

            _calcEngineService.GetSettingsAsync(IFixtureExtensions.FinancialInstitutionId)
                .Returns(new CalcEngineSettingsDto()
                {
                    Id = _calcSettingsId,
                    AmortMethod = "A1",
                    RoundToFactor = "R1",
                    FinancialInstitutionId = _financialInstitutionId,
                    TimeStamp = [0, 1, 2],
                });
            return await Controller.EditAsync((string?)null, (ToastMsgTypeEnum?)null);
        }

        public async Task<IActionResult> SaveEditPageAsync(bool saveIsSuccessful = true)
        {
            SetupStandardMocks(isSuperUser: true);

            _calcEngineService.SaveSettingsAsync(Arg.Any<CalcEngineSettingsDto>(), IFixtureExtensions.FinancialInstitutionId)
                .Returns(new GetOrCreateRecordResult<CalcEngineSetting>
                {
                    IsSuccessful = saveIsSuccessful,
                    ErrorMessage = saveIsSuccessful ? null : "Sample Error Saving Message",
                    Record = null,
                    FoundExistingRecord = false,
                    CreatingNewRecord = false,
                    EditingExistingRecord = false,
                });

            var model = new CalcEngineViewModel
            {
                AmortMethod = "A1",
                RoundToFactor = "R1",
                FiSlug = IFixtureExtensions.FiSlug,
                CalcEngineSettingId = _calcSettingsId,
                CalcEngineSettingTimeStamp = "",
                FinancialInstitutionId = _financialInstitutionId,
            };
            return await Controller.EditAsync(model, IFixtureExtensions.FiSlug);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task EditAsyncSuccessAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var rawResult = await rig.GetEditPageAsync();
        var view = rawResult as ViewResult;
        Assert.NotNull(view);
        var model = view.Model as CalcEngineViewModel;
        Assert.NotNull(model);
        model.FiSlug.Should().Be(IFixtureExtensions.FiSlug);

        model.AmortMethod.Should().Be("A1");
        model.AmortMethodChoices.Count.Should().Be(2);
        model.AmortMethodChoices[0].Text.Should().Be("A1");

        model.RoundToFactor.Should().Be("R1");
        model.RoundToFactorChoices.Count.Should().Be(2);
        model.RoundToFactorChoices[0].Text.Should().Be("A1");

        model.NavHeader.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        model.NavHeader.IsSuperUser.Should().BeTrue();
        model.NavHeader.IsSystemAdmin.Should().BeFalse();
        model.NavHeader.ShowCalcEngineSettings.Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public async Task EditAsyncNoPermissionsAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var rawResult = await rig.GetEditPageAsync(isSuperUser:false);
        var view = rawResult as ForbidResult;
        Assert.NotNull(view);
    }

    [Theory, AutoSubstituteData]
    public async Task SaveEditPageSuccessAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var rawResult = await rig.SaveEditPageAsync();
        var redirectToActionResult = rawResult as RedirectToActionResult;
        await rig.UnitOfWork.Received().CommitAsync();
        Assert.NotNull(redirectToActionResult);
        redirectToActionResult.ActionName.Should().Be("Edit");
        redirectToActionResult.ControllerName.Should().Be("LoanApplicationCalcEngine");
        redirectToActionResult.RouteValues?.Any(a =>
            a.Key == "fiSlug" && $"{a.Value}" == IFixtureExtensions.FiSlug).Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public async Task SaveEditPageSaveFailedAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var rawResult = await rig.SaveEditPageAsync(saveIsSuccessful:false);
        await rig.UnitOfWork.DidNotReceive().CommitAsync();
        var viewResult = rawResult as ViewResult;
        Assert.NotNull(viewResult);
        var model = viewResult.Model as CalcEngineViewModel;
        Assert.NotNull(model);
        model.SubmissionNotifications!.Message.Should().Be("formatted dummy error");
        model.FiSlug.Should().Be(IFixtureExtensions.FiSlug);

        model.AmortMethod.Should().Be("A1");
        model.AmortMethodChoices.Count.Should().Be(2);
        model.AmortMethodChoices[0].Text.Should().Be("A1");

        model.RoundToFactor.Should().Be("R1");
        model.RoundToFactorChoices.Count.Should().Be(2);
        model.RoundToFactorChoices[0].Text.Should().Be("A1");

        model.NavHeader.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        model.NavHeader.IsSuperUser.Should().BeTrue();
        model.NavHeader.IsSystemAdmin.Should().BeFalse();
        model.NavHeader.ShowCalcEngineSettings.Should().BeTrue();
    }
}
