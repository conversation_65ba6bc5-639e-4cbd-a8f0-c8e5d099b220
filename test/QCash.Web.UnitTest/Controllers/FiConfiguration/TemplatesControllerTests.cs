using AutoFixture;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NSubstitute;
using QCash.Service.Core;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.UnitTest;
using QCash.Service.Utilities.Extensions;
using QCash.Web.Areas.FIConfiguration.Controllers;
using QCash.Web.Areas.FIConfiguration.Models;
using QCash.Web.Models;
using Xunit;

namespace QCash.Web.UnitTest.Controllers.FiConfiguration;

public class TemplatesControllerTests
{
    private class TestRig(IFixture fixture)
    {
        public readonly IErrorNotificationService ErrorNotificationService = fixture.Freeze<IErrorNotificationService>();
        public readonly IUnitOfWork UnitOfWork = fixture.Freeze<IUnitOfWork>();
        public readonly ITemplateService TemplateService = fixture.Freeze<ITemplateService>();
        private TemplatesController Controller { get; set; } = fixture.Freeze<TemplatesController>();

        public async Task<IActionResult> GetEditPageAsync()
        {
            return await Controller.EditAsync(null, null, null);
        }
        
        public async Task<IActionResult> SaveEditPageAsync(bool? badSlug = false)
        {
            ErrorNotificationService.GetSuccessfulDatabaseActionMessage(Arg.Any<string>(), EntityState.Modified)
                .Returns("successful result message");
            var model = new TemplateSettingsViewModel
            {
                FiSlug = (badSlug == true) ? "garbage" : IFixtureExtensions.FiSlug,
                SettingId = Guid.NewGuid(),
                SettingTimeStamp = "",
                LanguageCode = "AliensSpeakLatin",
                ShouldShowLanguageChoices = false,
                EConsentFileNameTemplate = null,
                AANFileNameTemplate = null,
                AANDocumentId = null,
                EConsentDocumentId = null,
                EConsentDocumentTimeStamp = null,
                AANDocumentTimeStamp = null,
                AANFileName = null,
                EConsentFileName = null,
                AppLogsFileNameTemplate = "",
            };
            return await Controller.EditAsync(model, IFixtureExtensions.FiSlug);
        }
    }
    
    [Theory, AutoSubstituteData]
    public async Task EditGetAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var rawResult = await rig.GetEditPageAsync();
        var view = rawResult as ViewResult;
        var model = view?.Model as TemplateSettingsViewModel;
        model.ThrowIfNull();

        await rig.TemplateService.Received().GetSettingsAsync(IFixtureExtensions.FinancialInstitutionId, null);
    }
    
    [Theory, AutoSubstituteData]
    public async Task EditSaveBadSlugAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        await Assert.ThrowsAsync<Exception>(async () => 
            await rig.SaveEditPageAsync(badSlug:true)
        );

        await rig.TemplateService.DidNotReceiveWithAnyArgs().GetSettingsAsync(Arg.Any<Guid>(), Arg.Any<string?>());
    }
    
    [Theory, AutoSubstituteData]
    public async Task EditSaveSuccessAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var rawResult = await rig.SaveEditPageAsync();
        var redirectResult = rawResult as RedirectToActionResult;
        redirectResult.ThrowIfNull();
        redirectResult.ActionName.Should().Be("Edit");
        redirectResult.ControllerName.Should().Be("Templates");
        redirectResult.RouteValues.Should().Contain(a => a.Key == "fiSlug" && $"{a.Value}" == IFixtureExtensions.FiSlug);
        redirectResult.RouteValues.Should().Contain(a => a.Key == "area" && $"{a.Value}" == "FIConfiguration");
        redirectResult.RouteValues.Should().Contain(a => a.Key == "msg" && $"{a.Value}" == "successful result message");
        redirectResult.RouteValues.Should().Contain(a => a.Key == "msgType" && a.Value!=null && (ToastMsgTypeEnum)a.Value == ToastMsgTypeEnum.Success);
        redirectResult.RouteValues.Should().Contain(a => a.Key == "languageCode" && $"{a.Value}" == "AliensSpeakLatin");

        await rig.TemplateService.Received().SaveSettingsAsync(Arg.Any<TemplateSettingsDto>(), IFixtureExtensions.FinancialInstitutionId);;
    }
}