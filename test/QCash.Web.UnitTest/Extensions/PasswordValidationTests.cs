using QCash.Utils.Extensions;
using Xunit;

namespace QCash.Web.UnitTest.Extensions;

public class PasswordValidationTests
{
    private const string ValidPassword = "ValidP@ssw0rd";
    private const string SingleCharPassword = "a";

    private readonly PasswordValidator _validator = new()
    {
        RequiredLength = 8,
        RequireNonAlphanumeric = true,
        RequireDigit = true,
        RequireLowercase = true,
        RequireUppercase = true,
        RequireUniqueChars = true,
    };

    [Fact]
    public void ValidatePassword_WithValidPassword_ReturnsSuccess()
    {
        var result = _validator.ValidatePassword(ValidPassword);
        
        Assert.True(result.Succeeded);
        Assert.Empty(result.Errors);
    }
    
    [Theory]
    [InlineData("short", "Passwords must be at least 8 characters.")]
    [InlineData("nouppercase123", "Passwords must have at least one uppercase ('A'-'Z').")]
    [InlineData("NOLOWERCASE123", "Passwords must have at least one lowercase ('a'-'z').")]
    [InlineData("NoNumbers!", "Passwords must have at least one digit ('0'-'9').")]
    [InlineData("NoSpecial123", "Passwords must have at least one non alphanumeric character.")]
    [InlineData("ConsecutiveCharactersXXX", "Passwords cannot contain consecutive repeating characters.")]
    public void ValidatePassword_WithInvalidPassword_ReturnsExpectedErrors(string password, string expectedError)
    {
        var result = _validator.ValidatePassword(password);
        
        Assert.False(result.Succeeded);
        Assert.Contains(expectedError, result.Errors);
    }
    
    [Fact]
    public void ValidatePassword_WithMultipleViolations_ReturnsAllErrors()
    {
        var result = _validator.ValidatePassword(SingleCharPassword);
        
        Assert.False(result.Succeeded);
        Assert.Equal(4, result.Errors.Count);
    }
}