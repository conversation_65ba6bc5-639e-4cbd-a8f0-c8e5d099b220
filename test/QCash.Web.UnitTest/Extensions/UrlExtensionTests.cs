using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.AspNetCore.Routing;
using NSubstitute;
using Xunit;
using QCash.Web.Extensions;

namespace QCash.Web.UnitTest.Extensions;

public class UrlExtensionTests
{
    private class FakeOriginController : Controller
    {

    }

    // ReSharper disable once ClassNeverInstantiated.Local
    private class FakeDestinationController : Controller
    {
        public async Task<IActionResult> ListAsync()
        {
            var somethingUseless = await Task.FromResult(1);
            return Ok();
        }
    }

    [Fact]
    public void VerifyActionExtension1()
    {

        var controller = new FakeOriginController();
        var actionContext = new ActionContext
        {
            ActionDescriptor = new ActionDescriptor(),
            RouteData = new RouteData(),
        };
        controller.Url = new UrlHelper(actionContext);

        var urlHelper = Substitute.For<IUrlHelper>();
        urlHelper.ActionContext.Returns(actionContext);
        UrlActionContext? actualActionContext = null;

        urlHelper
            .Action(Arg.Any<UrlActionContext>())
            .ReturnsForAnyArgs(a =>
            {
                var something = a.Args();
                return "/TESTURL";
            })
            .AndDoes(x => actualActionContext = x.Arg<UrlActionContext>());

        var url = urlHelper.Action<FakeDestinationController>(a => a.ListAsync, new { LookupTypeName = "LT1", FiSlug = "FIS1", area = ""});
        url.Should().Be("/TESTURL");
        Assert.NotNull(actualActionContext);
        actualActionContext.Controller.Should().Be("FakeDestination");
        actualActionContext.Action.Should().Be("List");
    }
}
