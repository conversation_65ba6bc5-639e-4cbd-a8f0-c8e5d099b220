using FluentAssertions;
using QCash.Service.UnitTest;
using QCash.Web.Models.DecisionEngine;
using Xunit;

namespace QCash.Web.UnitTest.Models.DecisionEngine;

public class BusinessRuleViewModelTests
{
    [Fact]
    public void ToDto()
    {
        var id = Guid.NewGuid();
        var model = new BusinessRuleViewModel
        {
            Id = id,
            Name = "  name  ",
            TimeStamp = Convert.ToBase64String([1,2,3]),
            FiSlug = IFixtureExtensions.FiSlug,
            Detail = "  detail  ",
            Score = 10,
            ScoreTypeSlug = "Slug",
        };
        var result = model.ToDto();
        Assert.NotNull(result);
        result.Id.Should().Be(id);
        result.Name.Should().Be("name");
        result.TimeStamp.Should().Equal([1, 2, 3]);
        result.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        result.Detail.Should().Be("detail");
        result.Score.Should().Be(10);
        result.ScoreTypeSlug.Should().Be("Slug");
    }
}