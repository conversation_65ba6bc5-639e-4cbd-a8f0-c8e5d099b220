using FluentAssertions;
using QCash.Service.UnitTest;
using QCash.Web.Models.Lookup;
using Xunit;

namespace QCash.Web.UnitTest.Models.Lookup;

public class TrackingRecordDataMappingPostingViewModelTests
{
    [Fact]
    public void ToDto()
    {
        var id = Guid.NewGuid();
        var model = new TrackingRecordDataMappingPostingViewModel
        {
            Id = id,
            Description = "  Description  ",
            Name = "  Name  ",
            Slug = "Slug",
            TimeStamp = Convert.ToBase64String([1,2,3]),
            FiSlug = IFixtureExtensions.FiSlug,
            PageTitle = "  page title  ",
            TrackingRecordType = "  tracking record type ",
            TrackingRecordField = "  tracking record field  ",
        };
        var result = model.ToDto();
        Assert.NotNull(result);
        result.Id.Should().Be(id);
        result.Description.Should().Be("Description");
        result.Name.Should().Be("Name");
        result.Slug.Should().Be("Slug");
        result.TimeStamp.Should().Equal([1, 2, 3]);
        result.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        result.IsDeleted.Should().Be(false);
        result.TrackingRecordType.Should().Be("tracking record type");
        result.TrackingRecordField.Should().Be("tracking record field");
    }
}