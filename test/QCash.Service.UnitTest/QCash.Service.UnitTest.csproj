<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <WarningLevel>4</WarningLevel>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoFixture.AutoNSubstitute" Version="4.18.1" />
        <PackageReference Include="AutoFixture.Community.Customizations.LoggerFactory" Version="1.0.1" />
        <PackageReference Include="AutoFixture.Xunit2" Version="4.18.1" />
        <PackageReference Include="coverlet.collector" Version="6.0.0" />
        <PackageReference Include="Divergic.Logging.Xunit" Version="4.3.1" />
        <PackageReference Include="EntityFrameworkCore.Testing.NSubstitute" Version="8.1.1" />
        <PackageReference Include="FluentAssertions" Version="6.12.2" />
        <PackageReference Include="log4net" Version="3.1.0" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
        <PackageReference Include="MockQueryable.NSubstitute" Version="7.0.3" />
        <PackageReference Include="NSubstitute" Version="5.1.0" />
        <PackageReference Include="NSubstitute.Analyzers.CSharp" Version="1.0.17">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="System.Net.Http" Version="4.3.4" />
        <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
        <PackageReference Include="xunit" Version="2.9.3" />
        <PackageReference Include="xunit.runner.visualstudio" Version="3.1.1">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\src\QCash.Service\QCash.Service.csproj" />
    </ItemGroup>

</Project>
