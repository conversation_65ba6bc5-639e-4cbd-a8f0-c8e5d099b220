using System.Reflection;
using System.Security.Claims;
using AutoFixture;
using AutoFixture.AutoNSubstitute;
using AutoFixture.Kernel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.ChangeTracking.Internal;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using QCash.Data;
using QCash.Data.Context;
using QCash.Service.Models.MemberInterface;

namespace QCash.Service.UnitTest;
// ReSharper disable once InconsistentNaming
public static class IFixtureExtensions
{
    private const string FinancialInstitutionIdString = "12345678-0000-0000-0000-000000000001";
    public static readonly Guid FinancialInstitutionId = Guid.Parse(FinancialInstitutionIdString);
    private const string OtherFinancialInstitutionIdString = "*************-0000-0000-000000000001";
    public static readonly Guid OtherFinancialInstitutionId = Guid.Parse(OtherFinancialInstitutionIdString);
    public const string FiSlug = "FiTestSlug";
    public static IFixture Registrations(this IFixture fixture)
    {
        fixture.Register(() => new MultiTenantInfo
        {
            Id = FinancialInstitutionId,
            Name = "Test Name",
            Slug = FiSlug,
            IsResolved = true,
        });

        // MVC-related mocks to help auto fixture instantiate controllers
        fixture.Register(() =>
        {
            var ctx = Substitute.For<HttpContext>(); // A direct mock for the http context
            ctx.User = fixture.Create<ClaimsPrincipal>(); // Specify a user/claimsprincipal within the context
            var tenant = fixture.Create<MultiTenantInfo>();
            ctx.Items = new Dictionary<object, object?> { { nameof(MultiTenantInfo), tenant } };
            return ctx;
        });

        fixture.Register(() => CreateDbContext(fixture));
        fixture.Freeze<QCashContext>();

        //fixture.Register(CreateChangeTracker);

        fixture.Register(() => Substitute.For<BindingInfo>()); // The MVC model binder needs this

        // Set the context for each controller
        fixture.Register(() => new ControllerContext { HttpContext = fixture.Create<HttpContext>() });

        fixture.Register(() =>
        {
            // define the "logged-in user" that will be attached to the HttpContext for Controller-based tests
            var claimsIdentity = new ClaimsIdentity(IdentityConstants.ApplicationScheme, ClaimTypes.Name, ClaimTypes.Role);
            //claimsIdentity.AddClaim(new Claim(ClaimTypes.NameIdentifier, Constants.LoggedInUserId.ToString()));
            claimsIdentity.AddClaim(new Claim(ClaimTypes.Name, "<EMAIL>"));
            claimsIdentity.AddClaim(new Claim(ClaimTypes.GivenName, "Clark"));
            claimsIdentity.AddClaim(new Claim(ClaimTypes.Surname, "Kent"));

            claimsIdentity.AddClaim(new Claim(ClaimTypes.Role, "ShareSettlementCapitalAllowed"));
            claimsIdentity.AddClaim(new Claim("OrganizationCertificateMaintenance.Limit", "100000.00"));

            return new ClaimsPrincipal(claimsIdentity);
        });

        //fixture.Register<IMessageModel>(() => new MessageModel(fixture.Create<IWebSession>()));
        //fixture.Register<ISession>(() => new TestSession());

        fixture.Register(() => new DateOnly(2022, 1, 1));

        //fixture.Register<IReadOnlyCache>(GetReadOnlyCache);
        //fixture.Register<IOptionsSnapshot<ReadOnlyCacheSettings>>(GetCacheSettings);
        //fixture.Register<IOptionsSnapshot<EmailSettings>>(GetEmailSettings);

        return fixture;
    }

    // private static IReadOnlyCache GetReadOnlyCache()
    // {
    //     return new ReadOnlyCache();
    // }
    //
    // private static IOptionsSnapshot<ReadOnlyCacheSettings> GetCacheSettings()
    // {
    //     var settings = new ReadOnlyCacheSettings
    //     {
    //         MaxEntries = 0,
    //         DefaultCacheWindowSeconds = 5,
    //         NightCycleRunningCacheWindowSeconds = 5
    //     };
    //
    //     return OptionsSnapshot.Create(settings);
    // }
    //
    // private static IOptionsSnapshot<EmailSettings> GetEmailSettings()
    // {
    //     var settings = new EmailSettings();
    //
    //     return OptionsSnapshot.Create(settings);
    // }

    public static IFixture Customizations(this IFixture fixture)
    {
        fixture.Customize(new AutoNSubstituteCustomization() { ConfigureMembers = true });

        fixture.Customizations.Add(new IgnoreEntityNavigationPropertiesSpecimenBuilder());
        fixture.Customizations.Add(new SmartEnumPropertiesBuilder());
        
        fixture.Customize<LanguageSupportStatusDto>(composer => composer
            .With(x => x.Id, Guid.NewGuid())
            .With(x => x.LanguageId, Guid.NewGuid())
            .With(x => x.FinancialInstitutionId, IFixtureExtensions.FinancialInstitutionId)
        );
        return fixture;
    }

    public static IFixture Behaviors(this IFixture fixture)
    {
        fixture.Behaviors
            .OfType<ThrowingRecursionBehavior>()
            .ToList()
            .ForEach(b => fixture.Behaviors.Remove(b));

        fixture.Behaviors.Add(new OmitOnRecursionBehavior(recursionDepth: 2));

        return fixture;
    }

    private static QCashContext CreateDbContext(IFixture fixture)
    {
        // var options = new DbContextOptions<QCashContext>();
        // var context = Substitute.For<QCashContext>(options);
        //return context;

        var lf = fixture.Freeze<ILoggerFactory>();
        var options = new DbContextOptionsBuilder<QCashContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .EnableSensitiveDataLogging(true)
            .EnableDetailedErrors(true)
            //.UseLoggerFactory(lf)
            .LogTo(Console.WriteLine, LogLevel.Debug)
            .Options;

        var httpContextAccessor = Substitute.For<IHttpContextAccessor>();
        var httpContext = fixture.Create<HttpContext>();
        httpContextAccessor.HttpContext = httpContext;
        var qCashContextOptions = Substitute.For<IOptionsSnapshot<QCashContextOptions>>();
        return new QCashContext(options, httpContextAccessor, qCashContextOptions);
    }

    private static ChangeTracker CreateChangeTracker()
    {
#pragma warning disable EF1001 // Internal EF Core API usage.
        IStateManager stateManager = Substitute.For<IStateManager>();
        var changeDetector = Substitute.For<IChangeDetector>();
#pragma warning restore EF1001 // Internal EF Core API usage.
        var model = Substitute.For<IModel>();
        var entityEntryGraphIterator = Substitute.For<IEntityEntryGraphIterator>();

        var fakeContext = Substitute.For<DbContext>(new DbContextOptionsBuilder<DbContext>()
            .UseInMemoryDatabase(Guid.NewGuid().ToString())
            .Options);

        var changeTracker = Substitute.For<ChangeTracker>(fakeContext, stateManager, changeDetector, model, entityEntryGraphIterator);

        return changeTracker;
    }
}

public class SmartEnumPropertiesBuilder : ISpecimenBuilder
{
    public object Create(object request, ISpecimenContext context)
    {
        var propertyInfo = request as PropertyInfo;
        if (propertyInfo == null)
            return new NoSpecimen();

        if (!propertyInfo.Name.EndsWith("Id"))
            return new NoSpecimen();
        var type = Type.GetType($"QCash.Data.Models.Enums.{propertyInfo.Name[..^2]}Enum, QCash.Data");
        if (type is null)
            return new NoSpecimen();

        var typeEnum = type
            .GetProperty("Instance", BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)?
            .GetValue(null, null);

        dynamic? dict = typeEnum?
            .GetType()
            .BaseType?
            .GetField("_idToValue", BindingFlags.NonPublic | BindingFlags.Instance)?
            .GetValue(typeEnum);

        if (dict is null)
            return new NoSpecimen();

        var keys = (ICollection<int>)dict.Keys;
        var index = Random.Shared.Next(keys.Count);
        var id = keys.Skip(index).First();
        return id;
    }
}

public class IgnoreEntityNavigationPropertiesSpecimenBuilder : ISpecimenBuilder
{
    public object? Create(object request, ISpecimenContext context)
    {
        var propertyInfo = request as PropertyInfo;
        if (propertyInfo == null)
            return new NoSpecimen();

        if (propertyInfo.GetGetMethod()!.IsVirtual && propertyInfo.ReflectedType!.Namespace!.StartsWith("QCash.Data.Models"))
            return null;

        return new NoSpecimen();
    }
}
