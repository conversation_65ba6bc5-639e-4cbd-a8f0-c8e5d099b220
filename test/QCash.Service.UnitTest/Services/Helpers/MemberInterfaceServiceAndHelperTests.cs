using AutoFixture;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Data.Models.Enums;
using QCash.Service.Services;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Helpers;
using QCash.Service.Services.Interfaces;
using Xunit;

namespace QCash.Service.UnitTest.Services.Helpers;

public class MemberInterfaceServiceAndHelperTests
{
    private class TestRig : IDisposable, IAsyncDisposable
    {
        public readonly Guid EnglishLanguageId = LanguageEnum.English.Id;
        public readonly Guid SpanishLanguageId = LanguageEnum.Spanish.Id;
        public IMemberInterfaceHelper MemberInterfaceHelper { get; set; }
        public IMemberProcessInterfaceService MemberProcessInterfaceService { get; set; }
        public QCashContext DbContext { get; set; }
        private FinancialInstitution FinancialInstitution { get; set; }
        public FinancialInstitution FinancialInstitutionAlternate { get; set; }
        private ILogger<MemberInterfaceHelper> Logger { get; set; }
        private IGuidExtensionService GuidExtensionService { get; set; }
        private ILoanApplicationHelper LoanApplicationHelper { get; set; }

        public TestRig(IFixture fixture)
        {
            DbContext = fixture.Freeze<QCashContext>();

            FinancialInstitution = new FinancialInstitution
            {
                Id = IFixtureExtensions.FinancialInstitutionId,
                Slug = IFixtureExtensions.FiSlug,
                Address = "",
                City = "",
                ContactName = "",
                ContactPhone = "",
                GlLoanAccount = "",
                MailingAddress = "",
                Name = "Test1 Name",
                NoReplySubdomain = "TestSubd1",
                State = "",
                Zip = "12345",
                TimeStamp = [],
            };
            DbContext.FinancialInstitutions.Add(FinancialInstitution);
            FinancialInstitutionAlternate = new FinancialInstitution
            {
                Id = Guid.NewGuid(),
                Slug = IFixtureExtensions.FiSlug,
                Address = "",
                City = "",
                ContactName = "",
                ContactPhone = "",
                GlLoanAccount = "",
                MailingAddress = "",
                Name = "Test2 Name",
                NoReplySubdomain = "TestSubd2",
                State = "",
                Zip = "12345",
                TimeStamp = [],
            };
            DbContext.FinancialInstitutions.Add(FinancialInstitutionAlternate);

            //DbContext.Languages.AddRange(LanguageEnum.Instance.GetAll());
            var engLang = new Language()
            {
                Id = LanguageEnum.English.Id,
                LanguageCode = LanguageEnum.English.LanguageCode,
                Name = LanguageEnum.English.Name,
                TimeStamp = [],
            };
            DbContext.Languages.Add(engLang);
            var spanishLang = new Language()
            {
                Id = LanguageEnum.Spanish.Id,
                LanguageCode = LanguageEnum.Spanish.LanguageCode,
                Name = LanguageEnum.Spanish.Name,
                TimeStamp = [],
            };
            DbContext.Languages.Add(spanishLang);

            var memberInterfaceId = Guid.NewGuid();

            DbContext.InterfaceDefaultTexts.Add(new InterfaceDefaultText()
            {
                Id = memberInterfaceId,
                LanguageId = EnglishLanguageId,
                Name = "TestAbrv",
                FieldValue = "product is {Product.Name}",
                Abrv = "TestAbrv",
                TimeStamp = [],
            });
            
            DbContext.InterfaceDefaultTexts.Add(new InterfaceDefaultText()
            {
                Id = Guid.NewGuid(),
                LanguageId = SpanishLanguageId,
                Name = "TestAbrv",
                FieldValue = "el producto es {Product.Name}",
                Abrv = "TestAbrv",
                TimeStamp = [],
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
            });

            DbContext.InterfaceDefaultTexts.Add(new InterfaceDefaultText()
            {
                Id = Guid.NewGuid(),
                LanguageId = EnglishLanguageId,
                Name = "TestAbrv2",
                FieldValue = "product is {Product.XXName}",
                Abrv = "TestAbrv2",
                TimeStamp = [],
            });

            // Item 3 has an override but no foreign language version
            var item3 = new InterfaceDefaultText()
            {
                Id = Guid.NewGuid(),
                LanguageId = EnglishLanguageId,
                Name = "TestAbrv3",
                FieldValue = "product is {Product.XXName}",
                Abrv = "TestAbrv3",
                TimeStamp = [],
            };
            DbContext.InterfaceDefaultTexts.Add(item3);
            
            DbContext.InterfaceDefaultTexts.Add(new InterfaceDefaultText()
            {
                Id = Guid.NewGuid(),
                LanguageId = EnglishLanguageId,
                Name = "LoanApplicationIntroduction",
                FieldValue = "welcome to loan",
                Abrv = "LoanApplicationIntroduction",
                TimeStamp = [],
            });

            DbContext.SaveChanges();
            
            DbContext.InterfaceOverrideTexts.Add(new InterfaceOverrideText()
            {
                Id = Guid.NewGuid(),
                InterfaceDefaultTextId = item3.Id,
                InterfaceDefaultText = item3,
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                FieldValue = "welcome to loan OVERRIDE VALUE",
                TimeStamp = [],
            });
            DbContext.InterfaceOverrideTexts.Add(new InterfaceOverrideText()
            {
                Id = Guid.NewGuid(),
                InterfaceDefaultTextId = item3.Id,
                InterfaceDefaultText = item3,
                FinancialInstitutionId = FinancialInstitutionAlternate.Id,
                FieldValue = "WRONG",
                TimeStamp = [],
            });
            DbContext.SaveChanges();

            Logger = Substitute.For<ILogger<MemberInterfaceHelper>>();
            GuidExtensionService = Substitute.For<IGuidExtensionService>();
            LoanApplicationHelper = Substitute.For<ILoanApplicationHelper>();
            MemberInterfaceHelper =
                new MemberInterfaceHelper(DbContext, LoanApplicationHelper, GuidExtensionService, Logger);

            MemberProcessInterfaceService = new MemberProcessInterfaceService(DbContext, MemberInterfaceHelper);
        }

        public async Task<string?> GetTextAsync(string abrv = "TestAbrv", bool isStaticText = false, Guid? languageId = null)
        {
            return await MemberInterfaceHelper.GetTextAsync(abrv, isStaticText, languageId);
        }

        public void Dispose()
        {
            DbContext?.Dispose();
        }

        public async ValueTask DisposeAsync()
        {
            await DbContext.DisposeAsync();
        }
    }

    [Theory, AutoSubstituteData]
    public async Task GetFormattedInterfaceTextsForValidStepAsync(IFixture fixture)
    {
        await using var rig = new TestRig(fixture);
        var product = new Product
        {
            Name = "asdf",
        };
        Data.Models.LoanApplication la = new()
        {
            SelectedProduct = product,
        };

        var res = await rig.MemberProcessInterfaceService.GetFormattedInterfaceTextsForStepAsync(
            la, rig.EnglishLanguageId, Models.Core.Enums.LoanApplicationStep.Application);

        Assert.NotNull(res);
        res.ContainsKey("LoanApplicationIntroduction").Should().BeTrue();
        res["LoanApplicationIntroduction"].Should().Be("welcome to loan");
    }

    [Theory, AutoSubstituteData]
    public async Task GetFormattedInterfaceTextsForInvalidStepAsync(IFixture fixture)
    {
        await using var rig = new TestRig(fixture);

        var product = new Product
        {
            Name = "asdf",
        };
        Data.Models.LoanApplication la = new()
        {
            SelectedProduct = product,
        };

        var res = await rig.MemberProcessInterfaceService.GetFormattedInterfaceTextsForStepAsync(
            la, Guid.Empty, Models.Core.Enums.LoanApplicationStep.LoanLandingInitial);

        Assert.NotNull(res);
        res.ContainsKey("LoanApplicationIntroduction").Should().BeFalse();
    }

    [Theory, AutoSubstituteData]
    public async Task GetInterfaceTextsWithLanguageAsync(IFixture fixture)
    {
        await using var rig = new TestRig(fixture);

        var product = new Product
        {
            Name = "asdf",
        };
        Data.Models.LoanApplication la = new Data.Models.LoanApplication
        {
            SelectedProduct = product,
        };

        var mem = new InterfaceDefaultText
        {
            Abrv = "TestAbrv",
            FieldValue = "product is {Product.Name}",
            LanguageId = rig.EnglishLanguageId,
            Language = rig.DbContext.Languages.Single(l => l.Name == LanguageEnum.English.Name),
        };

        var res = await rig.MemberInterfaceHelper.GetFormattedMemberInterfaceTextsAsync(rig.SpanishLanguageId, la, mem.Abrv);

        Assert.NotNull(res);
        res.ContainsKey("TestAbrv").Should().BeTrue();
        res["TestAbrv"].Should().Be("el producto es asdf");
    }

    [Theory, AutoSubstituteData]
    public async Task GetInterfaceTextsWithInvalidLanguageAsync(IFixture fixture)
    {
        await using var rig = new TestRig(fixture);
        var product = new Product
        {
            Name = "asdf",
        };
        var la = new Data.Models.LoanApplication
        {
            SelectedProduct = product,
        };

        var mem = new InterfaceDefaultText
        {
            Abrv = "TestAbrv",
            FieldValue = "product is {Product.Name}",
            LanguageId = rig.EnglishLanguageId,
            Language = rig.DbContext.Languages.Single(l => l.Name == LanguageEnum.English.Name),
        };

        var invalidLanguageId = Guid.NewGuid();
        var res = await rig.MemberInterfaceHelper.GetFormattedMemberInterfaceTextsAsync(invalidLanguageId, la, mem.Abrv);

        Assert.NotNull(res);
        res.ContainsKey("TestAbrv").Should().BeTrue();
        res["TestAbrv"].Should().Be("product is asdf");
    }

    [Theory, AutoSubstituteData]
    public async Task GetInterfaceTextsWithInvalidAbrvAsync(IFixture fixture)
    {
        await using var rig = new TestRig(fixture);
        var product = new Product
        {
            Name = "asdf",
        };
        var la = new Data.Models.LoanApplication
        {
            SelectedProduct = product,
        };

        var mem = new InterfaceDefaultText
        {
            Abrv = "AAA",
            FieldValue = "product is {Product.Name}",
            LanguageId = rig.EnglishLanguageId,
            Language = rig.DbContext.Languages.Single(l => l.Name == LanguageEnum.English.Name),
        };

        var res = await rig.MemberInterfaceHelper.GetFormattedMemberInterfaceTextsAsync(rig.SpanishLanguageId, la, mem.Abrv);
        Assert.NotNull(res);
        res.ContainsKey("AAA").Should().BeTrue();
        res["AAA"].Should().Be(string.Empty);
    }

    [Theory, AutoSubstituteData]
    public async Task GetInterfaceTextsWithInvalidPlaceholderAsync(IFixture fixture)
    {
        await using var rig = new TestRig(fixture);
        var product = new Product
        {
            Name = "asdf",
        };
        var la = new Data.Models.LoanApplication
        {
            SelectedProduct = product,
        };

        var mem = new InterfaceDefaultText
        {
            Abrv = "TestAbrv2",
            FieldValue = "product is {Product.XXName}",
            LanguageId = rig.EnglishLanguageId,
            Language = rig.DbContext.Languages.Single(l => l.Name == LanguageEnum.English.Name),
        };

        var res = await rig.MemberInterfaceHelper.GetFormattedMemberInterfaceTextsAsync(rig.SpanishLanguageId, la, mem.Abrv);

        Assert.NotNull(res);
        res.ContainsKey("TestAbrv2").Should().BeTrue();
        res["TestAbrv2"].Should().Be("product is {Product.XXName}");
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetTextAsyncEnglishSuccess(IFixture fixture)
    {
        await using var rig = new TestRig(fixture);
        var textValue = await rig.GetTextAsync("TestAbrv", false, rig.EnglishLanguageId );
        textValue.Should().Be("product is {Product.Name}");
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetTextAsyncEnglishNoSuchAbrv(IFixture fixture)
    {
        await using var rig = new TestRig(fixture);
        var textValue = await rig.GetTextAsync("NoSuchItemExistsWithThisName", false, rig.EnglishLanguageId );
        textValue.Should().BeNull();
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetTextAsyncSpanishSuccess(IFixture fixture)
    {
        await using var rig = new TestRig(fixture);
        var textValue = await rig.GetTextAsync("TestAbrv", false, rig.SpanishLanguageId );
        textValue.Should().Be("el producto es {Product.Name}");
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetTextAsyncSpanishFallbackToEnglish(IFixture fixture)
    {
        await using var rig = new TestRig(fixture);
        var textValue = await rig.GetTextAsync("TestAbrv2", false, rig.SpanishLanguageId );
        textValue.Should().Be("product is {Product.XXName}");
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetTextAsyncEnglishWithOverride(IFixture fixture)
    {
        await using var rig = new TestRig(fixture);
        var textValue = await rig.GetTextAsync("TestAbrv3", false, rig.EnglishLanguageId );
        textValue.Should().Be("welcome to loan OVERRIDE VALUE");
    }
}
