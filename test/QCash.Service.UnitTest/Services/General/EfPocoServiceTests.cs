using System.Diagnostics.CodeAnalysis;
using System.Text;
using AutoFixture;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Data.Models.Interfaces;
using QCash.Service.Models;
using QCash.Service.Models.General;
using QCash.Service.Services.General;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Utilities.Extensions;
using QCash.Utils.Core;
using Xunit;
using Xunit.Abstractions;

namespace QCash.Service.UnitTest.Services.General;

[ExcludeFromCodeCoverage]
public class EfPocoServiceTests(ITestOutputHelper testOutputHelper)
{
    private class EfPocoServiceTestRig
    {
        public QCashContext Context { get; set; }
        private EfPocoService EfPocoService { get; set; }
        private IGuidExtensionService GuidExtensionService { get; set; }
        private ISystemClockService SystemClockService { get; set; }
        public readonly Guid NewId = Guid.NewGuid();
        public readonly DateTime NowFake = DateTime.Parse("8/1/2024 8:00 am");

        public EfPocoServiceTestRig(IFixture fixture)
        {
            Context = fixture.Freeze<QCashContext>();

            GuidExtensionService = fixture.Freeze<IGuidExtensionService>();
            GuidExtensionService.NewSequentialGuid().Returns(NewId);

            SystemClockService = fixture.Freeze<ISystemClockService>();
            SystemClockService.GetSystemTimeUtc().Returns(NowFake);

            EfPocoService = new EfPocoService(GuidExtensionService, SystemClockService, context: Context);
   
            var financialInstitution = new FinancialInstitution
            {
                Id = IFixtureExtensions.FinancialInstitutionId,
                Slug = IFixtureExtensions.FiSlug,
                Address = "", City = "", ContactName = "", ContactPhone = "", GlLoanAccount = "",
                MailingAddress = "",
                Name = "Test1 Name", NoReplySubdomain = "TestSubd1", State = "", Zip = "12345", TimeStamp = [],
            };

            Context.FinancialInstitutions.Add(financialInstitution);
            Context.SaveChanges();
            //EfPocoService = fixture.Freeze<EfPocoService>();
        }

        public T AddT<T>(T record) where T : class, IEFPoco =>
            EfPocoService.Add(record);

        public async Task<T?> GetAsync<T>(Guid id) where T : class, IEFPoco =>
            await EfPocoService.GetItemAsync<T>(id);

        public async Task<GetOrCreateRecordResult<T>> GetOrCreateRecordAsync<T>(Guid? id, Guid? applicationId,
            PerformCreateOrUpdateOptions<T> options) where T : class, IEFPoco =>
            await EfPocoService.GetOrCreateRecordAsync(id, applicationId, options);

        public async Task<GetOrCreateRecordResult<T>> CreateOrUpdateAsync<T>(Guid id, Guid? applicationId, byte[] timeStamp,
            PerformCreateOrUpdateOptions<T>? options) where T : class, IEFPoco =>
            await EfPocoService.CreateOrUpdateAsync(id, applicationId, timeStamp, options);

        public async Task<GetOrCreateRecordResult> CreateOrUpdateAsync(Type type, Guid id, Guid? applicationId, byte[] timeStamp,
            PerformCreateOrUpdateOptions? options) =>
            await EfPocoService.CreateOrUpdateAsync(type, id, applicationId, timeStamp, options);
        
        public async Task<GetOrCreateRecordResult> CreateOrUpdateAsync(Type type, Guid id, Guid? applicationId, byte[] timeStamp,
            PerformCreateOrUpdateOptionsAsync? options) =>
            await EfPocoService.CreateOrUpdateAsync(type, id, applicationId, timeStamp, options);
        
        public async Task<GenericActionResult> DeleteItemAsync<T>(DeleteItemDto dto) where T : class, IEFPoco =>
            await EfPocoService.DeleteItemAsync<T>(dto);

        public async Task<GenericActionResult> DeleteItemAsync(Type type, DeleteItemDto dto) =>
            await EfPocoService.DeleteItemAsync(type, dto);
    }

    [Theory, AutoSubstituteData]
    public async Task AddTAsync(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        rig.Context.AspNetUsers.Count().Should().Be(0);
        var record = rig.AddT(new AspNetUser { UserName = "asdf", FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId });
        rig.Context.Entry(record).State.Should().Be(EntityState.Added);
        await rig.Context.SaveChangesAsync();
        rig.Context.AspNetUsers.Count().Should().Be(1);
        var newRecord = rig.Context.AspNetUsers.First();
        newRecord.Id.Should().Be(rig.NewId);
        newRecord.DateUpdatedUtc.Should().NotBe(rig.NowFake);
        newRecord.DateCreatedUtc.Should().NotBe(rig.NowFake);
    }

    [Theory, AutoSubstituteData]
    public async Task GetAsync(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        rig.Context.AspNetUsers.Count().Should().Be(0);
        _ = rig.AddT(new AspNetUser { UserName = "Test User 1", FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId });
        await rig.Context.SaveChangesAsync();

        var testRecord = await rig.GetAsync<AspNetUser>(rig.NewId);
        Assert.NotNull(testRecord);
        testRecord.UserName.Should().Be("Test User 1");
    }

    [Theory, AutoSubstituteData]
    public async Task GetOrCreateRecordAsyncCreateRecord(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        rig.Context.AspNetUsers.Count().Should().Be(0);
        var result = await rig.GetOrCreateRecordAsync(Guid.Empty, null, new PerformCreateOrUpdateOptions<AspNetUser>
        {
            ExecuteRecordChangesFunc = param =>
            {
                param.Record.UserName = "Test User 1";
                param.Record.FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId;
            },
        } );
        await rig.Context.SaveChangesAsync();
        rig.Context.AspNetUsers.Count().Should().Be(1);
        result.Record.Should().Be(rig.Context.AspNetUsers.First());
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        result.CreatingNewRecord.Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public async Task GetOrCreateRecordAsyncFoundRecord(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        rig.Context.AspNetUsers.Count().Should().Be(0);
        var record = rig.AddT(new AspNetUser { UserName = "Test User 1", FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId });
        await rig.Context.SaveChangesAsync();
        rig.Context.AspNetUsers.Count().Should().Be(1);

        var result = await rig.GetOrCreateRecordAsync(record.Id, null, new PerformCreateOrUpdateOptions<AspNetUser>
        {
            ExecuteRecordChangesFunc = param =>
            {
                param.Record.UserName = "AAAAA";
            },
        } );

        rig.Context.AspNetUsers.Count().Should().Be(1);
        result.Record.Should().Be(rig.Context.AspNetUsers.First());
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        result.CreatingNewRecord.Should().BeFalse();
    }

    [Theory, AutoSubstituteData]
    public async Task GetOrCreateRecordAsyncNoSuchRecord(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        var searchingForId = Guid.NewGuid();
        var result = await rig.GetOrCreateRecordAsync(searchingForId, null, new PerformCreateOrUpdateOptions<AspNetUser>
        {
            ExecuteRecordChangesFunc = param =>
            {
                param.Record.UserName = "AAAAA";
            },
        } );

        result.Record.Should().BeNull();
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be($"No such item found: {searchingForId}");
        result.CreatingNewRecord.Should().BeFalse();
    }

    [Theory, AutoSubstituteData]
    public async Task GetOrCreateRecordAsyncFoundSingleRecord(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        var record = rig.AddT(new AspNetUser { UserName = "Test User 1", FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId });
        await rig.Context.SaveChangesAsync();
        var result = await rig.GetOrCreateRecordAsync(null, null, new PerformCreateOrUpdateOptions<AspNetUser>
        {
            QueryableCustomization = usersQ =>
            {
                return usersQ.Where(a => a.Id == record.Id);
            },
        } );

        rig.Context.AspNetUsers.Count().Should().Be(1);
        result.Record.Should().Be(rig.Context.AspNetUsers.First());
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        result.CreatingNewRecord.Should().BeFalse();
    }

    [Theory, AutoSubstituteData]
    public async Task GetOrCreateRecordAsyncCreateSingleRecord(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        var result = await rig.GetOrCreateRecordAsync(null, null, new PerformCreateOrUpdateOptions<AspNetUser>
        {
            ExecuteRecordChangesFunc = param =>
            {
                param.Record.UserName = "Test User 1";
                param.Record.FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId;
            },
        } );
        await rig.Context.SaveChangesAsync();

        rig.Context.AspNetUsers.Count().Should().Be(1);
        result.Record.Should().Be(rig.Context.AspNetUsers.First());
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        result.CreatingNewRecord.Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public async Task CreateOrUpdateAsyncCreate(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        var paramWasExecuted = false;
        var result = await rig.CreateOrUpdateAsync(Guid.Empty, null, [], new PerformCreateOrUpdateOptions<AspNetUser>
        {
            ExecuteRecordChangesFunc = param =>
            {
                paramWasExecuted = true;
                param.Record.UserName = "Test User 1";
                param.Record.FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId;
            },
        } );
        await rig.Context.SaveChangesAsync();

        rig.Context.AspNetUsers.Count().Should().Be(1);
        Assert.NotNull(result.Record);
        result.Record.Should().Be(rig.Context.AspNetUsers.First());
        result.Record.UserName.Should().Be("Test User 1");
        paramWasExecuted.Should().BeTrue();
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        result.CreatingNewRecord.Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public async Task CreateOrUpdateAsyncUpdate(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        var record = rig.AddT(new AspNetUser { UserName = "Test User 1", FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId });
        await rig.Context.SaveChangesAsync();
        var result = await rig.CreateOrUpdateAsync(record.Id, null, [], new PerformCreateOrUpdateOptions<AspNetUser>
        {
            ExecuteRecordChangesFunc = param =>
            {
                param.Record.UserName = "Test User 1 MODIFIED";
            },
        } );
        await rig.Context.SaveChangesAsync();

        rig.Context.AspNetUsers.Count().Should().Be(1);
        result.Record.Should().Be(rig.Context.AspNetUsers.First());
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        result.CreatingNewRecord.Should().BeFalse();
        Assert.NotNull(result.Record);
        result.Record.UserName.Should().Be("Test User 1 MODIFIED");
    }

    [Theory, AutoSubstituteData]
    public async Task CreateOrUpdateAsyncFailOnTimestampWithTypeParam(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        var record = rig.AddT(new ExclusionLoanPurposeCode()
        {
            Name = "Test ELP",
            Abrv = "asdf",
            AppAbrv = "aasdf",
            Description = "asdf",
            Slug = "asdf",
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            TimeStamp = [0,1,2],
        });
        await rig.Context.SaveChangesAsync();
        var result = await rig.CreateOrUpdateAsync(typeof(ExclusionLoanPurposeCode), record.Id, null, [3,4,5], new PerformCreateOrUpdateOptions()
        {
            ExecuteRecordChangesFunc = param =>
            {
                var r = param.Record as ExclusionLoanPurposeCode;
                r.ThrowIfNull();
                r.Name = "Test ELP MODIFIED";
            },
        } );
        await rig.Context.SaveChangesAsync();

        rig.Context.ExclusionLoanPurposeCodes.Count().Should().Be(1);
        result.Record.Should().Be(rig.Context.ExclusionLoanPurposeCodes.First());
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Error: Data could not be saved. The record being updated has been changed by another user.  Please reload the page and try again.");
        result.CreatingNewRecord.Should().BeFalse();
        Assert.NotNull(result.Record);
        //result.Record.Name.Should().Be("Test ELP MODIFIED");
    }
    
    [Theory, AutoSubstituteData]
    public async Task CreateOrUpdateAsyncFailOnTimestampWithTypeParamAsyncOptions(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        var record = rig.AddT(new ExclusionLoanPurposeCode()
        {
            Name = "Test ELP",
            Abrv = "asdf",
            AppAbrv = "aasdf",
            Description = "asdf",
            Slug = "asdf",
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            TimeStamp = [0,1,2],
        });
        await rig.Context.SaveChangesAsync();
        var result = await rig.CreateOrUpdateAsync(typeof(ExclusionLoanPurposeCode), record.Id, null, [3,4,5], new PerformCreateOrUpdateOptionsAsync()
        {
            ExecuteRecordChangesFunc = async param =>
            {
                var r = param.Record as ExclusionLoanPurposeCode;
                r.ThrowIfNull();
                r.Name = "Test ELP MODIFIED";
                var i = await Task.FromResult(1);
            },
        } );
        await rig.Context.SaveChangesAsync();

        rig.Context.ExclusionLoanPurposeCodes.Count().Should().Be(1);
        result.Record.Should().Be(rig.Context.ExclusionLoanPurposeCodes.First());
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Error: Data could not be saved. The record being updated has been changed by another user.  Please reload the page and try again.");
        result.CreatingNewRecord.Should().BeFalse();
        Assert.NotNull(result.Record);
        //result.Record.Name.Should().Be("Test ELP MODIFIED");
    }

    [Theory, AutoSubstituteData]
    public async Task CreateOrUpdateAsyncFailOnTimestamp(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        var record = rig.AddT(new AspNetUser { UserName = "Test User 1", TimeStamp = [0,1,2], FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId });
        await rig.Context.SaveChangesAsync();
        var result = await rig.CreateOrUpdateAsync(record.Id, null, [3,4,5], new PerformCreateOrUpdateOptions<AspNetUser>
        {
            ExecuteRecordChangesFunc = param =>
            {
                param.Record.UserName = "Test User 1 MODIFIED";
            },
        } );
        await rig.Context.SaveChangesAsync();

        rig.Context.AspNetUsers.Count().Should().Be(1);
        result.Record.Should().Be(rig.Context.AspNetUsers.First());
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Error: Data could not be saved. The record being updated has been changed by another user.  Please reload the page and try again.");
        result.CreatingNewRecord.Should().BeFalse();
        Assert.NotNull(result.Record);
        result.Record.UserName.Should().Be("Test User 1 MODIFIED");
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteItemTNormalSuccess(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        rig.Context.AspNetUsers.Count().Should().Be(0);
        var record = rig.AddT(new AspNetUser { UserName = "Test User 1", TimeStamp = [], FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId });
        await rig.Context.SaveChangesAsync();
        rig.Context.AspNetUsers.Count().Should().Be(1);
        var result = await rig.DeleteItemAsync<AspNetUser>( new DeleteItemDto { DeleteItemId = record.Id, TimeStamp = [] });
        await rig.Context.SaveChangesAsync();
        rig.Context.AspNetUsers.Count().Should().Be(0);
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteItemTBadTimestamp(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        rig.Context.AspNetUsers.Count().Should().Be(0);
        var record = rig.AddT(new AspNetUser { UserName = "Test User 1", TimeStamp = [0,1,2], FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId });
        await rig.Context.SaveChangesAsync();
        rig.Context.AspNetUsers.Count().Should().Be(1);
        var result = await rig.DeleteItemAsync<AspNetUser>( new DeleteItemDto { DeleteItemId = record.Id, TimeStamp = [0,1,2] });
        await rig.Context.SaveChangesAsync();
        rig.Context.AspNetUsers.Count().Should().Be(0);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Error: Data could not be saved. The record being updated has been changed by another user.  Please reload the page and try again.");
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteItemTNotFound(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        var result = await rig.DeleteItemAsync<AspNetUser>( new DeleteItemDto { DeleteItemId = Guid.NewGuid(), TimeStamp = [0,1,2] });
        await rig.Context.SaveChangesAsync();
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("No such item found.");
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteItemTypeNormalSuccessFullDelete(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        rig.Context.AspNetUsers.Count().Should().Be(0);
        var record = rig.AddT(new AspNetUser { UserName = "Test User 1", TimeStamp = [], FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId });
        await rig.Context.SaveChangesAsync();
        rig.Context.AspNetUsers.Count().Should().Be(1);
        var result = await rig.DeleteItemAsync(typeof(AspNetUser), new DeleteItemDto { DeleteItemId = record.Id, TimeStamp = [] });
        await rig.Context.SaveChangesAsync();
        rig.Context.AspNetUsers.Count().Should().Be(0);
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteItemTypeNormalSuccessDeleteFlag(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        rig.Context.LoanCodes.Count().Should().Be(0);
        var record = rig.AddT(new LoanCode
        {
            Name = "Test User 1",
            Abrv = "",
            AppAbrv = "",
            Description = "",
            Slug = "",
            TimeStamp = [],
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
        });
        
        await rig.Context.SaveChangesAsync();
        rig.Context.LoanCodes.Count().Should().Be(1);
        var result = await rig.DeleteItemAsync(typeof(LoanCode), new DeleteItemDto { DeleteItemId = record.Id, TimeStamp = [] });
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();

        await rig.Context.SaveChangesAsync();
        var testRecord = rig.Context.LoanCodes.First();
        testRecord.IsDeleted.Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteItemTypeBadTimestamp(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        rig.Context.AspNetUsers.Count().Should().Be(0);
        var record = rig.AddT(new AspNetUser { UserName = "Test User 1", TimeStamp = [0,1,2], FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId });
        await rig.Context.SaveChangesAsync();
        rig.Context.AspNetUsers.Count().Should().Be(1);
        var result = await rig.DeleteItemAsync( typeof(AspNetUser), new DeleteItemDto { DeleteItemId = record.Id, TimeStamp = [0,1,2] });
        await rig.Context.SaveChangesAsync();
        rig.Context.AspNetUsers.Count().Should().Be(0);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Error: Data could not be saved. The record being updated has been changed by another user.  Please reload the page and try again.");
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteItemTypeNotFound(IFixture fixture)
    {
        var rig = new EfPocoServiceTestRig(fixture);
        var result = await rig.DeleteItemAsync(typeof(AspNetUser),  new DeleteItemDto { DeleteItemId = Guid.NewGuid(), TimeStamp = [0,1,2] });
        await rig.Context.SaveChangesAsync();
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("No such item found.");
    }

    [Fact]
    public void EnsureDbClassesHaveInterfaces()
    {
        var results = new StringBuilder();
        var asm = typeof(LoanCode).Assembly;
        var types = asm.GetTypes()
            .Where(t => t.IsClass)
            .Where(t => !t.IsAbstract)
            .Where(t => t.Namespace == "QCash.Data.Models");
        foreach (var type in types)
        {
            if (!typeof(IEFPoco).IsAssignableFrom(type))
            {
                // Type is not an IEFPoco.  just ignore for now.
                continue;
            }

            var interfacesToCheck = new [] {typeof(IFinancialInstitutionId), typeof(IEFPocoWithDescription), };
            foreach(var interf in interfacesToCheck)
            {
                if (DoesClassImplementThisInterfacesProperties(interf, type))
                {
                    // if we're here, then the db model has the properties.  check if it also has the interface
                    if (interf.IsAssignableFrom(type))
                    {
                        // good.  it has the interface.
                        continue;
                    }
                    // nope.  it doesn't have the interface.
                    if (type.Name == "Product")
                    {
                        // special case.  Product does not implement IEFPocoWithDescription correctly (null).
                        continue;
                    }
                    results.AppendLine($"Type {type.Name} has fields but does not implement {interf.Name}");
                }
            }
        }

        if (results.Length > 0)
        {
            testOutputHelper.WriteLine(results.ToString());
            Assert.Fail();
        }
    }

    private bool DoesClassImplementThisInterfacesProperties(Type interf, Type type)
    {
        foreach(var prop in interf.GetProperties())
        {
            var propInfo = type.GetProperty(prop.Name);
            if (propInfo == null)
            {
                return false;
            }
        }
        return true;
    }
}
