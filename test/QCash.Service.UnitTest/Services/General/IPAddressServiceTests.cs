using FluentAssertions;
using QCash.Service.Services.General;
using Xunit;

namespace QCash.Service.UnitTest.Services.General;

public class IPAddressServiceTests
{
    private class TestRig
    {
        private IPAddressService IPAddressService { get; set; } = new IPAddressService();

        public string ParseIP(string? input) => IPAddressService.ParseIP(input);

        public bool IsIPAddressOrRange(string? input) => IPAddressService.IsIPAddressOrRange(input);
    }

    [Fact]
    public void ParseIPTests() {
        var rig = new TestRig();
        rig.ParseIP("***********").Should().Be("***********");
        rig.ParseIP("      **************  ").Should().Be("***********");
        rig.ParseIP("***********-*************").Should().Be("***********-*************");
        rig.ParseIP(" ************* - ***************").Should().Be("***********-*************");
        rig.ParseIP("A").Should().Be("");
        rig.ParseIP(null).Should().Be("");
    }

    [Fact]
    public void IsIPAddressOrRangeTests() {
        var rig = new TestRig();
        rig.IsIPAddressOrRange("***********").Should().Be(true);
        rig.IsIPAddressOrRange("      **************  ").Should().Be(true);
        rig.IsIPAddressOrRange("***********-*************").Should().BeTrue();
        rig.IsIPAddressOrRange(" ************* - ***************").Should().BeTrue();
        rig.IsIPAddressOrRange("A").Should().Be(false);
        rig.IsIPAddressOrRange(null).Should().Be(false);
    }
}
