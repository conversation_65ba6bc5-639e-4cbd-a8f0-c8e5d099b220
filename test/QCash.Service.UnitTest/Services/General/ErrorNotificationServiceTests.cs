using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using AutoFixture;
using EntityFramework.Exceptions.Common;
using FluentAssertions;
using Kendo.Mvc.Extensions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using QCash.Service.Services.General;
using Xunit;

namespace QCash.Service.UnitTest.Services.General;

[SuppressMessage("Non-substitutable member", "NS1000:Non-virtual setup specification.")]
public class ErrorNotificationServiceTests
{
    private class ErrorNotificationServiceTestRig(IFixture fixture)
    {
        public ILogger<ErrorNotificationService> Logger { get; set; } = fixture.Freeze<ILogger<ErrorNotificationService>>();
        private ErrorNotificationService ErrorNotificationService { get; set; } = fixture.Freeze<ErrorNotificationService>();

        public string? GetNotyErrorNotification(string input) =>
            ErrorNotificationService.GetNotyErrorNotification(input);

        public string GetDatabaseErrorMessage(List<string> constraintFields, Exception? exception = null)
        {
            var model = new FakeModelType() { Field1 = "test1", };
            exception ??= Substitute.For<UniqueConstraintException>();
            var val = constraintFields.ToReadOnlyCollection();

            if (exception is UniqueConstraintException uce)
            {
                var flags = BindingFlags.Public | BindingFlags.GetProperty | BindingFlags.Instance;
                var t = exception.GetType();
                var p = t.GetProperty(nameof(UniqueConstraintException.ConstraintProperties), flags)!;
                p.SetValue(exception, val);
            }

            return ErrorNotificationService.GetDatabaseErrorMessage(exception, model);
        }
    }

    [Theory, AutoSubstituteData]
    public void GetNotyErrorNotificationTest(IFixture fixture)
    {
        var rig = new ErrorNotificationServiceTestRig(fixture);
        var result = rig.GetNotyErrorNotification("Test Number One");
        var expect = @"{'message':'Test Number One','type':'error'}";
        expect = expect.Replace("'","\"");
        result.Should().Be(expect);
    }
    
    [Theory, AutoSubstituteData]
    public void GetNotyErrorNotificationSanitizationTest(IFixture fixture)
    {
        var rig = new ErrorNotificationServiceTestRig(fixture);
        var result = rig.GetNotyErrorNotification("<script>$('[name=PaymentGuardStartDate').val('3/30/2025');$('button[type=submit]').click();</script>");
        var expect = @"{""message"":"""",""type"":""error""}";
        result.Should().Be(expect);
    }

    private class FakeModelType
    {
        [Display(Name="Fancy Field 1 Name")]
        public required string Field1 { get; set; }
    }

    private void VerifyLog(ErrorNotificationServiceTestRig rig, bool shouldLogInfo, bool shouldLogError)
    {
        rig.Logger.Received(shouldLogInfo ? 1 : 0)
            .Log(LogLevel.Information, 0,
            Arg.Is<object>(x => $"{x}".Equals("Unique constraint exception")),
            Arg.Any<Exception>(), Arg.Any<Func<object, Exception?, string>>());


        rig.Logger.Received(shouldLogError ? 1 : 0).Log(LogLevel.Error, 0,
            Arg.Any<object>(), Arg.Any<Exception>(),
            Arg.Any<Func<object, Exception?, string>>());
    }
    
    [Theory, AutoSubstituteData]
    public void GetDatabaseErrorMessageForUniqueConstraint(IFixture fixture)
    {
        var rig = new ErrorNotificationServiceTestRig(fixture);
        var constraintFields = new List<string>() { "Field1" };
        var result = rig.GetDatabaseErrorMessage(constraintFields);
        result.Should().Be("Fancy Field 1 Name must be unique");
        VerifyLog(rig, true, false);
    }
    
    [Theory, AutoSubstituteData]
    public void GetDatabaseErrorMessageForUniqueConstraintWithFIId(IFixture fixture)
    {
        var rig = new ErrorNotificationServiceTestRig(fixture);
        var result = rig.GetDatabaseErrorMessage(["Field2", "FinancialInstitutionId"]);
        result.Should().Be("Field2 must be unique");
        VerifyLog(rig, true, false);
    }
    
    [Theory, AutoSubstituteData]
    public void GetDatabaseErrorMessageForUniqueConstraintWithNoFields(IFixture fixture)
    {
        var rig = new ErrorNotificationServiceTestRig(fixture);
        var result = rig.GetDatabaseErrorMessage([]);
        result.Should().Be("Data in this record must be unique");
        VerifyLog(rig, true, false);
    }
    
    [Theory, AutoSubstituteData]
    public void GetDatabaseErrorMessageForGenericError(IFixture fixture)
    {
        var rig = new ErrorNotificationServiceTestRig(fixture);
        var result = rig.GetDatabaseErrorMessage([], exception:new Exception());
        result.Should().Be("An unexpected application error has occurred");
        VerifyLog(rig, false, true);
    }
}
