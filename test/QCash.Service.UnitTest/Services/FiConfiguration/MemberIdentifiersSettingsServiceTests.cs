using AutoFixture;
using FluentAssertions;
using Kendo.Mvc.Extensions;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Services.FIConfiguration;
using Xunit;
using Enums = QCash.Service.Models.FIConfiguration.Enums;

namespace QCash.Service.UnitTest.Services.FiConfiguration;

public class MemberIdentifiersSettingsServiceTests
{
    private class MemberIdentifiersSettingsServiceTestRig(IFixture fixture)
    {
        private QCashContext Context { get; set; } = fixture.Freeze<QCashContext>();
        private MemberIdentifiersSettingsService MemberIdentifiersSettingsService { get; set; } = fixture.Freeze<MemberIdentifiersSettingsService>();
        private FinancialInstitution FinancialInstitution { get; set; } = null!;
        public void CreateFI()
        {
            MemberIdentifiersSettingsService = new MemberIdentifiersSettingsService(Context);
            Context.FinancialInstitutions.Add(
                FinancialInstitution = new FinancialInstitution
                {
                    Id = IFixtureExtensions.FinancialInstitutionId,
                    Slug = IFixtureExtensions.FiSlug,
                    Name = "Test1 Name",
                    NoReplySubdomain = "TestSubd1",
                    Address = "",
                    City = "",
                    ContactName = "",
                    ContactPhone = "",
                    GlLoanAccount = "",
                    MailingAddress = "",
                    State = "",
                    Zip = "12345",
                    TimeStamp = [],
                });
            
            Context.SaveChanges();
        }

        public async Task<MemberIdentifierFlagsDto?> GetMemberIdentifierFlagsAsync(
            bool taxIdIsEnabled = false, bool individualIdIsEnabled = false, bool baseAccountIdIsEnabled = false, bool memberIdIsEnabled = false)
        {
            CreateFI();
            var miList = new List<MemberIdentifiersSetting>()
            {
                new MemberIdentifiersSetting()
                {
                    Id = Guid.NewGuid(),
                    FinancialInstitutionId = FinancialInstitution.Id,
                    Key = (int)Enums.MemberIdentifierEnum.TaxId,
                    Used = taxIdIsEnabled,
                    Label = "TaxId",
                    TimeStamp = [],
                },
                new MemberIdentifiersSetting()
                {
                    Id = Guid.NewGuid(),
                    FinancialInstitutionId = FinancialInstitution.Id,
                    Key = (int)Enums.MemberIdentifierEnum.IndividualId,
                    Used = individualIdIsEnabled,
                    Label = "IndividualId",
                    TimeStamp = [],
                },
                new MemberIdentifiersSetting()
                {
                    Id = Guid.NewGuid(),
                    FinancialInstitutionId = FinancialInstitution.Id,
                    Key = (int)Enums.MemberIdentifierEnum.BaseAccountId,
                    Used = baseAccountIdIsEnabled,
                    Label = "BaseAccountId",
                    TimeStamp = [],
                },            
                new MemberIdentifiersSetting()
                {
                    Id = Guid.NewGuid(),
                    FinancialInstitutionId = FinancialInstitution.Id,
                    Key = (int)Enums.MemberIdentifierEnum.MemberId,
                    Used = memberIdIsEnabled,
                    Label = "MemberId",
                    TimeStamp = [],
                },  
            };
            Context.MemberIdentifiersSettings.AddRange(miList);
            FinancialInstitution.MemberIdentifiersSettings.AddRange(miList);
            await Context.SaveChangesAsync();
            
            return await MemberIdentifiersSettingsService.GetMemberIdentifierFlagsAsync(IFixtureExtensions.FinancialInstitutionId);
        }
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetMemberIdentifierFlagsTaxIdAsync(IFixture fixture)
    {
        var rig = new MemberIdentifiersSettingsServiceTestRig(fixture);
        var result = await rig.GetMemberIdentifierFlagsAsync(taxIdIsEnabled:true);
        Assert.NotNull(result);
        result.IsIdentifiedByTaxId.Should().BeTrue();
        result.IsIdentifiedByIndividualId.Should().BeFalse();
        result.IsIdentifiedByBaseAccountId.Should().BeFalse();
        result.IsIdentifiedByMemberId.Should().BeFalse();
    }
        
    [Theory, AutoSubstituteData]
    public async Task GetMemberIdentifierFlagsIndividualIdAsync(IFixture fixture)
    {
        var rig = new MemberIdentifiersSettingsServiceTestRig(fixture);
        var result = await rig.GetMemberIdentifierFlagsAsync(individualIdIsEnabled:true);
        Assert.NotNull(result);
        result.IsIdentifiedByTaxId.Should().BeFalse();
        result.IsIdentifiedByIndividualId.Should().BeTrue();
        result.IsIdentifiedByBaseAccountId.Should().BeFalse();
        result.IsIdentifiedByMemberId.Should().BeFalse();
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetMemberIdentifierFlagsBaseAccountIdAsync(IFixture fixture)
    {
        var rig = new MemberIdentifiersSettingsServiceTestRig(fixture);
        var result = await rig.GetMemberIdentifierFlagsAsync(baseAccountIdIsEnabled:true);
        Assert.NotNull(result);
        result.IsIdentifiedByTaxId.Should().BeFalse();
        result.IsIdentifiedByIndividualId.Should().BeFalse();
        result.IsIdentifiedByBaseAccountId.Should().BeTrue();
        result.IsIdentifiedByMemberId.Should().BeFalse();
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetMemberIdentifierFlagMemberIdAsync(IFixture fixture)
    {
        var rig = new MemberIdentifiersSettingsServiceTestRig(fixture);
        var result = await rig.GetMemberIdentifierFlagsAsync(memberIdIsEnabled:true);
        Assert.NotNull(result);
        result.IsIdentifiedByTaxId.Should().BeFalse();
        result.IsIdentifiedByIndividualId.Should().BeFalse();
        result.IsIdentifiedByBaseAccountId.Should().BeFalse();
        result.IsIdentifiedByMemberId.Should().BeTrue();
    }
}