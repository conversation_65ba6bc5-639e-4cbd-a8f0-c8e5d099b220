using AutoFixture;
using FluentAssertions;
using MockQueryable.NSubstitute;
using NSubstitute;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration;
using QCash.Service.Services.General.Interfaces;
using Xunit;

namespace QCash.Service.UnitTest.Services.FiConfiguration;

public class CalcEngineServiceTests
{
    private class TestRig(IFixture fixture)
    {
        public readonly IEfPocoService EfPocoService = fixture.Freeze<IEfPocoService>();
        private CalcEngineService CalcEngineService { get; set; } = fixture.Freeze<CalcEngineService>();
        public readonly Guid FinancialInstitutionId = Guid.NewGuid();
        public readonly Guid CalcEngineSettingId = Guid.NewGuid();

        public async Task<CalcEngineSettingsDto> GetSettingsAsync(bool hasData = true)
        {
            IQueryable<CalcEngineSetting> q;
            if (hasData)
            {
                q = new List<CalcEngineSetting>()
                {
                    new ()
                    {
                        Id = CalcEngineSettingId,
                        AmortMethod = "A",
                        RoundToFactor = "R",
                        TimeStamp = [0, 1, 2],
                        FinancialInstitutionId = FinancialInstitutionId,
                    },
                }.BuildMockDbSet();
            }
            else
            {
                q = new List<CalcEngineSetting>().BuildMockDbSet();
            }

            EfPocoService.GetQuery(Arg.Any<Func<IQueryable<CalcEngineSetting>, IQueryable<CalcEngineSetting>>?>())
                .Returns(q);

            return await CalcEngineService.GetSettingsAsync(IFixtureExtensions.FinancialInstitutionId);
        }

        public async Task<GetOrCreateRecordResult<CalcEngineSetting>> SaveSettingsAsync(
            bool saveIsSuccessful = true, bool createRecord = false)
        {
            if (createRecord)
            {
                var fiq = new List<FinancialInstitution>() { new() { Id = FinancialInstitutionId, }, }.BuildMockDbSet();
                EfPocoService.GetQuery(Arg.Any<Func<IQueryable<FinancialInstitution>, IQueryable<FinancialInstitution>>?>())
                    .Returns(fiq);
            }
            var dto = new CalcEngineSettingsDto
            {
                Id = createRecord ? Guid.Empty : CalcEngineSettingId,
                AmortMethod = "A2",
                RoundToFactor = "R2",
                TimeStamp = [ 0, 1, 2],
                FinancialInstitutionId = createRecord ? Guid.Empty : FinancialInstitutionId,
            };

            EfPocoService.CreateOrUpdateAsync(dto.Id, null, Arg.Any<byte[]>(),
                    Arg.Any<PerformCreateOrUpdateOptions<CalcEngineSetting>>())
                .Returns(Task.FromResult(new GetOrCreateRecordResult<CalcEngineSetting>
                {
                    IsSuccessful = saveIsSuccessful,
                    ErrorMessage = saveIsSuccessful ? null : "Databases are just weird",
                    Record = new CalcEngineSetting()
                    {
                        Id = CalcEngineSettingId,
                        AmortMethod = dto.AmortMethod,
                        RoundToFactor = dto.RoundToFactor,
                        TimeStamp = dto.TimeStamp,
                        FinancialInstitutionId = FinancialInstitutionId,
                    },
                    FoundExistingRecord = true,
                    CreatingNewRecord = false,
                    EditingExistingRecord = false,
                }));

            return await CalcEngineService.SaveSettingsAsync(dto, IFixtureExtensions.FinancialInstitutionId);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task GetSettingsNormalAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.GetSettingsAsync();
        Assert.NotNull(result);
        result.Id.Should().Be(rig.CalcEngineSettingId);
        result.FinancialInstitutionId.Should().Be(rig.FinancialInstitutionId);
        result.AmortMethod.Should().Be("A");
        result.RoundToFactor.Should().Be("R");

        rig.EfPocoService.Received()
            .GetQuery(Arg.Any<Func<IQueryable<CalcEngineSetting>, IQueryable<CalcEngineSetting>>?>());
    }

    [Theory, AutoSubstituteData]
    public async Task GetSettingsBlankAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.GetSettingsAsync(hasData: false);
        Assert.NotNull(result);
        result.Id.Should().Be(Guid.Empty);
        result.FinancialInstitutionId.Should().Be(Guid.Empty);
        result.AmortMethod.Should().Be("");
        result.RoundToFactor.Should().Be("");

        rig.EfPocoService.Received()
            .GetQuery(Arg.Any<Func<IQueryable<CalcEngineSetting>, IQueryable<CalcEngineSetting>>?>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveSettingsNormalExistingAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SaveSettingsAsync();
        Assert.NotNull(result);
        Assert.NotNull(result.Record);
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        result.Record.Id.Should().Be(rig.CalcEngineSettingId);
        result.Record.FinancialInstitutionId.Should().Be(rig.FinancialInstitutionId);
        result.Record.AmortMethod.Should().Be("A2");
        result.Record.RoundToFactor.Should().Be("R2");

        await rig.EfPocoService.Received().CreateOrUpdateAsync(rig.CalcEngineSettingId,
            null, Arg.Any<byte[]>(),
                Arg.Any<PerformCreateOrUpdateOptions<CalcEngineSetting>>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveSettingsFailedAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SaveSettingsAsync(saveIsSuccessful:false);
        Assert.NotNull(result);
        Assert.NotNull(result.Record);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Databases are just weird");
        result.Record.Id.Should().Be(rig.CalcEngineSettingId);
        result.Record.FinancialInstitutionId.Should().Be(rig.FinancialInstitutionId);
        result.Record.AmortMethod.Should().Be("A2");
        result.Record.RoundToFactor.Should().Be("R2");

        await rig.EfPocoService.Received().CreateOrUpdateAsync(rig.CalcEngineSettingId,
            null, Arg.Any<byte[]>(),
            Arg.Any<PerformCreateOrUpdateOptions<CalcEngineSetting>>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveSettingsCreateRecordAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SaveSettingsAsync(createRecord:true);
        Assert.NotNull(result);
        Assert.NotNull(result.Record);
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        result.Record.Id.Should().Be(rig.CalcEngineSettingId);
        result.Record.FinancialInstitutionId.Should().Be(rig.FinancialInstitutionId);
        result.Record.AmortMethod.Should().Be("A2");
        result.Record.RoundToFactor.Should().Be("R2");

        rig.EfPocoService.Received()
            .GetQuery(Arg.Any<Func<IQueryable<FinancialInstitution>, IQueryable<FinancialInstitution>>?>());

        await rig.EfPocoService.Received().CreateOrUpdateAsync(Guid.Empty,
            null, Arg.Any<byte[]>(),
            Arg.Any<PerformCreateOrUpdateOptions<CalcEngineSetting>>());
    }
}
