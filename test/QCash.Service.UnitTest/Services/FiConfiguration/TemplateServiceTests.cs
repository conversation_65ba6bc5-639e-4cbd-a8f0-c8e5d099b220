using AutoFixture;
using FluentAssertions;
using MockQueryable.NSubstitute;
using NSubstitute;
using QCash.Data.Models;
using QCash.Service.FileSystem;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Models.MemberInterface;
using QCash.Service.Services.FIConfiguration;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Utilities.Extensions;
using Xunit;
using Enums = QCash.Service.Models.Core.Enums;

namespace QCash.Service.UnitTest.Services.FiConfiguration;

public class TemplateServiceTests
{
    private class TemplateServiceTestRig(IFixture fixture)
    {
        private IFileSystemProvider FileSystemProvider { get;  } = fixture.Freeze<IFileSystemProvider>();
        private IEfPocoService EfPocoService { get; set; } = fixture.Freeze<IEfPocoService>();
        private ILanguageService LanguageService { get; set; } = fixture.Freeze<ILanguageService>();
        private TemplateService TemplateService { get; set; } = fixture.Freeze<TemplateService>();
        private readonly Guid _financialInstitutionId = IFixtureExtensions.FinancialInstitutionId;
        public readonly Guid SettingId = Guid.NewGuid();
        public readonly byte[] SettingTimeStamp = [0, 1, 2, 3];
        public readonly Guid EConsentInEnglishDocumentFileId = Guid.NewGuid();
        public readonly byte[] EConsentInEnglishDocumentFileTimeStamp = [5, 3, 3];
        public readonly Guid AANInEnglishDocumentFileId = Guid.NewGuid();
        public readonly byte[] AANInEnglishDocumentFileTimeStamp = [5, 3, 9];
        private readonly Guid _englishLanguageId = Guid.NewGuid();
        private readonly Guid _englishLanguageSupportStatusId = Guid.NewGuid();
        private readonly Guid _klingonLanguageId = Guid.NewGuid();
        private readonly Guid _klingonLanguageSupportStatusId = Guid.NewGuid();
        private readonly Guid _spanishLanguageId = Guid.NewGuid();
        private readonly Guid _spanishLanguageSupportStatusId = Guid.NewGuid();
        
        public async Task<TemplateSettingsDto> GetSettingsAsync(string? languageCode = null, bool settingRecordExists = true)
        {
            var languages = new List<LanguageDto>()
            {
                new()
                {
                    Id = _englishLanguageId, 
                    LanguageCode = "", 
                    Name = "Brainrot English",
                    LanguageSupportStatusDto = new LanguageSupportStatusDto
                    {
                        Id = _englishLanguageSupportStatusId, 
                        LanguageId = _englishLanguageId,
                        FinancialInstitutionId = _financialInstitutionId,
                        IsActive = true, 
                        TimeStamp = [],
                    },
                },
                new()
                {
                    Id = _klingonLanguageId, 
                    LanguageCode = "klingon", 
                    Name = "Klingon Language",
                    LanguageSupportStatusDto = new LanguageSupportStatusDto
                    {
                        Id = _klingonLanguageSupportStatusId, 
                        LanguageId = _klingonLanguageId,
                        FinancialInstitutionId = _financialInstitutionId,
                        IsActive = true, 
                        TimeStamp = [],
                    },
                },
                new()
                {
                    Id = _spanishLanguageId, 
                    LanguageCode = "es-MX", 
                    Name = "Español",
                    LanguageSupportStatusDto = new LanguageSupportStatusDto
                    {
                        Id = _spanishLanguageSupportStatusId, 
                        LanguageId = _spanishLanguageId,
                        FinancialInstitutionId = _financialInstitutionId,
                        IsActive = false, 
                        TimeStamp = [],
                    },
                },
            }; 
            
            var supportedLanguages = languages.Where(l => l.LanguageSupportStatusDto is { IsActive: true }).ToList();
            LanguageService.GetSupportedLanguagesAsync().Returns(Task.FromResult(supportedLanguages));
            var supportedLanguageQuery = supportedLanguages.BuildMockDbSet();
            LanguageService.GetSupportedLanguagesQuery().Returns(supportedLanguageQuery);

            var settingList = new List<Setting>();
            if (settingRecordExists)
            {
                settingList.Add(new Setting
                {
                    Id = SettingId,
                    FinancialInstitution = new FinancialInstitution()
                    {
                        Slug = IFixtureExtensions.FiSlug,
                    },
                    TimeStamp = SettingTimeStamp,
                });
            }
            var settingQuery = settingList.AsQueryable().BuildMockDbSet();
            EfPocoService.GetQuery(Arg.Any<Func<IQueryable<Setting>, IQueryable<Setting>>>())
                .Returns(settingQuery);
            
            var documentFileNameQuery = new List<DocumentFileName>()
            {
                new()
                {
                    Id = EConsentInEnglishDocumentFileId,
                    LanguageId = _englishLanguageId,
                    DocumentType = new DocumentType()
                    {
                        Id = Guid.NewGuid(),
                        Name = "blah blah",
                        Abrv = Enums.LoanApplicationDocumentType.EConsentDisclosure.ToDescriptionString() ?? "",
                    },
                    FileName = "asdfasdf EConsent.pdf",
                    TimeStamp = EConsentInEnglishDocumentFileTimeStamp,
                },
                new()
                {
                    Id = AANInEnglishDocumentFileId,
                    LanguageId = _englishLanguageId,
                    DocumentType = new DocumentType()
                    {
                        Id = Guid.NewGuid(),
                        Name = "blah blah 2",
                        Abrv = Enums.LoanApplicationDocumentType.AdverseActionNotice.ToDescriptionString() ?? "",
                    },
                    FileName = "asdfasdf AAN.PDF",
                    TimeStamp = AANInEnglishDocumentFileTimeStamp,
                },
            }.AsQueryable().BuildMockDbSet();
            EfPocoService.GetQuery(Arg.Any<Func<IQueryable<DocumentFileName>, IQueryable<DocumentFileName>>>())
                .Returns(documentFileNameQuery);
            
            FileSystemProvider.FileExistsAsync(Arg.Any<string>()).Returns(true);
            
            return await TemplateService.GetSettingsAsync(_financialInstitutionId, languageCode);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task GetSettingsTestEnglishSuccessAsync(IFixture fixture)
    {
        var rig = new TemplateServiceTestRig(fixture);
        var result = await rig.GetSettingsAsync();
        result.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        result.LanguageCode.Should().Be("");
        result.SettingId.Should().Be(rig.SettingId);
        result.SettingTimeStamp.Should().BeEquivalentTo(rig.SettingTimeStamp);
        result.EConsentDocumentId.Should().Be(rig.EConsentInEnglishDocumentFileId);
        result.EConsentDocumentTimeStamp.Should().BeEquivalentTo(rig.EConsentInEnglishDocumentFileTimeStamp);
        result.EConsentFileName.Should().Be("asdfasdf EConsent.pdf");
        result.AANDocumentId.Should().Be(rig.AANInEnglishDocumentFileId);
        result.AANDocumentTimeStamp.Should().BeEquivalentTo(rig.AANInEnglishDocumentFileTimeStamp);
        result.AANFileName.Should().Be("asdfasdf AAN.PDF");
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetSettingsTestKlingonSuccessAsync(IFixture fixture)
    {
        var rig = new TemplateServiceTestRig(fixture);
        var result = await rig.GetSettingsAsync("klingon");
        result.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        result.LanguageCode.Should().Be("klingon");
        result.SettingId.Should().Be(rig.SettingId);
        result.SettingTimeStamp.Should().BeEquivalentTo(rig.SettingTimeStamp);
        result.EConsentDocumentId.Should().BeNull();
        result.EConsentDocumentTimeStamp.Should().BeNull();
        result.AANDocumentId.Should().BeNull();
        result.AANDocumentTimeStamp.Should().BeNull();
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetSettingsTestSpanishSuccessAsync(IFixture fixture)
    {
        var rig = new TemplateServiceTestRig(fixture);
        var result = await rig.GetSettingsAsync("es-MX");
        result.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        result.LanguageCode.Should().Be("");
        result.SettingId.Should().Be(rig.SettingId);
        result.SettingTimeStamp.Should().BeEquivalentTo(rig.SettingTimeStamp);
        result.EConsentDocumentId.Should().BeNull();
        result.EConsentDocumentTimeStamp.Should().BeNull();
        result.AANDocumentId.Should().BeNull();
        result.AANDocumentTimeStamp.Should().BeNull();
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetSettingsTestNoLanguageRecordAsync(IFixture fixture)
    {
        var rig = new TemplateServiceTestRig(fixture);
        var languageResult = await rig.GetSettingsAsync("WRONG");
        languageResult.LanguageCode.Should().BeEmpty();
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetSettingsTestNoSettingRecordAsync(IFixture fixture)
    {
        var rig = new TemplateServiceTestRig(fixture);
        var ex = await Assert.ThrowsAsync<ArgumentNullException>(async () =>
            await rig.GetSettingsAsync(settingRecordExists: false)
        );
        ex.Message.Should().Be("Value cannot be null. (Parameter 'Setting record not found.')");
    }
}