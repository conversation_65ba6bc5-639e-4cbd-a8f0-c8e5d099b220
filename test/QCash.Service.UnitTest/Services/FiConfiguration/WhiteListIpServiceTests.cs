using AutoFixture;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using MockQueryable.NSubstitute;
using NSubstitute;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration;
using QCash.Service.Services.General.Interfaces;
using Xunit;
using Enums = QCash.Service.Models.Core.Enums;

namespace QCash.Service.UnitTest.Services.FiConfiguration;

public class WhiteListIpServiceTests
{
    private class TestRig(IFixture fixture)
    {
        private IIPAddressService IpAddressService { get; set; } = fixture.Freeze<IIPAddressService>();
        public IEfPocoService EfPocoService { get; set; } = fixture.Freeze<IEfPocoService>();
        private WhiteListIpService WhiteListIpService { get; set; } = fixture.Freeze<WhiteListIpService>();
        public Guid ItemId { get; set; } = Guid.NewGuid();

        public async Task<List<WhiteListIpDto>> GetAllAsync(Enums.WhiteListIpTypeEnum whiteListIpTypeEnum)
        {
            if (whiteListIpTypeEnum == Enums.WhiteListIpTypeEnum.AdminUI)
            {
                var data = new List<WhiteListIp>()
                {
                    new()
                    {
                        Id = Guid.NewGuid(), IpAddress = "127.0.0.1", Description = "Test", TimeStamp = [0, 1, 2],
                    },
                }.AsQueryable().BuildMockDbSet();
                EfPocoService.GetQuery<WhiteListIp>().Returns(data);
            }
            else if (whiteListIpTypeEnum == Enums.WhiteListIpTypeEnum.MemberUI)
            {
                var data = new List<IpRestriction>()
                {
                    new()
                    {
                        Id = Guid.NewGuid(), IpAddress = "127.0.0.1", Description = "Test", TimeStamp = [0, 1, 2],
                    },
                }.AsQueryable().BuildMockDbSet();
                EfPocoService.GetQuery<IpRestriction>().Returns(data);
            }

            return await WhiteListIpService.GetAllAsync(whiteListIpTypeEnum);
        }

        public async Task<GetOrCreateRecordResult> SaveAsync(Enums.WhiteListIpTypeEnum whiteListIpType)
        {
            var dto = new WhiteListIpDto()
            {
                WhiteListIpType = whiteListIpType,
                Id = ItemId, IpAddress = "127.0.0.1", Description = "Test", Port=null, TimeStamp = [0, 1, 2],
            };
            IpAddressService.ParseIP("127.0.0.1").Returns("127.0.0.1 Parsed");
            if (whiteListIpType == Enums.WhiteListIpTypeEnum.AdminUI)
            {
                var dbRecord = new WhiteListIp() { TimeStamp = [3,4,5]};
                EfPocoService.CreateOrUpdateAsync(ItemId, IFixtureExtensions.FinancialInstitutionId, Arg.Any<byte[]>(),
                        Arg.Any<PerformCreateOrUpdateOptions<WhiteListIp>>()
                    )
                    .Returns(a =>
                    {
                        var performCreateOrUpdateOptions = a.ArgAt<PerformCreateOrUpdateOptions<WhiteListIp>>(3);
                        Assert.NotNull(performCreateOrUpdateOptions);
                        Assert.NotNull(performCreateOrUpdateOptions.ExecuteRecordChangesFunc);

                        var performUpdateParam = new PerformUpdateParam<WhiteListIp>
                        {
                            Record = dbRecord,
                            CreatingNewRecord = false,
                        };

                        performCreateOrUpdateOptions.ExecuteRecordChangesFunc(performUpdateParam);
                        return new GetOrCreateRecordResult<WhiteListIp>()
                        {
                            IsSuccessful = true,
                            Record = dbRecord,
                            FoundExistingRecord = false,
                            CreatingNewRecord = true,
                            EditingExistingRecord = false,
                        };
                    });
            }
            else if (whiteListIpType == Enums.WhiteListIpTypeEnum.MemberUI)
            {
                var dbRecord = new IpRestriction() { TimeStamp = [4,5,6]};

                EfPocoService.CreateOrUpdateAsync(ItemId, IFixtureExtensions.FinancialInstitutionId, Arg.Any<byte[]>(),
                        Arg.Any<PerformCreateOrUpdateOptions<IpRestriction>>())
                    .Returns(a =>
                    {
                        var performCreateOrUpdateOptions = a.ArgAt<PerformCreateOrUpdateOptions<IpRestriction>>(3);
                        Assert.NotNull(performCreateOrUpdateOptions);
                        Assert.NotNull(performCreateOrUpdateOptions.ExecuteRecordChangesFunc);

                        var performUpdateParam = new PerformUpdateParam<IpRestriction>
                        {
                            Record = dbRecord,
                            CreatingNewRecord = false,
                        };

                        performCreateOrUpdateOptions.ExecuteRecordChangesFunc(performUpdateParam);
                        return new GetOrCreateRecordResult<IpRestriction>()
                        {
                            IsSuccessful = true,
                            Record = dbRecord,
                            FoundExistingRecord = false,
                            CreatingNewRecord = true,
                            EditingExistingRecord = false,
                        };
                    });
            }

            return await WhiteListIpService.SaveAsync(dto, IFixtureExtensions.FinancialInstitutionId);
        }

        public async Task<GenericActionResult> DeleteAsync(Enums.WhiteListIpTypeEnum whiteListIpType)
        {
            var dto = new DeleteItemDto() { DeleteItemId = ItemId, TimeStamp = [0, 1, 2] };
            EfPocoService.DeleteItemAsync<WhiteListIp>(Arg.Any<DeleteItemDto>())
                .Returns(new GenericActionResult() { IsSuccessful = true, });
            return await WhiteListIpService.DeleteItemAsync(whiteListIpType, dto);
        }

        public ModelStateDictionary ValidateModel(Enums.WhiteListIpTypeEnum whiteListIpType,
            bool hasPositiveValue = true,
            bool hasPortValue = true, bool hasIPAddressValue = true)
        {
            var modelState = new ModelStateDictionary();
            var dto = new WhiteListIpDto
            {
                Id = Guid.Empty,
                IpAddress = hasIPAddressValue ? "***********" : "ABC",
                Description = "",
                TimeStamp = [],
                Port = hasPortValue ?
                        (hasPositiveValue ? 43 : -43 )
                        : null,
                WhiteListIpType = whiteListIpType,
            };
            WhiteListIpService.ValidateModel(dto, modelState);
            return modelState;
        }

        public bool ShouldShowPortProperty(Enums.WhiteListIpTypeEnum whiteListIpType)
        {
            return WhiteListIpService.ShouldShowPortProperty(whiteListIpType);
        }

        public string GetListPageTitle(Enums.WhiteListIpTypeEnum whiteListIpType)
        {
            return WhiteListIpService.GetListPageTitle(whiteListIpType);
        }

        public WhiteListIpDto CreateBlankItem(Enums.WhiteListIpTypeEnum whiteListIpType)
        {
            return WhiteListIpService.CreateBlankItem(whiteListIpType);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task GetAllAdminUIAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var results = await rig.GetAllAsync(Enums.WhiteListIpTypeEnum.AdminUI);
        Assert.NotNull(results);
        results.Count.Should().Be(1);
        var result = results.Single();
        result.IpAddress.Should().Be("127.0.0.1");
        result.Description.Should().Be("Test");
        rig.EfPocoService.Received().GetQuery<WhiteListIp>();

        await Assert.ThrowsAsync<ArgumentException>(async () => await rig.GetAllAsync(Enums.WhiteListIpTypeEnum.Unknown));
    }

    [Theory, AutoSubstituteData]
    public async Task GetAllMemberUIAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var results = await rig.GetAllAsync(Enums.WhiteListIpTypeEnum.MemberUI);
        Assert.NotNull(results);
        results.Count.Should().Be(1);
        var result = results.Single();
        result.IpAddress.Should().Be("127.0.0.1");
        result.Description.Should().Be("Test");
        rig.EfPocoService.Received().GetQuery<IpRestriction>();
    }

    [Theory, AutoSubstituteData]
    public async Task SaveAdminUIAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var results = await rig.SaveAsync(Enums.WhiteListIpTypeEnum.AdminUI);
        Assert.NotNull(results);
        results.IsSuccessful.Should().BeTrue();
        var record = results.Record as WhiteListIp;
        Assert.NotNull(record);
        record.IpAddress.Should().Be("127.0.0.1 Parsed");
        record.Description.Should().Be("Test");
        record.TimeStamp.Should().BeEquivalentTo([3,4,5]);
        await rig.EfPocoService.Received().CreateOrUpdateAsync(rig.ItemId, IFixtureExtensions.FinancialInstitutionId, Arg.Any<byte[]>(),
            Arg.Any<PerformCreateOrUpdateOptions<WhiteListIp>>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveMemberUIAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var results = await rig.SaveAsync(Enums.WhiteListIpTypeEnum.MemberUI);
        Assert.NotNull(results);
        results.IsSuccessful.Should().BeTrue();
        var record = results.Record as IpRestriction;
        Assert.NotNull(record);
        record.IpAddress.Should().Be("127.0.0.1 Parsed");
        record.Description.Should().Be("Test");
        record.TimeStamp.Should().BeEquivalentTo([4,5,6]);
        await rig.EfPocoService.Received().CreateOrUpdateAsync(rig.ItemId, IFixtureExtensions.FinancialInstitutionId, Arg.Any<byte[]>(),
            Arg.Any<PerformCreateOrUpdateOptions<IpRestriction>>());
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteAdminUIAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var results = await rig.DeleteAsync(Enums.WhiteListIpTypeEnum.AdminUI);
        Assert.NotNull(results);
        results.IsSuccessful.Should().BeTrue();
        await rig.EfPocoService.Received().DeleteItemAsync<WhiteListIp>(Arg.Any<DeleteItemDto>());
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteMemberUIAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var results = await rig.DeleteAsync(Enums.WhiteListIpTypeEnum.MemberUI);
        Assert.NotNull(results);
        results.IsSuccessful.Should().BeTrue();
        await rig.EfPocoService.Received().DeleteItemAsync<IpRestriction>(Arg.Any<DeleteItemDto>());
    }

    [Theory, AutoSubstituteData]
    public void ValidateAdminUIModel(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        rig.ValidateModel(Enums.WhiteListIpTypeEnum.AdminUI).IsValid.Should().BeTrue();
        rig.ValidateModel(Enums.WhiteListIpTypeEnum.AdminUI, hasPortValue:false).IsValid.Should().BeTrue();
        rig.ValidateModel(Enums.WhiteListIpTypeEnum.AdminUI, hasIPAddressValue:false).IsValid.Should().BeFalse();
    }

    [Theory, AutoSubstituteData]
    public void ValidateMemberUI(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        rig.ValidateModel(Enums.WhiteListIpTypeEnum.MemberUI).IsValid.Should().BeTrue();
        rig.ValidateModel(Enums.WhiteListIpTypeEnum.MemberUI, hasPortValue:false).IsValid.Should().BeFalse();
        rig.ValidateModel(Enums.WhiteListIpTypeEnum.MemberUI, hasPositiveValue:false).IsValid.Should().BeFalse();
        rig.ValidateModel(Enums.WhiteListIpTypeEnum.MemberUI, hasIPAddressValue:false).IsValid.Should().BeFalse();
    }

    [Theory, AutoSubstituteData]
    public void ShouldShowPortProperty(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        rig.ShouldShowPortProperty(Enums.WhiteListIpTypeEnum.AdminUI).Should().BeFalse();
        rig.ShouldShowPortProperty(Enums.WhiteListIpTypeEnum.MemberUI).Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public void GetListPageTitle(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        rig.GetListPageTitle(Enums.WhiteListIpTypeEnum.AdminUI).Should().Be("Admin Portal Access: Allowed IPs");
        rig.GetListPageTitle(Enums.WhiteListIpTypeEnum.MemberUI).Should().Be("Application Access: Allowed IPs");
    }

    [Theory, AutoSubstituteData]
    public void CreateBlankItem(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        {
            var result = rig.CreateBlankItem(Enums.WhiteListIpTypeEnum.AdminUI);
            result.Id.Should().Be(Guid.Empty);
            result.IpAddress.Should().Be("");
            result.Description.Should().Be("");
            result.TimeStamp.Should().BeEmpty();
            result.WhiteListIpType.Should().Be(Enums.WhiteListIpTypeEnum.AdminUI);
        }
        {
            var result = rig.CreateBlankItem(Enums.WhiteListIpTypeEnum.MemberUI);
            result.Id.Should().Be(Guid.Empty);
            result.IpAddress.Should().Be("");
            result.Description.Should().Be("");
            result.TimeStamp.Should().BeEmpty();
            result.WhiteListIpType.Should().Be(Enums.WhiteListIpTypeEnum.MemberUI);
        }
    }
}
