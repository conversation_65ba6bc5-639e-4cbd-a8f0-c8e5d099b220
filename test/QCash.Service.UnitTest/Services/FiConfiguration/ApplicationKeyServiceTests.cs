using AutoFixture;
using FluentAssertions;
using NSubstitute;
using QCash.Common.Crypto;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration;
using QCash.Service.Services.General.Interfaces;
using Xunit;

namespace QCash.Service.UnitTest.Services.FiConfiguration;

public class ApplicationKeyServiceTests
{
    private class TestRig(IFixture fixture)
    {
        public IEfPocoService EfPocoService { get; set; } = fixture.Freeze<IEfPocoService>();
        public ITokenManagerService TokenManagerService { get; set; } = fixture.Freeze<ITokenManagerService>();
        private ApplicationKeyService ApplicationKeyService { get; set; } = fixture.Freeze<ApplicationKeyService>();
        private PerformCreateOrUpdateOptions<ApplicationKey>? PerformCreateOrUpdateOptions { get; set; }
        public Guid NewItemId => Guid.Parse("2b68f48e-4308-414c-89da-e6d1ea906d0c");

        public async Task<GetOrCreateRecordResult<ApplicationKey>> SaveAsync(ApplicationKeyDto dto)
        {
            TokenManagerService.Encrypt("A").Returns("AEncrypted");
            TokenManagerService.Encrypt("B").Returns("BEncrypted");
            TokenManagerService.Encrypt("C").Returns("CEncrypted");
            TokenManagerService.Encrypt("D").Returns("DEncrypted");
            
            EfPocoService.CreateOrUpdateAsync(dto.Id, IFixtureExtensions.FinancialInstitutionId,
                    Arg.Any<byte[]>(),
                    Arg.Do<PerformCreateOrUpdateOptions<ApplicationKey>>(a =>
                        { PerformCreateOrUpdateOptions = a; })
                )
                .Returns(new GetOrCreateRecordResult<ApplicationKey>
                {
                    IsSuccessful = true,
                    Record = new ApplicationKey()
                    {
                        Id = NewItemId,
                        TimeStamp = [0, 1, 2],
                    },
                    FoundExistingRecord = false,
                    CreatingNewRecord = false,
                    EditingExistingRecord = false,
                });
            
            return await ApplicationKeyService.SaveAsync(IFixtureExtensions.FinancialInstitutionId, dto);
        }
        
        public void SaveAsyncTestAction(ApplicationKey record)
        {
            Assert.NotNull(PerformCreateOrUpdateOptions?.ExecuteRecordChangesFunc);

            var data1 = new PerformUpdateParam<ApplicationKey>
            {
                Record = record,
                CreatingNewRecord = false,
            };
            PerformCreateOrUpdateOptions.ExecuteRecordChangesFunc(data1);
        }
    }
    
    [Theory, AutoSubstituteData]
    public async Task SaveAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var dto = new ApplicationKeyDto
        {
            Id = Guid.Empty,
            ApplicationId = Guid.NewGuid(),
            SharedKeyWeb = "A",
            SecondarySharedKey = "B",
            ApplicationSsoKey = "C",
            SecondaryApplicationSsoKey = "D",
            SsoSecondaryEnabled = false,
            TimeStamp = [],
        };
        var result = await rig.SaveAsync(dto);
        result.IsSuccessful.Should().BeTrue();
        Assert.NotNull(result.Record);
        result.Record.Id.Should().Be(rig.NewItemId);
        rig.SaveAsyncTestAction(result.Record);

        await rig.EfPocoService.Received().CreateOrUpdateAsync(Guid.Empty, IFixtureExtensions.FinancialInstitutionId,
            Arg.Any<byte[]>(), Arg.Any<PerformCreateOrUpdateOptions<ApplicationKey>>());

        rig.TokenManagerService.Received(4).Encrypt(Arg.Any<string>());
        rig.TokenManagerService.Received().Encrypt("A");
        rig.TokenManagerService.Received().Encrypt("B");
        rig.TokenManagerService.Received().Encrypt("C");
        rig.TokenManagerService.Received().Encrypt("D");
        
        result.Record.SsoV2SharedKeyWeb.Should().Be("AEncrypted");
        result.Record.SsoSecondaryAppSharedKeyWeb.Should().Be("BEncrypted");
        result.Record.SsoV2WebKey.Should().Be("CEncrypted");
        result.Record.SsoSecondaryAppWebKey.Should().Be("DEncrypted");
    }
}
