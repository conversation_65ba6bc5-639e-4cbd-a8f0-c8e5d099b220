using AutoFixture;
using FluentAssertions;
using MockQueryable.NSubstitute;
using NSubstitute;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration;
using QCash.Service.Services.General.Interfaces;
using Xunit;

namespace QCash.Service.UnitTest.Services.FiConfiguration;

public class InvoiceSettingsServiceTests
{
    private class TestRig(IFixture fixture)
    {
        public readonly IEfPocoService EfPocoService = fixture.Freeze<IEfPocoService>();
        private InvoiceSettingsService InvoiceSettingsService { get; set; } = fixture.Freeze<InvoiceSettingsService>();
        public readonly Guid InvoicePlanId = Guid.NewGuid();

        public async Task<InvoiceGeneralSettingsDto> GetGeneralInfoAsync(bool hasData = true)
        {
            IQueryable<InvoicePlan> q;
            if (hasData)
            {
                q = new List<InvoicePlan>()
                {
                    new ()
                    {
                        Id = InvoicePlanId,
                        CustomerId = "C",
                        TimeStamp = [0, 1, 2],
                    },
                }.BuildMockDbSet();
            }
            else
            {
                q = new List<InvoicePlan>().BuildMockDbSet();
            }

            EfPocoService.GetQuery(Arg.Any<Func<IQueryable<InvoicePlan>, IQueryable<InvoicePlan>>?>())
                .Returns(q);

            return await InvoiceSettingsService.GetGeneralInfoAsync(IFixtureExtensions.FinancialInstitutionId);
        }

        public async Task<GetOrCreateRecordResult<InvoicePlan>> SaveSettingsAsync(
            bool saveIsSuccessful = true, bool createRecord = false)
        {
            var dto = new InvoiceGeneralSettingsDto
            {
                InvoicePlanId = createRecord
                    ? Guid.Empty
                    : InvoicePlanId,
                CustomerId = "C",
                InvoicePlanTimeStamp = [0, 1, 2],
                ResellerName = null,
                TransactionType = "T",
                MinimumFee = null,
                FeeStartDate = null,
                FeeEndDate = null,
            };

            EfPocoService.CreateOrUpdateAsync(dto.InvoicePlanId, IFixtureExtensions.FinancialInstitutionId, Arg.Any<byte[]>(),
                    Arg.Any<PerformCreateOrUpdateOptions<InvoicePlan>>())
                .Returns(Task.FromResult(new GetOrCreateRecordResult<InvoicePlan>
                {
                    IsSuccessful = saveIsSuccessful,
                    ErrorMessage = saveIsSuccessful ? null : "Databases are just weird",
                    Record = new InvoicePlan()
                    {
                        Id = InvoicePlanId,
                        TimeStamp = dto.InvoicePlanTimeStamp,
                        CustomerId = dto.CustomerId,
                    },
                    FoundExistingRecord = true,
                    CreatingNewRecord = createRecord,
                    EditingExistingRecord = !createRecord,
                }));

            return await InvoiceSettingsService.SaveSettingsAsync(dto, IFixtureExtensions.FinancialInstitutionId);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task GetSettingsNormalAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.GetGeneralInfoAsync();
        Assert.NotNull(result);
        result.InvoicePlanId.Should().Be(rig.InvoicePlanId);
        result.CustomerId.Should().Be("C");
        rig.EfPocoService.Received()
            .GetQuery(Arg.Any<Func<IQueryable<InvoicePlan>, IQueryable<InvoicePlan>>?>());
    }

    [Theory, AutoSubstituteData]
    public async Task GetSettingsBlankAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.GetGeneralInfoAsync(hasData: false);
        Assert.NotNull(result);
        result.InvoicePlanId.Should().Be(Guid.Empty);
        result.CustomerId.Should().BeNull();
        result.MinimumFee.Should().BeNull();
        result.FeeStartDate.Should().BeNull();
        result.FeeEndDate.Should().BeNull();

        rig.EfPocoService.Received()
            .GetQuery(Arg.Any<Func<IQueryable<InvoicePlan>, IQueryable<InvoicePlan>>?>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveSettingsNormalExistingAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SaveSettingsAsync();
        Assert.NotNull(result);
        Assert.NotNull(result.Record);
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        result.Record.Id.Should().Be(rig.InvoicePlanId);
        result.Record.CustomerId.Should().Be("C");

        await rig.EfPocoService.Received().CreateOrUpdateAsync(rig.InvoicePlanId,
            IFixtureExtensions.FinancialInstitutionId, Arg.Any<byte[]>(),
                Arg.Any<PerformCreateOrUpdateOptions<InvoicePlan>>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveSettingsFailedAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SaveSettingsAsync(saveIsSuccessful:false);
        Assert.NotNull(result);
        Assert.NotNull(result.Record);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Databases are just weird");
        result.Record.Id.Should().Be(rig.InvoicePlanId);
        result.Record.CustomerId.Should().Be("C");

        await rig.EfPocoService.Received().CreateOrUpdateAsync(rig.InvoicePlanId,
            IFixtureExtensions.FinancialInstitutionId, Arg.Any<byte[]>(),
            Arg.Any<PerformCreateOrUpdateOptions<InvoicePlan>>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveSettingsCreateRecordAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SaveSettingsAsync(createRecord:true);
        Assert.NotNull(result);
        Assert.NotNull(result.Record);
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        result.Record.Id.Should().Be(rig.InvoicePlanId);
        result.Record.CustomerId.Should().Be("C");

        await rig.EfPocoService.Received().CreateOrUpdateAsync(Guid.Empty,
            IFixtureExtensions.FinancialInstitutionId, Arg.Any<byte[]>(),
            Arg.Any<PerformCreateOrUpdateOptions<InvoicePlan>>());
    }
}
