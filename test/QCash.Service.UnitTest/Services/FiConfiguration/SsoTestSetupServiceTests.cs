using System.Diagnostics.CodeAnalysis;
using AutoFixture;
using FluentAssertions;
using NSubstitute;
using QCash.Data.Models;
using QCash.Service.Core;
using QCash.Service.Models;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using Xunit;

namespace QCash.Service.UnitTest.Services.FiConfiguration;

[ExcludeFromCodeCoverage]
public class SsoTestSetupServiceTests
{
    private class SsoTestSetupServiceTestRig
    {
        public SsoTestSetupService SsoTestSetupService { get; set; }
        public IEfPocoService EfPocoService { get; set; }
        public IUnitOfWork UnitOfWork { get; set; }
        public IMemberIdentifiersSettingsService MemberIdentifiersSettingsService { get; set; }
        public IFiSettingService SettingService { get; set; }

        public SsoTestSetupServiceTestRig(IFixture fixture)
        {
            EfPocoService = fixture.Freeze<IEfPocoService>();
            UnitOfWork = fixture.Freeze<IUnitOfWork>();
            MemberIdentifiersSettingsService = fixture.Freeze<IMemberIdentifiersSettingsService>();
            SettingService = fixture.Freeze<IFiSettingService>();
            SsoTestSetupService = fixture.Freeze<SsoTestSetupService>();
        }

        public Task<SsoTestSetupDto?> GetSsoTestDataAsync(Guid applicationId)
        {
            return SsoTestSetupService.GetSsoTestDataAsync(applicationId);
        }

        public async Task<GenericActionResult> SaveAsync(SsoTestSetupDto dto, Guid applicationId)
        {
            return await SsoTestSetupService.SaveAsync(dto, applicationId);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task GetDataNormalCreate(IFixture fixture)
    {
        var rig = new SsoTestSetupServiceTestRig(fixture);
        rig.EfPocoService
            .GetOrCreateRecordAsync<SsoTestSetup>(id: null, IFixtureExtensions.FinancialInstitutionId)
            .Returns(new GetOrCreateRecordResult<SsoTestSetup>()
            {
                IsSuccessful = true,
                Record = new SsoTestSetup(),
                CreatingNewRecord = true,
                FoundExistingRecord = false,
                EditingExistingRecord = false,
            });
        var answer = rig.GetSsoTestDataAsync(IFixtureExtensions.FinancialInstitutionId);
        await rig.EfPocoService.Received()
            .GetOrCreateRecordAsync<SsoTestSetup>(id: null, IFixtureExtensions.FinancialInstitutionId);
        await rig.UnitOfWork.Received()
            .CommitAsync();
        await rig.MemberIdentifiersSettingsService.Received()
            .GetMemberIdentifierFlagsAsync(IFixtureExtensions.FinancialInstitutionId);
    }

    [Theory, AutoSubstituteData]
    public async Task GetDataNormalGet(IFixture fixture)
    {
        var rig = new SsoTestSetupServiceTestRig(fixture);
        rig.EfPocoService
            .GetOrCreateRecordAsync<SsoTestSetup>(id: null, IFixtureExtensions.FinancialInstitutionId)
            .Returns(new GetOrCreateRecordResult<SsoTestSetup>()
            {
                IsSuccessful = true,
                Record = new SsoTestSetup() { BaseAccount = "BaseAccount1"},
                CreatingNewRecord = false,
                FoundExistingRecord = true,
                EditingExistingRecord = true,
            });
        var answer = await rig.GetSsoTestDataAsync(IFixtureExtensions.FinancialInstitutionId);
        // Assert
        Assert.NotNull(answer);
        answer.BaseAccount.Should().Be("BaseAccount1");
        await rig.EfPocoService.Received()
            .GetOrCreateRecordAsync<SsoTestSetup>(id: null, IFixtureExtensions.FinancialInstitutionId);
        await rig.UnitOfWork.DidNotReceive()
            .CommitAsync();
        await rig.MemberIdentifiersSettingsService.Received()
            .GetMemberIdentifierFlagsAsync(IFixtureExtensions.FinancialInstitutionId);
    }

    [Theory, AutoSubstituteData]
    public async Task GetDataFailedToCreate(IFixture fixture)
    {
        var rig = new SsoTestSetupServiceTestRig(fixture);
        rig.EfPocoService
            .GetOrCreateRecordAsync<SsoTestSetup>(id: null, IFixtureExtensions.FinancialInstitutionId)
            .Returns(new GetOrCreateRecordResult<SsoTestSetup>()
            {
                IsSuccessful = false,
                Record = new SsoTestSetup(),
                CreatingNewRecord = true,
                FoundExistingRecord = false,
                EditingExistingRecord = false,
            });
        await Assert.ThrowsAnyAsync<Exception>(async () =>
        {
            await rig.GetSsoTestDataAsync(IFixtureExtensions.FinancialInstitutionId);
        });
        await rig.EfPocoService.Received()
            .GetOrCreateRecordAsync<SsoTestSetup>(id: null, IFixtureExtensions.FinancialInstitutionId);
    }

    [Theory, AutoSubstituteData]
    public async Task SaveSuccess(IFixture fixture)
    {
        var rig = new SsoTestSetupServiceTestRig(fixture);
        rig.SettingService.AdjustAccountNumber(Arg.Any<string?>(), Arg.Any<int?>())
            .Returns("AdjustedAccountNumber");
        var dto = new SsoTestSetupDto
        {
            Id = Guid.NewGuid(),
            BaseAccount = "BaseAccount1",
            Email = "<EMAIL>",
            Fund = true,
            Location = "location1",
            TellerId = "tellerId1",
            CoreDate = null,
            PhoneNumber = "************",
            UseSecondaryApiKeys = true,
            IsTaxId = true,
            IsBaseAccountId = true,
            IsMemberId = true,
            IsIndividualId = true,
            TaxId = "*********",
            TimeStamp = [0, 1, 2],
            MemberId = null,
        };
        await rig.SaveAsync(dto, IFixtureExtensions.FinancialInstitutionId);
        // Assert
        rig.SettingService.Received().AdjustAccountNumber(Arg.Any<string?>(), Arg.Any<int?>());

        await rig.EfPocoService.Received()
            .CreateOrUpdateAsync<SsoTestSetup>(id: dto.Id, IFixtureExtensions.FinancialInstitutionId,
                dto.TimeStamp, Arg.Any<PerformCreateOrUpdateOptions<SsoTestSetup>>());
    }
}
