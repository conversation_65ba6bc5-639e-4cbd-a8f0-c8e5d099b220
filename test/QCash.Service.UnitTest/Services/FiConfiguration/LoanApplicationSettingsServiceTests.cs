using AutoFixture;
using FluentAssertions;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Services.FIConfiguration;
using QCash.Service.Services.General.Interfaces;
using Xunit;

namespace QCash.Service.UnitTest.Services.FiConfiguration;

public class LoanApplicationSettingsServiceTests
{
    private class TestRig(IFixture fixture)
    {
        public readonly QCashContext DbContext = fixture.Freeze<QCashContext>();
        public readonly IAuthUserService AuthUserService = fixture.Freeze<IAuthUserService>(); 
        public readonly LoanApplicationSettingsService LoanApplicationSettingsService =
            fixture.Freeze<LoanApplicationSettingsService>();

        public readonly Guid FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId;
        public FinancialInstitution? FinancialInstitution { get; set; }
        public LoanApplicationSettingsNavHeaderDto GetNavHeader(bool hasPermissions = true)
        {
            AuthUserService.IsSystemAdmin().Returns(hasPermissions);
            AuthUserService.IsSuperUser().Returns(hasPermissions);
            AuthUserService.IsFiManager().Returns(hasPermissions);
            return LoanApplicationSettingsService.GetNavHeader(IFixtureExtensions.FiSlug);
        }

        public async Task<LoanApplicationGeneralSettingsDto> GetGeneralSettingsAsync(bool hasSettingRecord = true)
        {
            DbContext.FinancialInstitutions.Add(new FinancialInstitution
                {
                    Id = IFixtureExtensions.FinancialInstitutionId,
                    Slug = IFixtureExtensions.FiSlug,
                    Address = "", City = "", ContactName = "", ContactPhone = "", GlLoanAccount = "", MailingAddress = "",
                    Name = "Test1 Name", NoReplySubdomain = "TestSubd1", State = "", Zip = "12345", TimeStamp = [],
                }
            );
            if (hasSettingRecord)
            {
                DbContext.Settings.Add(new Setting()
                {
                    Id = Guid.NewGuid(), FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                    AppLogsFileNameTemplate = "", Bankruptcy = "", FilenameAan = "", FilenameEConsent = "",
                    LoanIdSource = "", Mla = "MLA Test Value", MonitorAccount = "",
                    TilaCalculatorSource = "", TimeStamp = [], TimeZone = "", FilenameTila = "",
                    MilitaryAnnualPercentageRate = 0.123M,
                });
            }

            await DbContext.SaveChangesAsync();
            return await LoanApplicationSettingsService.GetGeneralSettingsAsync(IFixtureExtensions.FinancialInstitutionId);
        }
    }
    
    [Theory, AutoSubstituteData]
    public void GetNavHeaderFullPermissions(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = rig.GetNavHeader();
        result.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        result.IsSuperUser.Should().Be(true);
        result.IsSystemAdmin.Should().Be(true);
        result.ShowCalcEngineSettings.Should().Be(true);
        result.ShowNSFSettings.Should().Be(true);
        result.ShowFinancialCoachingSettings.Should().Be(true);
        result.ShowInsuranceProductSettings.Should().Be(true);
    }
    
    [Theory, AutoSubstituteData]
    public void GetNavHeaderNoPermissions(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = rig.GetNavHeader(hasPermissions:false);
        result.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        result.IsSuperUser.Should().Be(false);
        result.IsSystemAdmin.Should().Be(false);
        result.ShowCalcEngineSettings.Should().Be(false);
        result.ShowNSFSettings.Should().Be(false);
        result.ShowFinancialCoachingSettings.Should().Be(false);
        result.ShowInsuranceProductSettings.Should().Be(true);
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetGeneralSettingsAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.GetGeneralSettingsAsync();
        Assert.NotNull(result);
        result.FinancialInstitutionId.Should().Be(rig.FinancialInstitutionId);
        result.Mla.Should().Be("MLA Test Value");
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetGeneralSettingsAsyncSettingMissing(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        await Assert.ThrowsAnyAsync<Exception>(async () =>
        {
            await rig.GetGeneralSettingsAsync(hasSettingRecord: false);
        });
    }

}