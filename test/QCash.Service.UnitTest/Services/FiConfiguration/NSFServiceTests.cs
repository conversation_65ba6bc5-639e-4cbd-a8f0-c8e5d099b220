using AutoFixture;
using FluentAssertions;
using MockQueryable.NSubstitute;
using NSubstitute;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration;
using QCash.Service.Services.General.Interfaces;
using Xunit;

namespace QCash.Service.UnitTest.Services.FiConfiguration;

public class NSFServiceTests
{
    private class TestRig(IFixture fixture)
    {
        public readonly IEfPocoService EfPocoService = fixture.Freeze<IEfPocoService>();
        private NSFService NSFService { get; set; } = fixture.Freeze<NSFService>();
        public readonly Guid FinancialInstitutionId = Guid.NewGuid();
        public readonly Guid NSFSettingId = Guid.NewGuid();

        public async Task<NSFSettingsDto> GetSettingsAsync(bool hasData = true)
        {
            IQueryable<NsfSetting> q;
            if (hasData)
            {
                q = new List<NsfSetting>()
                {
                    new ()
                    {
                        Id = NSFSettingId,
                        NsfCountSelector = "A",
                        NsfCountThreshold = 1,
                        NsfCountMonthsThreshold = 2,
                        TimeStamp = [0, 1, 2],
                        FinancialInstitutionId = FinancialInstitutionId,
                    },
                }.BuildMockDbSet();
            }
            else
            {
                q = new List<NsfSetting>().BuildMockDbSet();
            }

            EfPocoService.GetQuery(Arg.Any<Func<IQueryable<NsfSetting>, IQueryable<NsfSetting>>?>())
                .Returns(q);

            return await NSFService.GetSettingsAsync(IFixtureExtensions.FinancialInstitutionId);
        }

        public async Task<GetOrCreateRecordResult<NsfSetting>> SaveSettingsAsync(
            bool saveIsSuccessful = true, bool createRecord = false)
        {
            if (createRecord)
            {
                var fiq = new List<FinancialInstitution>() { new() { Id = FinancialInstitutionId, }, }.BuildMockDbSet();
                EfPocoService.GetQuery(Arg.Any<Func<IQueryable<FinancialInstitution>, IQueryable<FinancialInstitution>>?>())
                    .Returns(fiq);
            }
            var dto = new NSFSettingsDto
            {
                NsfSettingId = createRecord
                    ? Guid.Empty
                    : NSFSettingId,
                FinancialInstitutionId = createRecord
                    ? Guid.Empty
                    : FinancialInstitutionId,
                NsfSettingTimeStamp = [0, 1, 2],
                NsfCountThreshold = 1,
                NsfCountMonthsThreshold = 2,
                NsfCountSelector = "A",
            };

            EfPocoService.CreateOrUpdateAsync(dto.NsfSettingId, null, Arg.Any<byte[]>(),
                    Arg.Any<PerformCreateOrUpdateOptions<NsfSetting>>())
                .Returns(Task.FromResult(new GetOrCreateRecordResult<NsfSetting>
                {
                    IsSuccessful = saveIsSuccessful,
                    ErrorMessage = saveIsSuccessful ? null : "Databases are just weird",
                    Record = new NsfSetting()
                    {
                        Id = NSFSettingId,
                        TimeStamp = dto.NsfSettingTimeStamp,
                        FinancialInstitutionId = FinancialInstitutionId,
                        NsfCountSelector = "B",
                        NsfCountThreshold = 10,
                        NsfCountMonthsThreshold = 11,
                    },
                    FoundExistingRecord = true,
                    CreatingNewRecord = false,
                    EditingExistingRecord = false,
                }));

            return await NSFService.SaveSettingsAsync(dto, IFixtureExtensions.FinancialInstitutionId);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task GetSettingsNormalAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.GetSettingsAsync();
        Assert.NotNull(result);
        result.NsfSettingId.Should().Be(rig.NSFSettingId);
        result.FinancialInstitutionId.Should().Be(rig.FinancialInstitutionId);
        result.NsfCountSelector.Should().Be("A");
        result.NsfCountThreshold.Should().Be(1);
        result.NsfCountMonthsThreshold.Should().Be(2);

        rig.EfPocoService.Received()
            .GetQuery(Arg.Any<Func<IQueryable<NsfSetting>, IQueryable<NsfSetting>>?>());
    }

    [Theory, AutoSubstituteData]
    public async Task GetSettingsBlankAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.GetSettingsAsync(hasData: false);
        Assert.NotNull(result);
        result.NsfSettingId.Should().Be(Guid.Empty);
        result.FinancialInstitutionId.Should().Be(Guid.Empty);
        result.NsfCountSelector.Should().Be(null);
        result.NsfCountThreshold.Should().Be(0);

        rig.EfPocoService.Received()
            .GetQuery(Arg.Any<Func<IQueryable<NsfSetting>, IQueryable<NsfSetting>>?>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveSettingsNormalExistingAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SaveSettingsAsync();
        Assert.NotNull(result);
        Assert.NotNull(result.Record);
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        result.Record.Id.Should().Be(rig.NSFSettingId);
        result.Record.FinancialInstitutionId.Should().Be(rig.FinancialInstitutionId);
        result.Record.NsfCountSelector.Should().Be("B");
        result.Record.NsfCountThreshold.Should().Be(10);
        result.Record.NsfCountMonthsThreshold.Should().Be(11);

        await rig.EfPocoService.Received().CreateOrUpdateAsync(rig.NSFSettingId,
            null, Arg.Any<byte[]>(),
                Arg.Any<PerformCreateOrUpdateOptions<NsfSetting>>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveSettingsFailedAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SaveSettingsAsync(saveIsSuccessful:false);
        Assert.NotNull(result);
        Assert.NotNull(result.Record);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Databases are just weird");
        result.Record.Id.Should().Be(rig.NSFSettingId);
        result.Record.FinancialInstitutionId.Should().Be(rig.FinancialInstitutionId);
        result.Record.NsfCountSelector.Should().Be("B");
        result.Record.NsfCountThreshold.Should().Be(10);
        result.Record.NsfCountMonthsThreshold.Should().Be(11);

        await rig.EfPocoService.Received().CreateOrUpdateAsync(rig.NSFSettingId,
            null, Arg.Any<byte[]>(),
            Arg.Any<PerformCreateOrUpdateOptions<NsfSetting>>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveSettingsCreateRecordAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SaveSettingsAsync(createRecord:true);
        Assert.NotNull(result);
        Assert.NotNull(result.Record);
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        result.Record.Id.Should().Be(rig.NSFSettingId);
        result.Record.FinancialInstitutionId.Should().Be(rig.FinancialInstitutionId);
        result.Record.NsfCountSelector.Should().Be("B");
        result.Record.NsfCountThreshold.Should().Be(10);
        result.Record.NsfCountMonthsThreshold.Should().Be(11);

        rig.EfPocoService.Received()
            .GetQuery(Arg.Any<Func<IQueryable<FinancialInstitution>, IQueryable<FinancialInstitution>>?>());

        await rig.EfPocoService.Received().CreateOrUpdateAsync(Guid.Empty,
            null, Arg.Any<byte[]>(),
            Arg.Any<PerformCreateOrUpdateOptions<NsfSetting>>());
    }
}
