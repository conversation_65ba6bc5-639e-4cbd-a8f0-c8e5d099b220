using AutoFixture;
using FluentAssertions;
using QCash.Service.Services.FIConfiguration;
using Xunit;

namespace QCash.Service.UnitTest.Services.FiConfiguration;

public class FiSettingServiceTests
{
    public class FiSettingServiceTestRig
    {
        public FiSettingService SettingService { get; set; }

        public FiSettingServiceTestRig(IFixture fixture)
        {
            SettingService = fixture.Create<FiSettingService>();
        }
        public string AdjustAccountNumber(string? accountNumber, int? requiredDigitsParam)
        {
            return SettingService.AdjustAccountNumber(accountNumber, requiredDigitsParam);
        }
    }

    [Theory, AutoSubstituteData]
    public void AdjustAccountNumberEmpty(IFixture fixture)
    {
        var rig = new FiSettingServiceTestRig(fixture);
        rig.AdjustAccountNumber("", 1).Should().Be("");
        rig.AdjustAccountNumber(null, 1).Should().Be("");
    }

    [Theory, AutoSubstituteData]
    public void AdjustAccountNumberNegativeRequiredDigits(IFixture fixture)
    {
        var rig = new FiSettingServiceTestRig(fixture);
        rig.AdjustAccountNumber("88893", -1).Should().Be("88893");
        rig.AdjustAccountNumber("088893", -1).Should().Be("88893");
    }

    [Theory, AutoSubstituteData]
    public void AdjustAccountNumberHasDigitsButNoChangeNeeded(IFixture fixture)
    {
        var rig = new FiSettingServiceTestRig(fixture);
        rig.AdjustAccountNumber("88893", 4).Should().Be("88893");
        rig.AdjustAccountNumber("088893", 4).Should().Be("088893");
    }

    [Theory, AutoSubstituteData]
    public void AdjustAccountNumberAddPaddingZeroes(IFixture fixture)
    {
        var rig = new FiSettingServiceTestRig(fixture);
        rig.AdjustAccountNumber("88893", 6).Should().Be("088893");
        rig.AdjustAccountNumber("088893", 6).Should().Be("088893");
    }
}
