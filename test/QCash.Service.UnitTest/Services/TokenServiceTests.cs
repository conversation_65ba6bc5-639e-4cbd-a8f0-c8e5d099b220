using AutoFixture;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NSubstitute;
using QCash.Common.Crypto;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Services;
using QCash.Service.Services.General;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.LoanApplication;
using Xunit;

namespace QCash.Service.UnitTest.Services;

public class TokenServiceTests
{
    public class TokenServiceTestRig
    {
        public TokenService TokenService { get; set; }
        public QCashContext DbContext { get; set; }
        private IGuidExtensionService guidExtensionService { get; set; }
        private IDistributedCache distributedCache { get; set; }
        public FinancialInstitution? FinancialInstitution { get; set; }
        private TokenManagerService tokenManagerService { get; set; }

        public TokenServiceTestRig(IFixture fixture)
        {
            DbContext = fixture.Freeze<QCashContext>();

            guidExtensionService = new GuidExtensionService();

            var opts = Options.Create<MemoryDistributedCacheOptions>(new MemoryDistributedCacheOptions());
            distributedCache = new MemoryDistributedCache(opts);

            //distributedCache = Substitute.For<IDistributedCache>();

            tokenManagerService = new TokenManagerService(new FakeTokenManagerServiceOptions());

            TokenService = new TokenService(distributedCache, DbContext, tokenManagerService, guidExtensionService);

            BuildOutTestData();
        }

        private class FakeTokenManagerServiceOptions : IOptions<TokenManagerServiceOptions>
        {
            public TokenManagerServiceOptions Value =>
                new()
                {
                    TokenManagerKeys =
                        "YjhjNDEwNjhhN2QyMzdlYTUxNmIyZWFiNzY4NWIwODg=;mZhZGQ2MmFhMjFlNTZiOD;lNGY3ODhkMmJlMTR"
                };
        }

        private void BuildOutTestData()
        {
            FinancialInstitution = new FinancialInstitution
            {
                Id = IFixtureExtensions.FinancialInstitutionId,
                Slug = IFixtureExtensions.FiSlug,
                Address = "",
                City = "",
                ContactName = "",
                ContactPhone = "",
                GlLoanAccount = "",
                MailingAddress = "",
                Name = "Test1 Name",
                NoReplySubdomain = "TestSubd1",
                State = "",
                Zip = "12345",
                TimeStamp = [],
            };
            DbContext.FinancialInstitutions.Add(FinancialInstitution);
            DbContext.SaveChanges();

            var fiConfig = new FinancialInstitutionConfiguration
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Description = "Test Configuration 1",
                DefaultProviderId = Guid.NewGuid(),
                Active = true,
                TimeStamp = []
            };
            DbContext.FinancialInstitutionConfigurations.Add(fiConfig);
            DbContext.SaveChanges();

            //add member
            var financialInstitutionMemberId = Guid.NewGuid();
            var financialInstitutionMember = new FinancialInstitutionMember
            {
                Id = financialInstitutionMemberId,
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                FirstName = "Bob",
                MiddleName = "X",
                LastName = "Test",
                City = "Naperville",
                State = "IL",
                Zip = null,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                AccountId = "1",
                MemberIdHash = string.Empty,
                MemberIdMask = string.Empty
            };
            DbContext.FinancialInstitutionMembers.Add(financialInstitutionMember);
            DbContext.SaveChanges();

            //add memberbaseaccount
            var memberBaseAccount = new MemberBaseAccount
            {
                Id = Guid.NewGuid(),
                AccountId = "1234",
                FinancialInstitutionMemberId = financialInstitutionMemberId,
                OwnerShipType = "Primary",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                Type = "1",
                CloseDateUtc = null,
                OpenDateUtc = DateTime.UtcNow.AddYears(-10),
                BranchNumber = null,
                IsExcluded = null
            };
            DbContext.MemberBaseAccounts.Add(memberBaseAccount);
            DbContext.SaveChanges();

        }
    }

    [Theory, AutoSubstituteData]
    public async Task CheckAllowWhiteListIp(IFixture fixture)
    {
        var rig = new TokenServiceTestRig(fixture);

        var res = await rig.TokenService.AllowWhiteListIpAsync("127.0.0.1");
        res.Should().BeFalse();

        WhiteListIp ip = new WhiteListIp
        {
            Id = Guid.NewGuid(),
            IpAddress = "127.0.0.1",
            Description = "test",
            DateCreatedUtc = DateTime.UtcNow,
            DateUpdatedUtc = DateTime.UtcNow,
            TimeStamp = [],
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
        };

        rig.DbContext.WhiteListIps.Add(ip);
        await rig.DbContext.SaveChangesAsync();

        res = await rig.TokenService.AllowWhiteListIpAsync("127.0.0.1");
        res.Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public async Task CheckTempMemberDataAsync(IFixture fixture)
    {
        var rig = new TokenServiceTestRig(fixture);

        var fiMemberId = await rig.DbContext.FinancialInstitutionMembers.FirstAsync();

        var mlaMember = new TempMemberDTO
        {
            TaxId = "*********",
            MemberId = "00012345"
        };

        var beforeData = await rig.TokenService.GetTempMemberDataAsync(fiMemberId.Id.ToString());
        beforeData.Should().BeNull();

        await rig.TokenService.StoreTempMemberDataAsync(fiMemberId.Id.ToString(),
            rig.TokenService.EncryptValue(JsonConvert.SerializeObject(mlaMember)));

        var afterData = await rig.TokenService.GetTempMemberDataAsync(fiMemberId.Id.ToString());
        Assert.NotNull(afterData);
        afterData = rig.TokenService.DecryptValue(afterData);
        afterData.Should().Be("{\"TaxId\":\"*********\",\"MemberId\":\"00012345\"}");
        var mlaMemberAfter = JsonConvert.DeserializeObject<TempMemberDTO>(afterData);
        Assert.NotNull(mlaMemberAfter);
        mlaMemberAfter.MemberId.Should().Be("00012345");
        
        await rig.TokenService.ClearTempMemberDataAsync(fiMemberId.Id.ToString());
        var clearedData = await rig.TokenService.GetTempMemberDataAsync(fiMemberId.Id.ToString());
        clearedData.Should().BeNull();

    }
}
