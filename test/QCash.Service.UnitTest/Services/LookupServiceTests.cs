using EntityFrameworkCore.Testing.NSubstitute;
using FluentAssertions;
using MockQueryable;
using MockQueryable.NSubstitute;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Data.Models.Enums;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Services;
using QCash.Service.Services.Interfaces;
using Xunit;

namespace QCash.Service.UnitTest.Services;

public class LookupServiceTests
{
    private class LookupServiceTestRig
    {
        private ILookupService LookupService { get; set; }
        private ILookupQueries LookupQueries { get; set; }
        private IProductQueries ProductQueries { get; set; }
        public const string FiSlug = "FiSlug1";
        public const string FiSlugWrong = "FiSlugXXX";
        public readonly Guid Lt1Guid = Guid.NewGuid();
        public readonly Guid Lt2Guid = Guid.NewGuid();
        private QCashContext Context { get; set; } = Create.MockedDbContextFor<QCashContext>();

        public LookupServiceTestRig()
        {
            Context.LoanCategories.AddRange(new List<LoanCategory>()
            {
                new() {Id = Guid.NewGuid(), Name = "A1", Abrv = "A1", AppAbrv = "", Slug = "", TimeStamp = [], },
                new() {Id = Guid.NewGuid(), Name = "A2", Abrv = "A@", AppAbrv = "", Slug = "", TimeStamp = [], },
            });
            Context.SaveChanges();
            LookupQueries = Substitute.For<ILookupQueries>();
            var noLoanTypes = new List<LoanType>().AsQueryable().BuildMock();
            LookupQueries.GetLoanTypes(Arg.Any<string>())
                .Returns(noLoanTypes);
            var lt1 = new LoanType() { Id = Lt1Guid, Name = "LoanType1", };
            var lt2 = new LoanType() { Id = Lt2Guid, Name = "LoanType2", };
            var lt3 = new LoanType()
            {
                Id = Guid.NewGuid(),
                Name = "LoanType3", Abrv = QCash.Service.Services.LookupService.LoanTypeNoProductAbbreviation,
            };
            var loanTypesQ = new List<LoanType>() { lt1, lt2, lt3, }
                .AsQueryable().BuildMock();
            LookupQueries.GetLoanTypes(FiSlug)
                .Returns(loanTypesQ);

            var lc1 = new LoanCategory() { Id = Guid.NewGuid(), Name = "LC1", };
            var lc2 = new LoanCategory() { Id = Guid.NewGuid(), Name = "LC2", };
            var loanCategoriesQ = new List<LoanCategory>() { lc1, lc2 }
                .AsQueryable().BuildMock();
            LookupQueries.GetLoanCategoriesQAsync(FiSlug, Lt1Guid)
                .Returns(loanCategoriesQ);

            ProductQueries = Substitute.For<IProductQueries>();

            LookupService = new LookupService(LookupQueries, ProductQueries, Context);
        }

        public Task<List<QListItem<Guid>>> GetLoanTypesForDropdownAsync(string fiSlug) =>
            LookupService.GetLoanTypesForDropdownAsync(fiSlug);

        public LoanType? GetDefaultLoanType(List<LoanType> loanTypes) =>
            LookupService.GetDefaultLoanType(loanTypes);

        public Task<List<QListItem<Guid>>> GetLoanCategoriesForDropdownAsync(string fiSlug, Guid loanTypeId) =>
            LookupService.GetLoanCategoriesForDropdownAsync(fiSlug, loanTypeId);

        public List<QListItem<TEnum>> GetItemsFromEnum<TEnum>() where TEnum : Enum =>
            LookupService.GetItemsFromEnum<TEnum>();

        public List<QListItem<TEnum>> GetItemsFromEnumWithEnumString<TEnum>() where TEnum : Enum =>
            LookupService.GetItemsFromEnumWithEnumString<TEnum>();

        public List<QListItem<string>> GetTimeZoneChoices() =>
            LookupService.GetTimeZoneChoices();

        public async Task<List<QListItem<string>>> GetStatesAsync()
        {
            var data = new List<State>()
            {
                new() { Name = "N3", Abrv = "Abrv3", },
                new() { Name = "N2", Abrv = "Abrv2", },
                new() { Name = "N1", Abrv = "Abrv1", },
            };
            var q = data.AsQueryable().BuildMockDbSet();
            LookupQueries.GetStatesQueryable()
                .Returns(q);
            return await LookupService.GetStatesAsync();
        }

        public async Task<List<QListItem<Guid>>> GetProductsAsync()
        {
            var data = new List<Product>()
            {
                new() { Id = Guid.NewGuid(), Name = "P1", Abrv = "Abrv1", },
                new() { Id = Guid.NewGuid(), Name = "P2", Abrv = "Abrv2", },
            };
            var q = data.AsQueryable().BuildMockDbSet();
            ProductQueries.GetProducts()
                .Returns(q);
            return await LookupService.GetProductDropdownChoicesAsync();

        }
    }

    /// <summary>
    ///  There are three loan types for this FI, but one is Abrv = "NP", which should be excluded
    /// </summary>
    [Fact]
    public async Task GetLoanTypesForDropdownAsync()
    {
        var rig = new LookupServiceTestRig();
        var loanTypes = await rig.GetLoanTypesForDropdownAsync(LookupServiceTestRig.FiSlug);
        loanTypes.Count().Should().Be(2);

        var lt1 = loanTypes.Single(a => a.Text == "LoanType1");
        lt1.Value.Should().Be(rig.Lt1Guid);

        var lt2 = loanTypes.Single(a => a.Text == "LoanType2");
        lt2.Value.Should().Be(rig.Lt2Guid);
    }


    /// <summary>
    ///  There are three loan types for this FI, but one is Abrv = "NP", which should be excluded
    /// </summary>
    [Fact]
    public async Task GetLoanTypesForDropdownAsyncEmpty()
    {
        var rig = new LookupServiceTestRig();
        var loanTypes = await rig.GetLoanTypesForDropdownAsync(LookupServiceTestRig.FiSlug);
        loanTypes.Count().Should().Be(2);
    }

    [Fact]
    public async Task GetLoanTypesForDropdownAsyncEmptyWrongFI()
    {
        var rig = new LookupServiceTestRig();
        var loanTypes = await rig.GetLoanTypesForDropdownAsync(LookupServiceTestRig.FiSlugWrong);
        loanTypes.Count().Should().Be(0);
    }

    [Fact]
    public void GetDefaultLoanType()
    {
        var rig = new LookupServiceTestRig();
        var loanType = rig.GetDefaultLoanType(
        [
            new LoanType() { Id = Guid.NewGuid(), Abrv = "test1", },
            new LoanType() { Id = Guid.NewGuid(), Abrv = LookupService.LoanTypeInterestBasedAbrv, },
            new LoanType() { Id = Guid.NewGuid(), Abrv = "test2", },
        ]);
        loanType.Should().NotBeNull();
    }

    [Fact]
    public async Task GetLoanCategoriesForDropdownAsync()
    {
        var rig = new LookupServiceTestRig();
        var result = await rig.GetLoanCategoriesForDropdownAsync(
            LookupServiceTestRig.FiSlug, rig.Lt1Guid);
        result.Count.Should().Be(2);
        result.Count(a => a.Text == "LC1").Should().Be(1);
        result.Count(a => a.Text == "LC2").Should().Be(1);
    }

    [Fact]
    public void GetItemsFromEnum()
    {
        var rig = new LookupServiceTestRig();
        var items = rig.GetItemsFromEnum<Enums.DecisionEngineUIGroupEnum>();
        items.Count.Should().Be(7);

        items[0].Text.Should().Be("Unknown");
        items[0].Value.Should().Be(Enums.DecisionEngineUIGroupEnum.Unknown);

        items[1].Text.Should().Be("Eligibility Attributes");
        items[1].Value.Should().Be(Enums.DecisionEngineUIGroupEnum.EligibilityAttributes);

        items[2].Text.Should().Be("Accounts and Transactions");
        items[2].Value.Should().Be(Enums.DecisionEngineUIGroupEnum.AccountsAndTransactions);
    }

    [Fact]
    public void GetItemsFromEnumWithEnumString()
    {
        var rig = new LookupServiceTestRig();
        var items = AutopayOptionEnum.Instance.GetAll();

        items.Length.Should().Be(3);

        items[0].Name.Should().Be("Forced Autopay");
        items[0].Id.Should().Be(AutopayOptionEnum.ForcedAutopay.Id);

        items[1].Name.Should().Be("Opt Out Option");
        items[1].Id.Should().Be(AutopayOptionEnum.OptOutOption.Id);

        items[2].Name.Should().Be("Opt In Option");
        items[2].Id.Should().Be(AutopayOptionEnum.OptInOption.Id);
    }

    [Fact]
    public void GetTimeZoneChoices()
    {
        var rig = new LookupServiceTestRig();
        var items = rig.GetTimeZoneChoices();

        items.Count.Should().Be(9);

        items[0].Text.Should().Be("(UTC-10:00) Hawaii");
        items[0].Value.Should().Be("Hawaiian Standard Time");

        items[8].Text.Should().Be("(UTC-04:00) Georgetown, La Paz, Manaus, San Juan");
        items[8].Value.Should().Be("SA Western Standard Time");
    }

    [Fact]
    public async Task GetStatesAsync()
    {
        var rig = new LookupServiceTestRig();
        var items = await rig.GetStatesAsync();

        items.Count.Should().Be(3);

        items[0].Text.Should().Be("N1");
        items[0].Value.Should().Be("Abrv1");

        items[2].Text.Should().Be("N3");
        items[2].Value.Should().Be("Abrv3");
    }

    [Fact]
    public async Task GetProductsAsync()
    {
        var rig = new LookupServiceTestRig();
        var items = await rig.GetProductsAsync();

        items.Count.Should().Be(2);

        items[0].Text.Should().Be("P1");
        items[0].Value.Should().NotBeEmpty();

        items[1].Text.Should().Be("P2");
        items[1].Value.Should().NotBeEmpty();
    }
}
