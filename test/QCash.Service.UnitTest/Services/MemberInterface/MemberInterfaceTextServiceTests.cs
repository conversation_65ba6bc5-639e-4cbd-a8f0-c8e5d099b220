using AutoFixture;
using FluentAssertions;
using Kendo.Mvc.UI;
using MockQueryable.NSubstitute;
using NSubstitute;
using QCash.Data.Models;
using QCash.Data.Models.Enums;
using QCash.Service.Models.MemberInterface;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.MemberInterface;
using Xunit;

namespace QCash.Service.UnitTest.Services.MemberInterface;

public class MemberInterfaceTextServiceTests
{
    private class TestRig(IFixture fixture)
    {
        private IEfPocoService EfPocoService { get; set; } = fixture.Freeze<IEfPocoService>();
        private MemberInterfaceTextService MemberInterfaceTextService { get; set; } = fixture.Freeze<MemberInterfaceTextService>();
        public Task<DataSourceResult> GetAllAsync()
        {
            var englishLanguage = new Language()
            {
                Id = LanguageEnum.English.Id,
                Name = LanguageEnum.English.Name,
            };
            var spanishLanguage = new Language()
            {
                Id = LanguageEnum.Spanish.Id,
                Name = LanguageEnum.Spanish.Name,
            };
            var data = new List<InterfaceDefaultText>
            {
                new()
                {
                    Id = Guid.NewGuid(),
                    InterfaceOverrideTexts = [],
                    Name = "Test Record 1",
                    FieldValue = "Value 1",
                    Language = englishLanguage,
                    TimeStamp = [],
                },
                new()
                {
                    Id = Guid.NewGuid(),
                    InterfaceOverrideTexts = [ 
                        new InterfaceOverrideText()
                        {
                            Id = Guid.NewGuid(),
                            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                            FieldValue = "Value 2 override",
                        }],
                    Name = "Test Record 2",
                    FieldValue = "Value 2",
                    Language = englishLanguage,
                    TimeStamp = [],
                },
                new()
                {
                    Id = Guid.NewGuid(),
                    InterfaceOverrideTexts = [],
                    Name = "Test Record 3",
                    FieldValue = "Value 3",
                    Language = spanishLanguage,
                    TimeStamp = [],
                },
            };
            var q = data.AsQueryable().BuildMockDbSet();
            EfPocoService.GetQuery(Arg.Any<Func<IQueryable<InterfaceDefaultText>, IQueryable<InterfaceDefaultText>>>())
                .Returns(q);
            var dsr = new DataSourceRequest();
            return MemberInterfaceTextService.GetAllAsync(dsr);
        }
    }
    
    [Theory, AutoSubstituteData]
    public async Task GetAllAsync_NormalSuccess(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var data = await rig.GetAllAsync();
        data.Should().NotBeNull();
        data.Total.Should().Be(2);
        var list = data.Data.Cast<TextLandingPageListItemDto>().ToList();
        {
            var record = list.Single(a => a.FieldName == "Test Record 1");
            record.Value.Should().Be("Value 1");
        }
        {
            var record = list.Single(a => a.FieldName == "Test Record 2");
            record.Value.Should().Be("Value 2 override");
        }
    }
}