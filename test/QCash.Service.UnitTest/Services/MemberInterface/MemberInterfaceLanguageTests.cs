using AutoFixture;
using FluentAssertions;
using NSubstitute;
using QCash.Data.Models;
using QCash.Service.Models.General;
using QCash.Service.Models.MemberInterface;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.MemberInterface;
using QCash.Service.Services.MemberInterface.Interfaces;
using Xunit;

namespace QCash.Service.UnitTest.Services.MemberInterface;

public class MemberInterfaceLanguageTests
{
    private class TestRig(IFixture fixture)
    {
        public IEfPocoService EfPocoService { get; set; } = fixture.Freeze<IEfPocoService>();

        private IMemberInterfaceLanguageService MemberInterfaceLanguageService { get; set; } =
            fixture.Freeze<MemberInterfaceLanguageService>();
        
        public static readonly Guid LanguageSupportStatusId = Guid.NewGuid();
        private static readonly Guid LanguageId = Guid.NewGuid();
        public static readonly Guid FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId;

        private readonly LanguageSupportStatusDto _languageSupportStatusDto = new()
        {
            Id = LanguageSupportStatusId,
            LanguageId = LanguageId,
            FinancialInstitutionId = FinancialInstitutionId,
            IsActive = true,
            TimeStamp = [0, 1, 2],
        };
        
        public async Task<GetOrCreateRecordResult<LanguageSupportStatus>> SetupSuccessfulUpdate(bool isActive = false)
        {
            EfPocoService.CreateOrUpdateAsync(
                    Arg.Is<Guid>(id => id == LanguageSupportStatusId),
                    Arg.Is<Guid>(id => id == FinancialInstitutionId),
                    Arg.Any<byte[]>(),
                    Arg.Any<PerformCreateOrUpdateOptions<LanguageSupportStatus>>())
                .Returns(new GetOrCreateRecordResult<LanguageSupportStatus>
                {
                    IsSuccessful = true,
                    Record = new LanguageSupportStatus
                    {
                        Id = LanguageSupportStatusId,
                        LanguageId = LanguageId,
                        FinancialInstitutionId = FinancialInstitutionId,
                        IsActive = isActive,
                        TimeStamp = [0, 1, 2],
                    },
                    FoundExistingRecord = true,
                    CreatingNewRecord = false,
                    EditingExistingRecord = true,
                });
            return (await MemberInterfaceLanguageService.UpdateLanguageSupportStatusAsync(FinancialInstitutionId, _languageSupportStatusDto)
                as GetOrCreateRecordResult<LanguageSupportStatus>)!;
        }
        
        public async Task<GetOrCreateRecordResult<LanguageSupportStatus>> SetupFailedUpdate()
        {
            EfPocoService.CreateOrUpdateAsync(
                    Arg.Any<Guid>(),
                    Arg.Any<Guid>(),
                    Arg.Any<byte[]>(),
                    Arg.Any<PerformCreateOrUpdateOptions<LanguageSupportStatus>>())
                .Returns(new GetOrCreateRecordResult<LanguageSupportStatus>
                {
                    IsSuccessful = false,
                    ErrorMessage = "Update failed",
                    Record = null,
                    FoundExistingRecord = false,
                    CreatingNewRecord = false,
                    EditingExistingRecord = false,
                });
            return (await MemberInterfaceLanguageService.UpdateLanguageSupportStatusAsync(FinancialInstitutionId, _languageSupportStatusDto)
                as GetOrCreateRecordResult<LanguageSupportStatus>)!;
        }

    }

    [Theory, AutoSubstituteData]
    public async Task UpdateLanguageSupportStatus_WhenSuccessful_ReturnsExpectedResult
        (IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SetupSuccessfulUpdate(isActive: false);

        result.Should().NotBeNull();
        result.IsSuccessful.Should().BeTrue();
        result.Record.Should().NotBeNull();
        result.Record!.IsActive.Should().BeFalse();

        await rig.EfPocoService.Received(1).CreateOrUpdateAsync(
            Arg.Is<Guid>(id => id == TestRig.LanguageSupportStatusId),
            Arg.Is<Guid>(id => id == TestRig.FinancialInstitutionId),
            Arg.Any<byte[]>(),
            Arg.Any<PerformCreateOrUpdateOptions<LanguageSupportStatus>>());
    }
    
    [Theory, AutoSubstituteData]
    public async Task UpdateLanguageSupportStatus_WhenFailed_ReturnsExpectedResult
        (IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SetupFailedUpdate();

        result.Should().NotBeNull();
        result.IsSuccessful.Should().BeFalse();
        result.Record.Should().BeNull();
    }
}