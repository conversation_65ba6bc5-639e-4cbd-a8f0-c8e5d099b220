using AutoFixture;
using AutoFixture.Xunit2;
using EntityFrameworkCore.Testing.NSubstitute;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using MockQueryable.NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Services;
using Xunit;

namespace QCash.Service.UnitTest.Services;

public class FiQueriesTests
{
    private void Setup(IFixture fixture)
    {
        var context = fixture.Create<QCashContext>();

        var financialInstitution = new FinancialInstitution
        {
            Id = IFixtureExtensions.FinancialInstitutionId,
            Slug = IFixtureExtensions.FiSlug,
            Address = "", City = "", ContactName = "", ContactPhone = "", GlLoanAccount = "",
            MailingAddress = "",
            Name = "Test1 Name", NoReplySubdomain = "TestSubd1", State = "", Zip = "12345", TimeStamp = []
        };

        context.FinancialInstitutions.Add(financialInstitution);
        context.SaveChanges();

        //context.FinancialInstitutions = new[] { fi }.AsQueryable().BuildMockDbSet();
    }

    [Theory]
    [AutoSubstituteData]
    public async Task GetFiRecordQueryableNone(
        IFixture fixture,
        FiQueries fiQueries)
    {
        Setup(fixture);
        var result = await fiQueries
            .GetFIRecordQueryable("blahblah")
            .ToListAsync();

        result.Should()
            .BeEmpty();
    }

    [Theory]
    [AutoSubstituteData]
    public async Task GetFiRecordQueryableOne(
        IFixture fixture,
        FiQueries fiQueries
    )
    {
        Setup(fixture);
        var result = await fiQueries
            .GetFIRecordQueryable(IFixtureExtensions.FiSlug)
            .ToListAsync();

        result.Should()
            .NotBeEmpty()
            .And
            .HaveCount(1);
    }
}
