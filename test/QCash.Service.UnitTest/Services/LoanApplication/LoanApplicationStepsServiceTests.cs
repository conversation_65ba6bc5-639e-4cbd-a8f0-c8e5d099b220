using AutoFixture;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.LoanApplication;
using QCash.LoanApplication.Interfaces;
using QCash.LoanApplication.Models;
using QCash.Models.Api;
using QCash.Service.BusinessLogic.Validators.MemberExclusions;
using QCash.Service.BusinessLogic.Validators.MemberExclusions.Rules;
using QCash.Service.BusinessLogic.Validators.ProductsExclusions;
using QCash.Service.Core;
using QCash.Service.CoreProvider;
using QCash.Service.FileSystem;
using QCash.Service.MicroBuilt;
using QCash.Service.Models.Core;
using QCash.Service.Services;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Helpers;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.LoanApplication;
using QCash.Utils.Settings;
using Xunit;
using LoanType = QCash.Data.Models.LoanType;
using Product = QCash.Data.Models.Product;

namespace QCash.Service.UnitTest.Services.LoanApplication;

public class LoanApplicationStepsServiceTests
{
    private class LoanApplicationStepsServiceTestRig
    {
        public LoanApplicationService LoanApplicationService { get; set; }
        public LoanApplicationStepsService LoanApplicationStepsService { get; set; }
        public QCashContext DbContext { get; set; }
        private ICoreProvidersService coreProvidersService { get; set; }
        private ILogger<LoanApplicationService> loggerLAS { get; set; }
        private ILogger<LoanApplicationStepsService> loggerLASS { get; set; }
        private ILogger<FinancialInstitutionService> loggerFIS { get; set; }
        private ILogger<CoreProvidersService> loggerCPS { get; set; }
        private ILogger<ErrorHelper> loggerEH { get; set; }
        private ILogger<MemberExclusionsValidatorRunner> loggerMEVR { get; set; }
        private IMicroBuiltService microBuiltService { get; set; }
        private IGuidExtensionService guidExtensionService { get; set; }
        private ITaxIdService taxIdService { get; set; }
        private ITokenService tokenService { get; set; }
        private IDecisionManagerService decisionManagerService { get; set; }
        private IDecisionEngineService decisionEngineService { get; set; }
        private IFinancialInstitutionService financialInstitutionService { get; set; }
        private IFinancialInstitutionConfigurationService financialInstitutionConfigurationService { get; set; }
        private ILoanApplicationHelper loanApplicationHelper { get; set; }
        private IOptions<ApplicationOptions> options { get; set; }
        private IMailSender mailSender { get; set; }
        private IFileSystemProvider fileSystemProvider { get; set; }
        private IFilenameTemplateService filenameTemplateService { get; set; }
        private IGlobalContainer globalContainer { get; set; }
        private IProductsExclusionsValidatorRunner productsExclusionsValidatorRunner { get; set; }
        private INotificationService notificationService { get; set; }
        private IPaymentOptionService paymentOptionService { get; set; }
        private ILanguageService languageService { get; set; }
        private IMemberProcessInterfaceService memberProcessInterfaceService { get; set; }
        private IMemberExclusionsValidatorRunner memberExclusionsValidatorRunner { get; set; }
        private IMemberInterfaceHelper memberInterfaceHelper { get; set; }
        private IDistributedCache distributedCache { get; set; }
        private ICoreProvider coreProviderMock { get; set; }
        private ErrorHelper errorHelper { get; set; }
        public FinancialInstitution? FinancialInstitution { get; set; }

        private Guid defaultProviderId { get; set; }
        
        private const string MemberMock = "{\"MemberId\":\"*********\",\"TaxId\":\"*********\",\"BaseAccounts\":[{\"Id\":\"6964284\",\"Classification\":\"7\",\"WarningCode\":[],\"Accounts\":[{\"BaseAccountId\":\"6964284\",\"Category\":\"SHARE\",\"AccountId\":\"17\",\"WarningCode\":[],\"AvailableBalance\":65000,\"LedgerBalance\":0,\"OriginalBalance\":0,\"IsClosed\":false,\"IsChargedOff\":false,\"ChargedOffAmount\":0,\"PaymentDueDate\":\"2014-09-27T00:00:00\",\"Type\":\"14\",\"SubType\":null,\"LoanPurpose\":\"22\",\"Description\":\"EASY EQUITY LINE OF CREDIT\",\"TransactionHistory\":null,\"NumberOfPaymentMade\":0,\"LatePayments\":[{\"DayLateStart\":0,\"DayLateEnd\":10,\"NumberOfLatePayments\":0},{\"DayLateStart\":11,\"DayLateEnd\":30,\"NumberOfLatePayments\":0},{\"DayLateStart\":31,\"DayLateEnd\":60,\"NumberOfLatePayments\":0},{\"DayLateStart\":61,\"DayLateEnd\":90,\"NumberOfLatePayments\":0},{\"DayLateStart\":91,\"DayLateEnd\":120,\"NumberOfLatePayments\":0}],\"ExtraInfo\":{\"Infoes\":{\"CODE\":\"2\"}},\"GLAccountNumber\":null,\"OpenDate\":\"2014-03-04T00:00:00\",\"PayoffBalanceAsOfDate\":null,\"PayoffAmount\":0,\"InitialLoanAmount\":0,\"FeeAmountDue\":0}],\"OwnerShip\":0,\"OpenDate\":\"2011-02-10T00:00:00\",\"CloseDate\":null,\"ExtraInfo\":{\"Infoes\":{\"TrackingId\":\"43,53,94,63\",\"BranchNumber\":\"65\"}}},{\"Id\":\"**********\",\"Classification\":\"20\",\"WarningCode\":[{\"Code\":\"4\",\"ExpirationDate\":\"2014-09-26T00:00:00\",\"IsExpired\":true,\"ExtraInfo\":null}],\"Accounts\":[{\"BaseAccountId\":\"**********\",\"Category\":\"SHARE\",\"AccountId\":\"01\",\"WarningCode\":[],\"AvailableBalance\":155,\"LedgerBalance\":150,\"OriginalBalance\":null,\"IsClosed\":false,\"IsChargedOff\":false,\"ChargedOffAmount\":0,\"PaymentDueDate\":null,\"Type\":\"1\",\"SubType\":null,\"LoanPurpose\":null,\"Description\":\"REGULAR SAVINGS\",\"TransactionHistory\":null,\"NumberOfPaymentMade\":0,\"LatePayments\":null,\"ExtraInfo\":{\"Infoes\":{\"CODE\":\"0\"}},\"GLAccountNumber\":null,\"OpenDate\":\"2014-08-26T00:00:00\",\"PayoffBalanceAsOfDate\":null,\"PayoffAmount\":0,\"InitialLoanAmount\":0,\"FeeAmountDue\":0},{\"BaseAccountId\":\"**********\",\"Category\":\"SHARE\",\"AccountId\":\"09\",\"WarningCode\":[],\"AvailableBalance\":0,\"LedgerBalance\":0,\"OriginalBalance\":null,\"IsClosed\":true,\"IsChargedOff\":false,\"ChargedOffAmount\":0,\"PaymentDueDate\":null,\"Type\":\"10\",\"SubType\":null,\"LoanPurpose\":null,\"Description\":\"MONEY MOVER II\",\"TransactionHistory\":null,\"NumberOfPaymentMade\":0,\"LatePayments\":null,\"ExtraInfo\":{\"Infoes\":{\"CODE\":\"1\"}},\"GLAccountNumber\":null,\"OpenDate\":\"2014-08-26T00:00:00\",\"PayoffBalanceAsOfDate\":null,\"PayoffAmount\":0,\"InitialLoanAmount\":0,\"FeeAmountDue\":0},{\"BaseAccountId\":\"**********\",\"Category\":\"SHARE\",\"AccountId\":\"31\",\"WarningCode\":[],\"AvailableBalance\":-2000,\"LedgerBalance\":0,\"OriginalBalance\":null,\"IsClosed\":false,\"IsChargedOff\":false,\"ChargedOffAmount\":0,\"PaymentDueDate\":null,\"Type\":\"63\",\"SubType\":null,\"LoanPurpose\":null,\"Description\":\"CERTIFICATE 36 MONTH $2000 MIN\",\"TransactionHistory\":null,\"NumberOfPaymentMade\":0,\"LatePayments\":null,\"ExtraInfo\":{\"Infoes\":{\"CODE\":\"2\"}},\"GLAccountNumber\":null,\"OpenDate\":\"2014-08-26T00:00:00\",\"PayoffBalanceAsOfDate\":null,\"PayoffAmount\":0,\"InitialLoanAmount\":0,\"FeeAmountDue\":0}],\"OwnerShip\":0,\"OpenDate\":\"2014-08-26T00:00:00\",\"CloseDate\":null,\"ExtraInfo\":{\"Infoes\":{\"TrackingId\":\"94,43,53,54,40\",\"BranchNumber\":\"88\"}}}],\"WarningCodes\":null,\"DateOfBirth\":\"1968-07-07T00:00:00\",\"OpenDate\":\"2011-02-10T00:00:00\",\"Name\":{\"First\":\"JOE\",\"Last\":\"KOOL\",\"Middle\":\"TEST\",\"Suffix\":\"\"},\"Address\":{\"Street1\":\"330 UNION - HQ3 3RD FLOOR\",\"Street2\":null,\"Zip\":\"98501\",\"ZipExt\":null,\"City\":\"OLYMPIA\",\"State\":\"WA\",\"CountryCode\":\"\",\"AddressType\":0},\"Email\":\"<EMAIL>\",\"ExtraInfo\":null}";
        public LoanApplicationStepsServiceTestRig(IFixture fixture)
        {
            DbContext = fixture.Freeze<QCashContext>();

            defaultProviderId = Guid.NewGuid();

            coreProvidersService = Substitute.For<ICoreProvidersService>();
            loggerLAS = Substitute.For<ILogger<LoanApplicationService>>();
            loggerLASS = Substitute.For<ILogger<LoanApplicationStepsService>>();
            loggerEH = Substitute.For<ILogger<ErrorHelper>>();
            loggerFIS = Substitute.For<ILogger<FinancialInstitutionService>>();
            loggerCPS = Substitute.For <ILogger<CoreProvidersService>>();
            loggerMEVR = Substitute.For<ILogger<MemberExclusionsValidatorRunner>>();
            microBuiltService = Substitute.For<IMicroBuiltService>();
            guidExtensionService = new GuidExtensionService();
            taxIdService = Substitute.For<ITaxIdService>();
            tokenService = Substitute.For<ITokenService>();
            decisionManagerService = Substitute.For<IDecisionManagerService>();
            decisionEngineService = Substitute.For<IDecisionEngineService>();
            distributedCache = Substitute.For<IDistributedCache>();
            loanApplicationHelper = Substitute.For<ILoanApplicationHelper>();
            options = new FakeApplicationOptions();
            mailSender = Substitute.For<IMailSender>();
            fileSystemProvider = Substitute.For<IFileSystemProvider>();
            filenameTemplateService = Substitute.For<IFilenameTemplateService>();
            globalContainer = Substitute.For<IGlobalContainer>();
            productsExclusionsValidatorRunner = new ProductsExclusionsValidatorRunner(DbContext, taxIdService);
            
            notificationService = Substitute.For<INotificationService>();
            paymentOptionService = Substitute.For<IPaymentOptionService>();
            languageService = Substitute.For<ILanguageService>();
            memberProcessInterfaceService = Substitute.For<IMemberProcessInterfaceService>();
            memberInterfaceHelper = Substitute.For<IMemberInterfaceHelper>();

            coreProviderMock = fixture.Freeze<ICoreProvider>();
            CoreInfo coreInfo = new CoreInfo()
            {
                Core = new QCash.LoanApplication.Core()
                {
                    Id = defaultProviderId,
                },
                Id = defaultProviderId,
            };
            coreProviderMock.GetSupportedCoresAsync(Arg.Any<string?>(), Arg.Any<CancellationToken>())
                .Returns(Task.FromResult(new CoreInfo[] { coreInfo }));

            Member member = JsonConvert.DeserializeObject<Member>(MemberMock)!;

            coreProviderMock.GetMemberAsync(Arg.Any<Guid>(), Arg.Any<GetMemberParameter>(), Arg.Any<string>())
                .ReturnsForAnyArgs(Task.FromResult(member));
            
            errorHelper = new ErrorHelper(loggerEH, fileSystemProvider);

            financialInstitutionConfigurationService = new FinancialInstitutionConfigurationService(DbContext);

            financialInstitutionService = new FinancialInstitutionService(DbContext, guidExtensionService,
                memberInterfaceHelper, notificationService, tokenService,
                memberProcessInterfaceService, options, loggerFIS);

            coreProvidersService = new CoreProvidersService(coreProviderMock, distributedCache, loggerCPS,
                financialInstitutionConfigurationService);

            loanApplicationHelper = new LoanApplicationHelper(DbContext, decisionManagerService, distributedCache);

            LoanApplicationService = new LoanApplicationService(DbContext, coreProvidersService, loggerLAS, microBuiltService, guidExtensionService, taxIdService,
                tokenService, financialInstitutionService, loanApplicationHelper, options, mailSender, fileSystemProvider, filenameTemplateService,
                globalContainer, productsExclusionsValidatorRunner, memberInterfaceHelper);

            memberExclusionsValidatorRunner = new MemberExclusionsValidatorRunner(DbContext, LoanApplicationService, loggerMEVR,
                memberInterfaceHelper);

            LoanApplicationStepsService = new LoanApplicationStepsService(DbContext, loggerLASS, guidExtensionService, tokenService, financialInstitutionService,
                LoanApplicationService, loanApplicationHelper, options, errorHelper, productsExclusionsValidatorRunner, fileSystemProvider, filenameTemplateService,
                memberProcessInterfaceService, memberInterfaceHelper, decisionManagerService, decisionEngineService, taxIdService, notificationService,
                paymentOptionService, languageService, memberExclusionsValidatorRunner);

            BuildOutTestData();
        }

        public class FakeApplicationOptions : IOptions<ApplicationOptions>
        {
            public ApplicationOptions Value
            {
                get
                {
                    return new ApplicationOptions
                    {
                        Csp = string.Empty,
                        DisplayDiagnosticPages = false,
                        EnableHiddenPages = false,
                        PageSizes =
                        [
                        ],
                        ReportingMaxRecords = 0,
                        ShowExceptionStackTrace = false,
                        WizardURL = string.Empty,
                        MiddlewareRouterBaseUrl = string.Empty,
                        CoreConnectionStatusExpirationSeconds = 0,
                        GetCoreConnectionStatusTimeoutSeconds = 0,
                        MicrobuiltBaseUrl = string.Empty,
                        MicrobuiltClientId = string.Empty,
                        MicrobuiltClientSecret = string.Empty,
                        SaveAdminLogs = false,
                        RetryServiceQueueName = string.Empty,
                        RetryServiceMaxRetries = 0,
                        RetryServiceRetryInterval = 0,
                        NoReplyEmailAddress = string.Empty,
                        TwilioSid = string.Empty,
                        TwilioToken = string.Empty,
                        QCashNotificationPhoneNumber = string.Empty
                    };
                }
            }
        }

        private void BuildOutTestData()
        {
            FinancialInstitution = new FinancialInstitution
            {
                Id = IFixtureExtensions.FinancialInstitutionId,
                Slug = IFixtureExtensions.FiSlug,
                Address = "",
                City = "",
                ContactName = "",
                ContactPhone = "",
                GlLoanAccount = "",
                MailingAddress = "",
                Name = "Test1 Name",
                NoReplySubdomain = "TestSubd1",
                State = "",
                Zip = "12345",
                TimeStamp = [],
            };

            DbContext.FinancialInstitutions.Add(FinancialInstitution);

            LoanType loanTypeIB;
            DbContext.LoanTypes.Add(loanTypeIB = new LoanType()
            {
                Id = Guid.NewGuid(),
                Name = "Interest Based",
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Abrv = "IB",
                AppAbrv = "IB",
                TimeStamp = [],
            });
            DbContext.SaveChanges();

            LoanCategory loanCategory = new LoanCategory
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "UC",
                Abrv = "UC",
                Slug = "unsecured_closed_end",
                Name = "Unsecured Closed-End",
                Description = "Unsecured Closed-End (UCE)",
                IsOpen = false,
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                IsDeleted = false,
            };
            DbContext.LoanCategories.Add(loanCategory);
            DbContext.SaveChanges();

            Product product = new Product()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Slug = "ProductSlug1",
                Name = "Product1",
                Abrv = "",
                AppAbrv = "",
                InvoicePlanId = Guid.NewGuid(),
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                LoanTypeId = loanTypeIB.Id,
                GlTranslationAccount = "",
                InvoiceId = "",
                IsActive = true,
                TimeStamp = [],
                LoanCategory = loanCategory,
            };
            DbContext.Products.Add(product);
            DbContext.SaveChanges();

            DecisionModelType decisionModelType = new DecisionModelType()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "TR",
                Abrv = "TR",
                Slug = "T",
                Name = "traditional",
                Description = "Traditional model type",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                IsDeleted = false,
                TimeStamp = [],
            };
            DbContext.DecisionModelTypes.Add(decisionModelType);

            DecisionModelStatus decisionModelStatus = new DecisionModelStatus()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "INCT",
                Abrv = "INCT",
                Slug = "inactive",
                Name = "Inactive",
                TimeStamp = [],
            };
            DecisionModelStatus decisionModelStatus2 = new DecisionModelStatus()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "ACTV",
                Abrv = "ACTV",
                Slug = "active",
                Name = "Active",
                TimeStamp = [],
            };
            DecisionModelStatus decisionModelStatus3 = new DecisionModelStatus()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "ARHV",
                Abrv = "ARHV",
                Slug = "archived",
                Name = "Archived",
                TimeStamp = [],
            };
            DbContext.DecisionModelStatuses.Add(decisionModelStatus);
            DbContext.DecisionModelStatuses.Add(decisionModelStatus2);
            DbContext.DecisionModelStatuses.Add(decisionModelStatus3);

            //add decisionmodel and productdecisionmodel
            DecisionModel decisionModel = new DecisionModel()
            {
                Id = Guid.NewGuid(),
                Description = "test",
                FiHandle = "testModel",
                StatusDate = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                DateCreatedUtc = DateTime.UtcNow,
                ModelStatus = decisionModelStatus2,
                ModelType = decisionModelType,
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                TimeStamp = [],
            };
            DbContext.DecisionModels.Add(decisionModel);
            DbContext.SaveChanges();

            ProductDecisionModel productDecisionModel = new ProductDecisionModel()
            {
                Id = Guid.NewGuid(),
                DateUpdatedUtc = DateTime.UtcNow,
                DateCreatedUtc = DateTime.UtcNow,
                Product = product,
                DecisionModel = decisionModel,
                TimeStamp = [],
            };
            DbContext.ProductDecisionModels.Add(productDecisionModel);
            DbContext.SaveChanges();

            ScoreType scoreType = new ScoreType()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                DateUpdatedUtc = DateTime.UtcNow,
                DateCreatedUtc = DateTime.UtcNow,
                AppAbrv = "TransformationScore",
                Abrv = "TransformationScore",
                Slug = "transformation_score",
                Name = "Transformation Score",
                Description = "Transformation Score Type",
                IsDeleted = false,
                TimeStamp = [],
            };
            DbContext.ScoreTypes.Add(scoreType);
            DbContext.SaveChanges();

            var fiConfig = new FinancialInstitutionConfiguration
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Description = "Test Configuration 1",
                DefaultProviderId = defaultProviderId,
                Active = true,
                TimeStamp = []
            };
            DbContext.FinancialInstitutionConfigurations.Add(fiConfig);
            DbContext.SaveChanges();

            var loanStatusTypes = new List<LoanStatusType> {
                new() { Name = "Eligible Account Exclusion", Abrv = "LSTEAE", AppAbrv = "LSTEAE", Slug = "eligible_account_exclusion", Description = "Eligible Account Exclusion", ProductDependency = "PreProduct" },
                new() { Name = "Contact Info Changed Exclusion", Abrv = "LSTCICE", AppAbrv = "LSTCICE", Slug = "contact_info_changed_exclusion", Description = "Contact Info Changed Exclusion", ProductDependency = "PreProduct" },
                new() { Name = "Funding Pending", Abrv = "LSTFP", AppAbrv = "LSTFP", Slug = "funding_pending", Description = "Funding Pending", ProductDependency = "Product" },
                new() { Name = "Excluded by Bad Address", Abrv = "LSTBAE", AppAbrv = "LSTBAE", Slug = "bad_address_exclusion", Description = "Excluded by Bad Address", ProductDependency = "PreProduct" },
                new() { Name = "Processing Error", Abrv = "LSTPE", AppAbrv = "LSTPE", Slug = "processing_error", Description = "Processing Error", ProductDependency = "Product" },
                new() { Name = "Excluded by Bad Email", Abrv = "LSTBEE", AppAbrv = "LSTBEE", Slug = "bad_email_exclusion", Description = "Excluded by Bad Email", ProductDependency = "PreProduct" },
                new() { Name = "Approved", Abrv = "LSTA", AppAbrv = "LSTA", Slug = "approved", Description = "Approved", ProductDependency = "PreProduct" },
                new() { Name = "Denied", Abrv = "LSTD", AppAbrv = "LSTD", Slug = "denied", Description = "Denied", ProductDependency = "PreProduct" },
                new() { Name = "Canceled", Abrv = "LSTC", AppAbrv = "LSTC", Slug = "canceled", Description = "Canceled", ProductDependency = "None" },
                new() { Name = "Declined TILA", Abrv = "LSTDET", AppAbrv = "LSTDET", Slug = "declined_tila", Description = "Declined TILA", ProductDependency = "Product" },
                new() { Name = "Declined eConsent", Abrv = "LSTDEE", AppAbrv = "LSTDEE", Slug = "declined_econsent", Description = "Declined eConsent", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Account", Abrv = "LSTEA", AppAbrv = "LSTEA", Slug = "excluded_account", Description = "Excluded Account", ProductDependency = "PreProduct" },
                new() { Name = "Excluded State", Abrv = "LSTES", AppAbrv = "LSTES", Slug = "excluded_state", Description = "Excluded State", ProductDependency = "PreProduct" },
                new() { Name = "Initiate", Abrv = "LSTI", AppAbrv = "LSTI", Slug = "initiate", Description = "Initiate", ProductDependency = "PreProduct" },
                new() { Name = "Military No Product", Abrv = "LSTMNP", AppAbrv = "LSTMNP", Slug = "military_no_product", Description = "Military No Product", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled Before DE", Abrv = "LSTCBD", AppAbrv = "LSTCBD", Slug = "cancelled_before_de", Description = "Cancelled Before DE", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled After DE", Abrv = "LSTCAD", AppAbrv = "LSTCAD", Slug = "cancelled_after_de", Description = "Cancelled After DE", ProductDependency = "PreProduct" },
                new() { Name = "Restricted due to Loan in Process", Abrv = "LSTLIP", AppAbrv = "LSTLIP", Slug = "loan_in_process", Description = "Restricted due to Loan in Process", ProductDependency = "PreProduct" },
                new() { Name = "Is eConsent", Abrv = "LSTIE", AppAbrv = "LSTDIE", Slug = "is_econsent", Description = "Is eConsent", ProductDependency = "PreProduct" },
                new() { Name = "Accept eConsent", Abrv = "LSTAE", AppAbrv = "LSTDAE", Slug = "accept_econsent", Description = "Accept eConsent", ProductDependency = "PreProduct" },
                new() { Name = "Loan Application", Abrv = "LSTLA", AppAbrv = "LSTLA", Slug = "loan_application", Description = "Loan Application", ProductDependency = "PreProduct" },
                new() { Name = "Accept TILA", Abrv = "LSTAT", AppAbrv = "LSTAT", Slug = "accept_tila", Description = "Accept TILA", ProductDependency = "Product" },
                new() { Name = "Display TILA", Abrv = "LSTDT", AppAbrv = "LSTDT", Slug = "display_tila", Description = "Display TILA", ProductDependency = "Product" },
                new() { Name = "Loan Application Fee", Abrv = "LSTLAF", AppAbrv = "LSTLAF", Slug = "loan_fee", Description = "Loan Application Fee", ProductDependency = "PreProduct" },
                new() { Name = "Payoff Initiate", Abrv = "LSTPOINI", AppAbrv = "LSTPOINI", Slug = "payoff_initiate", Description = "Payoff Initiate", ProductDependency = "None" },
                new() { Name = "Payoff Completed", Abrv = "LSTPOCMP", AppAbrv = "LSTPOCMP", Slug = "payoff_complete", Description = "Payoff Completed", ProductDependency = "None" },
                new() { Name = "Payoff Cancelled", Abrv = "LSTPOCNC", AppAbrv = "LSTPOCNC", Slug = "payoff_cancel", Description = "Payoff Cancelled", ProductDependency = "None" },
                new() { Name = "Payoff Error", Abrv = "LSTPOERR", AppAbrv = "LSTPOERR", Slug = "payoff_error", Description = "Payoff Error", ProductDependency = "None" },
                new() { Name = "Payoff Complete", Abrv = "LSTPOPOC", AppAbrv = "LSTPOPOC", Slug = "payoff_po_complete", Description = "Payoff Complete", ProductDependency = "Product" },
                new() { Name = "Payoff Failed", Abrv = "LSTPOPOF", AppAbrv = "LSTPOPOF", Slug = "payoff_po_failed", Description = "Payoff Failed", ProductDependency = "Product" },
                new() { Name = "Local", Abrv = "mla_local", AppAbrv = "mla_local", Slug = "mla_local", Description = "MLA Local", ProductDependency = "None" },
                new() { Name = "Remote", Abrv = "mla_remote", AppAbrv = "mla_remote", Slug = "mla_remote", Description = "MLA Remote", ProductDependency = "None" },
                new() { Name = "Error", Abrv = "mla_error", AppAbrv = "mla_error", Slug = "mla_error", Description = "MLA Error", ProductDependency = "None" },
                new() { Name = "Display Loan Landing", Abrv = "LSTDL", AppAbrv = "LSTDL", Slug = "display_loan_landing", Description = "Display Loan Landing", ProductDependency = "PreProduct" },
                new() { Name = "Display Awareness", Abrv = "LSTDA", AppAbrv = "LSTDA", Slug = "display_awareness", Description = "Display Awareness", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled PreApp", Abrv = "LSTCPA", AppAbrv = "LSTCPA", Slug = "cancelled_pre_app", Description = "Cancelled PreApp", ProductDependency = "PreProduct" },
                new() { Name = "Loan Hub", Abrv = "LSTLH", AppAbrv = "LSTLH", Slug = "loan_hub", Description = "Loan Hub", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled Awareness", Abrv = "LSTCA", AppAbrv = "LSTCA", Slug = "cancelled_awareness", Description = "Cancelled Awareness", ProductDependency = "PreProduct" },
                new() { Name = "Restricted Due To Denied Loans", Abrv = "LSTEDL", AppAbrv = "LSTEDL", Slug = "Denied Loan", Description = "Restricted Due To Denied Loans", ProductDependency = "PreProduct" },
                new() { Name = "ESignature Pending", Abrv = "LSTESP", AppAbrv = "LSTESP", Slug = "esignature_pending", Description = "ESignature Pending", ProductDependency = "Product" },
                new() { Name = "Excluded Missing Email", Abrv = "LSTEME", AppAbrv = "LSTEME", Slug = "excluded_missing_email", Description = "Excluded Missing Email", ProductDependency = "PreProduct" },
                new() { Name = "ESignature Error", Abrv = "LSTESE", AppAbrv = "LSTESE", Slug = "esignature_error", Description = "ESignature Error", ProductDependency = "Product" },
                new() { Name = "Restricted Due To Bankruptcy", Abrv = "LSTBEX", AppAbrv = "LSTBEX", Slug = "bankruptcy_exclusion", Description = "Restricted Due To Bankruptcy", ProductDependency = "PreProduct" },
                new() { Name = "Excluded By Age", Abrv = "LSTAGE", AppAbrv = "LSTAGE", Slug = "age_exclusion", Description = "Excluded By Age", ProductDependency = "PreProduct" },
                new() { Name = "Troubled Debt Exclusion", Abrv = "LSTTDE", AppAbrv = "LSTTDE", Slug = "troubled_debt_exclusion", Description = "Troubled Debt Exclusion", ProductDependency = "PreProduct" },
                new() { Name = "Restricted Due To Signature Pending", Abrv = "LSTRESP", AppAbrv = "LSTRESP", Slug = "restricted_esignature_pending", Description = "Restricted Due To Signature Pending", ProductDependency = "PreProduct" },
                new() { Name = "Not Funded", Abrv = "LSTNF", AppAbrv = "LSTNF", Slug = "not_funded", Description = "Not Funded", ProductDependency = "Product" },
                new() { Name = "Excluded Personal Loan", Abrv = "LSTEPL", AppAbrv = "LSTEPL", Slug = "excluded_personal_loan", Description = "Excluded Personal Loan", ProductDependency = "PreProduct" },
                new() { Name = "Maintenance", Abrv = "LSTM", AppAbrv = "LSTM", Slug = "maintenance", Description = "Maintenance", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Joint Account", Abrv = "LSTJAEX", AppAbrv = "LSTJAEX", Slug = "joint_account_exclusion", Description = "Excluded Joint Account", ProductDependency = "PreProduct" },
                new() { Name = "Fraud Control", Abrv = "LSTFC", AppAbrv = "LSTFC", Slug = "fraud_control", Description = "Fraud Control", ProductDependency = "PreProduct" },
                new() { Name = "Fraud Control Code Expired", Abrv = "LSTFCE", AppAbrv = "LSTFCE", Slug = "fraud_control_expired", Description = "Fraud Control Code Expired", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Fraud Control", Abrv = "LSEFC", AppAbrv = "LSEFC", Slug = "excluded_fraud_control", Description = "Excluded Fraud Control", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Missing Email and Phone", Abrv = "LSEMEP", AppAbrv = "LSEMEP", Slug = "excluded_missing_email_and_phone", Description = "Excluded Missing Email and Phone", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled Fraud Control", Abrv = "LSTCFC", AppAbrv = "LSTCFC", Slug = "cancelled_fraud_control", Description = "Cancelled Fraud Control", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Loan Funding Wait Period", Abrv = "LSELFWP", AppAbrv = "LSELFWP", Slug = "excluded_loan_funding_wait_period", Description = "Excluded Loan Funding Wait Period", ProductDependency = "PreProduct" },
                new() { Name = "QCF Audit", Abrv = "LSTQCFA", AppAbrv = "LSTQCFA", Slug = "qcf_audit", Description = "QCF Audit", ProductDependency = "None" },
                new() { Name = "Blocklist Exclusion", Abrv = "LSTBLE", AppAbrv = "LSTBLE", Slug = "blocklist_exclusion", Description = "Blocklist Exclusion", ProductDependency = "PreProduct" },
                new() { Name = "Funding Complete", Abrv = "LSTCMP", AppAbrv = "LSTCMP", Slug = "completed", Description = "Funding Complete", ProductDependency = "Product" },
                new() { Name = "Declined Data Collection", Abrv = "LSTDEDC", AppAbrv = "LSTDEDC", Slug = "declinde_data_collection", Description = "Declined Data Collection", ProductDependency = "None" },
                new() { Name = "Data Collection", Abrv = "LSTDC", AppAbrv = "LSTDC", Slug = "data_collection", Description = "Data Collection", ProductDependency = "None" },
                new() { Name = "In Process", Abrv = "LSTIP", AppAbrv = "LSTIP", Slug = "in_progress", Description = "In Progress", ProductDependency = "None" },
            };
            foreach (var item in loanStatusTypes.Select((lsType, index) => new { index, lsType }))
            {
                item.lsType.Id = Guid.NewGuid();
                item.lsType.FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId;
                item.lsType.DateCreatedUtc = DateTime.UtcNow;
                item.lsType.DateUpdatedUtc = DateTime.UtcNow;
                item.lsType.TimeStamp = [];
                item.lsType.IsDeleted = false;
            }
            DbContext.LoanStatusTypes.AddRange(loanStatusTypes);
            DbContext.SaveChanges();

            var setting = new Setting
            {
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Id = Guid.NewGuid(),
                IsActive = true,
                FilenameAan = "",
                FilenameTila = "",
                LoanIdSource = "",
                Mla = "",
                TimeZone = "Central Standard Time",
                MonitorAccount = "",
                TilaCalculatorSource = "",
                AppLogsFileNameTemplate = "",
                Bankruptcy = "",
                FilenameEConsent = "",
                TimeStamp = [],
            };

            DbContext.Settings.Add(setting);
            DbContext.SaveChanges();

            var leSetting = new LoanExclusionSetting
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                DeniedLoansExclusion = false,
                NumberOfDeniedLoans = 0,
                DeniedLoansTreshold = 0,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
            };
            DbContext.LoanExclusionSettings.Add(leSetting);
            DbContext.SaveChanges();

            var saTypes = new SavingAccountType
            {
                Id = Guid.NewGuid(),
                Value = "1",
                Description = "REGULAR SAVINGS",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            };
            DbContext.SavingAccountTypes.Add(saTypes);
            DbContext.SaveChanges();
        }

    }

    [Theory, AutoSubstituteData]
    public void CheckIsUnderMaintenance(IFixture fixture)
    {
        var rig = new LoanApplicationStepsServiceTestRig(fixture);

        var res = rig.LoanApplicationStepsService.CheckIsUnderMaintenance(null);
        res.Should().BeFalse();

        MaintenanceSetting maint = new MaintenanceSetting();
        maint.IsMaintenanceActive = true;
        res = rig.LoanApplicationStepsService.CheckIsUnderMaintenance(maint);
        res.Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public async Task StartLoanApplicationAsyncTest(IFixture fixture)
    {
        var rig = new LoanApplicationStepsServiceTestRig(fixture);
        //This test goes through the initial pass of bringing a loan request into the database, running basic exclusions, etc.

        var sso = new SsoInitiateApiModel
        {
            BaseAccount = "**********",    //matches mock
            MemberId = "*********",
            TaxId = "*********",
            Email = "Email1",
            PhoneNumber = "PhoneNumber1",
            Location = "Location1",
        };

        await rig.LoanApplicationStepsService.StartLoanApplicationAsync(sso, Guid.NewGuid());

        rig.DbContext.LoanApplications.Count().Should().Be(1);
        rig.DbContext.MemberBaseAccounts.Count().Should().Be(2);
        rig.DbContext.MemberBaseAccountWarningCodes.Count().Should().Be(1);
        rig.DbContext.MemberAccounts.Count().Should().Be(4);
        rig.DbContext.FinancialInstitutionMembers.Count().Should().Be(1);
        rig.DbContext.LoanApplicationSettingsLogs.Count().Should().BeGreaterThan(0);
        rig.DbContext.LoanApplicationLogs.Count().Should().BeGreaterThan(0);
        //this pass should end on an LSTI
        rig.DbContext.LoanApplications.Include(o => o.LoanStatus).First().LoanStatus!.Abrv.Should().Be("LSTI");
        rig.DbContext.LoanApplications.First().NextStep.Should().Be(2);
        
    }

}
