using EntityFrameworkCore.Testing.NSubstitute;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.LoanApplication;
using QCash.Models.Api;
using QCash.Service.BusinessLogic.Validators.ProductsExclusions;
using QCash.Service.CoreProvider;
using QCash.Service.FileSystem;
using QCash.Service.MicroBuilt;
using QCash.Service.Models.Core;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.LoanApplication;
using QCash.Utils.Settings;
using Xunit;

namespace QCash.Service.UnitTest.Services.LoanApplication;

public class LoanApplicationServiceTests
{
    private class LoanApplicationServiceTestRig
    {
        public LoanApplicationService LoanApplicationService { get; set;}
        private QCashContext Context { get; set; } = Create.MockedDbContextFor<QCashContext>();

        private ICoreProvidersService coreProvidersService { get; set; }
        private ILogger<LoanApplicationService> logger { get; set; }
        private IMicroBuiltService microBuiltService { get; set; }
        private IGuidExtensionService guidExtensionService { get; set; }
        private ITaxIdService taxIdService { get; set; }
        private ITokenService tokenService { get; set; }

        //private IDecisionManagerService decisionManagerService { get; set; }
        private IFinancialInstitutionService financialInstitutionService { get; set; }

        private ILoanApplicationHelper loanApplicationHelper { get; set; }

        //private IDistributedCache distributedCache { get; set; }

        private IOptions<ApplicationOptions> options { get; set; }

        private IMailSender mailSender { get; set; }
        private IFileSystemProvider fileSystemProvider { get; set; }
        private IFilenameTemplateService filenameTemplateService { get; set; }
        private IGlobalContainer globalContainer { get; set; }
        private IProductsExclusionsValidatorRunner productsExclusionsValidatorRunner { get; set; }
        private IMemberInterfaceHelper memberInterfaceHelper { get; set; }

        public ValidationResult ValidateSso(SsoInitiateApiModel sso) => LoanApplicationService.ValidateSso(sso);
        
        public Task<ExtraInfo> GetExtraInfoForCreateAndFundLoanAsync(
            Data.Models.LoanApplication loanApplication, 
            MemberBaseAccount baseAccount, 
            NewLoan newMiddlewareLoan, 
            Setting settings, 
            IList<LoanApplicationSettingsLog> settingsLog, 
            CampaignCode? campaignCode) => 
            LoanApplicationService.GetExtraInfoForCreateAndFundLoanAsync(
                loanApplication, 
                baseAccount, 
                newMiddlewareLoan, 
                settings, 
                settingsLog, 
                campaignCode);
        
        public LoanApplicationServiceTestRig()
        {
            coreProvidersService = Substitute.For<ICoreProvidersService>();
            logger = Substitute.For<ILogger<LoanApplicationService>>();
            microBuiltService = Substitute.For<IMicroBuiltService>();
            guidExtensionService = Substitute.For<IGuidExtensionService>();
            taxIdService = Substitute.For<ITaxIdService>();
            tokenService = Substitute.For<ITokenService>();
            //decisionManagerService = Substitute.For<IDecisionManagerService>();
            financialInstitutionService = Substitute.For<IFinancialInstitutionService>();
            //distributedCache = Substitute.For<IDistributedCache>();
            loanApplicationHelper = Substitute.For<ILoanApplicationHelper>();
            options = Substitute.For<IOptions<ApplicationOptions>>();
            mailSender = Substitute.For<IMailSender>();
            fileSystemProvider = Substitute.For<IFileSystemProvider>();
            filenameTemplateService = Substitute.For<IFilenameTemplateService>();
            globalContainer = Substitute.For<IGlobalContainer>();
            productsExclusionsValidatorRunner = Substitute.For<IProductsExclusionsValidatorRunner>();
            memberInterfaceHelper = Substitute.For<IMemberInterfaceHelper>();

            LoanApplicationService = new LoanApplicationService(Context, coreProvidersService, logger, microBuiltService, guidExtensionService, taxIdService,
                tokenService, financialInstitutionService, loanApplicationHelper, options, mailSender, fileSystemProvider, filenameTemplateService,
                globalContainer, productsExclusionsValidatorRunner, memberInterfaceHelper);
        }
    }

    [Fact]
    public void ValidateSsoValid()
    {
        var rig = new LoanApplicationServiceTestRig();
        var result = rig.ValidateSso(new SsoInitiateApiModel
        {
            BaseAccount = "BaseAccount1",
            MemberId = "MemberId1",
            TaxId = "TaxId1",
            Email = "Email1",
            PhoneNumber = "PhoneNumber1",
            Location = "Location1",
        });
        result.Errors.Errors.Any().Should().BeFalse();
    }

    [Fact]
    public void ValidateSsoNoLocation()
    {
        var rig = new LoanApplicationServiceTestRig();
        var result = rig.ValidateSso(new SsoInitiateApiModel
        {
            BaseAccount = "BaseAccount1",
            MemberId = "MemberId1",
            TaxId = "TaxId1",
            Email = "Email1",
            PhoneNumber = "PhoneNumber1",
            // Location = "Location1",
        });
        result.Errors.Errors.Single().Should().Be("Location is required.");
    }

    [Fact]
    public void ValidateSsoNoMemberIdentifier()
    {
        var rig = new LoanApplicationServiceTestRig();
        var result = rig.ValidateSso(new SsoInitiateApiModel
        {
            //BaseAccount = "BaseAccount1",
            //MemberId = "MemberId1",
            //TaxId = "TaxId1",
            Email = "Email1",
            PhoneNumber = "PhoneNumber1",
            Location = "Location1",
        });
        result.Errors.Errors.Single().Should().Be("Member Identifier (BaseAccount, MemberId, TaxId) is required.");
    }
    
    [Fact]
    public async Task CreateAndFundLoanAsync_ShouldPassCorrectBranchNumber_WhenProductHasNoCustomBranchNumber()
    {
        var rig = new LoanApplicationServiceTestRig();
        
        var loanApplication = new Data.Models.LoanApplication
        {
            Id = Guid.NewGuid(),
            SelectedProduct = new Data.Models.Product
            {
                CustomBranchNumber = null,
                PersonalLoanCampaign = false,
                LoanCategory = new LoanCategory{ IsOpen = false }
            },
            LoanApplicationSso = new LoanApplicationSso
            {
                TellerId = "TELLER123",
                Location = "Location1",
            },
            FinancialInstitutionMember = new FinancialInstitutionMember
            {
                FirstName = "John",
                LastName = "Doe"
            }
        };
        
        var memberBaseAccount = new MemberBaseAccount { BranchNumber = "123" };
        
        var extraInfo = 
            await rig.GetExtraInfoForCreateAndFundLoanAsync(
                loanApplication, 
                memberBaseAccount, 
                new NewLoan(), 
                new Setting(), 
                new List<LoanApplicationSettingsLog>(), null);
        
        extraInfo.Should().NotBeNull();
        extraInfo.Infoes.TryGetValue("BranchNumber", out var branchNumber);
        branchNumber.Should().Be("123");
    }
    
    [Fact]
    public async Task CreateAndFundLoanAsync_ShouldPassCorrectBranchNumber_WhenProductHasCustomBranchNumber()
    {
        var rig = new LoanApplicationServiceTestRig();
        
        var loanApplication = new Data.Models.LoanApplication
        {
            Id = Guid.NewGuid(),
            SelectedProduct = new Data.Models.Product
            {
                CustomBranchNumber = 987,
                PersonalLoanCampaign = false,
                LoanCategory = new LoanCategory{ IsOpen = false }
            },
            LoanApplicationSso = new LoanApplicationSso
            {
                TellerId = "TELLER123",
                Location = "Location1",
            },
            FinancialInstitutionMember = new FinancialInstitutionMember
            {
                FirstName = "John",
                LastName = "Doe"
            }
        };
        
        var memberBaseAccount = new MemberBaseAccount { BranchNumber = "123" };
        
        var extraInfo = 
            await rig.GetExtraInfoForCreateAndFundLoanAsync(
                loanApplication, 
                memberBaseAccount, 
                new NewLoan(), 
                new Setting(), 
                new List<LoanApplicationSettingsLog>(), null);
        
        extraInfo.Should().NotBeNull();
        extraInfo.Infoes.TryGetValue("BranchNumber", out var branchNumber);
        branchNumber.Should().Be("987");
    }
    
    [Fact]
    public async Task CreateAndFundLoanAsync_ShouldPassNullBranchNumber_WhenProductHasNoCustomBranchNumberAndBaseAccountHasNullBranchNumber()
    {
        var rig = new LoanApplicationServiceTestRig();
        
        var loanApplication = new Data.Models.LoanApplication
        {
            Id = Guid.NewGuid(),
            SelectedProduct = new Data.Models.Product
            {
                CustomBranchNumber = null,
                PersonalLoanCampaign = false,
                LoanCategory = new LoanCategory{ IsOpen = false }
            },
            LoanApplicationSso = new LoanApplicationSso
            {
                TellerId = "TELLER123",
                Location = "Location1",
            },
            FinancialInstitutionMember = new FinancialInstitutionMember
            {
                FirstName = "John",
                LastName = "Doe"
            }
        };
        
        var memberBaseAccount = new MemberBaseAccount { BranchNumber = null };
        
        var extraInfo = 
            await rig.GetExtraInfoForCreateAndFundLoanAsync(
                loanApplication, 
                memberBaseAccount, 
                new NewLoan(), 
                new Setting(), 
                new List<LoanApplicationSettingsLog>(), null);
        
        extraInfo.Should().NotBeNull();
        extraInfo.Infoes.TryGetValue("BranchNumber", out var branchNumber);
        branchNumber.Should().BeNull();
    }
}
