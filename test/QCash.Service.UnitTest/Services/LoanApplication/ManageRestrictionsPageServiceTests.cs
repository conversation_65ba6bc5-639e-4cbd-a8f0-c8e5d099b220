using System.Diagnostics;
using AutoFixture;
using FluentAssertions;
using MockQueryable.NSubstitute;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.General;
using QCash.Service.Models.LoanApplication;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.LoanApplication;
using Xunit;

namespace QCash.Service.UnitTest.Services.LoanApplication;

public class ManageRestrictionsPageServiceTests
{
    private class TestRig
    {
        public IProductQueries ProductQueries { get; set; }
        public ManageRestrictionsPageService ManageRestrictionsPageService { get; set; }
        public QCashContext DbContext { get; set; }
        public Setting Setting { get; set; }
        public FinancialInstitution FinancialInstitution { get; set; }
        public Product Product { get; set; }
        public IEfPocoService EfPocoService { get; set; }
        public GlobalExclusionState GlobalExclusionState { get; set; }
        public StateRestrictedInterestRate StateRestrictedInterestRate { get; set; }

        public TestRig(IFixture fixture)
        {
            ProductQueries = fixture.Freeze<IProductQueries>();
            EfPocoService = fixture.Freeze<IEfPocoService>();

            DbContext = fixture.Freeze<QCashContext>();

            FinancialInstitution = new FinancialInstitution
            {
                Id = IFixtureExtensions.FinancialInstitutionId,
                Slug = IFixtureExtensions.FiSlug,
                Address = "", City = "", ContactName = "", ContactPhone = "", GlLoanAccount = "",
                MailingAddress = "",
                Name = "Test1 Name", NoReplySubdomain = "TestSubd1", State = "", Zip = "12345", TimeStamp = []
            };

            DbContext.FinancialInstitutions.Add(FinancialInstitution);

            DbContext.SaveChanges();
            LoanType loanType1;
            DbContext.LoanTypes.Add(loanType1 = new LoanType()
            {
                Id = Guid.NewGuid(), Name = "LT1", FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId, Abrv = "", AppAbrv="", TimeStamp = [],
            });
            DbContext.SaveChanges();
            DbContext.Products.Add(Product = new Product()
            {   Id = Guid.NewGuid(), FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId, Slug = "ProductSlug1", Name = "Product1", Abrv = "", AppAbrv = "",
                InvoicePlanId = Guid.NewGuid(), DateCreatedUtc = DateTime.Now, DateUpdatedUtc = DateTime.Now, LoanTypeId = loanType1.Id,
                GlTranslationAccount = "", InvoiceId = "", IsActive = true, TimeStamp = [],
            });
            var products = new List<Product>() {Product};
            var productQuery = products.BuildMockDbSet();
            DbContext.Settings.Add(Setting = new Setting()
                {   Id = Guid.NewGuid(), FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                    AppLogsFileNameTemplate = "", Bankruptcy = "", FilenameAan = "", FilenameEConsent = "", LoanIdSource = "", Mla = "", MonitorAccount = "",
                    TilaCalculatorSource = "", TimeStamp = [], TimeZone = "", FilenameTila = "",
                    MilitaryAnnualPercentageRate = 0.123M,
                });
            DbContext.SaveChanges();
            DbContext.GlobalExclusionStates.Add(GlobalExclusionState = new GlobalExclusionState() {
                Id = Guid.NewGuid(), ProductId = Product.Id, State = "Test State1", TimeStamp = [],
            });
            DbContext.StateRestrictedInterestRates.Add(StateRestrictedInterestRate = new StateRestrictedInterestRate() {
                Id = Guid.NewGuid(), ProductId = Product.Id, State = "Test Rate1", DateCreatedUtc = DateTime.Now, DateUpdatedUtc = DateTime.Now, Rate = 0.123M, TimeStamp = [],
            });
            DbContext.SaveChanges();

            ProductQueries.GetProducts().Returns(productQuery);
            ManageRestrictionsPageService = fixture.Freeze<ManageRestrictionsPageService>();
        }

        public async Task<ManageRestrictionsPageDto?> GetAsync()
        {
            return await ManageRestrictionsPageService.GetAsync(true);
        }

        public async Task<GenericActionResult> SaveAsync(ManageRestrictionsPageDto dto)
        {
            EfPocoService
                .DeleteItemAsync<StateRestrictedInterestRate>(Arg.Is<DeleteItemDto>(a =>
                    a.DeleteItemId == StateRestrictedInterestRate.Id))
                .Returns(new GenericActionResult() { IsSuccessful = true, });

            EfPocoService.CreateOrUpdateAsync(Arg.Any<Guid>(), null, Arg.Any<byte[]>(),
                    Arg.Any<PerformCreateOrUpdateOptions<Setting>>())
                .Returns(Task.FromResult(new GetOrCreateRecordResult<Setting>
                {
                    IsSuccessful = true,
                    ErrorMessage = "",
                    Record = null,
                    FoundExistingRecord = false,
                    CreatingNewRecord = false,
                    EditingExistingRecord = false,
                }));

            return await ManageRestrictionsPageService.SaveAsync(dto);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task GetAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.GetAsync();
        Assert.NotNull(result);
        result.FiSlug.Should().Be(IFixtureExtensions.FiSlug);
        result.SettingId.Should().Be(rig.Setting.Id);
        result.IncludeInactiveProducts.Should().BeTrue();
        result.MilitaryAnnualPercentageRate.Should().Be(0.123M);

        result.Products.Count.Should().Be(1);
        var p = result.Products.Single();
        p.Name.Should().Be("Product1");
    }

    [Theory, AutoSubstituteData]
    public async Task SaveAsyncCheckSetting(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var dto = new ManageRestrictionsPageDto()
        {
            FiSlug = IFixtureExtensions.FiSlug,
            SettingId = Guid.NewGuid(),
            SettingTimeStamp = [1,2,3],
            IncludeInactiveProducts = true,
            MilitaryAnnualPercentageRate = 0.123M,
            ExcludedStates =
            [
                new ManageRestrictionStateDto() { Id = Guid.Empty, ProductId = rig.Product.Id, State = "CreateMe", CreateRequested = true, TimeStamp = [], },
                new ManageRestrictionStateDto() { Id = rig.GlobalExclusionState.Id, ProductId = rig.Product.Id, State = "DeleteMe", DeleteRequested = true, TimeStamp = [0, 1, 2], },
            ],
        };
        var result = await rig.SaveAsync(dto);
        Assert.NotNull(result);
        result.IsSuccessful.Should().BeTrue();

        await rig.EfPocoService.Received()
            .CreateOrUpdateAsync(dto.SettingId, null, dto.SettingTimeStamp, Arg.Any<PerformCreateOrUpdateOptions<Setting>>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveAsyncCheckGlobalExclusions(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var dto = new ManageRestrictionsPageDto()
        {
            FiSlug = IFixtureExtensions.FiSlug,
            SettingId = Guid.NewGuid(),
            SettingTimeStamp = [1,2,3],
            IncludeInactiveProducts = true,
            MilitaryAnnualPercentageRate = 0.123M,
            ExcludedStates =
            [
                new ManageRestrictionStateDto() { Id = Guid.Empty, ProductId = rig.Product.Id, State = "CreateMe", CreateRequested = true, TimeStamp = [], },
                new ManageRestrictionStateDto() { Id = rig.GlobalExclusionState.Id, ProductId = rig.Product.Id, State = "DeleteMe", DeleteRequested = true, TimeStamp = [0, 1, 2], },
            ],
        };
        var result = await rig.SaveAsync(dto);
        Assert.NotNull(result);
        result.IsSuccessful.Should().BeTrue();
        await rig.DbContext.SaveChangesAsync();

        var setting = rig.DbContext.Settings.Single();
        setting.MilitaryAnnualPercentageRate.Should().Be(0.123M);
        var globalExclusionStates = rig.DbContext.GlobalExclusionStates.ToList();
        globalExclusionStates.Count.Should().Be(1);
        // Verify a state exclusion was created
        var createdItem = globalExclusionStates.Single(a => a.State == "CreateMe");
        createdItem.TimeStamp.Should().BeEmpty();
        // Verify a state exclusion was deleted
        var deleteCheck = globalExclusionStates.FirstOrDefault(a => a.State == "DeleteMe");
        deleteCheck.Should().BeNull();
    }

    [Theory, AutoSubstituteData]
    public async Task SaveAsyncCheckRestrictedRates(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var dto = new ManageRestrictionsPageDto()
        {
            FiSlug = IFixtureExtensions.FiSlug,
            SettingId = Guid.NewGuid(),
            SettingTimeStamp = [1,2,3],
            IncludeInactiveProducts = true,
            MilitaryAnnualPercentageRate = 0.123M,
            RestrictedRates = [
                new ManageRestrictionRateDto() { Id = Guid.Empty, ProductId = rig.Product.Id, State = "CreateMe", Rate = 0.123M, TimeStamp = [], CreateRequested = true},
                new ManageRestrictionRateDto() { Id = rig.StateRestrictedInterestRate.Id, ProductId = rig.Product.Id, State = "DeleteMe", Rate = 0.123M, TimeStamp = [1,2,3], DeleteRequested = true,},
            ],
        };
        var result = await rig.SaveAsync(dto);
        Assert.NotNull(result);
        result.IsSuccessful.Should().BeTrue();
        await rig.DbContext.SaveChangesAsync();

        var restrictedRates = rig.DbContext.StateRestrictedInterestRates.ToList();
        restrictedRates.Count.Should().Be(1);
        // Verify a rate was created
        rig.EfPocoService.Received().AddRange(Arg.Any<List<StateRestrictedInterestRate>>());

        // Verify a rate was deleted
        await rig.EfPocoService.Received().DeleteItemAsync<StateRestrictedInterestRate>(
        Arg.Is<DeleteItemDto>(a =>
                a.DeleteItemId == rig.StateRestrictedInterestRate.Id
                && a.TimeStamp == rig.StateRestrictedInterestRate.TimeStamp
        ));
    }
}
