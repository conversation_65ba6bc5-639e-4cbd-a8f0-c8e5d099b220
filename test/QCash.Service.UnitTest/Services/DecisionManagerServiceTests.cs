using AutoFixture;
using Microsoft.Extensions.Logging;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Helpers;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services;
using FluentAssertions;
using Microsoft.Extensions.Caching.Distributed;
using QCash.Service.Services.General;
using Xunit;
using QCash.Service.Classes.DecisionManager;

namespace QCash.Service.UnitTest.Services;

public class DecisionManagerServiceTests
{
    private class DecisionManagerServiceTestRig
    {
        public QCashContext DbContext { get; set; }
        public IDecisionManagerService DecisionManagerService { get; set; }

        private FinancialInstitution FinancialInstitution { get; set; }
        private ILogger<MemberInterfaceHelper> Logger { get; set; }
        public IGuidExtensionService GuidExtensionService { get; set; }
        private IDecisionHelperService DecisionHelperService { get; set; }
        private IDistributedCache Cache { get; set; }
        public DecisionManagerServiceTestRig(IFixture fixture)
        {
            DbContext = fixture.Freeze<QCashContext>();

            FinancialInstitution = new FinancialInstitution
            {
                Id = IFixtureExtensions.FinancialInstitutionId,
                Slug = IFixtureExtensions.FiSlug,
                Address = "",
                City = "",
                ContactName = "",
                ContactPhone = "",
                GlLoanAccount = "",
                MailingAddress = "",
                Name = "Test1 Name",
                NoReplySubdomain = "TestSubd1",
                State = "",
                Zip = "12345",
                TimeStamp = [],
            };

            DbContext.FinancialInstitutions.Add(FinancialInstitution);

            //add product
            LoanType loanType1;
            DbContext.LoanTypes.Add(loanType1 = new LoanType()
            {
                Id = Guid.NewGuid(),
                Name = "LT1",
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Abrv = "",
                AppAbrv = "",
                TimeStamp = [],
            });
            DbContext.SaveChanges();

            Product product = new Product()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Slug = "ProductSlug1",
                Name = "Product1",
                Abrv = "",
                AppAbrv = "",
                InvoicePlanId = Guid.NewGuid(),
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                LoanTypeId = loanType1.Id,
                GlTranslationAccount = "",
                InvoiceId = "",
                IsActive = true,
                TimeStamp = [],
                LoanCategoryId = Guid.NewGuid(),
            };
            DbContext.Products.Add(product);
            DbContext.SaveChanges();

            DecisionModelType decisionModelType = new DecisionModelType()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "TR",
                Abrv = "TR",
                Slug = "T",
                Name = "traditional",
                Description = "Traditional model type",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                IsDeleted = false,
                TimeStamp = [],
            };
            DbContext.DecisionModelTypes.Add(decisionModelType);

            DecisionModelStatus decisionModelStatus = new DecisionModelStatus()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "INCT",
                Abrv = "INCT",
                Slug = "inactive",
                Name = "Inactive",
                TimeStamp = [],
            };
            DecisionModelStatus decisionModelStatus2 = new DecisionModelStatus()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "ACTV",
                Abrv = "ACTV",
                Slug = "active",
                Name = "Active",
                TimeStamp = [],
            };                
            DecisionModelStatus decisionModelStatus3 = new DecisionModelStatus()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "ARHV",
                Abrv = "ARHV",
                Slug = "archived",
                Name = "Archived",
                TimeStamp = [],
            };
            DbContext.DecisionModelStatuses.Add(decisionModelStatus);
            DbContext.DecisionModelStatuses.Add(decisionModelStatus2);
            DbContext.DecisionModelStatuses.Add(decisionModelStatus3);

            //add decisionmodel and productdecisionmodel
            DecisionModel decisionModel = new DecisionModel()
            {
                Id = Guid.NewGuid(),
                Description = "test",
                FiHandle = "testModel",
                StatusDate = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                DateCreatedUtc = DateTime.UtcNow,
                ModelStatus = decisionModelStatus2,
                ModelType = decisionModelType,
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                TimeStamp = [],
            };
            DbContext.DecisionModels.Add(decisionModel);
            DbContext.SaveChanges();

            ProductDecisionModel productDecisionModel = new ProductDecisionModel()
            {
                Id = Guid.NewGuid(),
                DateUpdatedUtc = DateTime.UtcNow,
                DateCreatedUtc = DateTime.UtcNow,
                Product = product,
                DecisionModel = decisionModel,
                TimeStamp = [],
            };
            DbContext.ProductDecisionModels.Add(productDecisionModel);
            DbContext.SaveChanges();

            ScoreType scoreType = new ScoreType()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                DateUpdatedUtc = DateTime.UtcNow,
                DateCreatedUtc = DateTime.UtcNow,
                AppAbrv = "TransformationScore",
                Abrv = "TransformationScore",
                Slug = "transformation_score",
                Name = "Transformation Score",
                Description = "Transformation Score Type",
                IsDeleted = false,
                TimeStamp = [],
            };
            DbContext.ScoreTypes.Add(scoreType);
            DbContext.SaveChanges();

            Logger = Substitute.For<ILogger<MemberInterfaceHelper>>();
            GuidExtensionService = new GuidExtensionService();
            DecisionHelperService = new DecisionHelperService(DbContext, GuidExtensionService);
            Cache = Substitute.For<IDistributedCache>();
            DecisionManagerService = new DecisionManagerService(DbContext, GuidExtensionService, Cache, DecisionHelperService);
        }

        public void CreateTransformationTestDepth(int depth, ModelTransformation inputTransformation)
        {
            //tuck under the first transform
            var lastPar = inputTransformation.InverseParent.First();

            for (int i = 0; i < depth; i++)
            {
                var qq = new ModelTransformation
                {
                    Id = Guid.NewGuid(), // GuidExtensionService.NewSequentialGuid(),
                    TimeStamp = [],
                    ValidationType = null,
                    Name = "Sub name " + i,
                    TransformationType = (int)TransformationType.Reason,
                    Parent = lastPar,
                };

                lastPar.InverseParent.Add(qq);
                lastPar = qq;
            }
        }

    }

    [Theory, AutoSubstituteData]
    public void CreateRootTransformationTest(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;
            
        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        //modeltransformationchild requires a timestamp, but this is db generated - how to do this in tests?
            
        Assert.NotNull(res);

        //since none existed prior to this call, the result should be a "clean" master transformation
        //one master, one eligibility, one migs, one decisionengine, one qualified amount
        rig.DbContext.ModelTransformations.Count().Should().Be(5);

        //then check to make sure the relationships are good
        rig.DbContext.ModelTransformations.Count(a => a.ParentId == null).Should().Be(1);

        //then start on other tests, add/remove, check, flatten, etc.
    }

    [Theory, AutoSubstituteData]
    public void CreateRootTransformationPlusOtherChildren(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        Assert.NotNull(res);

        rig.DbContext.ModelTransformations.Count().Should().Be(5);

        var decisionEngineModelTransformation =
            rig.DbContext.ModelTransformations.First(o => o.Id == res.DecisionEngine!.Id);
        rig.CreateTransformationTestDepth(6, decisionEngineModelTransformation);
        rig.DbContext.SaveChanges();

        rig.DbContext.ModelTransformations.Count().Should().Be(11);

        //then check to make sure the relationships are good
        rig.DbContext.ModelTransformations.Count(a => a.ParentId == null).Should().Be(1);
    }


    [Theory, AutoSubstituteData]
    public void AddChildTransformationsThenDelete(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        Assert.NotNull(res);

        //rig.DbContext.ModelTransformations.Count().Should().Be(5);

        var decisionEngineModelTransformation =
            rig.DbContext.ModelTransformations.First(o => o.Id == res.DecisionEngine!.Id);
        rig.CreateTransformationTestDepth(6, decisionEngineModelTransformation);
        rig.DbContext.SaveChanges();

        //rig.DbContext.ModelTransformations.Count().Should().Be(11);

        //then check to make sure the relationships are good
        //rig.DbContext.ModelTransformationChildren.Count().Should().Be(10);
        var sub2Transformation = rig.DbContext.ModelTransformations.First(o => o.Name == "Sub name 2").Id;
        var didDelete = rig.DecisionManagerService.DeleteTransformation(sub2Transformation, modelId);

        didDelete.Should().BeTrue();

        rig.DbContext.ModelTransformations.Count().Should().Be(7);
        rig.DbContext.ModelTransformations.Count(a => a.ParentId == null).Should().Be(1);

    }


    [Theory, AutoSubstituteData]
    public void AddChildTransformationsThenDeleteInvalid(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        Assert.NotNull(res);

        var decisionEngineModelTransformation =
            rig.DbContext.ModelTransformations.First(o => o.Id == res.DecisionEngine!.Id);
        rig.CreateTransformationTestDepth(6, decisionEngineModelTransformation);
        rig.DbContext.SaveChanges();
            
        var tranCountBefore = rig.DbContext.ModelTransformations.Count();

        //shouldn't be allowed to delete the top-level Decision Model node
        var didDelete = rig.DecisionManagerService.DeleteTransformation(decisionEngineModelTransformation.Id, modelId);
        didDelete.Should().BeFalse();

        var tranCountAfter = rig.DbContext.ModelTransformations.Count();

        tranCountBefore.Equals(tranCountAfter).Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public void TransformationValidate(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        var eligTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "Eligibility");
        var migsTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "MemberInGoodStanding");
        var deTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "DecisionEngine");
        var qaTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "QualifiedAmount");

        //note: with transformation formulas being written in like this, only "base" values are allowed to be used
        //without needing a transformation underneath it to provide the actual value
        //Local, Product, Model, NSF, LoanLimit
        //MW.XXX are Param type

        //this one would need ones underneath it to resolve HasReachedOpenLoansMaximumNumber, etc.
        //eligTransformation.Formula = "HasReachedOpenLoansMaximumNumber = 0 & HasReachedOpenLoansAmount = 0 & HasReachedNumberOfLoansHistoryLimit = 0 & MustMeetLengthOfRelationship = 1";
        eligTransformation.Formula = "Local.XYZ = 33";
        //this one would need ones underneath it to resolve MustHaveSSN, etc.
        //migsTransformation.Formula = "MustHaveSSN = 1 & MustBeaPrimaryAccountHolder = 1 & MustBeEighteenYearsOrGreater = 1 & MustNotHaveExclusionLoans = 1 & MustNotHavePastDueLoans = 1 & MustNotHaveChargeOffAccountWithBalance = 1 & MustHaveOpenShareAccount = 1 & MustHaveCUAccountThatIsNotAnExclusionAccType = 1 & MustNotHaveAccountWarningCodes = 1 & MustNotHaveMemberWarningCodes = 1 & MustNotHaveExclusionLoansByPurpose > 0";
        migsTransformation.Formula = "Model.XYZ & LoanLimit.XYZ = 296";

        //this one is OK because we do have the QualifiedAmount transformation underneath it
        deTransformation.Formula = "Product.MinimumLoanAmount = Min(QualifiedAmount, Product.MinimumLoanAmount)";
        qaTransformation.Formula = "Round(Max(Product.ImaginaryAmount - Product.LoanBalance, 0), 0)";

        rig.DbContext.SaveChanges();

        Assert.NotNull(res);

        bool isvalid = rig.DecisionManagerService.ValidateTransformation(Guid.Empty, modelId, out string message);

        isvalid.Should().BeTrue();
        message.Should().BeNullOrEmpty();
    }


    [Theory, AutoSubstituteData]
    public void UpdateTransformation(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        var eligTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "Eligibility");
        var migsTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "MemberInGoodStanding");
        var deTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "DecisionEngine");
        var qaTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "QualifiedAmount");
            
        //these are valid
        eligTransformation.Formula = "Local.XYZ = 33";
        migsTransformation.Formula = "Model.XYZ & LoanLimit.XYZ = 296";
        deTransformation.Formula = "Product.MinimumLoanAmount = Min(QualifiedAmount, Product.MinimumLoanAmount)";
        qaTransformation.Formula = "Round(Max(Product.ImaginaryAmount - Product.LoanBalance, 0), 0)";

        rig.DbContext.SaveChanges();

        var transformation = rig.DecisionManagerService.GetTransformation(modelId, qaTransformation.Id);
        Assert.NotNull(transformation);
        transformation.Formula = "1 + 1 = 2";
        bool isvalid = rig.DecisionManagerService.UpdateTransformation(transformation, modelId);
        isvalid.Should().BeTrue();

        var modelTransformation = rig.DecisionManagerService.GetModelTransformation(qaTransformation.Id);
        Assert.NotNull(modelTransformation);
        modelTransformation.Formula.Should().Be("1 + 1 = 2");
    }


    [Theory, AutoSubstituteData]
    public void UpdateTransformationInvalidName(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        var eligTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "Eligibility");
        var migsTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "MemberInGoodStanding");
        var deTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "DecisionEngine");
        var qaTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "QualifiedAmount");

        //these are valid
        eligTransformation.Formula = "Local.XYZ = 33";
        migsTransformation.Formula = "Model.XYZ & LoanLimit.XYZ = 296";
        deTransformation.Formula = "Product.MinimumLoanAmount = Min(QualifiedAmount, Product.MinimumLoanAmount)";
        qaTransformation.Formula = "Round(Max(Product.ImaginaryAmount - Product.LoanBalance, 0), 0)";

        rig.DbContext.SaveChanges();

        var decisionEngineModelTransformation =
            rig.DbContext.ModelTransformations.First(o => o.Id == res.DecisionEngine!.Id);
        rig.CreateTransformationTestDepth(1, decisionEngineModelTransformation);
        rig.DbContext.SaveChanges();

        var rs0 = rig.DbContext.ModelTransformations.First(o => o.Name == "Sub name 0");

        var transformation = rig.DecisionManagerService.GetTransformation(modelId, rs0.Id);
        Assert.NotNull(transformation);
        transformation.Name = "@#$@%#^$&^%&^";

        var updated = rig.DecisionManagerService.UpdateTransformation(transformation, modelId);
        updated.Should().BeFalse();
 
        var modelTransformation = rig.DecisionManagerService.GetModelTransformation(qaTransformation.Id);
        Assert.NotNull(modelTransformation);
        modelTransformation.Name.Should().Be("QualifiedAmount");
    }


    [Theory, AutoSubstituteData]
    public void InsertTransformation(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        var eligTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "Eligibility");
        var migsTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "MemberInGoodStanding");
        var deTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "DecisionEngine");
        var qaTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "QualifiedAmount");

        //these are valid
        eligTransformation.Formula = "Local.XYZ = 33";
        migsTransformation.Formula = "Model.XYZ & LoanLimit.XYZ = 296";
        deTransformation.Formula = "Product.MinimumLoanAmount = Min(QualifiedAmount, Product.MinimumLoanAmount)";
        qaTransformation.Formula = "Round(Max(Product.ImaginaryAmount - Product.LoanBalance, 0), 0)";

        rig.DbContext.SaveChanges();

        Transformation newtransformation = new Transformation(rig.GuidExtensionService)
        {
            Id = Guid.NewGuid(),
            ValidationType = null,
            Name = "New name",
            TransformationType = TransformationType.Formula,
        };

        bool inserted = rig.DecisionManagerService.InsertTransformation(newtransformation, modelId, qaTransformation.Id);

        inserted.Should().BeTrue();

        var modelTransformation = rig.DecisionManagerService.GetModelTransformation(newtransformation.Id);
        Assert.NotNull(modelTransformation);
        modelTransformation.Name.Should().Be("New name");
    }

    [Theory, AutoSubstituteData]
    public void TestValidateBothValidInvalid(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        var eligTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "Eligibility");
        var migsTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "MemberInGoodStanding");
        var deTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "DecisionEngine");
        var qaTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "QualifiedAmount");

        //these would all be valid
        eligTransformation.Formula = "Local.XYZ = 33"; 
        migsTransformation.Formula = "Model.XYZ & LoanLimit.XYZ = 296";
        deTransformation.Formula = "Product.MinimumLoanAmount = Min(QualifiedAmount, Product.MinimumLoanAmount)";
        qaTransformation.Formula = "Round(Max(Product.ImaginaryAmount - Product.LoanBalance, 0), 0)";

        rig.DbContext.SaveChanges();
        Assert.NotNull(res);
        bool isvalidValid = rig.DecisionManagerService.ValidateTransformation(Guid.Empty, modelId, out string messageValid);
        isvalidValid.Should().BeTrue();
        messageValid.Should().BeNullOrEmpty();

        //this one would need ones underneath it to resolve HasReachedOpenLoansMaximumNumber, etc.
        eligTransformation.Formula = "HasReachedOpenLoansMaximumNumber = 0 & HasReachedOpenLoansAmount = 0 & HasReachedNumberOfLoansHistoryLimit = 0 & MustMeetLengthOfRelationship = 1";
        rig.DbContext.SaveChanges();
        Assert.NotNull(res);
        bool isvalid = rig.DecisionManagerService.ValidateTransformation(Guid.Empty, modelId, out string message);

        isvalid.Should().BeFalse();
        message.Should().NotBeNullOrEmpty();

    }

    [Theory, AutoSubstituteData]
    public void TransformationWithInvalidFormulasThenValidate(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        var eligTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "Eligibility");
        var migsTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "MemberInGoodStanding");
        var deTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "DecisionEngine");
        var qaTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "QualifiedAmount");

        //this one would need ones underneath it to resolve HasReachedOpenLoansMaximumNumber, etc.
        eligTransformation.Formula = "HasReachedOpenLoansMaximumNumber = 0 & HasReachedOpenLoansAmount = 0 & HasReachedNumberOfLoansHistoryLimit = 0 & MustMeetLengthOfRelationship = 1";
        //this one would need ones underneath it to resolve MustHaveSSN, etc.
        migsTransformation.Formula = "MustHaveSSN = 1 & MustBeaPrimaryAccountHolder = 1 & MustBeEighteenYearsOrGreater = 1 & MustNotHaveExclusionLoans = 1 & MustNotHavePastDueLoans = 1 & MustNotHaveChargeOffAccountWithBalance = 1 & MustHaveOpenShareAccount = 1 & MustHaveCUAccountThatIsNotAnExclusionAccType = 1 & MustNotHaveAccountWarningCodes = 1 & MustNotHaveMemberWarningCodes = 1 & MustNotHaveExclusionLoansByPurpose > 0";

        //this one is OK because we do have the QualifiedAmount transformation underneath it
        deTransformation.Formula = "Product.MinimumLoanAmount = Min(QualifiedAmount, Product.MinimumLoanAmount)";
        qaTransformation.Formula = "Round(Max(Product.ImaginaryAmount - Product.LoanBalance, 0), 0)";

        rig.DbContext.SaveChanges();

        Assert.NotNull(res);

        bool isvalid = rig.DecisionManagerService.ValidateTransformation(Guid.Empty, modelId, out string message);

        isvalid.Should().BeFalse();
        message.Should().NotBeNullOrEmpty();
    }

    [Theory, AutoSubstituteData]
    public void DeleteEntireRootTransformation(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        Assert.NotNull(res);

        rig.DbContext.ModelTransformations.Count().Should().Be(5);

        var decisionEngineModelTransformation =
            rig.DbContext.ModelTransformations.First(o => o.Id == res.DecisionEngine!.Id);
        rig.CreateTransformationTestDepth(6, decisionEngineModelTransformation);
        rig.DbContext.SaveChanges();

        var rTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "Root Transformation").Id;
        var didDelete = rig.DecisionManagerService.DeleteTransformation(rTransformation, modelId);

        didDelete.Should().BeTrue();

        rig.DbContext.ModelTransformations.Count().Should().Be(0);
    }

    [Theory, AutoSubstituteData]
    public void InsertCopiedDecisionModel(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        var eligTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "Eligibility");
        var migsTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "MemberInGoodStanding");
        var deTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "DecisionEngine");
        var qaTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "QualifiedAmount");

        //these are valid
        eligTransformation.Formula = "Local.XYZ = 33";
        migsTransformation.Formula = "Model.XYZ & LoanLimit.XYZ = 296";
        deTransformation.Formula = "Product.MinimumLoanAmount = Min(QualifiedAmount, Product.MinimumLoanAmount)";
        qaTransformation.Formula = "Round(Max(Product.ImaginaryAmount - Product.LoanBalance, 0), 0)";

        rig.DbContext.SaveChanges();

        Transformation newtransformation = new Transformation(rig.GuidExtensionService)
        {
            Id = Guid.NewGuid(),
            ValidationType = null,
            Name = "Happy",
            TransformationType = TransformationType.Formula,
            Formula = "Product.HappyFace + 1 = 3",
        };

        rig.DecisionManagerService.InsertTransformation(newtransformation, modelId, qaTransformation.Id);

        Transformation newtransformation2 = new Transformation(rig.GuidExtensionService)
        {
            Id = Guid.NewGuid(),
            ValidationType = null,
            Name = "Happy_AANReason",
            TransformationType = TransformationType.Reason,
            Formula = "Happy = 3",
        };

        rig.DecisionManagerService.InsertTransformation(newtransformation2, modelId, newtransformation.Id);

        //also include some scores so that we can verify that MapModelScoreToDomain works properly as well

        var scoreType = rig.DbContext.ScoreTypes.First(o => o.Abrv == "TransformationScore");

        Data.Models.Score score = new Data.Models.Score()
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            ScoreType = scoreType,
            Description = "test score",
            From = "0",
            To = 100,
            Value = 55,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        ModelTransformationScore modelTransformationScore = new ModelTransformationScore()
        {
            Id = Guid.NewGuid(),
            ModelTransformationId = newtransformation.Id,
            Score = score,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        rig.DecisionManagerService.InsertModelTransformationScore(modelTransformationScore);

        rig.DbContext.ModelTransformationScores.Count().Should().Be(1);

        Data.Models.Score score2 = new Data.Models.Score()
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            ScoreType = scoreType,
            Description = "test score",
            From = "101",
            To = 200,
            Value = 65,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        ModelTransformationScore modelTransformationScore2 = new ModelTransformationScore()
        {
            Id = Guid.NewGuid(),
            ModelTransformationId = newtransformation.Id,
            Score = score2,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        rig.DecisionManagerService.InsertModelTransformationScore(modelTransformationScore2);

        rig.DbContext.ModelTransformationScores.Count().Should().Be(2);

        //end insert scores

        res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);
        var decisionModel = rig.DecisionManagerService.GetDecisionModelWithProduct(modelId);
        //will be like this when ProductDecisionModel is removed:
        //var product = decisionModel.Product;
        var product = decisionModel.ProductDecisionModels.First().Product;

        //our new DecisionModel to insert as a copy
        DecisionModel newModel = new DecisionModel()
        {
            Id = Guid.NewGuid(),
            Description = decisionModel.Description,
            FiHandle = decisionModel.FiHandle + "_2",
            StatusDate = DateTime.UtcNow,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            ModelStatus = decisionModel.ModelStatus,
            ModelType = decisionModel.ModelType,
            FinancialInstitutionId = decisionModel.FinancialInstitutionId,
            TimeStamp = [],
        };

        //now copy the whole thing and verify
        var insertedNew = rig.DecisionManagerService.InsertCopiedDecisionModel(newModel, res.Id, product.Id);
        insertedNew.Should().BeTrue();

        //verify that we have the same transformations but with different IDs now in the system
        //can compare flattens, or just look for Happy #1 and #2, etc
        var res1 = rig.DecisionManagerService.GetModelTransformationMaster(modelId);
        var res2 = rig.DecisionManagerService.GetModelTransformationMaster(newModel.Id);

        var flatres1 = res1.GetFlattenTransformations().ToList();
        var flatres2 = res2.GetFlattenTransformations().ToList();
        flatres1.Count().Should().Be(flatres2.Count());
        foreach (var item in flatres1)
        {
            flatres2.Count(o => o.Name == item.Name && o.Id != item.Id).Should().Be(1);
            rig.DbContext.ModelTransformations.Count(o => o.Name == item.Name).Should().Be(2);
        }

        //check the scores
        rig.DbContext.ModelTransformationScores.Count().Should().Be(4);

    }


    [Theory, AutoSubstituteData]
    public void GetParamsFromTransformation(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        var eligTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "Eligibility");
        var migsTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "MemberInGoodStanding");
        var deTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "DecisionEngine");
        var qaTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "QualifiedAmount");

        //these are valid
        eligTransformation.Formula = "Local.XYZ = 33";
        migsTransformation.Formula = "Model.XYZ & LoanLimit.XYZ = 296";
        deTransformation.Formula = "Product.MinimumLoanAmount = Min(QualifiedAmount, Product.MinimumLoanAmount)";
        qaTransformation.Formula = "Round(Max(Product.ImaginaryAmount - Product.LoanBalance, 0), 0)";

        rig.DbContext.SaveChanges();

        Transformation newtransformation = new Transformation(rig.GuidExtensionService)
        {
            Id = Guid.NewGuid(),
            ValidationType = null,
            Name = "Happy",
            TransformationType = TransformationType.Formula,
            Formula = "Product.HappyFace + 1 = 3",
        };

        bool inserted = rig.DecisionManagerService.InsertTransformation(newtransformation, modelId, qaTransformation.Id);
        inserted.Should().BeTrue();

        Transformation newtransformation2 = new Transformation(rig.GuidExtensionService)
        {
            Id = Guid.NewGuid(),
            ValidationType = null,
            Name = "Happy_AANReason",
            TransformationType = TransformationType.Reason,
            Formula = "Happy = 3",
        };

        bool inserted2 = rig.DecisionManagerService.InsertTransformation(newtransformation2, modelId, newtransformation.Id);
        inserted2.Should().BeTrue();

        //to get one with the .Transformations collections intact, better to call GetTransformations now
        newtransformation = rig.DecisionManagerService.GetTransformation(modelId, newtransformation.Id)!;
        newtransformation2 = rig.DecisionManagerService.GetTransformation(modelId, newtransformation2.Id)!;

        //looking at GetMissingTransformationParams

        //because this Reason Transformation is a sibling of the one that actually produces its value, GetMissing
        //gives that value as 'missing'
        res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);
        var parms = rig.DecisionManagerService.GetMissingTransformationParams(res, newtransformation2);
        parms.Count.Should().Be(1);
        parms.Keys.First().Should().Be("Happy");

        //by comparison, moving up one level and calling the method again produces no missing params
        var parms2 = rig.DecisionManagerService.GetMissingTransformationParams(res, newtransformation);
        parms2.Count.Should().Be(0);

        //now looking at GetParamsFromTransformation
        var parms23 = rig.DecisionManagerService.GetParamsFromTransformation(res, newtransformation);
        parms23.Count.Should().Be(1);
        var parms13 = rig.DecisionManagerService.GetParamsFromTransformation(res, newtransformation2);
        parms13.Count.Should().Be(0);

        //For more meaningful results out of the GetParamsFromTransformation call, we'll move up to the QualifiedAmount node
        //This should return all the parameters needed for this transformation to produce an output value
        var qaTrans = rig.DecisionManagerService.GetTransformation(modelId, qaTransformation.Id)!;
        var parms3 = rig.DecisionManagerService.GetParamsFromTransformation(res, qaTrans);

        parms3.Count.Should().Be(3);
        parms3.Keys.Should().Contain("Product.HappyFace");
        parms3.Keys.Should().Contain("Product.ImaginaryAmount");
        parms3.Keys.Should().Contain("Product.LoanBalance");
    }

    [Theory, AutoSubstituteData]
    public void GetTransformationRecursiveAndFlatten(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        Assert.NotNull(res);

        rig.DbContext.ModelTransformations.Count().Should().Be(5);

        var decisionEngineModelTransformation =
            rig.DbContext.ModelTransformations.First(o => o.Id == res.DecisionEngine!.Id);
        rig.CreateTransformationTestDepth(6, decisionEngineModelTransformation);
        rig.DbContext.SaveChanges();

        var rTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "Root Transformation").Id;
            
        var rec = rig.DecisionManagerService.GetTransformationRecursive(rTransformation);

        var sub5 = rec.Transformations.First(o => o.Name == "DecisionEngine").Transformations
            .First(o => o.Name == "QualifiedAmount").Transformations.First(o => o.Name == "Sub name 0")
            .Transformations.First(o => o.Name == "Sub name 1").Transformations.First(o => o.Name == "Sub name 2")
            .Transformations.First(o => o.Name == "Sub name 3").Transformations.First(o => o.Name == "Sub name 4")
            .Transformations.First(o => o.Name == "Sub name 5");

        Assert.NotNull(sub5);
        sub5.Transformations.Count.Should().Be(0);

        var flat = rec.GetFlattenTransformations();
        flat.Count().Should().Be(11);
    }

    [Theory, AutoSubstituteData]
    public void InsertModelTransformationScore(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        Assert.NotNull(res);

        var decisionEngineModelTransformation =
            rig.DbContext.ModelTransformations.First(o => o.Id == res.DecisionEngine!.Id);
        rig.CreateTransformationTestDepth(6, decisionEngineModelTransformation);
        rig.DbContext.SaveChanges();

        var rs0 = rig.DbContext.ModelTransformations.First(o => o.Name == "Sub name 0");
        rs0.TransformationType = (int)TransformationType.Score;
        rs0.Formula = "MW.LengthOfRelationship";

        var rs1 = rig.DbContext.ModelTransformations.First(o => o.Name == "Sub name 1");
        rs1.TransformationType = (int)TransformationType.Param;
        rs1.Name = "MW.LengthOfRelationship";
        rig.DbContext.SaveChanges();

        var scoreType = rig.DbContext.ScoreTypes.First(o => o.Abrv == "TransformationScore");

        Data.Models.Score score = new Data.Models.Score()
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            ScoreType = scoreType,
            Description = "test score",
            From = "0",
            To = 100,
            Value = 55,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };
            
        ModelTransformationScore modelTransformationScore = new ModelTransformationScore()
        {
            Id = Guid.NewGuid(),
            ModelTransformation = rs0,
            Score = score,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        rig.DecisionManagerService.InsertModelTransformationScore(modelTransformationScore);

        rig.DbContext.ModelTransformationScores.Count().Should().Be(1);

        var mtScore = rig.DecisionManagerService.GetModelTransformationScore(rs0.Id, score.Id);
        Assert.NotNull(mtScore);
        mtScore.Id.Should().Be(modelTransformationScore.Id);

        Data.Models.Score score2 = new Data.Models.Score()
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            ScoreType = scoreType,
            Description = "test score",
            From = "101",
            To = 200,
            Value = 65,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        ModelTransformationScore modelTransformationScore2 = new ModelTransformationScore()
        {
            Id = Guid.NewGuid(),
            ModelTransformation = rs0,
            ModelTransformationId = rs0.Id,
            Score = score2,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        rig.DecisionManagerService.InsertModelTransformationScore(modelTransformationScore2);

        var mtScores = rig.DecisionManagerService.GetModelTransformationScores(rs0.Id);
        Assert.NotNull(mtScores);
        mtScores.Count().Should().Be(2);

        var mScores = rig.DecisionManagerService.GetScoresForModelTransformation(rs0.Id);
        Assert.NotNull(mScores);
        mScores.Count().Should().Be(2);
    }


    [Theory, AutoSubstituteData]
    public void ValidateTransformationScore(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        Assert.NotNull(res);

        var decisionEngineModelTransformation =
            rig.DbContext.ModelTransformations.First(o => o.Id == res.DecisionEngine!.Id);
        rig.CreateTransformationTestDepth(6, decisionEngineModelTransformation);
        rig.DbContext.SaveChanges();

        var rs0 = rig.DbContext.ModelTransformations.First(o => o.Name == "Sub name 0");
        rs0.TransformationType = (int)TransformationType.Score;
        rs0.Formula = "MW.LengthOfRelationship";

        var rs1 = rig.DbContext.ModelTransformations.First(o => o.Name == "Sub name 1");
        rs1.TransformationType = (int)TransformationType.Param;
        rs1.Name = "MW.LengthOfRelationship";
        rig.DbContext.SaveChanges();

        var scoreType = rig.DbContext.ScoreTypes.First(o => o.Abrv == "TransformationScore");

        Data.Models.Score score = new Data.Models.Score()
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            ScoreType = scoreType,
            Description = "test score",
            From = "0",
            To = 100,
            Value = 55,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        ModelTransformationScore modelTransformationScore = new ModelTransformationScore()
        {
            Id = Guid.NewGuid(),
            ModelTransformation = rs0,
            Score = score,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        rig.DecisionManagerService.InsertModelTransformationScore(modelTransformationScore);

        Data.Models.Score score2 = new Data.Models.Score()
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            ScoreType = scoreType,
            Description = "test score",
            From = "100",
            To = 200,
            Value = 65,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        ModelTransformationScore modelTransformationScore2 = new ModelTransformationScore()
        {
            Id = Guid.NewGuid(),
            ModelTransformation = rs0,
            ModelTransformationId = rs0.Id,
            Score = score2,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        //the value overlaps the existing score, so this should fail when calling ValidateScorevalues
        Assert.Throws<Exception>(() =>
        {
            rig.DecisionManagerService.InsertModelTransformationScore(modelTransformationScore2);
        });
            
        var mtScores = rig.DecisionManagerService.GetModelTransformationScores(rs0.Id);
        Assert.NotNull(mtScores);
        mtScores.Count().Should().Be(1);

        var mScores = rig.DecisionManagerService.GetScoresForModelTransformation(rs0.Id);
        Assert.NotNull(mScores);
        mScores.Count().Should().Be(1);
    }


    [Theory, AutoSubstituteData]
    public void UpdateModelTransformationScore(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        Assert.NotNull(res);

        var decisionEngineModelTransformation =
            rig.DbContext.ModelTransformations.First(o => o.Id == res.DecisionEngine!.Id);
        rig.CreateTransformationTestDepth(6, decisionEngineModelTransformation);
        rig.DbContext.SaveChanges();

        var rs0 = rig.DbContext.ModelTransformations.First(o => o.Name == "Sub name 0");
        rs0.TransformationType = (int)TransformationType.Score;
        rs0.Formula = "MW.LengthOfRelationship";

        var rs1 = rig.DbContext.ModelTransformations.First(o => o.Name == "Sub name 1");
        rs1.TransformationType = (int)TransformationType.Param;
        rs1.Name = "MW.LengthOfRelationship";
        rig.DbContext.SaveChanges();

        var scoreType = rig.DbContext.ScoreTypes.First(o => o.Abrv == "TransformationScore");

        Data.Models.Score score = new Data.Models.Score()
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            ScoreType = scoreType,
            Description = "test score",
            From = "0",
            To = 100,
            Value = 55,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        ModelTransformationScore modelTransformationScore = new ModelTransformationScore()
        {
            Id = Guid.NewGuid(),
            ModelTransformation = rs0,
            Score = score,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        rig.DecisionManagerService.InsertModelTransformationScore(modelTransformationScore);

        rig.DbContext.ModelTransformationScores.Count().Should().Be(1);

        var mtScore = rig.DecisionManagerService.GetModelTransformationScore(rs0.Id, score.Id);
        Assert.NotNull(mtScore);
        mtScore.Id.Should().Be(modelTransformationScore.Id);

        Data.Models.Score score2 = new Data.Models.Score()
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            ScoreType = scoreType,
            Description = "test score",
            From = "101",
            To = 200,
            Value = 65,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        ModelTransformationScore modelTransformationScore2 = new ModelTransformationScore()
        {
            Id = Guid.NewGuid(),
            ModelTransformation = rs0,
            ModelTransformationId = rs0.Id,
            Score = score2,
            DateUpdatedUtc = DateTime.UtcNow,
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };

        rig.DecisionManagerService.InsertModelTransformationScore(modelTransformationScore2);

        var mtScores = rig.DecisionManagerService.GetModelTransformationScores(rs0.Id);
        Assert.NotNull(mtScores);
        mtScores.Count().Should().Be(2);

        var mScores = rig.DecisionManagerService.GetScoresForModelTransformation(rs0.Id);
        Assert.NotNull(mScores);
        mScores.Count().Should().Be(2);

        score2.From = "102";
        var success = rig.DecisionManagerService.UpdateTransformationScore(modelTransformationScore2);
        success.Should().Be(true);

        score2.From = "100";
        //the value overlaps the existing score, so this should fail when calling ValidateScorevalues
        Assert.Throws<Exception>(() =>
        {
            rig.DecisionManagerService.InsertModelTransformationScore(modelTransformationScore2);
        });

    }

    [Theory, AutoSubstituteData]
    public void SetModelInactive(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        Assert.NotNull(res);

        //also need to set the product to inactive and/or remove mapping from product, otherwise should fail
        var decisionModel = rig.DecisionManagerService.GetDecisionModelWithProduct(modelId);

        //will be like this when ProductDecisionModel is removed
        //var product = decisionModel.Product;
        var product = decisionModel.ProductDecisionModels.First().Product;
        product.DefaultModel = decisionModel;
        product.IsActive = false;

        var dmsActv = rig.DbContext.DecisionModelStatuses.First(o => o.Abrv == "ACTV");
        decisionModel.ModelStatus = dmsActv;
        rig.DbContext.SaveChanges();
            
        rig.DecisionManagerService.SetModelInactive(modelId);
        decisionModel.ModelStatus.Abrv.Should().Be("INCT");
    }

    [Theory, AutoSubstituteData]
    public void SetModelInactiveInvalid(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        Assert.NotNull(res);

        var decisionModel = rig.DecisionManagerService.GetDecisionModelWithProduct(modelId);

        //will be like this when ProductDecisionModel is removed
        //var product = decisionModel.Product;
        var product = decisionModel.ProductDecisionModels.First().Product;
        product.DefaultModel = decisionModel;
        product.IsActive = true;    //should cause SetModelInactive to fail

        var dmsActv = rig.DbContext.DecisionModelStatuses.First(o => o.Abrv == "ACTV");
        decisionModel.ModelStatus = dmsActv;
        rig.DbContext.SaveChanges();

        Assert.Throws<Exception>(() =>
        {
            rig.DecisionManagerService.SetModelInactive(modelId);
        });
            
        decisionModel.ModelStatus.Abrv.Should().Be("ACTV");
    }

    [Theory, AutoSubstituteData]
    public void SetModelArchived(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        Assert.NotNull(res);

        //also need to set the product to inactive and/or remove mapping from product, otherwise should fail
        var decisionModel = rig.DecisionManagerService.GetDecisionModelWithProduct(modelId);

        //will be like this when ProductDecisionModel is removed
        //var product = decisionModel.Product;
        var product = decisionModel.ProductDecisionModels.First().Product;

        product.IsActive = false;

        var dmsActv = rig.DbContext.DecisionModelStatuses.First(o => o.Abrv == "ACTV");
        decisionModel.ModelStatus = dmsActv;
        rig.DbContext.SaveChanges();

        decisionModel.ModelStatus.Abrv.Should().Be("ACTV");

        rig.DecisionManagerService.SetArchivedByProduct(product.Id);

        decisionModel.ModelStatus.Abrv.Should().Be("ARHV");
    }

    [Theory, AutoSubstituteData]
    public void SetModelArchivedInvalid(IFixture fixture)
    {
        var rig = new DecisionManagerServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        Assert.NotNull(res);

        //also need to set the product to inactive and/or remove mapping from product, otherwise should fail
        var decisionModel = rig.DecisionManagerService.GetDecisionModelWithProduct(modelId);
            
        //by not setting this, it should throw an exception
        //decisionModel.Products.First().IsActive = false;

        var dmsActv = rig.DbContext.DecisionModelStatuses.First(o => o.Abrv == "ACTV");
        decisionModel.ModelStatus = dmsActv;
        rig.DbContext.SaveChanges();

        decisionModel.ModelStatus.Abrv.Should().Be("ACTV");

        //will be like this when ProductDecisionModel is removed
        //var product = decisionModel.Product;
        var product = decisionModel.ProductDecisionModels.First().Product;

        Assert.Throws<Exception>(() =>
        {
            rig.DecisionManagerService.SetArchivedByProduct(product.Id);
        });

        decisionModel.ModelStatus.Abrv.Should().Be("ACTV");
    }



}