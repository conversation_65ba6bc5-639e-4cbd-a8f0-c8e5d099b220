using EntityFrameworkCore.Testing.NSubstitute;
using FluentAssertions;
using QCash.Data.Context;
using QCash.Service.Models;
using QCash.Service.Services.User;
using Xunit;
using static QCash.Service.Models.FIConfiguration.UserManagement.Constants;

namespace QCash.Service.UnitTest.Services.Pages.FIConfiguration.UserManagement;

public class RoleServiceTests
{
    private class RoleServiceTestRig
    {
        private QCashContext Context { get; set; } = Create.MockedDbContextFor<QCashContext>();
        private RoleService RoleService { get; set; }

        public RoleServiceTestRig() => RoleService = new RoleService(Context);

        public bool IsSuperUser(List<QListItem<Guid>> roles) =>
             RoleService.IsSuperUser(roles);

        public bool IsFIManager(List<QListItem<Guid>> roles) =>
            RoleService.IsFIManager(roles);

        public bool IsDeManager(List<QListItem<Guid>> roles) =>
            RoleService.IsDeManager(roles);

        public bool IsSystemAdmin(List<QListItem<Guid>> roles) =>
            RoleService.IsSystemAdmin(roles);

        public bool CanEditOwnRoles(string editedUserName, string loggedInUserName, string loggedInUserRoles) =>
            RoleService.CanEditOwnRoles(editedUserName, loggedInUserName, loggedInUserRoles);
    }

    [Fact]
    public void IsSuperUserAsyncPositive()
    {
        var rig = new RoleServiceTestRig();
        var result = rig.IsSuperUser([
            new QListItem<Guid>() { Text = SuperUserAbrv, Value = Guid.NewGuid(), },
        ]);
        result.Should().BeTrue();
    }

    [Fact]
    public void IsSuperUserAsyncNegative()
    {
        var rig = new RoleServiceTestRig();
        var result = rig.IsSuperUser([
            new QListItem<Guid>() { Text = "nobody", Value = Guid.NewGuid(), },
        ]);
        result.Should().BeFalse();
    }

    [Fact]
    public void IsFIManagerAsyncPositive()
    {
        var rig = new RoleServiceTestRig();
        var result = rig.IsFIManager([
            new QListItem<Guid>() { Text = FiManagerAbrv, Value = Guid.NewGuid(), },
        ]);
        result.Should().BeTrue();
    }

    [Fact]
    public void IsFIManagerAsyncNegative()
    {
        var rig = new RoleServiceTestRig();
        var result = rig.IsFIManager([
            new QListItem<Guid>() { Text = "NotFIManager", Value = Guid.NewGuid(), },
        ]);
        result.Should().BeFalse();
    }

    [Fact]
    public void IsDeManagerAsyncPositive()
    {
        var rig = new RoleServiceTestRig();
        var result = rig.IsDeManager([
            new QListItem<Guid>() { Text = DeManagerAbrv, Value = Guid.NewGuid(), },
        ]);
        result.Should().BeTrue();
    }

    [Fact]
    public void IsDeManagerAsyncNegative()
    {
        var rig = new RoleServiceTestRig();
        var result = rig.IsDeManager([
            new QListItem<Guid>() { Text = "imposter", Value = Guid.NewGuid(), },
        ]);
        result.Should().BeFalse();
    }

    [Fact]
    public void IsSystemAdminAsyncPositive()
    {
        var rig = new RoleServiceTestRig();
        var result = rig.IsSystemAdmin([
            new QListItem<Guid>() { Text = SystemAdminAbrv, Value = Guid.NewGuid(), },
        ]);
        result.Should().BeTrue();
    }

    [Fact]
    public void IsSystemAdminAsyncNegative()
    {
        var rig = new RoleServiceTestRig();
        var result = rig.IsSystemAdmin([
            new QListItem<Guid>() { Text = "WRONGVALUE", Value = Guid.NewGuid(), },
        ]);
        result.Should().BeFalse();
    }

    [Fact]
    public void CanEditOwnRolesNotOwnUser()
    {
        var rig = new RoleServiceTestRig();
        var result = rig.CanEditOwnRoles("user1","NOTUSER1","asdf");
        result.Should().BeTrue();
    }

    [Fact]
    public void CanEditOwnRolesWithoutPermission()
    {
        var rig = new RoleServiceTestRig();
        var result = rig.CanEditOwnRoles("user1","user1","asdf");
        result.Should().BeTrue();
    }

    [Fact]
    public void CanEditOwnRolesWithRoleAdmin()
    {
        var rig = new RoleServiceTestRig();
        var result = rig.CanEditOwnRoles("user1","user1",RoleAdminAbrv);
        result.Should().BeFalse();
    }

    [Fact]
    public void CanEditOwnRolesWithSuperUser()
    {
        var rig = new RoleServiceTestRig();
        var result = rig.CanEditOwnRoles("user1","user1",SuperUserAbrv);
        result.Should().BeFalse();
    }



}
