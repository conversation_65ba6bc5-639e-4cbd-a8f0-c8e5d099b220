using AutoFixture;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using MockQueryable;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.Product;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.Product;
using QCash.Utils.Core;
using Xunit;

namespace QCash.Service.UnitTest.Services.Pages;

public class ProductServiceTests
{
    private class ProductServiceTestRig
    {
        public const string FiSlug = IFixtureExtensions.FiSlug;
        public const string ProductSlug = "ProductSlug1";
        public readonly Guid FiId = IFixtureExtensions.FinancialInstitutionId;
        public readonly Guid FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId;
        public readonly Guid ProductId = Guid.NewGuid();
        public readonly Guid LoanCategoryDefaultId = Guid.NewGuid();
        public readonly Guid LoanTypeDefaultId = Guid.NewGuid();
        private ProductService ProductService { get; set; }
        private ILogger<ProductService> Logger { get; set; }
        private QCashContext Context { get; set; }
        private IFiQueries FiQueries { get; set; }
        private ILookupService LookupService { get; set; }
        private ILookupQueries LookupQueries { get; set; }
        private IProductQueries ProductQueries { get; set; }
        private IErrorNotificationService ErrorNotificationService { get; set; }
        private IGuidExtensionService GuidExtensionService { get; set; }
        private ISystemClockService SystemClockService { get; set; }
        private IPermissionDebuggingService PermissionDebuggingService { get; set; }
        public IEfPocoService EfPocoService { get; set; }
        
        public ProductServiceTestRig(IFixture fixture, bool hasLoanCategories = true)
        {
            Logger = fixture.Freeze<ILogger<ProductService>>();
            Context = fixture.Freeze<QCashContext>();
            EfPocoService = fixture.Freeze<IEfPocoService>();

            FiQueries = fixture.Freeze<IFiQueries>();
            var fiList = (new List<FinancialInstitution>()
                {
                    new FinancialInstitution() { Id = IFixtureExtensions.FinancialInstitutionId, Slug = IFixtureExtensions.FiSlug, Name = "",  },
                })
                .AsQueryable().BuildMock();
            FiQueries.GetFIRecordQueryable(IFixtureExtensions.FiSlug)
                .Returns(fiList);
            
            LookupService = fixture.Freeze<ILookupService>();

            var defaultLoanTypeDdItem = new QListItem<Guid>() { Text = "LT-D", Value = LoanTypeDefaultId, };
            var loanTypeDropDownOptions = new List<QListItem<Guid>>
            {
                defaultLoanTypeDdItem,
                new() { Text = "LT1", Value = Guid.NewGuid(), },
                new() { Text = "LT2", Value = Guid.NewGuid(), },
            };
            LookupService.GetLoanTypesForDropdownAsync(Arg.Any<IQueryable<LoanType>>())
                .Returns(loanTypeDropDownOptions);

            var loanCategories = hasLoanCategories ? new List<QListItem<Guid>>
            {
                new() { Text = "LC1", Value = LoanCategoryDefaultId, },
                new() { Text = "LC2", Value = Guid.NewGuid(), },
            } : [];
            LookupService.GetLoanCategoriesForDropdownAsync(FiSlug, loanTypeDropDownOptions.First().Value)
                .Returns(loanCategories);

            LookupService.GetDefaultLoanType(Arg.Any<List<LoanType>>())
                .Returns(new LoanType { Name = "something", Id = LoanTypeDefaultId });

            LookupQueries = fixture.Freeze<ILookupQueries>();

            var loanTypes = new List<LoanType>().AsQueryable().BuildMock();
            LookupQueries.GetLoanTypes(FiSlug)
                .Returns(loanTypes);

            ProductQueries = fixture.Freeze<IProductQueries>();
            var products = (new List<Product>()
                {
                    new Product() { Id = ProductId, Slug = ProductSlug, Name = "Product 1", Abrv = "P1",  },
                })
                .AsQueryable().BuildMock();
            ProductQueries.GetProducts(IFixtureExtensions.FinancialInstitutionId)
                .Returns(products);
            
            ErrorNotificationService = fixture.Freeze<IErrorNotificationService>();
            GuidExtensionService = fixture.Freeze<IGuidExtensionService>();
            SystemClockService = fixture.Freeze<ISystemClockService>();
            PermissionDebuggingService = fixture.Freeze<IPermissionDebuggingService>();

            this.GuidExtensionService.NewSequentialGuid().Returns(ProductId);

            IQueryable<LoanCategory> q = Enumerable.Range(1, 3).Select(a => new LoanCategory()
            {
                Id = Guid.NewGuid(),
                Name = $"Test {a}",
                //LoanCategoryProducts = new List<LoanCategoryProduct> { new() { ProductId = ProductId } },
                Products = new List<Product>() { new () {Id = ProductId, }},
                TimeStamp = [],
                Slug = $"Test Slug {a}",
                Abrv = $"Test Abrv {a}",
                AppAbrv = $"Test AppAbrv {a}",
                FinancialInstitutionId = FinancialInstitutionId,
            }).ToList().AsQueryable(); //.BuildMock();
            var getLoanCategoriesResult = q.BuildMock();
            LookupQueries.GetLoanCategories(Arg.Any<Guid>())
                .Returns(getLoanCategoriesResult);

            var getLoanCategoriesResultAsync = Task.FromResult(getLoanCategoriesResult);
            LookupQueries.GetLoanCategoriesQAsync(IFixtureExtensions.FiSlug, Arg.Any<Guid>()) // LoanTypeDefaultId)
                .Returns(getLoanCategoriesResultAsync);

            ProductService = new ProductService(Logger, Context, FiQueries,
                LookupService, LookupQueries,
                SystemClockService, ProductQueries, PermissionDebuggingService, EfPocoService);
        }

        public async Task<ProductInfoTabDto?> GetPageDataAsync(string fiSlug, string productSlug)
        {
            var q = new List<Product>()
            {
                new Product() //{ Id = ProductId, Slug = ProductSlug, Name = "Product 1", Abrv = "P1", },
                {
                    Id = ProductId,
                    //ApplicationId = FiId,
                    IsActive = true,
                    Name = "Product 1",
                    Abrv = "P1",
                    Description = "This is Product Numero Uno",
                    Slug = "ProductSlug1",
                    GlTranslationAccount = "PP11",   // TransferClearingUserGLCode
                    // SelectedLoanCategoryId = rig.LoanCategoryId,
                    LoanCategory = new LoanCategory()
                    {
                        Name = "Unsecured Closed-End",
                    },
                    FinancialInstitution = new FinancialInstitution()
                        {
                            Id = IFixtureExtensions.FinancialInstitutionId,
                            Slug = IFixtureExtensions.FiSlug,
                            Setting = new Setting()
                            {
                                TimeZone = "Eastern",
                            },
                        },
                },
            }.BuildMock();
            ProductQueries.GetProducts().Returns(q);
            return await ProductService.GetProductInfoPageDataAsync(fiSlug, productSlug);
        }

        public async Task<ProductCreatePageDto> GetCreatePageModelAsync(string fiSlug) =>
            await ProductService.GetCreatePageModelAsync(fiSlug, FinancialInstitutionId);

        public async Task<ProductCategoryPartialDto> GetCreatePageModelOnLoanTypeChangeAsync(string fiSlug, Guid loanTypeId,
            Guid oldSelectedCategoryId)
        {
            return await ProductService.GetCreatePageModelOnLoanTypeChangeAsync(fiSlug, loanTypeId, oldSelectedCategoryId);
        }

        public async Task<ProductCreateResultDto> CreateNewProductAsync(ProductCreatePageDto dto) => 
            await ProductService.CreateNewProductAsync(dto);

        public ProductCreatePageDto GetProductDto(string name = "sample name 1", bool hasLoanType = true)
        {
            var dto = new ProductCreatePageDto
            {
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                SelectedLoanCategoryId = Guid.Empty,
                LoanTypeId = hasLoanType ? LoanTypeDefaultId : null,
                Name = name,
                Abrv = "abrv 1",
                Description = "desc 1",
                PersonalLoanCampaign = true,
                IsArchived = false,
                Title = "title 1",
                FiSlug = IFixtureExtensions.FiSlug,
                IsActive = false,
            };
            return dto;
        }
    }

    [Theory, AutoSubstituteData]
    public async Task GetPageDataAsync(IFixture fixture)
    {
        var rig = new ProductServiceTestRig(fixture);
        var result = await rig.GetPageDataAsync(ProductServiceTestRig.FiSlug, ProductServiceTestRig.ProductSlug);
        result.Should().BeEquivalentTo(new
        {
            Id = rig.ProductId,
            FinancialInstitutionId = rig.FiId,
            IsActive = true,
            Name = "Product 1",
            Abrv = "P1",
            Description = "This is Product Numero Uno",
            Slug = "ProductSlug1",
            TransferClearingUserGLCode = "PP11",
            SelectedLoanCategoryId = Guid.Empty,
        });
    }

    [Theory, AutoSubstituteData]
    public async Task GetCreatePageModelAsync(IFixture fixture)
    {
        var rig = new ProductServiceTestRig(fixture);
        var result = await rig.GetCreatePageModelAsync(ProductServiceTestRig.FiSlug);
        result.Should().BeEquivalentTo(new
        {
            ProductServiceTestRig.FiSlug,
            IsArchived = false,
            Title = "New Product",
            Description = "",
            LoanTypeId = rig.LoanTypeDefaultId,
        });

        result.LoanTypeOptions.Count.Should().Be(3);
        result.LoanCategoryOptions.Count.Should().Be(2);
    }

    [Theory, AutoSubstituteData]
    public async Task GetCreatePageModelOnLoanTypeChangeAsync(IFixture fixture)
    {
        var rig = new ProductServiceTestRig(fixture);
        var newSelectedLoanTypeId = Guid.NewGuid();
        var oldSelectedCategoryId = Guid.Empty;
        var result = await rig.GetCreatePageModelOnLoanTypeChangeAsync(
            ProductServiceTestRig.FiSlug, newSelectedLoanTypeId, oldSelectedCategoryId);

        result.Should().BeEquivalentTo(new
        {
            IsActive = false, FinancialInstitutionId = rig.FinancialInstitutionId,
        });
        result.LoanCategoryOptions.Count.Should().Be(3);
    }
    
    [Theory, AutoSubstituteData]
    public async Task CreateNewProductNoNameAsync(IFixture fixture)
    {
        var rig = new ProductServiceTestRig(fixture);
        var dto = rig.GetProductDto(name: "");
        var result = await rig.CreateNewProductAsync(dto);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Product must have a name.");
    }
    
    [Theory, AutoSubstituteData]
    public async Task CreateNewProductDuplicateAsync(IFixture fixture)
    {
        var rig = new ProductServiceTestRig(fixture);
        var dto = rig.GetProductDto(name: ProductServiceTestRig.ProductSlug);
        var result = await rig.CreateNewProductAsync(dto);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Product with the same name already exists.");
    }
    
    [Theory, AutoSubstituteData]
    public async Task CreateNewProductNoLoanTypeAsync(IFixture fixture)
    {
        var rig = new ProductServiceTestRig(fixture);
        var dto = rig.GetProductDto(hasLoanType:false);
        var result = await rig.CreateNewProductAsync(dto);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("The Loan Type field is required.");
    }
    
    [Theory, AutoSubstituteData]
    public async Task CreateNewProductNoLoanCategoriesAsync(IFixture fixture)
    {
        var rig = new ProductServiceTestRig(fixture, hasLoanCategories:false);
        var dto = rig.GetProductDto();
        var result = await rig.CreateNewProductAsync(dto);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("No Loan Categories exist.");
    }
        
    [Theory, AutoSubstituteData]
    public async Task CreateNewProductSuccessAsync(IFixture fixture)
    {
        var rig = new ProductServiceTestRig(fixture);
        var dto = rig.GetProductDto();
        var result = await rig.CreateNewProductAsync(dto);
        result.IsSuccessful.Should().BeTrue();

        rig.EfPocoService.Received().Add(Arg.Is<Product>(p =>
            p.FinancialInstitutionId == rig.FinancialInstitutionId
            && p.Abrv == "abrv 1"
            && p.AppAbrv == ""
            && p.GlTranslationAccount == "0"
            && p.InvoiceId == "APP"
            && p.Slug == "sample-name-1"
            && p.Margin == 0
            && p.Description == "desc 1"
            && p.Name == "sample name 1"
            && p.LoanTypeId == rig.LoanTypeDefaultId
            && p.LoanCategoryId == rig.LoanCategoryDefaultId
            && p.PersonalLoanCampaign == true
            ));
    }
}
