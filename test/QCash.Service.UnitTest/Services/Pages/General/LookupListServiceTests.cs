using AutoFixture;
using FluentAssertions;
using Kendo.Mvc;
using Kendo.Mvc.UI;
using MockQueryable;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Data.Models.Interfaces;
using QCash.LoanApplication;
using QCash.LoanApplication.Interfaces;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using Xunit;

namespace QCash.Service.UnitTest.Services.Pages.General;

public class LookupListServiceTests
{
    private class LookupListPageServiceTestRig
    {
        private ILookupListService LookupListService { get; set; }
        private IDecisionEngineSettingsService DecisionEngineSettingsService { get; set; }
        public IEfPocoService EfPocoService { get; set; }
        private IScoreTypeService ScoreTypeService { get; set; }
        private IDecisionEngineParametersProvider DecisionEngineParametersProvider { get; set; }
        private QCashContext Context { get; set; }
        private IFiQueries FiQueries { get; set; }
        private readonly Guid Item1Id = Guid.NewGuid();

        public LookupListPageServiceTestRig(IFixture fixture)
        {
            Context = fixture.Freeze<QCashContext>();

            ScoreTypeService = fixture.Freeze<IScoreTypeService>();
            DecisionEngineParametersProvider = fixture.Freeze<IDecisionEngineParametersProvider>();
            FiQueries = fixture.Freeze<IFiQueries>();
            EfPocoService = fixture.Freeze<IEfPocoService>();
            DecisionEngineSettingsService = fixture.Freeze<IDecisionEngineSettingsService>();
            DecisionEngineSettingsService.GetLookupDbObjectType(Enums.DecisionEngineLookupTypeEnum.LoanCode)
                .Returns(typeof(LoanCode));
            DecisionEngineSettingsService.GetLookupTypeEnum(nameof(Enums.DecisionEngineLookupTypeEnum.LoanCode))
                .Returns(Enums.DecisionEngineLookupTypeEnum.LoanCode);

            LookupListService = fixture.Freeze<LookupListService>();
        }

        public Task<DataSourceResult> GetAllLookupDataAsync(string lookupTypeName,
            DataSourceRequest request)
        {
            var q = new List<IEFPocoLookup>()
            {
                new LoanCode() { Slug = "Reason1", Name = "Reason 1", },
                new LoanCode() { Slug = "Reason2", Name = "Reason 2", },
            }.BuildMock();
            DecisionEngineSettingsService.GetQueryForLookupAsync(Enums.DecisionEngineLookupTypeEnum.LoanCode)
                .Returns(Task.FromResult(q));

            return LookupListService.GetAllLookupDataAsync(IFixtureExtensions.FinancialInstitutionId, lookupTypeName, request);
        }

        public async Task DeleteAsync()
        {
            EfPocoService.DeleteItemAsync(typeof(LoanCode), Arg.Any<DeleteItemDto>())
                .Returns(new GenericActionResult() { IsSuccessful = true, });

            var dto = new DeleteItemDto() { DeleteItemId = Item1Id, TimeStamp = [], };
            await LookupListService.DeleteItemAsync(IFixtureExtensions.FinancialInstitutionId,
                Enums.DecisionEngineLookupTypeEnum.LoanCode, dto);
        }

        public bool CanDelete(Enums.DecisionEngineLookupTypeEnum lookupType)
        {
            return LookupListService.CanDelete(lookupType);

        }
    }

    [Theory, AutoSubstituteData]
    public async Task GetAllLookupDataAsyncNormalAsync(IFixture fixture)
    {
        var rig = new LookupListPageServiceTestRig(fixture);

        var result = await rig.GetAllLookupDataAsync( nameof(Enums.DecisionEngineLookupTypeEnum.LoanCode), new DataSourceRequest());
        Assert.NotNull(result);
        var data = result.Data as List<IEFPocoLookup>;
        Assert.NotNull(data);
        data.Count().Should().Be(2);
        data.Any(a => a.Slug == "Reason1").Should().Be(true);
        data.Any(a => a.Slug == "Reason2").Should().Be(true);
    }

    [Theory, AutoSubstituteData]
    public async Task GetAllLookupDataAsyncSortByCodeAsync(IFixture fixture)
    {
        var rig = new LookupListPageServiceTestRig(fixture);
        var result = await rig.GetAllLookupDataAsync( nameof(Enums.DecisionEngineLookupTypeEnum.LoanCode),
            new DataSourceRequest() { Sorts = new List<SortDescriptor>() { new () { Member = nameof(IEFPocoWithCode.Code),}}});
        Assert.NotNull(result);
        var codeList = result.Data as List<IEFPocoLookup>;
        Assert.NotNull(codeList);
        var data = codeList.Cast<IEFPocoLookup>().ToList();
        Assert.NotNull(data);
        data.Count().Should().Be(2);
        data.Any(a => a.Slug == "Reason2").Should().Be(true);
        data.Any(a => a.Slug == "Reason1").Should().Be(true);
    }

    [Theory, AutoSubstituteData]
    public async Task GetAllLookupDataAsyncSortByDescAsync(IFixture fixture)
    {
        var rig = new LookupListPageServiceTestRig(fixture);

        var result = await rig.GetAllLookupDataAsync( nameof(Enums.DecisionEngineLookupTypeEnum.LoanCode),
            new DataSourceRequest() { Sorts = new List<SortDescriptor>() { new () { Member = nameof(IEFPocoWithDescription.Description),}}});
        Assert.NotNull(result);
        var descList = result.Data as List<IEFPocoWithDescription>;
        Assert.NotNull(descList);
        var data = descList.Cast<IEFPocoLookup>().ToList();
        Assert.NotNull(data);
        data.Count().Should().Be(2);
        data.Any(a => a.Slug == "Reason2").Should().Be(true);
        data.Any(a => a.Slug == "Reason1").Should().Be(true);
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteItemNormalAsync(IFixture fixture)
    {
        var rig = new LookupListPageServiceTestRig(fixture);
        await rig.DeleteAsync();

        await rig.EfPocoService.Received()
            .DeleteItemAsync(typeof(LoanCode), Arg.Any<DeleteItemDto>());
    }

    [Theory, AutoSubstituteData]
    public void CanDeleteTests(IFixture fixture)
    {
        var rig = new LookupListPageServiceTestRig(fixture);
        rig.CanDelete(Enums.DecisionEngineLookupTypeEnum.InvoicePlan)
            .Should().Be(false);
        rig.CanDelete(Enums.DecisionEngineLookupTypeEnum.Weekday)
            .Should().Be(false);
        rig.CanDelete(Enums.DecisionEngineLookupTypeEnum.Unknown)
            .Should().Be(false);
        rig.CanDelete(Enums.DecisionEngineLookupTypeEnum.DocumentType)
            .Should().Be(true);
    }
}
