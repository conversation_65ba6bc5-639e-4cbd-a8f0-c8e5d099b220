using System.Diagnostics.CodeAnalysis;
using AutoFixture;
using FluentAssertions;
using MockQueryable;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Data.Models.Interfaces;
using QCash.LoanApplication.Interfaces;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General;
using QCash.Service.Services.General.Interfaces;
using QCash.Utils.Core;
using Xunit;

namespace QCash.Service.UnitTest.Services.Pages.General;

[ExcludeFromCodeCoverage]
public class LookupItemServiceTests
{
    private class LookupItemPageServiceTestRig
    {
        public IDecisionEngineSettingsService DecisionEngineSettingsService { get; set; }
        private LookupItemService LookupItemService { get; set; }
        private IGuidExtensionService GuidExtensionService { get; set; }
        public IEfPocoService EfPocoService { get; set; }

        public const string FiSlug = "FiSlug1";
        public readonly Guid ApplicationId = IFixtureExtensions.FinancialInstitutionId;
        public readonly Guid NewRecordId = Guid.NewGuid();
        public readonly Guid RecordIdBeingEdited = Guid.NewGuid();

        public LookupItemPageServiceTestRig(IFixture fixture)
        {
            fixture.Freeze<ISystemClockService>();
            fixture.Freeze<QCashContext>();

            GuidExtensionService = fixture.Freeze<IGuidExtensionService>();
            GuidExtensionService.NewSequentialGuid()
                .Returns(NewRecordId);

            EfPocoService = fixture.Freeze<IEfPocoService>();
            fixture.Freeze<IScoreTypeService>();
            fixture.Freeze<IDecisionEngineParametersProvider>();

            DecisionEngineSettingsService = fixture.Freeze<IDecisionEngineSettingsService>();
            var accountWarningCodeType = typeof(AccountWarningCode);
            DecisionEngineSettingsService.GetLookupDbObjectType(Enums.DecisionEngineLookupTypeEnum.AccountWarningCode)
                .Returns(accountWarningCodeType);
            DecisionEngineSettingsService.GetLookupTypeEnum("AccountWarningCode")
                .Returns(Enums.DecisionEngineLookupTypeEnum.AccountWarningCode);
            DecisionEngineSettingsService.GetLookupTypeEnum(nameof(Enums.DecisionEngineLookupTypeEnum.ExclusionLoan))
                .Returns(Enums.DecisionEngineLookupTypeEnum.ExclusionLoan);

            LookupItemService = fixture.Freeze<LookupItemService>();
        }

        public async Task<IEFPocoLookup?> GetItemAsync(string fiSlug, string lookupTypeName, string lookupItemSlug) =>
            await LookupItemService.GetItemAsync(fiSlug, lookupTypeName, lookupItemSlug);

        public async Task SetInitialDataAsync(EFPocoLookupDto dto, IEFPocoLookup dbRecord,
            Enums.DecisionEngineLookupTypeEnum lookupTypeEnum, Guid applicationId)
        {
            var q = new List<IEFPocoLookup>().BuildMock();
            DecisionEngineSettingsService.GetQueryForLookupAsync(lookupTypeEnum)
                .Returns(Task.FromResult(q));
            await LookupItemService.SetInitialDataAsync(dto, dbRecord, lookupTypeEnum, applicationId);
        }

        public async Task<GenericActionResult> SaveAsync(bool slugMatches = true, bool creatingRecord = false, bool errorWhileSaving = false)
        {
            var lookupType = Enums.DecisionEngineLookupTypeEnum.AccountWarningCode;
            var dto = new EFPocoLookupDto()
            {
                Id = creatingRecord ? Guid.Empty : RecordIdBeingEdited,
                Name = "Sample Name 1", Description = "Sample Description 1",
                Slug = slugMatches ? "AccountWarningCode1" : "BADSLUG",
                Code = "", AppAbrv = "",
                TimeStamp = [],
                FiSlug = LookupItemPageServiceTestRig.FiSlug,
                LookupTypeName = "", 
            };

            EfPocoService.CreateOrUpdateAsync(typeof(AccountWarningCode),
                    creatingRecord ? Guid.Empty : RecordIdBeingEdited, ApplicationId,
                    [], Arg.Any<PerformCreateOrUpdateOptionsAsync?>())
                .Returns(Task.FromResult(new GetOrCreateRecordResult
                {
                    IsSuccessful = !errorWhileSaving,
                    ErrorMessage = errorWhileSaving ? "This is a random error message" : null,
                    Record = null,
                    FoundExistingRecord = !creatingRecord,
                    CreatingNewRecord = creatingRecord,
                    EditingExistingRecord = !creatingRecord,
                }));

            return await LookupItemService.SaveAsync(IFixtureExtensions.FinancialInstitutionId, lookupType.ToString(), dto);
        }

        public bool ShouldShowTranslationName(Enums.DecisionEngineLookupTypeEnum lookupType, LanguageDto? lang) =>
            LookupItemService.ShouldShowTranslationName(lookupType, lang);

        public bool ShouldShowTranslationDescription(Enums.DecisionEngineLookupTypeEnum lookupType, LanguageDto? lang)=>
            LookupItemService.ShouldShowTranslationDescription(lookupType, lang);
    }

    [Theory, AutoSubstituteData]
    public async Task GetItemAsyncNormalAsync(IFixture fixture)
    {
        var rig = new LookupItemPageServiceTestRig(fixture);
        var q = new List<IEFPocoLookup>()
            {
                new ExclusionLoan() { Slug = "Slug1", Name = "Reason 1"},
                new ExclusionLoan() { Slug = "Slug2", Name = "Reason 2"},
            }
            .BuildMock();
        rig.DecisionEngineSettingsService.GetQueryForLookupAsync(Enums.DecisionEngineLookupTypeEnum.ExclusionLoan)
            .Returns(Task.FromResult(q));

        var result = await rig.GetItemAsync("FiSlug", nameof(Enums.DecisionEngineLookupTypeEnum.ExclusionLoan), "Slug2");
        Assert.NotNull(result);
        result.Name.Should().Be("Reason 2");
    }

    [Theory, AutoSubstituteData]
    public async Task SetInitialDataAsyncNormal(IFixture fixture)
    {
        var rig = new LookupItemPageServiceTestRig(fixture);
        var dbRecord = new AccountWarningCode();
        var dto = new EFPocoLookupDto() { Name = "Sample Name 1", Description = "Sample Description 1", Code = "", Abrv="SN", AppAbrv = "", Slug = "z", TimeStamp = [], FiSlug = LookupItemPageServiceTestRig.FiSlug, LookupTypeName = "", };
        await rig.SetInitialDataAsync(dto, dbRecord, Enums.DecisionEngineLookupTypeEnum.AccountWarningCode,
            rig.ApplicationId);

        dbRecord.Slug.Should().Be(rig.NewRecordId.ToString());
        dbRecord.Abrv.Should().Be("AWC");
        dbRecord.AppAbrv.Should().Be("AWC");
    }

    [Theory, AutoSubstituteData]
    public async Task SetInitialDataAsyncNoName(IFixture fixture)
    {
        var rig = new LookupItemPageServiceTestRig(fixture);
        var dbRecord = new AccountWarningCode();
        var dto = new EFPocoLookupDto() { Name = "", Description = "Sample Description 1", Code = "", AppAbrv = "", Slug = "z", TimeStamp = [], FiSlug = LookupItemPageServiceTestRig.FiSlug, LookupTypeName = "", };
        await rig.SetInitialDataAsync(dto, dbRecord, Enums.DecisionEngineLookupTypeEnum.AccountWarningCode,
            rig.ApplicationId);

        dbRecord.Slug.Should().Be(rig.NewRecordId.ToString());
        dbRecord.Abrv.Should().Be("AWC");
        dbRecord.AppAbrv.Should().Be("AWC");
    }

    // Skipping until fixed
    //[SkipInPipelineFact]
    [Theory, AutoSubstituteData]
    public async Task SaveAsyncNormalExistingAsync(IFixture fixture)
    {
        var rig = new LookupItemPageServiceTestRig(fixture);
        var result = await rig.SaveAsync();
        result.IsSuccessful.Should().BeTrue();
        await rig.EfPocoService.Received().CreateOrUpdateAsync(typeof(AccountWarningCode), rig.RecordIdBeingEdited, rig.ApplicationId,
            [], Arg.Any<PerformCreateOrUpdateOptionsAsync?>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveAsyncUpdateButErrorOccurred(IFixture fixture)
    {
        var rig = new LookupItemPageServiceTestRig(fixture);
        var result = await rig.SaveAsync(errorWhileSaving: true);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().StartWith("This is a random error message");
        await rig.EfPocoService.Received().CreateOrUpdateAsync(typeof(AccountWarningCode), rig.RecordIdBeingEdited, rig.ApplicationId,
            [], Arg.Any<PerformCreateOrUpdateOptionsAsync?>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveAsyncInsertNormal(IFixture fixture)
    {
        var rig = new LookupItemPageServiceTestRig(fixture);

        var result = await rig.SaveAsync(creatingRecord:true);
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();

        await rig.EfPocoService.Received().CreateOrUpdateAsync(typeof(AccountWarningCode), Guid.Empty, rig.ApplicationId,
            [], Arg.Any<PerformCreateOrUpdateOptionsAsync?>());
    }

    [Theory, AutoSubstituteData]
    public void ShouldShowTranslationNameYes(IFixture fixture)
    {
        var rig = new LookupItemPageServiceTestRig(fixture);
        var lang = new LanguageDto() { Id = Guid.NewGuid(), LanguageCode = "", Name = "", };
        var result = rig.ShouldShowTranslationName(Enums.DecisionEngineLookupTypeEnum.PurposeOfLoanType, lang);
        result.Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public void ShouldShowTranslationNameWrongType(IFixture fixture)
    {
        var rig = new LookupItemPageServiceTestRig(fixture);
        var lang = new LanguageDto() { Id = Guid.NewGuid(), LanguageCode = "", Name = "",  };
        var result = rig.ShouldShowTranslationName(Enums.DecisionEngineLookupTypeEnum.DocumentType, lang);
        result.Should().BeFalse();
    }

    [Theory, AutoSubstituteData]
    public void ShouldShowTranslationNameNoLang(IFixture fixture)
    {
        var rig = new LookupItemPageServiceTestRig(fixture);
        LanguageDto? lang = null;
        var result = rig.ShouldShowTranslationName(Enums.DecisionEngineLookupTypeEnum.PurposeOfLoanType, lang);
        result.Should().BeFalse();
    }

    [Theory, AutoSubstituteData]
    public void ShouldShowTranslationDescriptionYes(IFixture fixture)
    {
        var rig = new LookupItemPageServiceTestRig(fixture);
        var lang = new LanguageDto() { Id = Guid.NewGuid(), LanguageCode = "", Name = "",  };
        var result = rig.ShouldShowTranslationDescription(Enums.DecisionEngineLookupTypeEnum.Weekday, lang);
        result.Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public void ShouldShowTranslationDescriptionWrongType(IFixture fixture)
    {
        var rig = new LookupItemPageServiceTestRig(fixture);
        var lang = new LanguageDto() { Id = Guid.NewGuid(), LanguageCode = "", Name = "",  };
        var result = rig.ShouldShowTranslationDescription(Enums.DecisionEngineLookupTypeEnum.DocumentType, lang);
        result.Should().BeFalse();
    }

    [Theory, AutoSubstituteData]
    public void ShouldShowTranslationDescriptionNoLang(IFixture fixture)
    {
        var rig = new LookupItemPageServiceTestRig(fixture);
        LanguageDto? lang = null;
        var result = rig.ShouldShowTranslationDescription(Enums.DecisionEngineLookupTypeEnum.Weekday, lang);
        result.Should().BeFalse();
    }

}

