using FluentAssertions;
using QCash.LoanApplication;
using QCash.Service.LoanCalculator;
using QCash.Service.Models.LoanApplication;
using Xunit;

namespace QCash.Service.UnitTest.Services
{
    public class AprCalcTests
    {
        private class AprCalcTestRig
        {
            public IAprCalc AprCalc { get; set; } = new CunaAprCalc();
        }

        [Fact]
        public void CalculateDisclosureTest()
        {
            var rig = new AprCalcTestRig();
            var loanOpenDate = DateTime.Parse("1/10/2025 5:00 am");
            var req = new TILARequest
            {
                AppId = $"asdf.1234",
                Abrv = "AA",
                LoanFee = 500,
                IsOpenEndedLoan = false,
                AmountBorrowed = 1000,
                FirstPaymentDayOfMonth = 1,
                BaseAccountId = "1234",
                InterestRate = 0.03M,
                DateOfBirth = DateTime.MinValue,
                LoanOriginationFee = 25,
                AmortizationMethod = AmortizationMethod.ActualDay,
                PaymentFrequency = (PaymentFrequency)PaymentFrequency.Monthly,
                PaymentAcceleration = PaymentAcceleration.None,
                RoundToIndicator = RoundToIndicator.RoundUp,
                LoanOpenDate = loanOpenDate,
                ExtraInfo = null,
                LoanType = LoanType.InterestBased,
                DocStampFeeRate = 0,
                CorrelationId = null,
                ApplicationId = 1234,
                ClientTimeZone = null,
                FirstPaymentDate = loanOpenDate.AddMonths(1),
                LoanTermInMonths = 10,
            };

            var ret = rig.AprCalc.ReturnTila(req);

            //These are the return values we actually use
            ret.AdjustedAPR.Should().Be(5.587M);
            ret.AmountFinanced.Should().Be(975);
            ret.DocStampTax.Should().Be(0);
            ret.FinalPaymentAmount.Should().Be(99.96M);
            ret.FinanceCharge.Should().Be(25.14M);
            ret.NumberOfPayments.Should().Be(10);
            ret.RegularPaymentAmount.Should().Be(100.02M);
            ret.TotalOfPayments.Should().Be(1000.14M);

        }

        [Fact]
        public void CalculateDisclosureTest2()
        {
            var rig = new AprCalcTestRig();
            var loanOpenDate = DateTime.Parse("5/10/2025 5:00 pm");
            var req = new TILARequest
            {
                AppId = $"asdf.1234",
                Abrv = "AA",
                LoanFee = 500,
                IsOpenEndedLoan = false,
                AmountBorrowed = 10000,
                FirstPaymentDayOfMonth = 1,
                BaseAccountId = "1234",
                InterestRate = 13.99M,
                DateOfBirth = DateTime.MinValue,
                LoanOriginationFee = 25,
                AmortizationMethod = AmortizationMethod.ActualDay,
                PaymentFrequency = (PaymentFrequency)PaymentFrequency.Monthly,
                PaymentAcceleration = PaymentAcceleration.None,
                RoundToIndicator = RoundToIndicator.RoundUp,
                LoanOpenDate = loanOpenDate,
                ExtraInfo = null,
                LoanType = LoanType.InterestBased,
                DocStampFeeRate = 0,
                CorrelationId = null,
                ApplicationId = 12345,
                ClientTimeZone = null,
                FirstPaymentDate = loanOpenDate.AddMonths(1),
                LoanTermInMonths = 12,
            };

            var ret = rig.AprCalc.ReturnTila(req);

            //These are the return values we actually use
            ret.AdjustedAPR.Should().Be(14.532M);
            ret.AmountFinanced.Should().Be(9975);
            ret.DocStampTax.Should().Be(0);
            ret.FinalPaymentAmount.Should().Be(898.04M);
            ret.FinanceCharge.Should().Be(802.47M);
            ret.NumberOfPayments.Should().Be(12);
            ret.RegularPaymentAmount.Should().Be(898.13M);
            ret.TotalOfPayments.Should().Be(10777.47M);

        }

        [Fact]
        public void CalculateDisclosureTestError()
        {
            var rig = new AprCalcTestRig();
            var loanOpenDate = DateTime.Parse("1/10/2025 12:00 pm");

            var req = new TILARequest
            {
                AppId = $"asdf.1234",
                Abrv = "AA",
                LoanFee = 500,
                IsOpenEndedLoan = false,
                AmountBorrowed = 0,
                FirstPaymentDayOfMonth = 1,
                BaseAccountId = "1234",
                InterestRate = 13.99M,
                DateOfBirth = DateTime.MinValue,
                LoanOriginationFee = 25,
                AmortizationMethod = AmortizationMethod.ActualDay,
                PaymentFrequency = (PaymentFrequency)PaymentFrequency.Monthly,
                PaymentAcceleration = PaymentAcceleration.None,
                RoundToIndicator = RoundToIndicator.RoundUp,
                LoanOpenDate = loanOpenDate,
                ExtraInfo = null,
                LoanType = LoanType.InterestBased,
                DocStampFeeRate = 0,
                CorrelationId = null,
                ApplicationId = 12345,
                ClientTimeZone = null,
                FirstPaymentDate = loanOpenDate.AddMonths(1),
                LoanTermInMonths = 99,
            };

            Assert.Throws<Exception>(() =>
            {
                //missing_amount
                var ret = rig.AprCalc.ReturnTila(req);
            });
            
        }

    }
}