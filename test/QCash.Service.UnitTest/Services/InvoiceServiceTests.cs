using EntityFrameworkCore.Testing.NSubstitute;
using AutoFixture;
using FluentAssertions;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.General;
using QCash.Service.Services;
using QCash.Service.BusinessLogic.InvoiceGenerator;
using QCash.Service.BusinessLogic.PricingScenarioCalculator;
using QCash.Service.BusinessLogic.Validators.InvoicePlan;
using QCash.Service.BusinessLogic.Validators.PricingScenario;
using Xunit;

namespace QCash.Service.UnitTest.Services;

public class InvoiceServiceTests
{
    private class InvoiceServiceTestRig
    {
        public InvoiceService InvoiceService { get; set; }
        public QCashContext DbContext { get; set; }

        private IInvoiceGeneratorRunner InvoiceGeneratorRunner { get; set; }
        private IInvoicePlanItemValidator InvoicePlanItemValidator { get; set; }

        private IPricingScenarioCalculatorRunner PricingScenarioCalculatorRunner { get; set; }
        private IPricingScenarioValidator PricingScenarioValidator { get; set; }
        private IGuidExtensionService GuidExtensionService { get; set; }
        public FinancialInstitution? FinancialInstitution { get; set; }
        
        public InvoiceServiceTestRig(IFixture fixture)
        {
            DbContext = fixture.Freeze<QCashContext>();

            GuidExtensionService = new GuidExtensionService();

            PricingScenarioValidator = new PricingScenarioValidator(DbContext);

            PricingScenarioCalculatorRunner = new PricingScenarioCalculatorRunner(DbContext);

            InvoicePlanItemValidator = new InvoicePlanItemValidator(DbContext, PricingScenarioValidator);

            InvoiceGeneratorRunner = new InvoiceGeneratorRunner(DbContext, PricingScenarioCalculatorRunner, GuidExtensionService);

            InvoiceService = new InvoiceService(DbContext, InvoicePlanItemValidator, InvoiceGeneratorRunner);

            BuildOutTestData();
        }

        private void BuildOutTestData()
        {
            FinancialInstitution = new FinancialInstitution
            {
                Id = IFixtureExtensions.FinancialInstitutionId,
                Slug = IFixtureExtensions.FiSlug,
                Address = "",
                City = "",
                ContactName = "",
                ContactPhone = "",
                GlLoanAccount = "",
                MailingAddress = "",
                Name = "Test1 Name",
                NoReplySubdomain = "TestSubd1",
                State = "",
                Zip = "12345",
                TimeStamp = [],
            };

            DbContext.FinancialInstitutions.Add(FinancialInstitution);

            Data.Models.LoanType loanTypeIB;
            DbContext.LoanTypes.Add(loanTypeIB = new Data.Models.LoanType()
            {
                Id = Guid.NewGuid(),
                Name = "Interest Based",
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Abrv = "IB",
                AppAbrv = "IB",
                TimeStamp = [],
            });
            DbContext.SaveChanges();

            LoanCategory loanCategory = new LoanCategory
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "UC",
                Abrv = "UC",
                Slug = "unsecured_closed_end",
                Name = "Unsecured Closed-End",
                Description = "Unsecured Closed-End (UCE)",
                IsOpen = false,
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                IsDeleted = false,
            };
            DbContext.LoanCategories.Add(loanCategory);
            DbContext.SaveChanges();

            Data.Models.Product product = new Data.Models.Product()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Slug = "ProductSlug1",
                Name = "Product1",
                Abrv = "",
                AppAbrv = "",
                InvoicePlanId = Guid.NewGuid(),
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                LoanTypeId = loanTypeIB.Id,
                GlTranslationAccount = "",
                InvoiceId = "",
                IsActive = true,
                TimeStamp = [],
                LoanCategory = loanCategory,
            };
            DbContext.Products.Add(product);
            DbContext.SaveChanges();

            var fiConfig = new FinancialInstitutionConfiguration
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Description = "Test Configuration 1",
                DefaultProviderId = Guid.NewGuid(),
                Active = true,
                TimeStamp = []
            };
            DbContext.FinancialInstitutionConfigurations.Add(fiConfig);
            DbContext.SaveChanges();

            var loanStatusTypes = new List<LoanStatusType> {
                new() { Name = "Eligible Account Exclusion", Abrv = "LSTEAE", AppAbrv = "LSTEAE", Slug = "eligible_account_exclusion", Description = "Eligible Account Exclusion", ProductDependency = "PreProduct" },
                new() { Name = "Contact Info Changed Exclusion", Abrv = "LSTCICE", AppAbrv = "LSTCICE", Slug = "contact_info_changed_exclusion", Description = "Contact Info Changed Exclusion", ProductDependency = "PreProduct" },
                new() { Name = "Funding Pending", Abrv = "LSTFP", AppAbrv = "LSTFP", Slug = "funding_pending", Description = "Funding Pending", ProductDependency = "Product" },
                new() { Name = "Excluded by Bad Address", Abrv = "LSTBAE", AppAbrv = "LSTBAE", Slug = "bad_address_exclusion", Description = "Excluded by Bad Address", ProductDependency = "PreProduct" },
                new() { Name = "Processing Error", Abrv = "LSTPE", AppAbrv = "LSTPE", Slug = "processing_error", Description = "Processing Error", ProductDependency = "Product" },
                new() { Name = "Excluded by Bad Email", Abrv = "LSTBEE", AppAbrv = "LSTBEE", Slug = "bad_email_exclusion", Description = "Excluded by Bad Email", ProductDependency = "PreProduct" },
                new() { Name = "Approved", Abrv = "LSTA", AppAbrv = "LSTA", Slug = "approved", Description = "Approved", ProductDependency = "PreProduct" },
                new() { Name = "Denied", Abrv = "LSTD", AppAbrv = "LSTD", Slug = "denied", Description = "Denied", ProductDependency = "PreProduct" },
                new() { Name = "Canceled", Abrv = "LSTC", AppAbrv = "LSTC", Slug = "canceled", Description = "Canceled", ProductDependency = "None" },
                new() { Name = "Declined TILA", Abrv = "LSTDET", AppAbrv = "LSTDET", Slug = "declined_tila", Description = "Declined TILA", ProductDependency = "Product" },
                new() { Name = "Declined eConsent", Abrv = "LSTDEE", AppAbrv = "LSTDEE", Slug = "declined_econsent", Description = "Declined eConsent", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Account", Abrv = "LSTEA", AppAbrv = "LSTEA", Slug = "excluded_account", Description = "Excluded Account", ProductDependency = "PreProduct" },
                new() { Name = "Excluded State", Abrv = "LSTES", AppAbrv = "LSTES", Slug = "excluded_state", Description = "Excluded State", ProductDependency = "PreProduct" },
                new() { Name = "Initiate", Abrv = "LSTI", AppAbrv = "LSTI", Slug = "initiate", Description = "Initiate", ProductDependency = "PreProduct" },
                new() { Name = "Military No Product", Abrv = "LSTMNP", AppAbrv = "LSTMNP", Slug = "military_no_product", Description = "Military No Product", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled Before DE", Abrv = "LSTCBD", AppAbrv = "LSTCBD", Slug = "cancelled_before_de", Description = "Cancelled Before DE", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled After DE", Abrv = "LSTCAD", AppAbrv = "LSTCAD", Slug = "cancelled_after_de", Description = "Cancelled After DE", ProductDependency = "PreProduct" },
                new() { Name = "Restricted due to Loan in Process", Abrv = "LSTLIP", AppAbrv = "LSTLIP", Slug = "loan_in_process", Description = "Restricted due to Loan in Process", ProductDependency = "PreProduct" },
                new() { Name = "Is eConsent", Abrv = "LSTIE", AppAbrv = "LSTDIE", Slug = "is_econsent", Description = "Is eConsent", ProductDependency = "PreProduct" },
                new() { Name = "Accept eConsent", Abrv = "LSTAE", AppAbrv = "LSTDAE", Slug = "accept_econsent", Description = "Accept eConsent", ProductDependency = "PreProduct" },
                new() { Name = "Loan Application", Abrv = "LSTLA", AppAbrv = "LSTLA", Slug = "loan_application", Description = "Loan Application", ProductDependency = "PreProduct" },
                new() { Name = "Accept TILA", Abrv = "LSTAT", AppAbrv = "LSTAT", Slug = "accept_tila", Description = "Accept TILA", ProductDependency = "Product" },
                new() { Name = "Display TILA", Abrv = "LSTDT", AppAbrv = "LSTDT", Slug = "display_tila", Description = "Display TILA", ProductDependency = "Product" },
                new() { Name = "Loan Application Fee", Abrv = "LSTLAF", AppAbrv = "LSTLAF", Slug = "loan_fee", Description = "Loan Application Fee", ProductDependency = "PreProduct" },
                new() { Name = "Payoff Initiate", Abrv = "LSTPOINI", AppAbrv = "LSTPOINI", Slug = "payoff_initiate", Description = "Payoff Initiate", ProductDependency = "None" },
                new() { Name = "Payoff Completed", Abrv = "LSTPOCMP", AppAbrv = "LSTPOCMP", Slug = "payoff_complete", Description = "Payoff Completed", ProductDependency = "None" },
                new() { Name = "Payoff Cancelled", Abrv = "LSTPOCNC", AppAbrv = "LSTPOCNC", Slug = "payoff_cancel", Description = "Payoff Cancelled", ProductDependency = "None" },
                new() { Name = "Payoff Error", Abrv = "LSTPOERR", AppAbrv = "LSTPOERR", Slug = "payoff_error", Description = "Payoff Error", ProductDependency = "None" },
                new() { Name = "Payoff Complete", Abrv = "LSTPOPOC", AppAbrv = "LSTPOPOC", Slug = "payoff_po_complete", Description = "Payoff Complete", ProductDependency = "Product" },
                new() { Name = "Payoff Failed", Abrv = "LSTPOPOF", AppAbrv = "LSTPOPOF", Slug = "payoff_po_failed", Description = "Payoff Failed", ProductDependency = "Product" },
                new() { Name = "Local", Abrv = "mla_local", AppAbrv = "mla_local", Slug = "mla_local", Description = "MLA Local", ProductDependency = "None" },
                new() { Name = "Remote", Abrv = "mla_remote", AppAbrv = "mla_remote", Slug = "mla_remote", Description = "MLA Remote", ProductDependency = "None" },
                new() { Name = "Error", Abrv = "mla_error", AppAbrv = "mla_error", Slug = "mla_error", Description = "MLA Error", ProductDependency = "None" },
                new() { Name = "Display Loan Landing", Abrv = "LSTDL", AppAbrv = "LSTDL", Slug = "display_loan_landing", Description = "Display Loan Landing", ProductDependency = "PreProduct" },
                new() { Name = "Display Awareness", Abrv = "LSTDA", AppAbrv = "LSTDA", Slug = "display_awareness", Description = "Display Awareness", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled PreApp", Abrv = "LSTCPA", AppAbrv = "LSTCPA", Slug = "cancelled_pre_app", Description = "Cancelled PreApp", ProductDependency = "PreProduct" },
                new() { Name = "Loan Hub", Abrv = "LSTLH", AppAbrv = "LSTLH", Slug = "loan_hub", Description = "Loan Hub", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled Awareness", Abrv = "LSTCA", AppAbrv = "LSTCA", Slug = "cancelled_awareness", Description = "Cancelled Awareness", ProductDependency = "PreProduct" },
                new() { Name = "Restricted Due To Denied Loans", Abrv = "LSTEDL", AppAbrv = "LSTEDL", Slug = "Denied Loan", Description = "Restricted Due To Denied Loans", ProductDependency = "PreProduct" },
                new() { Name = "ESignature Pending", Abrv = "LSTESP", AppAbrv = "LSTESP", Slug = "esignature_pending", Description = "ESignature Pending", ProductDependency = "Product" },
                new() { Name = "Excluded Missing Email", Abrv = "LSTEME", AppAbrv = "LSTEME", Slug = "excluded_missing_email", Description = "Excluded Missing Email", ProductDependency = "PreProduct" },
                new() { Name = "ESignature Error", Abrv = "LSTESE", AppAbrv = "LSTESE", Slug = "esignature_error", Description = "ESignature Error", ProductDependency = "Product" },
                new() { Name = "Restricted Due To Bankruptcy", Abrv = "LSTBEX", AppAbrv = "LSTBEX", Slug = "bankruptcy_exclusion", Description = "Restricted Due To Bankruptcy", ProductDependency = "PreProduct" },
                new() { Name = "Excluded By Age", Abrv = "LSTAGE", AppAbrv = "LSTAGE", Slug = "age_exclusion", Description = "Excluded By Age", ProductDependency = "PreProduct" },
                new() { Name = "Troubled Debt Exclusion", Abrv = "LSTTDE", AppAbrv = "LSTTDE", Slug = "troubled_debt_exclusion", Description = "Troubled Debt Exclusion", ProductDependency = "PreProduct" },
                new() { Name = "Restricted Due To Signature Pending", Abrv = "LSTRESP", AppAbrv = "LSTRESP", Slug = "restricted_esignature_pending", Description = "Restricted Due To Signature Pending", ProductDependency = "PreProduct" },
                new() { Name = "Not Funded", Abrv = "LSTNF", AppAbrv = "LSTNF", Slug = "not_funded", Description = "Not Funded", ProductDependency = "Product" },
                new() { Name = "Excluded Personal Loan", Abrv = "LSTEPL", AppAbrv = "LSTEPL", Slug = "excluded_personal_loan", Description = "Excluded Personal Loan", ProductDependency = "PreProduct" },
                new() { Name = "Maintenance", Abrv = "LSTM", AppAbrv = "LSTM", Slug = "maintenance", Description = "Maintenance", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Joint Account", Abrv = "LSTJAEX", AppAbrv = "LSTJAEX", Slug = "joint_account_exclusion", Description = "Excluded Joint Account", ProductDependency = "PreProduct" },
                new() { Name = "Fraud Control", Abrv = "LSTFC", AppAbrv = "LSTFC", Slug = "fraud_control", Description = "Fraud Control", ProductDependency = "PreProduct" },
                new() { Name = "Fraud Control Code Expired", Abrv = "LSTFCE", AppAbrv = "LSTFCE", Slug = "fraud_control_expired", Description = "Fraud Control Code Expired", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Fraud Control", Abrv = "LSEFC", AppAbrv = "LSEFC", Slug = "excluded_fraud_control", Description = "Excluded Fraud Control", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Missing Email and Phone", Abrv = "LSEMEP", AppAbrv = "LSEMEP", Slug = "excluded_missing_email_and_phone", Description = "Excluded Missing Email and Phone", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled Fraud Control", Abrv = "LSTCFC", AppAbrv = "LSTCFC", Slug = "cancelled_fraud_control", Description = "Cancelled Fraud Control", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Loan Funding Wait Period", Abrv = "LSELFWP", AppAbrv = "LSELFWP", Slug = "excluded_loan_funding_wait_period", Description = "Excluded Loan Funding Wait Period", ProductDependency = "PreProduct" },
                new() { Name = "QCF Audit", Abrv = "LSTQCFA", AppAbrv = "LSTQCFA", Slug = "qcf_audit", Description = "QCF Audit", ProductDependency = "None" },
                new() { Name = "Blocklist Exclusion", Abrv = "LSTBLE", AppAbrv = "LSTBLE", Slug = "blocklist_exclusion", Description = "Blocklist Exclusion", ProductDependency = "PreProduct" },
                new() { Name = "Funding Complete", Abrv = "LSTCMP", AppAbrv = "LSTCMP", Slug = "completed", Description = "Funding Complete", ProductDependency = "Product" },
                new() { Name = "Declined Data Collection", Abrv = "LSTDEDC", AppAbrv = "LSTDEDC", Slug = "declinde_data_collection", Description = "Declined Data Collection", ProductDependency = "None" },
                new() { Name = "Data Collection", Abrv = "LSTDC", AppAbrv = "LSTDC", Slug = "data_collection", Description = "Data Collection", ProductDependency = "None" },
                new() { Name = "In Process", Abrv = "LSTIP", AppAbrv = "LSTIP", Slug = "in_progress", Description = "In Progress", ProductDependency = "None" },
            };
            foreach (var item in loanStatusTypes.Select((lsType, index) => new { index, lsType }))
            {
                item.lsType.Id = Guid.NewGuid();
                item.lsType.FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId;
                item.lsType.DateCreatedUtc = DateTime.UtcNow;
                item.lsType.DateUpdatedUtc = DateTime.UtcNow;
                item.lsType.TimeStamp = [];
                item.lsType.IsDeleted = false;
            }
            DbContext.LoanStatusTypes.AddRange(loanStatusTypes);
            DbContext.SaveChanges();

            InvoicePlan invoicePlan = new InvoicePlan
            {
                Id = Guid.NewGuid(),
                CustomerId = "1234",
                ResellerName = null,
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                TransactionType = "AASD",
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId
            };
            DbContext.InvoicePlans.Add(invoicePlan);
            DbContext.SaveChanges();

            InvoicePlanItemType invoicePlanItemType = new InvoicePlanItemType
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "ITMS",
                Abrv = "ITMS",
                Slug = "monthly_support",
                Name = "Monthly Support",
                Description = "Monthly Support",
                RelationshipType = "IndividualItem",
                PricingType = "SingleItem",
                SortOrder = 0,
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                IsDeleted = false,
            };
            DbContext.InvoicePlanItemTypes.Add(invoicePlanItemType);
            DbContext.SaveChanges();

            InvoicePlanItemType invoicePlanItemType2 = new InvoicePlanItemType
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "GBLS",
                Abrv = "GBLS",
                Slug = "GBLS",
                Name = "GBLS",
                Description = "GBLS",
                RelationshipType = "IndividualItem",
                PricingType = "SingleItem",
                SortOrder = 0,
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                IsDeleted = false,
            };
            DbContext.InvoicePlanItemTypes.Add(invoicePlanItemType2);
            DbContext.SaveChanges();

            InvoicePlanItemType invoicePlanItemType3 = new InvoicePlanItemType
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "GBPR",
                Abrv = "GBPR",
                Slug = "GBPR",
                Name = "GBPR",
                Description = "GBPR",
                RelationshipType = "IndividualItem",
                PricingType = "SingleItem",
                SortOrder = 0,
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                IsDeleted = false,
            };
            DbContext.InvoicePlanItemTypes.Add(invoicePlanItemType3);
            DbContext.SaveChanges();

            InvoicePlanItemType invoicePlanItemType4 = new InvoicePlanItemType
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "ITMLS",
                Abrv = "ITMLS",
                Slug = "ITMLS",
                Name = "ITMLS",
                Description = "ITMLS",
                RelationshipType = "IndividualItem",
                PricingType = "SingleItem",
                SortOrder = 0,
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                IsDeleted = false,
            };
            DbContext.InvoicePlanItemTypes.Add(invoicePlanItemType4);
            DbContext.SaveChanges();

            PricingPlanType pricingPlanType = new PricingPlanType
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "PTSS",
                Abrv = "PTSS",
                Slug = "single_scenario",
                Name = "Single Scenario",
                Description = "Single Scenario Pricing Plan type",
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                IsDeleted = false,
            };
            DbContext.PricingPlanTypes.Add(pricingPlanType);
            DbContext.SaveChanges();

            PricingPlanType pricingPlanType2 = new PricingPlanType
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "PTED",
                Abrv = "PTED",
                Slug = "effective_date",
                Name = "Effective Date",
                Description = "Effective Date Pricing Plan type",
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                IsDeleted = false,
            };
            DbContext.PricingPlanTypes.Add(pricingPlanType2);
            DbContext.SaveChanges();

            InvoicePlanItem invoicePlanItem = new InvoicePlanItem
            {
                Id = Guid.NewGuid(),
                InvoicePlanId = invoicePlan.Id,
                InvoicePlanItemTypeId = invoicePlanItemType.Id,
                PricingPlanTypeId = pricingPlanType.Id,
                ItemId = "MS001",
                Description = null,
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
            };
            DbContext.InvoicePlanItems.Add(invoicePlanItem);
            DbContext.SaveChanges();

            PricingScenarioType pricingScenarioType = new PricingScenarioType
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "PSFX",
                Abrv = "PSFX",
                Slug = "fixed_pricing",
                Name = "Fixed Pricing",
                Description = "Fixed Pricing",
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                IsDeleted = false,
            };
            DbContext.PricingScenarioTypes.Add(pricingScenarioType);
            DbContext.SaveChanges();

            PricingScenarioType pricingScenarioType2 = new PricingScenarioType
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "PSVL",
                Abrv = "PSVL",
                Slug = "volume_pricing",
                Name = "Volume Pricing",
                Description = "Volume Pricing",
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                IsDeleted = false,
            };
            DbContext.PricingScenarioTypes.Add(pricingScenarioType2);
            DbContext.SaveChanges();

            PricingScenarioType pricingScenarioType3 = new PricingScenarioType
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "PSTR",
                Abrv = "PSTR",
                Slug = "tiered_pricing",
                Name = "Tiered Pricing",
                Description = "Tiered Pricing",
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                IsDeleted = false,
            };
            DbContext.PricingScenarioTypes.Add(pricingScenarioType3);
            DbContext.SaveChanges();

            PricingScenario pricingScenario = new PricingScenario
            {
                Id = Guid.NewGuid(),
                InvoicePlanItemId = invoicePlanItem.Id,
                PricingPlanTypeId = pricingPlanType.Id,
                PricingScenarioTypeId = pricingScenarioType.Id,
                FixedPrice = 10.99M,
                EffectiveStartDate = null,
                EffectiveEndDate = null,
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                DisplayAveragePriceForVolumePricing = false,
            };
            DbContext.PricingScenarios.Add(pricingScenario);
            DbContext.SaveChanges();

            var financialInstitutionMember = new FinancialInstitutionMember
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                FirstName = "Bob",
                MiddleName = "X",
                LastName = "Test",
                City = "Naperville",
                State = "IL",
                Zip = null,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                AccountId = "1",
                MemberIdHash = string.Empty,
                MemberIdMask = string.Empty
            };
            DbContext.FinancialInstitutionMembers.Add(financialInstitutionMember);
            DbContext.SaveChanges();

            Data.Models.LoanApplication loanApplication = new Data.Models.LoanApplication
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionMemberId = financialInstitutionMember.Id,
                SelectedProductId = product.Id,
                DecisionModelId = null,
                ActiveDuty = null,
                AmountFinanced = 0,
                AmountBorrowed = 0,
                AnnualPercentageRate = 0,
                FinanceCharge = 0,
                TotalOfPayments = 0,
                LoanStatusId = loanStatusTypes.Single(o => o.Abrv == "LSTFP").Id,
                IsInProgress = true,
                PurposeOfLoanId = null,
                MarketingCampaignCode = null,
                AccountId = null,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                AppId = 0,
                AdverseActionNoticeDocumentId = null,
                MemberBaseAccountId = null,
                LoanAmount = null,
                MatureDateUtc = default,
                IsMoved = false,
                SelectedLanguageId = null,
                IsFinancialCoachingSuitable = null,
                FeeRefundStep = "0",
                AverageMonthlyCheckingDepositBalance = null,
                DefaultAccountForTransactionId = null,
                IsBankrupt = null,
                SelectedAccountForTransactionId = null,
                IsEsignatureFlow = null,
                DateSigned = null,
                SelectedLoanHubItemId = null,
                MovedAppLogs = false,
                CurrentStep = 0,
                FirstStep = 0,
                NextStep = 0,
                NumberOfDeniedLoans = null,
                PayOffAccountId = null,
                PaymentGuardReportStatusId = 0,
                RetryCount = 0,
            };
            DbContext.LoanApplications.Add(loanApplication);
            DbContext.SaveChanges();

            LoanApplicationSettingsLog loanApplicationSettingsLog = new LoanApplicationSettingsLog
            {
                Id = Guid.NewGuid(),
                LoanApplicationId = loanApplication.Id,
                SettingKey = "FraudControl",
                SettingValue = true,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
            };
            DbContext.LoanApplicationSettingsLogs.Add(loanApplicationSettingsLog);
            DbContext.SaveChanges();
        }
    }


    [Theory, AutoSubstituteData]
    public async Task GenerateInvoiceTestAsync(IFixture fixture)
    {
        var rig = new InvoiceServiceTestRig(fixture);

        var res = await rig.InvoiceService.GenerateInvoiceAsync(DateTime.Parse("1/1/2025"), DateTime.UtcNow.AddDays(1));

        Assert.NotNull(res);
        Assert.NotNull(res);

        res.InvoiceItems.Count().Should().Be(1);
        res.InvoiceItems.First().UnitPrice.Should().Be(10.99M);

    }

}
