using AutoFixture;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.FileSystem;
using QCash.Service.Infrastructure;
using QCash.Service.Services;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Utils.Settings;
using FluentAssertions;
using Xunit;

namespace QCash.Service.UnitTest.Services;

public class ReportServiceTests
{
    private class ReportServiceTestRig
    {
        public QCashContext DbContext { get; set; }
        private ILogger<ReportService> logger { get; set; }
        //private IGuidExtensionService guidExtensionService { get; set; }
        private IOptions<ApplicationOptions> options { get; set; }
        private IMailSender mailSender { get; set; }
        private ErrorHelper errorHelper { get; set; }
        public IReportService ReportService { get; set; }
        private IFileSystemProvider fileSystemProvider { get; set; }
        private ILogger<ErrorHelper> loggerEH { get; set; }
        private IFunctionAppClient functionAppClient { get; set; }
        public FinancialInstitution? FinancialInstitution { get; set; }

        public ReportServiceTestRig(IFixture fixture)
        {
            DbContext = fixture.Freeze<QCashContext>();
            
            logger = Substitute.For<ILogger<ReportService>>();
            loggerEH = Substitute.For<ILogger<ErrorHelper>>();
            fileSystemProvider = Substitute.For<IFileSystemProvider>();
            errorHelper = new ErrorHelper(loggerEH, fileSystemProvider);
            functionAppClient = Substitute.For<IFunctionAppClient>();
            options = new FakeApplicationOptions();
            mailSender = Substitute.For<IMailSender>();

            ReportService = new ReportService(DbContext, functionAppClient, errorHelper, options, mailSender, logger);
            
            BuildOutTestData();
        }

        public class FakeApplicationOptions : IOptions<ApplicationOptions>
        {
            public ApplicationOptions Value
            {
                get
                {
                    return new ApplicationOptions
                    {
                        Csp = string.Empty,
                        DisplayDiagnosticPages = false,
                        EnableHiddenPages = false,
                        PageSizes =
                        [
                        ],
                        ReportingMaxRecords = 0,
                        ShowExceptionStackTrace = false,
                        WizardURL = string.Empty,
                        MiddlewareRouterBaseUrl = string.Empty,
                        CoreConnectionStatusExpirationSeconds = 0,
                        GetCoreConnectionStatusTimeoutSeconds = 0,
                        MicrobuiltBaseUrl = string.Empty,
                        MicrobuiltClientId = string.Empty,
                        MicrobuiltClientSecret = string.Empty,
                        SaveAdminLogs = false,
                        RetryServiceQueueName = string.Empty,
                        RetryServiceMaxRetries = 0,
                        RetryServiceRetryInterval = 0,
                        NoReplyEmailAddress = string.Empty,
                        TwilioSid = string.Empty,
                        TwilioToken = string.Empty,
                        QCashNotificationPhoneNumber = string.Empty
                    };
                }
            }
        }

        private void BuildOutTestData()
        {
            FinancialInstitution = new FinancialInstitution
            {
                Id = IFixtureExtensions.FinancialInstitutionId,
                Slug = IFixtureExtensions.FiSlug,
                Address = "",
                City = "",
                ContactName = "",
                ContactPhone = "",
                GlLoanAccount = "",
                MailingAddress = "",
                Name = "Test1 Name",
                NoReplySubdomain = "TestSubd1",
                State = "",
                Zip = "12345",
                TimeStamp = [],
            };

            DbContext.FinancialInstitutions.Add(FinancialInstitution);

            LoanType loanTypeIB;
            DbContext.LoanTypes.Add(loanTypeIB = new LoanType()
            {
                Id = Guid.NewGuid(),
                Name = "Interest Based",
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Abrv = "IB",
                AppAbrv = "IB",
                TimeStamp = [],
            });
            DbContext.SaveChanges();

            LoanCategory loanCategory = new LoanCategory
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "UC",
                Abrv = "UC",
                Slug = "unsecured_closed_end",
                Name = "Unsecured Closed-End",
                Description = "Unsecured Closed-End (UCE)",
                IsOpen = false,
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                TimeStamp = [],
                IsDeleted = false,
            };
            DbContext.LoanCategories.Add(loanCategory);
            DbContext.SaveChanges();

            Product product = new Product()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Slug = "ProductSlug1",
                Name = "Product1",
                Abrv = "",
                AppAbrv = "",
                InvoicePlanId = Guid.NewGuid(),
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                LoanTypeId = loanTypeIB.Id,
                GlTranslationAccount = "",
                InvoiceId = "",
                IsActive = true,
                TimeStamp = [],
                LoanCategory = loanCategory,
            };
            DbContext.Products.Add(product);
            DbContext.SaveChanges();

            DecisionModelType decisionModelType = new DecisionModelType()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "TR",
                Abrv = "TR",
                Slug = "T",
                Name = "traditional",
                Description = "Traditional model type",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                IsDeleted = false,
                TimeStamp = [],
            };
            DbContext.DecisionModelTypes.Add(decisionModelType);

            DecisionModelStatus decisionModelStatus = new DecisionModelStatus()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "INCT",
                Abrv = "INCT",
                Slug = "inactive",
                Name = "Inactive",
                TimeStamp = [],
            };
            DecisionModelStatus decisionModelStatus2 = new DecisionModelStatus()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "ACTV",
                Abrv = "ACTV",
                Slug = "active",
                Name = "Active",
                TimeStamp = [],
            };
            DecisionModelStatus decisionModelStatus3 = new DecisionModelStatus()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "ARHV",
                Abrv = "ARHV",
                Slug = "archived",
                Name = "Archived",
                TimeStamp = [],
            };
            DbContext.DecisionModelStatuses.Add(decisionModelStatus);
            DbContext.DecisionModelStatuses.Add(decisionModelStatus2);
            DbContext.DecisionModelStatuses.Add(decisionModelStatus3);

            //add decisionmodel and productdecisionmodel
            DecisionModel decisionModel = new DecisionModel()
            {
                Id = Guid.NewGuid(),
                Description = "test",
                FiHandle = "testModel",
                StatusDate = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                DateCreatedUtc = DateTime.UtcNow,
                ModelStatus = decisionModelStatus2,
                ModelType = decisionModelType,
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                TimeStamp = [],
            };
            DbContext.DecisionModels.Add(decisionModel);
            DbContext.SaveChanges();

            ProductDecisionModel productDecisionModel = new ProductDecisionModel()
            {
                Id = Guid.NewGuid(),
                DateUpdatedUtc = DateTime.UtcNow,
                DateCreatedUtc = DateTime.UtcNow,
                Product = product,
                DecisionModel = decisionModel,
                TimeStamp = [],
            };
            DbContext.ProductDecisionModels.Add(productDecisionModel);
            DbContext.SaveChanges();

            ScoreType scoreType = new ScoreType()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                DateUpdatedUtc = DateTime.UtcNow,
                DateCreatedUtc = DateTime.UtcNow,
                AppAbrv = "TransformationScore",
                Abrv = "TransformationScore",
                Slug = "transformation_score",
                Name = "Transformation Score",
                Description = "Transformation Score Type",
                IsDeleted = false,
                TimeStamp = [],
            };
            DbContext.ScoreTypes.Add(scoreType);
            DbContext.SaveChanges();

            var fiConfig = new FinancialInstitutionConfiguration
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Description = "Test Configuration 1",
                DefaultProviderId = Guid.NewGuid(),
                Active = true,
                TimeStamp = []
            };
            DbContext.FinancialInstitutionConfigurations.Add(fiConfig);
            DbContext.SaveChanges();

            var loanStatusTypes = new List<LoanStatusType> {
                new() { Name = "Eligible Account Exclusion", Abrv = "LSTEAE", AppAbrv = "LSTEAE", Slug = "eligible_account_exclusion", Description = "Eligible Account Exclusion", ProductDependency = "PreProduct" },
                new() { Name = "Contact Info Changed Exclusion", Abrv = "LSTCICE", AppAbrv = "LSTCICE", Slug = "contact_info_changed_exclusion", Description = "Contact Info Changed Exclusion", ProductDependency = "PreProduct" },
                new() { Name = "Funding Pending", Abrv = "LSTFP", AppAbrv = "LSTFP", Slug = "funding_pending", Description = "Funding Pending", ProductDependency = "Product" },
                new() { Name = "Excluded by Bad Address", Abrv = "LSTBAE", AppAbrv = "LSTBAE", Slug = "bad_address_exclusion", Description = "Excluded by Bad Address", ProductDependency = "PreProduct" },
                new() { Name = "Processing Error", Abrv = "LSTPE", AppAbrv = "LSTPE", Slug = "processing_error", Description = "Processing Error", ProductDependency = "Product" },
                new() { Name = "Excluded by Bad Email", Abrv = "LSTBEE", AppAbrv = "LSTBEE", Slug = "bad_email_exclusion", Description = "Excluded by Bad Email", ProductDependency = "PreProduct" },
                new() { Name = "Approved", Abrv = "LSTA", AppAbrv = "LSTA", Slug = "approved", Description = "Approved", ProductDependency = "PreProduct" },
                new() { Name = "Denied", Abrv = "LSTD", AppAbrv = "LSTD", Slug = "denied", Description = "Denied", ProductDependency = "PreProduct" },
                new() { Name = "Canceled", Abrv = "LSTC", AppAbrv = "LSTC", Slug = "canceled", Description = "Canceled", ProductDependency = "None" },
                new() { Name = "Declined TILA", Abrv = "LSTDET", AppAbrv = "LSTDET", Slug = "declined_tila", Description = "Declined TILA", ProductDependency = "Product" },
                new() { Name = "Declined eConsent", Abrv = "LSTDEE", AppAbrv = "LSTDEE", Slug = "declined_econsent", Description = "Declined eConsent", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Account", Abrv = "LSTEA", AppAbrv = "LSTEA", Slug = "excluded_account", Description = "Excluded Account", ProductDependency = "PreProduct" },
                new() { Name = "Excluded State", Abrv = "LSTES", AppAbrv = "LSTES", Slug = "excluded_state", Description = "Excluded State", ProductDependency = "PreProduct" },
                new() { Name = "Initiate", Abrv = "LSTI", AppAbrv = "LSTI", Slug = "initiate", Description = "Initiate", ProductDependency = "PreProduct" },
                new() { Name = "Military No Product", Abrv = "LSTMNP", AppAbrv = "LSTMNP", Slug = "military_no_product", Description = "Military No Product", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled Before DE", Abrv = "LSTCBD", AppAbrv = "LSTCBD", Slug = "cancelled_before_de", Description = "Cancelled Before DE", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled After DE", Abrv = "LSTCAD", AppAbrv = "LSTCAD", Slug = "cancelled_after_de", Description = "Cancelled After DE", ProductDependency = "PreProduct" },
                new() { Name = "Restricted due to Loan in Process", Abrv = "LSTLIP", AppAbrv = "LSTLIP", Slug = "loan_in_process", Description = "Restricted due to Loan in Process", ProductDependency = "PreProduct" },
                new() { Name = "Is eConsent", Abrv = "LSTIE", AppAbrv = "LSTDIE", Slug = "is_econsent", Description = "Is eConsent", ProductDependency = "PreProduct" },
                new() { Name = "Accept eConsent", Abrv = "LSTAE", AppAbrv = "LSTDAE", Slug = "accept_econsent", Description = "Accept eConsent", ProductDependency = "PreProduct" },
                new() { Name = "Loan Application", Abrv = "LSTLA", AppAbrv = "LSTLA", Slug = "loan_application", Description = "Loan Application", ProductDependency = "PreProduct" },
                new() { Name = "Accept TILA", Abrv = "LSTAT", AppAbrv = "LSTAT", Slug = "accept_tila", Description = "Accept TILA", ProductDependency = "Product" },
                new() { Name = "Display TILA", Abrv = "LSTDT", AppAbrv = "LSTDT", Slug = "display_tila", Description = "Display TILA", ProductDependency = "Product" },
                new() { Name = "Loan Application Fee", Abrv = "LSTLAF", AppAbrv = "LSTLAF", Slug = "loan_fee", Description = "Loan Application Fee", ProductDependency = "PreProduct" },
                new() { Name = "Payoff Initiate", Abrv = "LSTPOINI", AppAbrv = "LSTPOINI", Slug = "payoff_initiate", Description = "Payoff Initiate", ProductDependency = "None" },
                new() { Name = "Payoff Completed", Abrv = "LSTPOCMP", AppAbrv = "LSTPOCMP", Slug = "payoff_complete", Description = "Payoff Completed", ProductDependency = "None" },
                new() { Name = "Payoff Cancelled", Abrv = "LSTPOCNC", AppAbrv = "LSTPOCNC", Slug = "payoff_cancel", Description = "Payoff Cancelled", ProductDependency = "None" },
                new() { Name = "Payoff Error", Abrv = "LSTPOERR", AppAbrv = "LSTPOERR", Slug = "payoff_error", Description = "Payoff Error", ProductDependency = "None" },
                new() { Name = "Payoff Complete", Abrv = "LSTPOPOC", AppAbrv = "LSTPOPOC", Slug = "payoff_po_complete", Description = "Payoff Complete", ProductDependency = "Product" },
                new() { Name = "Payoff Failed", Abrv = "LSTPOPOF", AppAbrv = "LSTPOPOF", Slug = "payoff_po_failed", Description = "Payoff Failed", ProductDependency = "Product" },
                new() { Name = "Local", Abrv = "mla_local", AppAbrv = "mla_local", Slug = "mla_local", Description = "MLA Local", ProductDependency = "None" },
                new() { Name = "Remote", Abrv = "mla_remote", AppAbrv = "mla_remote", Slug = "mla_remote", Description = "MLA Remote", ProductDependency = "None" },
                new() { Name = "Error", Abrv = "mla_error", AppAbrv = "mla_error", Slug = "mla_error", Description = "MLA Error", ProductDependency = "None" },
                new() { Name = "Display Loan Landing", Abrv = "LSTDL", AppAbrv = "LSTDL", Slug = "display_loan_landing", Description = "Display Loan Landing", ProductDependency = "PreProduct" },
                new() { Name = "Display Awareness", Abrv = "LSTDA", AppAbrv = "LSTDA", Slug = "display_awareness", Description = "Display Awareness", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled PreApp", Abrv = "LSTCPA", AppAbrv = "LSTCPA", Slug = "cancelled_pre_app", Description = "Cancelled PreApp", ProductDependency = "PreProduct" },
                new() { Name = "Loan Hub", Abrv = "LSTLH", AppAbrv = "LSTLH", Slug = "loan_hub", Description = "Loan Hub", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled Awareness", Abrv = "LSTCA", AppAbrv = "LSTCA", Slug = "cancelled_awareness", Description = "Cancelled Awareness", ProductDependency = "PreProduct" },
                new() { Name = "Restricted Due To Denied Loans", Abrv = "LSTEDL", AppAbrv = "LSTEDL", Slug = "Denied Loan", Description = "Restricted Due To Denied Loans", ProductDependency = "PreProduct" },
                new() { Name = "ESignature Pending", Abrv = "LSTESP", AppAbrv = "LSTESP", Slug = "esignature_pending", Description = "ESignature Pending", ProductDependency = "Product" },
                new() { Name = "Excluded Missing Email", Abrv = "LSTEME", AppAbrv = "LSTEME", Slug = "excluded_missing_email", Description = "Excluded Missing Email", ProductDependency = "PreProduct" },
                new() { Name = "ESignature Error", Abrv = "LSTESE", AppAbrv = "LSTESE", Slug = "esignature_error", Description = "ESignature Error", ProductDependency = "Product" },
                new() { Name = "Restricted Due To Bankruptcy", Abrv = "LSTBEX", AppAbrv = "LSTBEX", Slug = "bankruptcy_exclusion", Description = "Restricted Due To Bankruptcy", ProductDependency = "PreProduct" },
                new() { Name = "Excluded By Age", Abrv = "LSTAGE", AppAbrv = "LSTAGE", Slug = "age_exclusion", Description = "Excluded By Age", ProductDependency = "PreProduct" },
                new() { Name = "Troubled Debt Exclusion", Abrv = "LSTTDE", AppAbrv = "LSTTDE", Slug = "troubled_debt_exclusion", Description = "Troubled Debt Exclusion", ProductDependency = "PreProduct" },
                new() { Name = "Restricted Due To Signature Pending", Abrv = "LSTRESP", AppAbrv = "LSTRESP", Slug = "restricted_esignature_pending", Description = "Restricted Due To Signature Pending", ProductDependency = "PreProduct" },
                new() { Name = "Not Funded", Abrv = "LSTNF", AppAbrv = "LSTNF", Slug = "not_funded", Description = "Not Funded", ProductDependency = "Product" },
                new() { Name = "Excluded Personal Loan", Abrv = "LSTEPL", AppAbrv = "LSTEPL", Slug = "excluded_personal_loan", Description = "Excluded Personal Loan", ProductDependency = "PreProduct" },
                new() { Name = "Maintenance", Abrv = "LSTM", AppAbrv = "LSTM", Slug = "maintenance", Description = "Maintenance", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Joint Account", Abrv = "LSTJAEX", AppAbrv = "LSTJAEX", Slug = "joint_account_exclusion", Description = "Excluded Joint Account", ProductDependency = "PreProduct" },
                new() { Name = "Fraud Control", Abrv = "LSTFC", AppAbrv = "LSTFC", Slug = "fraud_control", Description = "Fraud Control", ProductDependency = "PreProduct" },
                new() { Name = "Fraud Control Code Expired", Abrv = "LSTFCE", AppAbrv = "LSTFCE", Slug = "fraud_control_expired", Description = "Fraud Control Code Expired", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Fraud Control", Abrv = "LSEFC", AppAbrv = "LSEFC", Slug = "excluded_fraud_control", Description = "Excluded Fraud Control", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Missing Email and Phone", Abrv = "LSEMEP", AppAbrv = "LSEMEP", Slug = "excluded_missing_email_and_phone", Description = "Excluded Missing Email and Phone", ProductDependency = "PreProduct" },
                new() { Name = "Cancelled Fraud Control", Abrv = "LSTCFC", AppAbrv = "LSTCFC", Slug = "cancelled_fraud_control", Description = "Cancelled Fraud Control", ProductDependency = "PreProduct" },
                new() { Name = "Excluded Loan Funding Wait Period", Abrv = "LSELFWP", AppAbrv = "LSELFWP", Slug = "excluded_loan_funding_wait_period", Description = "Excluded Loan Funding Wait Period", ProductDependency = "PreProduct" },
                new() { Name = "QCF Audit", Abrv = "LSTQCFA", AppAbrv = "LSTQCFA", Slug = "qcf_audit", Description = "QCF Audit", ProductDependency = "None" },
                new() { Name = "Blocklist Exclusion", Abrv = "LSTBLE", AppAbrv = "LSTBLE", Slug = "blocklist_exclusion", Description = "Blocklist Exclusion", ProductDependency = "PreProduct" },
                new() { Name = "Funding Complete", Abrv = "LSTCMP", AppAbrv = "LSTCMP", Slug = "completed", Description = "Funding Complete", ProductDependency = "Product" },
                new() { Name = "Declined Data Collection", Abrv = "LSTDEDC", AppAbrv = "LSTDEDC", Slug = "declinde_data_collection", Description = "Declined Data Collection", ProductDependency = "None" },
                new() { Name = "Data Collection", Abrv = "LSTDC", AppAbrv = "LSTDC", Slug = "data_collection", Description = "Data Collection", ProductDependency = "None" },
                new() { Name = "In Process", Abrv = "LSTIP", AppAbrv = "LSTIP", Slug = "in_progress", Description = "In Progress", ProductDependency = "None" },
            };
            foreach (var item in loanStatusTypes.Select((lsType, index) => new { index, lsType }))
            {
                item.lsType.Id = Guid.NewGuid();
                item.lsType.FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId;
                item.lsType.DateCreatedUtc = DateTime.UtcNow;
                item.lsType.DateUpdatedUtc = DateTime.UtcNow;
                item.lsType.TimeStamp = [];
                item.lsType.IsDeleted = false;
            }
            DbContext.LoanStatusTypes.AddRange(loanStatusTypes);
            DbContext.SaveChanges();

            var setting = new Setting
            {
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Id = Guid.NewGuid(),
                IsActive = true,
                FilenameAan = "",
                FilenameTila = "",
                LoanIdSource = "",
                Mla = "",
                TimeZone = "Central Standard Time",
                MonitorAccount = "",
                TilaCalculatorSource = "",
                AppLogsFileNameTemplate = "",
                Bankruptcy = "",
                FilenameEConsent = "",
                TimeStamp = [],
            };

            DbContext.Settings.Add(setting);
            DbContext.SaveChanges();

            var leSetting = new LoanExclusionSetting
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                DeniedLoansExclusion = false,
                NumberOfDeniedLoans = 0,
                DeniedLoansTreshold = 0,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
            };
            DbContext.LoanExclusionSettings.Add(leSetting);
            DbContext.SaveChanges();

            var saTypes = new SavingAccountType
            {
                Id = Guid.NewGuid(),
                Value = "1",
                Description = "REGULAR SAVINGS",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            };
            DbContext.SavingAccountTypes.Add(saTypes);
            DbContext.SaveChanges();

            var financialInstitutionMember = new FinancialInstitutionMember
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                FirstName = "Bob",
                MiddleName = "X",
                LastName = "Test",
                City = "Naperville",
                State = "IL",
                Zip = null,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                AccountId = "1",
                MemberIdHash = string.Empty,
                MemberIdMask = string.Empty
            };
            DbContext.FinancialInstitutionMembers.Add(financialInstitutionMember);
            DbContext.SaveChanges();

            //add completed LoanApplication
            Data.Models.LoanApplication loanApplication = new Data.Models.LoanApplication
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionMemberId = financialInstitutionMember.Id,
                SelectedProductId = product.Id,
                DecisionModelId = null,
                ActiveDuty = null,
                AmountFinanced = 750,
                AmountBorrowed = 750,
                AnnualPercentageRate = 2,
                FinanceCharge = 2,
                TotalOfPayments = 800,
                LoanStatusId = loanStatusTypes.Single(o => o.Abrv == "LSTCMP").Id,
                IsInProgress = false,
                PurposeOfLoanId = null,
                MarketingCampaignCode = null,
                AccountId = null,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                AppId = 0,
                AdverseActionNoticeDocumentId = null,
                MemberBaseAccountId = null,
                LoanAmount = 987.65M,
                MatureDateUtc = default,
                IsMoved = false,
                SelectedLanguageId = null,
                IsFinancialCoachingSuitable = null,
                FeeRefundStep = "0",
                AverageMonthlyCheckingDepositBalance = null,
                DefaultAccountForTransactionId = null,
                IsBankrupt = null,
                SelectedAccountForTransactionId = null,
                IsEsignatureFlow = null,
                DateSigned = null,
                SelectedLoanHubItemId = null,
                MovedAppLogs = false,
                CurrentStep = 0,
                FirstStep = 0,
                NextStep = 0,
                NumberOfDeniedLoans = null,
                PayOffAccountId = null,
                PaymentGuardReportStatusId = 0,
                RetryCount = 0,
            };
            DbContext.LoanApplications.Add(loanApplication);
            DbContext.SaveChanges();


            //add denied LoanApplication
            Data.Models.LoanApplication loanApplication2 = new Data.Models.LoanApplication
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionMemberId = financialInstitutionMember.Id,
                SelectedProductId = product.Id,
                DecisionModelId = null,
                ActiveDuty = null,
                AmountFinanced = 650,
                AmountBorrowed = 650,
                AnnualPercentageRate = 2,
                FinanceCharge = 2,
                TotalOfPayments = 700,
                LoanStatusId = loanStatusTypes.Single(o => o.Abrv == "LSTD").Id,
                IsInProgress = false,
                PurposeOfLoanId = null,
                MarketingCampaignCode = null,
                AccountId = null,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                AppId = 0,
                AdverseActionNoticeDocumentId = null,
                MemberBaseAccountId = null,
                LoanAmount = null,
                MatureDateUtc = default,
                IsMoved = false,
                SelectedLanguageId = null,
                IsFinancialCoachingSuitable = null,
                FeeRefundStep = "0",
                AverageMonthlyCheckingDepositBalance = null,
                DefaultAccountForTransactionId = null,
                IsBankrupt = null,
                SelectedAccountForTransactionId = null,
                IsEsignatureFlow = null,
                DateSigned = null,
                SelectedLoanHubItemId = null,
                MovedAppLogs = false,
                CurrentStep = 0,
                FirstStep = 0,
                NextStep = 0,
                NumberOfDeniedLoans = null,
                PayOffAccountId = null,
                PaymentGuardReportStatusId = 0,
                RetryCount = 0,
            };
            DbContext.LoanApplications.Add(loanApplication2);
            DbContext.SaveChanges();

            AanReasonDefaultText reason = new AanReasonDefaultText
            {
                Id = Guid.NewGuid(),
                Slug = "denied_reason",
                Name = "Denied Reason",
                Description = "Denied Reason",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                Code = null
            };
            DbContext.AanReasonDefaultTexts.Add(reason);
            DbContext.SaveChanges();

            AanReasonDefaultText reason2 = new AanReasonDefaultText
            {
                Id = Guid.NewGuid(),
                Slug = "denied2_reason",
                Name = "Denied2 Reason",
                Description = "Denied2 Reason",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                Code = null
            };
            DbContext.AanReasonDefaultTexts.Add(reason2);
            DbContext.SaveChanges();

            AANReasonOverrideText reason2override = new AANReasonOverrideText
            {
                Id = Guid.NewGuid(),
                AanReasonDefaultTextId = reason2.Id,
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Description = "Denial2XYZ",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
            };
            DbContext.AANReasonOverrideTexts.Add(reason2override);
            DbContext.SaveChanges();

            LoanApplicationAan laaan = new LoanApplicationAan
            {
                Id = Guid.NewGuid(),
                LoanApplicationId = loanApplication2.Id,
                AanReasonId = reason.Id,
                ProductId = product.Id,
                ShowInAanDocument = true,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
            };
            DbContext.LoanApplicationAans.Add(laaan);
            DbContext.SaveChanges();

            LoanApplicationAan laaan2 = new LoanApplicationAan
            {
                Id = Guid.NewGuid(),
                LoanApplicationId = loanApplication2.Id,
                AanReasonId = reason2.Id,
                ProductId = product.Id,
                ShowInAanDocument = true,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
            };
            DbContext.LoanApplicationAans.Add(laaan2);
            DbContext.SaveChanges();

            CalcResult cr = new CalcResult
            {
                Id = Guid.NewGuid(),
                LoanApplicationId = loanApplication2.Id,
                DecisionModelId = decisionModel.Id,
                ProductId = product.Id,
                ModelSelectorId = null,
                IsStatistical = false,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                Transformation = null,
                TransformationResults = null,
                TransformationErrors = null,
                TransformationAans = null,
                FinalScore = null,
                RngNumber = null,
                AppliedCampaignCodeId = null,
                AppliedCampaignCode = null
            };
            DbContext.CalcResults.Add(cr);
            DbContext.SaveChanges();
            
        }
    }

    [Theory, AutoSubstituteData]
    public async Task DeniedLoanReportValidDataAsync(IFixture fixture)
    {
        var rig = new ReportServiceTestRig(fixture);
        var startDate = DateTime.Parse("1/1/2020");
        var endDate = DateTime.UtcNow.AddYears(1);
        
        var res = await rig.ReportService.GetDeniedLoanReportAsync(startDate, endDate);

        var resStr = System.Text.Encoding.ASCII.GetString(res);
        res.Should().NotBeNull();
        resStr.Should().Contain("Denied Reason");
        resStr.Should().NotContain("Denied2 Reason");
        resStr.Should().Contain("Denial2XYZ");
    }

    [Theory, AutoSubstituteData]
    public async Task DeniedLoanReportNoDataAsync(IFixture fixture)
    {
        var rig = new ReportServiceTestRig(fixture);
        var startDate = DateTime.Parse("1/1/2020");
        var endDate = DateTime.Parse("1/2/2020");
        var res = await rig.ReportService.GetDeniedLoanReportAsync(startDate, endDate);

        res.Should().NotBeNull();

        var resStr = System.Text.Encoding.ASCII.GetString(res);
        resStr.Should().Be("\"AppId\",\"FirstName\",\"LastName\",\"MailingAddress\",\"City\",\"State\",\"Zip\",\"ApplicationDate\",\"AccountNumber\",\"LoanTypeCode\",\"DenialReason\"\r\n");
    }

    [Theory, AutoSubstituteData]
    public async Task GetUniqueMembersReportCsvAsyncFail(IFixture fixture)
    {
        var rig = new ReportServiceTestRig(fixture);
        var startDate = DateTime.Parse("1/1/2020");
        var endDate = DateTime.Parse("1/2/2020");

        var ex = await Assert.ThrowsAsync<NoDataInDateRangeException>(async () =>
            await rig.ReportService.GetUniqueMembersReportCsvAsync(startDate, endDate, "TestFI")
        );

    }

    [Theory, AutoSubstituteData]
    public async Task GetUniqueMembersReportCsvAsyncValid(IFixture fixture)
    {
        var rig = new ReportServiceTestRig(fixture);
        var startDate = DateTime.Parse("1/1/2020");
        var endDate = DateTime.UtcNow.AddYears(1);

        var res = await rig.ReportService.GetUniqueMembersReportCsvAsync(startDate, endDate, "TestFI");
        res.Should().NotBeNull();

        var resStr = System.Text.Encoding.ASCII.GetString(res);
        resStr.Should().Contain("\"# of Unique Counts/Values for the Selected Period Above\",\"\",\"1\",\"0\",\"1\",\"0\",\"987.65\"");
    }

}
