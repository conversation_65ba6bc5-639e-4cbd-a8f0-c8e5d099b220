using AutoFixture;
using FluentAssertions;
using NSubstitute;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;
using Xunit;

namespace QCash.Service.UnitTest.Services.DecisionEngine;

public class SavingAccountServiceTests
{
    private class TestRig(IFixture fixture)
    {
        public IEfPocoService EfPocoService { get; set; } = fixture.Freeze<IEfPocoService>();
        public ISavingAccountService SavingAccountService { get; set; } = fixture.Freeze<SavingAccountService>();
        public readonly Guid ItemId = Guid.NewGuid();
        public PerformCreateOrUpdateOptions<SavingAccountType>? PerformCreateOrUpdateOptions { get; set; }

        public async Task<GetOrCreateRecordResult<SavingAccountType>> SaveAsync()
        {
            var dto = new GenericDescriptionValueDto
            {
                Id = ItemId,
                Value = "ValueA",
                Description = "DescriptionA",
                TimeStamp = [0,1,2],
                LookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.SavingAccountType),
            };
            EfPocoService.CreateOrUpdateAsync(ItemId, IFixtureExtensions.FinancialInstitutionId,
                    Arg.Any<byte[]>(),
                    Arg.Do<PerformCreateOrUpdateOptions<SavingAccountType>>(a =>
                        { PerformCreateOrUpdateOptions = a; })
                    )
                .Returns(new GetOrCreateRecordResult<SavingAccountType>
                {
                    IsSuccessful = true,
                    Record = new SavingAccountType()
                    {
                        Id = ItemId,
                        Value = "Value1",
                        Description = "Description1",
                        TimeStamp = [0,1,2],
                    },
                    FoundExistingRecord = false,
                    CreatingNewRecord = false,
                    EditingExistingRecord = false,
                });

            return (await SavingAccountService.SaveAsync(IFixtureExtensions.FinancialInstitutionId, dto)
                as GetOrCreateRecordResult<SavingAccountType>)!;
        }

        public async Task<GenericDescriptionValueDto?> GetItemAsync(bool recordExistsInDatabase = true)
        {
            EfPocoService.GetItemAsync<SavingAccountType>(ItemId)
                .Returns(recordExistsInDatabase ? new SavingAccountType()
                {
                    Id = ItemId,
                    Value = "Value1",
                } : null);

            return await SavingAccountService.GetItemAsync(ItemId);
        }

        public async Task<GenericActionResult> DeleteItemAsync()
        {
            var dto = new DeleteItemDto() { DeleteItemId = ItemId, TimeStamp = [0,1,2], };
            return await SavingAccountService.DeleteItemAsync(dto);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task SaveAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SaveAsync();
        result.IsSuccessful.Should().BeTrue();
        Assert.NotNull(result.Record);
        result.Record.Description.Should().Be("Description1");
        SaveAsyncTestAction(rig);

        await rig.EfPocoService.Received().CreateOrUpdateAsync(rig.ItemId, IFixtureExtensions.FinancialInstitutionId,
            Arg.Any<byte[]>(), Arg.Any<PerformCreateOrUpdateOptions<SavingAccountType>>());
    }

    private static void SaveAsyncTestAction(TestRig rig)
    {
        Assert.NotNull(rig.PerformCreateOrUpdateOptions?.ExecuteRecordChangesFunc);

        var data1 = new PerformUpdateParam<SavingAccountType>
        {
            Record = new SavingAccountType() {},
            CreatingNewRecord = false,
        };
        rig.PerformCreateOrUpdateOptions.ExecuteRecordChangesFunc(data1);
        data1.Record.Description.Should().Be("DescriptionA");
        data1.Record.Value.Should().Be("ValueA");
    }

    [Theory, AutoSubstituteData]
    public async Task GetItemAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.GetItemAsync();
        Assert.NotNull(result);
        result.Value.Should().Be("Value1");

        await rig.EfPocoService.Received().GetItemAsync<SavingAccountType>(rig.ItemId);
    }

    [Theory, AutoSubstituteData]
    public async Task GetItemAsyncNoRecord(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.GetItemAsync(recordExistsInDatabase:false);
        Assert.Null(result);

        await rig.EfPocoService.Received().GetItemAsync<SavingAccountType>(rig.ItemId);
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteItemAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.DeleteItemAsync();
        Assert.NotNull(result);
        result.IsSuccessful.Should().BeTrue();

        await rig.EfPocoService.Received().DeleteItemAsync<SavingAccountType>(
            Arg.Is<DeleteItemDto>(a => a.DeleteItemId == rig.ItemId));
    }
}
