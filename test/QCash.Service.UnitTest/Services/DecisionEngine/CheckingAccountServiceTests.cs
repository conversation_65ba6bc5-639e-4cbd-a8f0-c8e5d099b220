using AutoFixture;
using FluentAssertions;
using NSubstitute;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;
using Xunit;

namespace QCash.Service.UnitTest.Services.DecisionEngine;

public class CheckingAccountServiceTests
{
    private class TestRig(IFixture fixture)
    {
        public IEfPocoService EfPocoService { get; set; } = fixture.Freeze<IEfPocoService>();
        public ICheckingAccountService CheckingAccountService { get; set; } = fixture.Freeze<CheckingAccountService>();
        public readonly Guid ItemId = Guid.NewGuid();
        public PerformCreateOrUpdateOptions<CheckingAccountType>? PerformCreateOrUpdateOptions { get; set; }

        public async Task<GetOrCreateRecordResult<CheckingAccountType>> SaveAsync()
        {
            var dto = new GenericDescriptionValueDto
            {
                Id = ItemId,
                Value = "ValueA",
                Description = "DescriptionA",
                TimeStamp = [0, 1, 2],
                LookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.CheckingAccountType),
            };
            EfPocoService.CreateOrUpdateAsync(ItemId, IFixtureExtensions.FinancialInstitutionId,
                    Arg.Any<byte[]>(),
                    Arg.Do<PerformCreateOrUpdateOptions<CheckingAccountType>>(a =>
                        { PerformCreateOrUpdateOptions = a; })
                    )
                .Returns(new GetOrCreateRecordResult<CheckingAccountType>
                {
                    IsSuccessful = true,
                    Record = new CheckingAccountType()
                    {
                        Id = ItemId,
                        Value = "Value1",
                        Description = "Description1",
                        TimeStamp = [0, 1, 2],
                    },
                    FoundExistingRecord = false,
                    CreatingNewRecord = false,
                    EditingExistingRecord = false,
                });

            return (await CheckingAccountService.SaveAsync(IFixtureExtensions.FinancialInstitutionId, dto)
                as GetOrCreateRecordResult<CheckingAccountType>)!;
        }

        public async Task<GenericDescriptionValueDto?> GetItemAsync(bool recordExistsInDatabase = true)
        {
            EfPocoService.GetItemAsync<CheckingAccountType>(ItemId)
                .Returns(recordExistsInDatabase ? new CheckingAccountType()
                {
                    Id = ItemId,
                    Value = "Value1",
                } : null);

            return await CheckingAccountService.GetItemAsync(ItemId);
        }

        public async Task<GenericActionResult> DeleteItemAsync()
        {
            var dto = new DeleteItemDto() { DeleteItemId = ItemId, TimeStamp = [0, 1, 2], };
            return await CheckingAccountService.DeleteItemAsync(dto);
        }
    }

    [Theory, AutoSubstituteData]
    public async Task SaveAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.SaveAsync();
        result.IsSuccessful.Should().BeTrue();
        Assert.NotNull(result.Record);
        result.Record.Description.Should().Be("Description1");
        SaveAsyncTestAction(rig);

        await rig.EfPocoService.Received().CreateOrUpdateAsync(rig.ItemId, IFixtureExtensions.FinancialInstitutionId,
            Arg.Any<byte[]>(), Arg.Any<PerformCreateOrUpdateOptions<CheckingAccountType>>());
    }

    private static void SaveAsyncTestAction(TestRig rig)
    {
        Assert.NotNull(rig.PerformCreateOrUpdateOptions?.ExecuteRecordChangesFunc);

        var data1 = new PerformUpdateParam<CheckingAccountType>
        {
            Record = new CheckingAccountType() {},
            CreatingNewRecord = false,
        };
        rig.PerformCreateOrUpdateOptions.ExecuteRecordChangesFunc(data1);
        data1.Record.Description.Should().Be("DescriptionA");
        data1.Record.Value.Should().Be("ValueA");
    }

    [Theory, AutoSubstituteData]
    public async Task GetItemAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.GetItemAsync();
        Assert.NotNull(result);
        result.Value.Should().Be("Value1");

        await rig.EfPocoService.Received().GetItemAsync<CheckingAccountType>(rig.ItemId);
    }

    [Theory, AutoSubstituteData]
    public async Task GetItemAsyncNoRecord(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.GetItemAsync(recordExistsInDatabase:false);
        Assert.Null(result);

        await rig.EfPocoService.Received().GetItemAsync<CheckingAccountType>(rig.ItemId);
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteItemAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.DeleteItemAsync();
        Assert.NotNull(result);
        result.IsSuccessful.Should().BeTrue();

        await rig.EfPocoService.Received().DeleteItemAsync<CheckingAccountType>(
            Arg.Is<DeleteItemDto>(a => a.DeleteItemId == rig.ItemId));
    }
}
