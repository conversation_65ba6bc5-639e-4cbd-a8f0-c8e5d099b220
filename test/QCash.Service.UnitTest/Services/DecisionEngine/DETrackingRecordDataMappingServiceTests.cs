using EntityFrameworkCore.Testing.NSubstitute;
using FluentAssertions;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Services.DecisionEngine;
using QCash.Service.Services.General.Interfaces;
using QCash.Utils.Core;
using Xunit;

namespace QCash.Service.UnitTest.Services.DecisionEngine;

public class DETrackingRecordDataMappingServiceTests
{
    private class DETrackingRecordDataMappingPageServiceTestRig
    {
        private readonly Guid _applicationId = Guid.NewGuid();
        public const string LookupItemSlug = "ABCD";
        private DETrackingRecordDataMappingService DETrackingRecordDataMappingService { get; set; }
        private IEfPocoService EfPocoService { get; set; }
        private IGuidExtensionService GuidExtensionService { get; set; }
        private QCashContext Context { get; set; }
        private ILookupItemService ILookupItemService { get; set; }
        public DETrackingRecordDataMappingPageServiceTestRig()
        {
            EfPocoService = Substitute.For<IEfPocoService>();
            GuidExtensionService = Substitute.For<IGuidExtensionService>();
            Context = Create.MockedDbContextFor<QCashContext>();
            Context.DecisionEngineTrackingRecordDataMappings.Add(new DecisionEngineTrackingRecordDataMapping
            {
                Id = Guid.NewGuid(),
                Name = "Test DecisionEngineTrackingRecordDataMapping 1",
                Description = "Desc",
                Slug = LookupItemSlug,
                Abrv = "abrv",
                AppAbrv = "appabrv",
                TrackingRecordLevel = "",
                TrackingRecordField = "",
                TrackingRecordPullOption = "",
                TrackingRecordType = "",
                TimeStamp = [0, 1, 2, 3],
            });
            Context.SaveChanges();
            ILookupItemService = Substitute.For<ILookupItemService>();

            DETrackingRecordDataMappingService = new DETrackingRecordDataMappingService(EfPocoService, GuidExtensionService,
                Context, ILookupItemService);
        }

        public async Task<DETrackingRecordDataMappingDto?> GetItemAsync() =>
            await DETrackingRecordDataMappingService.GetItemAsync(_applicationId, LookupItemSlug);
    }

    //[SkipInPipelineFact]
    public async Task GetItemAsync()
    {
        var rig = new DETrackingRecordDataMappingPageServiceTestRig();
        var data = await rig.GetItemAsync();
        Assert.NotNull(data);
        data.Name.Should().Be("Test DecisionEngineTrackingRecordDataMapping 1");
        data.Description.Should().Be("Desc");
        data.Slug.Should().Be(DETrackingRecordDataMappingPageServiceTestRig.LookupItemSlug);
        data.Abrv.Should().Be("abrv");
        data.AppAbrv.Should().Be("appabrv");
        data.TimeStamp.Should().BeEquivalentTo([0, 1, 2, 3]);
    }
}
