using AutoFixture;
using Microsoft.Extensions.Logging;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Helpers;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services;
using FluentAssertions;
using Microsoft.Extensions.Caching.Distributed;
using QCash.Service.BusinessLogic.EligibilityRules;
using QCash.Service.Services.General;
using Xunit;
using QCash.Service.Classes.DecisionManager;
using QCash.Service.Services.DecisionEngine;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.LoanApplication;
using LoanType = QCash.Data.Models.LoanType;
using Product = QCash.Data.Models.Product;
using Newtonsoft.Json;
using QCash.Data.Models.Enums;

namespace QCash.Service.UnitTest.Services.DecisionEngine;

public class DecisionEngineServiceTests
{
    private class DecisionEngineServiceTestRig
    {
        public QCashContext DbContext { get; set; }
        public IDecisionManagerService DecisionManagerService { get; set; }
        public IEligibilityRulesRunner EligibilityRulesRunner { get; set; }
        public IDecisionEngineService DecisionEngineService { get; set; }
        private FinancialInstitution FinancialInstitution { get; set; }
        private ILogger<MemberInterfaceHelper> Logger { get; set; }
        private IGuidExtensionService GuidExtensionService { get; set; }
        public Guid FinancialInstitutionMemberId { get; set; }
        private IDecisionHelperService DecisionHelperService { get; set; }
        private IDistributedCache Cache { get; set; }

        public DecisionEngineParameters MockDecisionEngineParameters => 
            JsonConvert.DeserializeObject<DecisionEngineParameters>(DecisionEngineParametersMock)!;
        
        private const string DecisionEngineParametersMock = "{ \"ProductResults\": [ { \"Code\": \"58\", \"Name\": \"Q-Cash\", \"ParametersSet\": { \"Description\": \"SETTLED\", \"Parameters\": [ { \"Expression\": \"\", \"Name\": \"MW.LengthOfRelationship\", \"Value\": 172 }, { \"Expression\": \"\", \"Name\": \"MW.Product.HaveExclusionLoans\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.Product.NumberOfOpenLoans\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.Product.LoanBalance\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.Product.ReceivedLoans\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.Product.HavePastDueLoans\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.Product.AggregateAccountDeposit\", \"Value\": 3432.38 }, { \"Expression\": \"\", \"Name\": \"MW.Product.NumberOfElectronicTransactions\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.Product.NumberOfCreditUnionProducts\", \"Value\": 11 }, { \"Expression\": \"\", \"Name\": \"MW.Product.NumberOfDirectDeposit\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.HaveSSN\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.IsPrimaryAccountHolder\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.IsEighteenYearsOrGreater\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.HaveCreditUnionAccountThatIsNotAnExclusionAccountType\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.HaveNoChargeOffAccountWithBalance\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.HaveOpenShareAccount\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.PrincipleBalanceOfOpenLoans\", \"Value\": 7964.99 }, { \"Expression\": \"\", \"Name\": \"MW.TotalLoanPayments\", \"Value\": 51 }, { \"Expression\": \"\", \"Name\": \"MW.LateLoanPayments\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.LengthOfRelationship\", \"Value\": 67 } ] }, \"Type\": 1, \"DecisionModelId\": \"D7F4EF46-6F30-44FA-B118-A7CC010F36EE\", \"FIHandle\": \"Traditional Fee Based Copy\" }, { \"Code\": \"57\", \"Name\": \"Q-CashPlus\", \"ParametersSet\": { \"Description\": null, \"Parameters\": [ { \"Expression\": \"\", \"Name\": \"MW.LengthOfRelationship\", \"Value\": 172 }, { \"Expression\": \"\", \"Name\": \"MW.Product.HaveExclusionLoans\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.Product.NumberOfOpenLoans\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.Product.LoanBalance\", \"Value\": 1170 }, { \"Expression\": \"\", \"Name\": \"MW.Product.ReceivedLoans\", \"Value\": 12 }, { \"Expression\": \"\", \"Name\": \"MW.Product.HavePastDueLoans\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.Product.AggregateAccountDeposit\", \"Value\": 9610.38 }, { \"Expression\": \"\", \"Name\": \"MW.Product.NumberOfElectronicTransactions\", \"Value\": 152 }, { \"Expression\": \"\", \"Name\": \"MW.Product.NumberOfCreditUnionProducts\", \"Value\": 7 }, { \"Expression\": \"\", \"Name\": \"MW.Product.NumberOfDirectDeposit\", \"Value\": 6 }, { \"Expression\": \"\", \"Name\": \"MW.HaveSSN\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.IsPrimaryAccountHolder\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.IsEighteenYearsOrGreater\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.HaveCreditUnionAccountThatIsNotAnExclusionAccountType\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.HaveNoChargeOffAccountWithBalance\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.HaveOpenShareAccount\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.PrincipleBalanceOfOpenLoans\", \"Value\": 7964.99 }, { \"Expression\": \"\", \"Name\": \"MW.TotalLoanPayments\", \"Value\": 51 }, { \"Expression\": \"\", \"Name\": \"MW.LateLoanPayments\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.LengthOfRelationship\", \"Value\": 67 }, { \"Expression\": \"Loan.Codeis'3'\", \"Name\": \"MW.Visa\", \"Value\": 2 }, { \"Expression\": \"Loan.Typeis'0'ORLoan.Typeis'01'ORLoan.Typeis'18'ORLoan.Typeis'19'ORLoan.Typeis'20'ORLoan.Typeis'21'ORLoan.Typeis'22'ORLoan.Typeis'23'ORLoan.Typeis'24'ORLoan.Typeis'25'ORLoan.Typeis'57'ORLoan.Typeis'07'\", \"Name\": \"MW.Auto_SmartMoney\", \"Value\": 0 }, { \"Expression\": \"Account.Tracking.Typeis'42'\", \"Name\": \"MW.BillPay\", \"Value\": 0 }, { \"Expression\": \"Account.Tracking.Typeis'40'\", \"Name\": \"MW.Mobile\", \"Value\": 0 }, { \"Expression\": \"Account.Tracking.Typeis'1'ORLoan.Typeis'11'ORLoan.Typeis'29'ORLoan.Typeis'33'\", \"Name\": \"MW.MortgageProduct\", \"Value\": 0 }, { \"Expression\": \"Loan.Typeis'29'ORLoan.Typeis'30'ORLoan.Typeis'31'ORLoan.Typeis'33'ORLoan.Typeis'35'ORLoan.Typeis'36'ORLoan.Typeis'37'ORLoan.Typeis'08'ORLoan.Typeis'09'ORLoan.Typeis'10'ORLoan.Typeis'12'ORLoan.Typeis'13'ORLoan.Typeis'14'\", \"Name\": \"MW.LOC_Other\", \"Value\": 0 }, { \"Expression\": \"Loan.Typeis'02'ORLoan.Typeis'03'ORLoan.Typeis'47'ORLoan.Typeis'48'ORLoan.Typeis'49'ORLoan.Typeis'50'ORLoan.Typeis'42'ORLoan.Typeis\u201843\u2019ORLoan.Typeis'44'ORLoan.Typeis'45'\", \"Name\": \"MW.RV_Boat\", \"Value\": 1 }, { \"Expression\": \"Loan.Typeis'51'ORLoan.Typeis'52'ORLoan.Typeis'17'\", \"Name\": \"MW.Motorcycle\", \"Value\": 0 } ] }, \"Type\": 0, \"DecisionModelId\": \"7B03DFD0-AD7E-4FE0-B3CF-21A9F2F3816D\", \"FIHandle\": \"Traditional LPQ InterestBased\" }, { \"Code\": \"30\", \"Name\": \"Q-CashLOC\", \"ParametersSet\": { \"Description\": null, \"Parameters\": [ { \"Expression\": \"\", \"Name\": \"MW.LengthOfRelationship\", \"Value\": 172 }, { \"Expression\": \"\", \"Name\": \"MW.Product.HaveExclusionLoans\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.Product.NumberOfOpenLoans\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.Product.LoanBalance\", \"Value\": 1170 }, { \"Expression\": \"\", \"Name\": \"MW.Product.ReceivedLoans\", \"Value\": 12 }, { \"Expression\": \"\", \"Name\": \"MW.Product.HavePastDueLoans\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.Product.AggregateAccountDeposit\", \"Value\": 9610.38 }, { \"Expression\": \"\", \"Name\": \"MW.Product.NumberOfElectronicTransactions\", \"Value\": 152 }, { \"Expression\": \"\", \"Name\": \"MW.Product.NumberOfCreditUnionProducts\", \"Value\": 7 }, { \"Expression\": \"\", \"Name\": \"MW.Product.NumberOfDirectDeposit\", \"Value\": 6 }, { \"Expression\": \"\", \"Name\": \"MW.HaveSSN\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.IsPrimaryAccountHolder\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.IsEighteenYearsOrGreater\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.HaveCreditUnionAccountThatIsNotAnExclusionAccountType\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.HaveNoChargeOffAccountWithBalance\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.HaveOpenShareAccount\", \"Value\": 1 }, { \"Expression\": \"\", \"Name\": \"MW.PrincipleBalanceOfOpenLoans\", \"Value\": 7964.99 }, { \"Expression\": \"\", \"Name\": \"MW.TotalLoanPayments\", \"Value\": 51 }, { \"Expression\": \"\", \"Name\": \"MW.LateLoanPayments\", \"Value\": 0 }, { \"Expression\": \"\", \"Name\": \"MW.LengthOfRelationship\", \"Value\": 67 }, { \"Expression\": \"Loan.Codeis'3'\", \"Name\": \"MW.Visa\", \"Value\": 2 }, { \"Expression\": \"Loan.Typeis'0'ORLoan.Typeis'01'ORLoan.Typeis'18'ORLoan.Typeis'19'ORLoan.Typeis'20'ORLoan.Typeis'21'ORLoan.Typeis'22'ORLoan.Typeis'23'ORLoan.Typeis'24'ORLoan.Typeis'25'ORLoan.Typeis'57'ORLoan.Typeis'07'\", \"Name\": \"MW.Auto_SmartMoney\", \"Value\": 0 }, { \"Expression\": \"Account.Tracking.Typeis'42'\", \"Name\": \"MW.BillPay\", \"Value\": 0 }, { \"Expression\": \"Account.Tracking.Typeis'40'\", \"Name\": \"MW.Mobile\", \"Value\": 0 }, { \"Expression\": \"Account.Tracking.Typeis'1'ORLoan.Typeis'11'ORLoan.Typeis'29'ORLoan.Typeis'33'\", \"Name\": \"MW.MortgageProduct\", \"Value\": 0 }, { \"Expression\": \"Loan.Typeis'29'ORLoan.Typeis'30'ORLoan.Typeis'31'ORLoan.Typeis'33'ORLoan.Typeis'35'ORLoan.Typeis'36'ORLoan.Typeis'37'ORLoan.Typeis'08'ORLoan.Typeis'09'ORLoan.Typeis'10'ORLoan.Typeis'12'ORLoan.Typeis'13'ORLoan.Typeis'14'\", \"Name\": \"MW.LOC_Other\", \"Value\": 0 }, { \"Expression\": \"Loan.Typeis'02'ORLoan.Typeis'03'ORLoan.Typeis'47'ORLoan.Typeis'48'ORLoan.Typeis'49'ORLoan.Typeis'50'ORLoan.Typeis'42'ORLoan.Typeis\u201843\u2019ORLoan.Typeis'44'ORLoan.Typeis'45'\", \"Name\": \"MW.RV_Boat\", \"Value\": 1 }, { \"Expression\": \"Loan.Typeis'51'ORLoan.Typeis'52'ORLoan.Typeis'17'\", \"Name\": \"MW.Motorcycle\", \"Value\": 0 } ] }, \"Type\": 0, \"DecisionModelId\": \"C5402C78-8219-4747-BFAD-A75C00DE7E2D\", \"FIHandle\": \"LOC Model\" }, { \"Code\": \"43\", \"Name\": \"Pre-Approved Personal Loan\", \"ParametersSet\": { \"Description\": null, \"Parameters\": [ { \"Expression\": null, \"Name\": \"MW.HaveSSN\", \"Value\": \"1\" }, { \"Expression\": null, \"Name\": \"MW.IsEighteenYearsOrGreater\", \"Value\": \"1\" }, { \"Expression\": null, \"Name\": \"MW.HaveNoChargeOffAccountWithBalance\", \"Value\": \"1\" }, { \"Expression\": null, \"Name\": \"MW.IsPrimaryAccountHolder\", \"Value\": \"1\" }, { \"Expression\": null, \"Name\": \"MW.HaveCreditUnionAccountThatIsNotAnExclusionAccountType\", \"Value\": \"1\" }, { \"Expression\": null, \"Name\": \"MW.HaveOpenShareAccount\", \"Value\": \"1\" }, { \"Expression\": null, \"Name\": \"MW.Product.HaveExclusionLoans\", \"Value\": \"0\" }, { \"Expression\": null, \"Name\": \"MW.HaveExclusionLoansByPurpose\", \"Value\": \"0\" }, { \"Expression\": null, \"Name\": \"MW.Product.NumberOfOpenLoans\", \"Value\": \"0\" }, { \"Expression\": null, \"Name\": \"MW.Product.ReceivedLoans\", \"Value\": \"0\" }, { \"Expression\": null, \"Name\": \"MW.Product.HavePastDueLoans\", \"Value\": \"0\" }, { \"Expression\": null, \"Name\": \"MW.LengthOfRelationship\", \"Value\": \"83\" } ] }, \"Type\": 0, \"DecisionModelId\": \"871999B5-C0BD-4B9E-AD39-A88601063806\", \"FIHandle\": \"Pre-Approved Model #10\" } ] }";

        public DecisionEngineServiceTestRig(IFixture fixture)
        {
            DbContext = fixture.Freeze<QCashContext>();

            FinancialInstitution = new FinancialInstitution
            {
                Id = IFixtureExtensions.FinancialInstitutionId,
                Slug = IFixtureExtensions.FiSlug,
                Address = "",
                City = "",
                ContactName = "",
                ContactPhone = "",
                GlLoanAccount = "",
                MailingAddress = "",
                Name = "Test1 Name",
                NoReplySubdomain = "TestSubd1",
                State = "",
                Zip = "12345",
                TimeStamp = [],
            };

            DbContext.FinancialInstitutions.Add(FinancialInstitution);

            //add settings
            DbContext.Settings.Add(_ = new Setting()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppLogsFileNameTemplate = "",
                Bankruptcy = "",
                FilenameAan = "",
                FilenameEConsent = "",
                LoanIdSource = "",
                Mla = "",
                MonitorAccount = "",
                TilaCalculatorSource = "",
                TimeStamp = [],
                TimeZone = "",
                FilenameTila = "",
                MilitaryAnnualPercentageRate = 0.123M,
                ShouldDenyOnMaxLoans = true,
                MaxNumberOfOpenLoans = 1,
            });
            DbContext.SaveChanges();

            //add product
            LoanType loanType1;
            DbContext.LoanTypes.Add(loanType1 = new LoanType()
            {
                Id = Guid.NewGuid(),
                Name = "LT1",
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Abrv = "",
                AppAbrv = "",
                TimeStamp = [],
            });
            DbContext.SaveChanges();

            Product product = new Product()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Slug = "ProductSlug1",
                Name = "Q-Cash",
                Abrv = "58",
                AppAbrv = "P1",
                InvoicePlanId = Guid.NewGuid(),
                DateCreatedUtc = DateTime.Now,
                DateUpdatedUtc = DateTime.Now,
                LoanTypeId = loanType1.Id,
                GlTranslationAccount = "",
                InvoiceId = "",
                IsActive = true,
                TimeStamp = [],
                LengthOfRelationship = 100,
                PercentageOfAggregateDepositForLoanAmount = 50,
            };
            DbContext.Products.Add(product);
            DbContext.SaveChanges();

            LoanStatusType loanStatusType = new LoanStatusType
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "LSTIP",
                Abrv = "LSTIP",
                Slug = "in_progress",
                Name = "In Process",
                Description = "In Progress",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                IsDeleted = false,
                TimeStamp = [],
                ProductDependency = "None",
            };
            DbContext.LoanStatusTypes.Add(loanStatusType);
            DbContext.SaveChanges();

            DecisionModelType decisionModelType = new DecisionModelType()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "TR",
                Abrv = "TR",
                Slug = "T",
                Name = "traditional",
                Description = "Traditional model type",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                IsDeleted = false,
                TimeStamp = [],
            };
            DbContext.DecisionModelTypes.Add(decisionModelType);

            DecisionModelStatus decisionModelStatus = new DecisionModelStatus()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "INCT",
                Abrv = "INCT",
                Slug = "inactive",
                Name = "Inactive",
                TimeStamp = [],
            };
            DecisionModelStatus decisionModelStatus2 = new DecisionModelStatus()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "ACTV",
                Abrv = "ACTV",
                Slug = "active",
                Name = "Active",
                TimeStamp = [],
            };
            DecisionModelStatus decisionModelStatus3 = new DecisionModelStatus()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                AppAbrv = "ARHV",
                Abrv = "ARHV",
                Slug = "archived",
                Name = "Archived",
                TimeStamp = [],
            };
            DbContext.DecisionModelStatuses.Add(decisionModelStatus);
            DbContext.DecisionModelStatuses.Add(decisionModelStatus2);
            DbContext.DecisionModelStatuses.Add(decisionModelStatus3);

            //add decisionmodel and productdecisionmodel
            DecisionModel decisionModel = new DecisionModel()
            {
                Id = new Guid("D7F4EF46-6F30-44FA-B118-A7CC010F36EE"),  //must match a result in the DE parameters mock we're using
                Description = "test",
                FiHandle = "Traditional Fee Based Copy",
                StatusDate = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                DateCreatedUtc = DateTime.UtcNow,
                ModelStatus = decisionModelStatus2,
                ModelType = decisionModelType,
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                TimeStamp = [],
            };
            DbContext.DecisionModels.Add(decisionModel);
            DbContext.SaveChanges();

            ProductDecisionModel productDecisionModel = new ProductDecisionModel()
            {
                Id = Guid.NewGuid(),
                DateUpdatedUtc = DateTime.UtcNow,
                DateCreatedUtc = DateTime.UtcNow,
                Product = product,
                DecisionModel = decisionModel,
                TimeStamp = [],
            };
            DbContext.ProductDecisionModels.Add(productDecisionModel);
            DbContext.SaveChanges();

            ScoreType scoreType = new ScoreType()
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                DateUpdatedUtc = DateTime.UtcNow,
                DateCreatedUtc = DateTime.UtcNow,
                AppAbrv = "TransformationScore",
                Abrv = "TransformationScore",
                Slug = "transformation_score",
                Name = "Transformation Score",
                Description = "Transformation Score Type",
                IsDeleted = false,
                TimeStamp = [],
            };
            DbContext.ScoreTypes.Add(scoreType);
            DbContext.SaveChanges();

            //add member
            FinancialInstitutionMemberId = Guid.NewGuid();
            var financialInstitutionMember = new FinancialInstitutionMember
            {
                Id = FinancialInstitutionMemberId,
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                FirstName = "Bob",
                MiddleName = "X",
                LastName = "Test",
                City = "Naperville",
                State = "IL",
                Zip = null,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                AccountId = "1",
                MemberIdHash = string.Empty,
                MemberIdMask = string.Empty,
            };
            DbContext.FinancialInstitutionMembers.Add(financialInstitutionMember);
            DbContext.SaveChanges();

            //add memberbaseaccount
            var memberBaseAccount = new MemberBaseAccount
            {
                Id = Guid.NewGuid(),
                AccountId = "1234",
                FinancialInstitutionMemberId = FinancialInstitutionMemberId,
                OwnerShipType = "Primary",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                Type = "1",
                CloseDateUtc = null,
                OpenDateUtc = DateTime.UtcNow.AddYears(-10),
                BranchNumber = null,
                IsExcluded = null,
            };
            DbContext.MemberBaseAccounts.Add(memberBaseAccount);
            DbContext.SaveChanges();


            //add loan application
            var loanApplication = new Data.Models.LoanApplication
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionMemberId = financialInstitutionMember.Id,
                SelectedProductId = product.Id,
                DecisionModelId = decisionModel.Id,
                ActiveDuty = null,
                AmountFinanced = 0,
                AmountBorrowed = 0,
                AnnualPercentageRate = 0,
                FinanceCharge = 0,
                TotalOfPayments = 0,
                LoanStatusId = loanStatusType.Id,
                IsInProgress = true,
                AccountId = "1234",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
                AppId = 100101,
                MemberBaseAccountId = memberBaseAccount.Id,
                LoanAmount = 750,
                MatureDateUtc = DateTime.UtcNow.AddYears(10),
                IsMoved = false,
                FeeRefundStep = "None",
                AverageMonthlyCheckingDepositBalance = 500,
                DefaultAccountForTransactionId = null,
                IsBankrupt = false,
                SelectedAccountForTransactionId = null,
                MovedAppLogs = false,
                CurrentStep = 4,
                FirstStep = 4,
                NextStep = 2,
                PaymentGuardReportStatusId = 3,
                RetryCount = 0,
                AdverseActionNoticeDocument = null,
                //DecisionModel = null,
            };
            DbContext.LoanApplications.Add(loanApplication);
            DbContext.SaveChanges();

            Logger = fixture.Freeze<ILogger<MemberInterfaceHelper>>();
            GuidExtensionService = new GuidExtensionService();
            DecisionHelperService = new DecisionHelperService(DbContext, GuidExtensionService);
            Cache = Substitute.For<IDistributedCache>();
            DecisionManagerService = new DecisionManagerService(DbContext, GuidExtensionService, Cache, DecisionHelperService);
            EligibilityRulesRunner = new EligibilityRulesRunner(DbContext);
            DecisionEngineService = new DecisionEngineService(DbContext, GuidExtensionService,
                EligibilityRulesRunner, DecisionHelperService, DecisionManagerService);
        }

        public void AddTransformations()
        {
            var modelId = DbContext.DecisionModels.First().Id;

            var res = DecisionManagerService.GetModelTransformationMaster(modelId);

            var eligTransformation = DbContext.ModelTransformations.First(o => o.Name == "Eligibility");
            var migsTransformation = DbContext.ModelTransformations.First(o => o.Name == "MemberInGoodStanding");
            var deTransformation = DbContext.ModelTransformations.First(o => o.Name == "DecisionEngine");
            var qaTransformation = DbContext.ModelTransformations.First(o => o.Name == "QualifiedAmount");

            eligTransformation.Formula = "Local.XYZ = 33";
            migsTransformation.Formula = "Model.XYZ & LoanLimit.XYZ = 296";

            deTransformation.Formula = "Product.MinimumLoanAmount = Min(QualifiedAmount, Product.MinimumLoanAmount)";
            qaTransformation.Formula = "Round(Max(Product.ImaginaryAmount - Product.LoanBalance, 0), 0)";

            DbContext.SaveChanges();

            //for elig
            //MW.IsEighteenYearsOrGreater MW.IsPrimaryAccountHolder MW.HaveOpenShareAccount
            Transformation newtransformationE = new Transformation(GuidExtensionService)
            {
                Id = Guid.NewGuid(),
                ValidationType = null,
                Name = "MW.IsEighteenYearsOrGreater",
                TransformationType = TransformationType.Param,
                Formula = "MW.IsEighteenYearsOrGreater",
            };
            DecisionManagerService.InsertTransformation(newtransformationE, modelId, eligTransformation.Id);

            Transformation newtransformationIsP = new Transformation(GuidExtensionService)
            {
                Id = Guid.NewGuid(),
                ValidationType = null,
                Name = "MW.IsPrimaryAccountHolder",
                TransformationType = TransformationType.Param,
                Formula = "MW.IsPrimaryAccountHolder",
            };
            DecisionManagerService.InsertTransformation(newtransformationIsP, modelId, eligTransformation.Id);

            Transformation newtransformationHopsa = new Transformation(GuidExtensionService)
            {
                Id = Guid.NewGuid(),
                ValidationType = null,
                Name = "MW.HaveOpenShareAccount",
                TransformationType = TransformationType.Param,
                Formula = "MW.HaveOpenShareAccount",
            };
            DecisionManagerService.InsertTransformation(newtransformationHopsa, modelId, eligTransformation.Id);

            //for migs
            //MW.LengthOfRelationship MW.LateLoanPayments
            Transformation newtransformationLor = new Transformation(GuidExtensionService)
            {
                Id = Guid.NewGuid(),
                ValidationType = null,
                Name = "MW.LengthOfRelationship",
                TransformationType = TransformationType.Param,
                Formula = "MW.LengthOfRelationship",
            }; 
            DecisionManagerService.InsertTransformation(newtransformationLor, modelId, migsTransformation.Id);

            Transformation newtransformationLpmt = new Transformation(GuidExtensionService)
            {
                Id = Guid.NewGuid(),
                ValidationType = null,
                Name = "MW.LateLoanPayments",
                TransformationType = TransformationType.Param,
                Formula = "MW.LateLoanPayments",
            };
            DecisionManagerService.InsertTransformation(newtransformationLpmt, modelId, migsTransformation.Id);

            //for qa
            //MW.Product.AggregateAccountDeposit
            Transformation newtransformationAad = new Transformation(GuidExtensionService)
            {
                Id = Guid.NewGuid(),
                ValidationType = null,
                Name = "MW.Product.AggregateAccountDeposit",
                TransformationType = TransformationType.Param,
                Formula = "MW.Product.AggregateAccountDeposit",
            };
            DecisionManagerService.InsertTransformation(newtransformationAad, modelId, qaTransformation.Id);
        }
    }

    //test eligibilityrulesrunner
    [Theory, AutoSubstituteData]
    public async Task EligibilityRulesRunnerTest(IFixture fixture)
    {
        var rig = new DecisionEngineServiceTestRig(fixture);
        
        rig.DbContext.Languages.Add(new Language()
        {
            Id = Guid.NewGuid(),
            Name = LanguageEnum.English.Name,
            LanguageCode = "",
            TimeStamp = [0, 1, 2],
        });

        EligibilityResult result = new EligibilityResult();

        await rig.EligibilityRulesRunner.RunAsync(result, true, null, rig.FinancialInstitutionMemberId);

        result.IsEligible.Should().NotBeFalse();    //can be null or true

        var memberBaseAccountId = rig.DbContext.MemberBaseAccounts.First().Id;

        var memberAccountLoan1 = new MemberAccount
        {
            Id = Guid.NewGuid(),
            AccountId = "111",
            MemberBaseAccountId = memberBaseAccountId,
            Category = "LOAN",
            AvailableBalance = 0,
            LedgerBalance = 0,
            IsClosed = false,
            IsInactive = false,
            IsChargedOff = false,
            Type = "58",
            SubType = null,
            Description = null,
            DateCreatedUtc = default,
            DateUpdatedUtc = default,
            TimeStamp = [],
            PaymentDueDateUtc = null,
            PayOffBalanceAsOfDate = null,
            PayOffAmount = null,
            InitialLoanAmount = null,
            DateOpened = null,
            OriginalBalance = null,
            InterestPaid = null,
            OdpCount = null,
            NsfCount = null,
            DateClosedUtc = null,
            DateChargedOffUtc = null,
            IsExcluded = null,
            IsTroubledDebt = false,
        };
        rig.DbContext.MemberAccounts.Add(memberAccountLoan1);
        await rig.DbContext.SaveChangesAsync();

        //now that the member already has a qcash loan in place, they should fail on the eligibility requirement
        await rig.EligibilityRulesRunner.RunAsync(result, true, null, rig.FinancialInstitutionMemberId);

        result.IsEligible.Should().BeFalse();
    }

    //test memberingoodstanding
    [Theory, AutoSubstituteData]
    public void MemberInGoodStandingTest(IFixture fixture)
    {
        var rig = new DecisionEngineServiceTestRig(fixture);
            
        rig.AddTransformations();

        var modelId = rig.DbContext.DecisionModels.First().Id;
        var migsModelTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "MemberInGoodStanding");
        migsModelTransformation.Formula = "Model.XYZ + LoanLimit.XYZ = 2";
        rig.DbContext.SaveChanges();

        var migsTransformation = rig.DecisionManagerService.GetTransformation(modelId, migsModelTransformation.Id);

        var transformationSettings = migsTransformation!.GetTransformationSettings();
        var keys = transformationSettings.Select(p => p.Key).ToArray();
        foreach (var tSetting in keys)
        {
            if (string.IsNullOrWhiteSpace(transformationSettings[tSetting]))
            {
                transformationSettings[tSetting] = "1";
            }
        }

        var memberInGoodStandingResult = rig.DecisionEngineService.ValidateMemberInGoodStanding(migsTransformation!, true,
            new List<LoanApplicationLogDetail>(), transformationSettings);

        memberInGoodStandingResult.IsEligible.Should().BeTrue();


        migsModelTransformation.Formula = "Model.XYZ + LoanLimit.XYZ = 3";
        rig.DbContext.SaveChanges();
        migsTransformation = rig.DecisionManagerService.GetTransformation(modelId, migsModelTransformation.Id);

        transformationSettings = migsTransformation!.GetTransformationSettings();
        keys = transformationSettings.Select(p => p.Key).ToArray();
        foreach (var tSetting in keys)
        {
            if (string.IsNullOrWhiteSpace(transformationSettings[tSetting]))
            {
                transformationSettings[tSetting] = "1";
            }
        }

        memberInGoodStandingResult = rig.DecisionEngineService.ValidateMemberInGoodStanding(migsTransformation!, true,
            new List<LoanApplicationLogDetail>(), transformationSettings);

        memberInGoodStandingResult.IsEligible.Should().BeFalse();
    }

    [Theory, AutoSubstituteData]
    public async Task GenerateResultTest(IFixture fixture)
    {
        var rig = new DecisionEngineServiceTestRig(fixture);

        var modelId = rig.DbContext.DecisionModels.First().Id;

        rig.AddTransformations();

        var res = rig.DecisionManagerService.GetModelTransformationMaster(modelId);

        var eligTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "Eligibility");
        var migsTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "MemberInGoodStanding");
        var deTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "DecisionEngine");
        var qaTransformation = rig.DbContext.ModelTransformations.First(o => o.Name == "QualifiedAmount");

        //these need to be based on information in deparameters mock
        eligTransformation.Formula = "MW.IsEighteenYearsOrGreater = 1 & MW.IsPrimaryAccountHolder = 1 & MW.HaveOpenShareAccount = 1";
        migsTransformation.Formula = "Product.LengthOfRelationship = Min(Product.LengthOfRelationship, MW.LengthOfRelationship) & MW.LateLoanPayments = 0";

        deTransformation.Formula = "Product.MinimumLoanAmount = Min(QualifiedAmount, Product.MinimumLoanAmount)";
        qaTransformation.Formula = "(Product.PercentageOfAggregateDepositForLoanAmount / 100) * (MW.Product.AggregateAccountDeposit)";

        await rig.DbContext.SaveChangesAsync();

        var logs = new List<LoanApplicationLogDetail>();

        var loanApplication = rig.DbContext.LoanApplications.First();
        var financialInstitutionMemberId = rig.DbContext.FinancialInstitutionMembers.First().Id;
        var product = rig.DbContext.Products.First();
        var decisionModel = rig.DbContext.DecisionModels.First();
        var decisionModelSelection = new DecisionModelSelection(decisionModel.Id, false)
        {
            //have to do this explicitly because we are not going in the normal order
            //(this would be called within LoanApplicationService.ValidateDecisionEngineAsync)
            CachedTransformations = rig.DecisionManagerService.GetTransformationMaster(decisionModel.Id),
        };

        var decisionModelSelectionList = new List<DecisionModelSelection> { decisionModelSelection };

        List<ProductAvailableResult> availableProducts = [];

        //build productavailableresult
        var productAvailableResult = new ProductAvailableResult { Product = product, DecisionModelsSelection = decisionModelSelectionList };
        availableProducts.Add(productAvailableResult);

        //build decisionengineparameters
        DecisionEngineParameters parameters = rig.MockDecisionEngineParameters;

        //test
        var dto = new DecisionEngineDTO
        {
            LoanApplication = loanApplication,
            AvailableProducts = availableProducts,
            FinancialInstitutionMemberId = financialInstitutionMemberId,
            DecisionEngineParameters = parameters,
            NSFSetting = null,
        };

        var resultAllProducts = await rig.DecisionEngineService.GenerateResultAsync(dto, logs);
        resultAllProducts.QualifiedProducts.Count.Should().Be(1);
        resultAllProducts.Result[0].DecisionEngine.IsEligible.Should().BeTrue();
        resultAllProducts.Result[0].DecisionEngine.QualifiedAmount.Should().Be(1716.19M);

        //var jsonOutput = JsonConvert.SerializeObject(resultAllProducts, Formatting.Indented, new JsonSerializerSettings() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore });

        //try with two products, one that qualifies and one that doesn't? or just update a parameter to make it fail
        product.MinimumLoanAmount = 1800;
        await rig.DbContext.SaveChangesAsync();

        dto = new DecisionEngineDTO
        {
            LoanApplication = loanApplication,
            AvailableProducts = availableProducts,
            FinancialInstitutionMemberId = financialInstitutionMemberId,
            DecisionEngineParameters = parameters,
            NSFSetting = null,
        };

        resultAllProducts = await rig.DecisionEngineService.GenerateResultAsync(dto, logs);
        resultAllProducts.QualifiedProducts.Count.Should().Be(0);
        resultAllProducts.Result[0].DecisionEngine.IsEligible.Should().BeFalse();
    }
}