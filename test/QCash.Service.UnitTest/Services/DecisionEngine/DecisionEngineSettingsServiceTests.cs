using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Text;
using AutoFixture;
using Divergic.Logging.Xunit;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using MockQueryable.NSubstitute;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Data.Models.Interfaces;
using QCash.LoanApplication;
using QCash.LoanApplication.Interfaces;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Utilities.Extensions;
using Xunit;
using Xunit.Abstractions;
using Enums = QCash.Service.Models.DecisionEngine.Enums;

namespace QCash.Service.UnitTest.Services.DecisionEngine;

[ExcludeFromCodeCoverage]
public class DecisionEngineSettingsServiceTests //ITestOutputHelper testOutputHelper)
{
    private static ITestOutputHelper? TestOutputHelper { get; set; }
    public DecisionEngineSettingsServiceTests(ITestOutputHelper testOutputHelper) =>
        TestOutputHelper = testOutputHelper;

    private class DecisionEngineSettingsServiceTestRig
    {
        private QCashContext Context { get; set; }
        public IEfPocoService EfPocoService { get; set; }
        private IScoreTypeService ScoreTypeService { get; set; }
        private IDecisionEngineParametersProvider DecisionEngineParametersProvider { get; set; }

        private DecisionEngineSettingsService DecisionEngineSettingsService { get; set; }
        public readonly Guid BusinessRuleRecordId = Guid.NewGuid();
        public DecisionEngineSettingsServiceTestRig(IFixture fixture)
        {
            if (TestOutputHelper != null)
            {
                var loggerFactory = LogFactory.Create(TestOutputHelper);
                fixture.RegisterLoggerFactory(loggerFactory);
            }
            Context = fixture.Freeze<QCashContext>();
            Context.FinancialInstitutions.Add(new FinancialInstitution
            {
                Id = IFixtureExtensions.FinancialInstitutionId,
                Slug = IFixtureExtensions.FiSlug,
                Name = "Test1 Name",
                NoReplySubdomain = "TestSubd1",
                Address = "",
                City = "",
                ContactName = "",
                ContactPhone = "",
                GlLoanAccount = "",
                MailingAddress = "",
                State = "",
                Zip = "12345",
                TimeStamp = [],
            });

            Context.ExclusionLoans.Add(new ExclusionLoan
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Name = "Test Exclusion Loan 1",
                Description = "Description",
                Slug = "Test_Exclusion_Loan_1",
                Abrv = "",
                AppAbrv = "",
                TimeStamp = [],
                IsDeleted = false,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
            });
            
            Context.ExclusionLoans.Add(new ExclusionLoan
            {
                Id = Guid.NewGuid(),
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                Name = "Test Exclusion Loan 2",
                Description = "Description",
                Slug = "Test_Exclusion_Loan_2",
                Abrv = "",
                AppAbrv = "",
                TimeStamp = [],
                IsDeleted = false,
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
            });

            Context.SaveChanges();
            EfPocoService = fixture.Freeze<IEfPocoService>();
            ScoreTypeService = fixture.Freeze<IScoreTypeService>();
            DecisionEngineParametersProvider = fixture.Freeze<IDecisionEngineParametersProvider>();
            DecisionEngineSettingsService = fixture.Freeze<DecisionEngineSettingsService>();
            //DecisionEngineSettingsService = new DecisionEngineSettingsService(Context, EfPocoService, ScoreTypeService, DecisionEngineParametersProvider);
        }

        public Enums.DecisionEngineLookupTypeEnum GetLookupTypeEnum(string lookupTypeName) =>
            DecisionEngineSettingsService.GetLookupTypeEnum(lookupTypeName);

        public async Task<IQueryable<IEFPocoLookup>> GetQueryForLookupAsync(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum) =>
            await DecisionEngineSettingsService.GetQueryForLookupAsync(lookupTypeEnum);

        public string GetNameHeader(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum) =>
            DecisionEngineSettingsService.GetNameHeader(lookupTypeEnum);

        public List<DecisionEngineLookupListType> GetLookups() =>
            DecisionEngineSettingsService.GetLookups();

        public Type GetLookupDbObjectType(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum) =>
            DecisionEngineSettingsService.GetLookupDbObjectType(lookupTypeEnum);

        public bool GetSupportsForeignLanguageStatus(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum) =>
            DecisionEngineSettingsService.GetSupportsForeignLanguageStatus(lookupTypeEnum);

        public DecisionEngineLookupListType GetLookup(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum)
        {
            return DecisionEngineSettingsService.GetLookup(lookupTypeEnum);
        }

        public Enums.DecisionEngineLookupTypeEnum GetLookupTypeEnumFromScoreType(Enums.ScoreTypesEnum scoreTypeEnum) =>
            DecisionEngineSettingsService.GetLookupTypeEnumFromScoreType(scoreTypeEnum);

        public async Task<GenericActionResult> SaveAsync(bool filterIsValid = true, bool scoreTypeIsValid = true, bool saveIsSuccessful = true)
        {
            var dto = GetSampleBusinessRuleDto();
            DecisionEngineParametersProvider.ParseProductTypeFilterExpressionAsync(Arg.Any<Guid>(), Arg.Any<string>(), dto.ScoreTypeSlug)
                .Returns(Task.FromResult(new ProductTypeFilterExpressionParsingResult()
                {
                    IsValid = filterIsValid,
                    ErrorMessage = null,
                }));

            ScoreTypeService.GetScoreTypeRecord(dto.ScoreTypeSlug)
                .Returns(scoreTypeIsValid ? new ScoreTypeDto
                    {
                        Id = Guid.NewGuid(),
                        Slug = "asxdf",
                        Name = "asefd",
                    }
                    : null);

            EfPocoService.CreateOrUpdateAsync(dto.Id, IFixtureExtensions.FinancialInstitutionId, dto.TimeStamp,
                Arg.Any<PerformCreateOrUpdateOptions<BusinessRule>>())
                .Returns(Task.FromResult(new GetOrCreateRecordResult<BusinessRule>
                {
                    IsSuccessful = saveIsSuccessful,
                    ErrorMessage = saveIsSuccessful ? null : "Databases are just weird",
                    Record = null,
                    FoundExistingRecord = false,
                    CreatingNewRecord = false,
                    EditingExistingRecord = false,
                }));

            return await DecisionEngineSettingsService.SaveAsync(IFixtureExtensions.FinancialInstitutionId, dto);
        }

        public bool ShouldHideScore(Enums.ScoreTypesEnum scoreTypesEnum) =>
            DecisionEngineSettingsService.ShouldHideScore(scoreTypesEnum);

        public async Task<GenericActionResult> DeleteItemAsync()
        {
            var id = Guid.NewGuid();
            var dto = new DeleteItemDto() { DeleteItemId = id, TimeStamp = [], };

            EfPocoService.DeleteItemAsync<Weekday>(Arg.Any<DeleteItemDto>())
                .Returns(Task.FromResult(
                    new GenericActionResult() { IsSuccessful = true, ErrorMessage = null, }));

            return await DecisionEngineSettingsService.DeleteItemAsync(Enums.DecisionEngineLookupTypeEnum.Weekday, dto);
        }

        public async Task<BusinessRuleDto?> GetBusinessRuleAsync()
        {
            var id = BusinessRuleRecordId;
            var q = new List<BusinessRule>()
            {
                new()
                {
                    Id = BusinessRuleRecordId,
                    Detail = "Detail1",
                    ScoreType = new ScoreType()
                    {
                        FinancialInstitution = new FinancialInstitution()
                        {
                            Slug = IFixtureExtensions.FiSlug,
                        },
                    },
                },
            }.BuildMockDbSet();
            EfPocoService.GetQuery(Arg.Any<Func<IQueryable<BusinessRule>, IQueryable<BusinessRule>>>())
                .Returns(q);
            return await DecisionEngineSettingsService.GetBusinessRuleAsync(Enums.ScoreTypesEnum.VoidedTransaction, id);
        }

        private BusinessRuleDto GetSampleBusinessRuleDto() =>
            new()
            {
                Id = Guid.NewGuid(),
                TimeStamp = [],
                FiSlug = IFixtureExtensions.FiSlug,
                Name = "",
                Detail = "",
                Score = 0,
                ScoreTypeSlug = "ScoreTypeSlug1",
            };

        public async Task<ModelStateDictionary> ValidateModelAsync(bool detailIsGood)
        {
            var dto = GetSampleBusinessRuleDto();
            dto.Detail = detailIsGood ? "something Is '1'" : "baddetails";
            var modelState = new ModelStateDictionary();
            await DecisionEngineSettingsService.ValidateModelAsync(dto, modelState);
            return modelState;
        }

        public bool IsDvLookup(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum) =>
            DecisionEngineSettingsService.IsDvLookup(lookupTypeEnum);

        public string GetPageTitle(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum, bool creatingRecord) =>
            DecisionEngineSettingsService.GetPageTitle(lookupTypeEnum,creatingRecord);
    }

    [Theory, AutoSubstituteData]
    public void GetLookupTypeEnumNormalSuccess(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var result = rig.GetLookupTypeEnum("ScoreType");
        result.Should().Be(Enums.DecisionEngineLookupTypeEnum.ScoreType);
    }

    [Theory, AutoSubstituteData]
    public void GetLookupTypeEnumNotValue(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var result = rig.GetLookupTypeEnum("Nothing");
        result.Should().Be(Enums.DecisionEngineLookupTypeEnum.Unknown);
    }

    [Theory, AutoSubstituteData]
    public void GetLookupDbObjectTypeSuccess(IFixture fixture) {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        Type result = rig.GetLookupDbObjectType(Enums.DecisionEngineLookupTypeEnum.InvoicePlan);
        result.Should().BeSameAs(typeof(InvoicePlan));
    }

    [Theory, AutoSubstituteData]
    public void GetLookupDbObjectTypeNoType(IFixture fixture) {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        Assert.Throws<ArgumentNullException>(() =>
        {
            Type result = rig.GetLookupDbObjectType(Enums.DecisionEngineLookupTypeEnum.Unknown);
        });
    }

    [Theory, AutoSubstituteData]
    public async Task GetLookupQueryNormalAsync(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var query = await rig.GetQueryForLookupAsync(Enums.DecisionEngineLookupTypeEnum.ExclusionLoan);
        var list = query.ToList();
        list.Count().Should().Be(2);
        list.Any(a => a.Name == "Test Exclusion Loan 1").Should().BeTrue();
        list.Any(a => a.Name == "Test Exclusion Loan 2").Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public void GetNameHeaderCode(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var result = rig.GetNameHeader(Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeLoanInitiate);
        result.Should().Be("Code");
    }

    [Theory, AutoSubstituteData]
    public void GetNameHeaderValue(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var result = rig.GetNameHeader(Enums.DecisionEngineLookupTypeEnum.AccountDescriptionMapping);
        result.Should().Be("Value");
    }

    [Theory, AutoSubstituteData]
    public void GetNameHeaderName(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var result = rig.GetNameHeader(Enums.DecisionEngineLookupTypeEnum.Unknown);
        result.Should().Be("Name");
    }


    [Theory, AutoSubstituteData]
    public void GetLookupsNormal(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var results = rig.GetLookups();
        var result = results.Single(a =>
            a.LookupType == Enums.DecisionEngineLookupTypeEnum.ExclusionLoan);

        result.LinkText.Should().Be("Exclusion Loans");
        result.CssClass.Should().Be("exclusion-loans");
        result.Title.Should().Be("Exclusion Loans lookup list");
        result.CreateTitle.Should().Be("Exclusion Loans");
        result.ListHelpTextId.Should().Be(Guid.Parse("5B44DDC7-A4F1-47B3-989C-59D35DA83098"));

        result.NameHeader.Should().Be("Code");
        result.ShowInUi.Should().Be(true);
        result.UIGroupEnum.Should().Be(Enums.DecisionEngineUIGroupEnum.EligibilityAttributes);
        result.UIOrder.Should().Be(1);
    }

    [Theory, AutoSubstituteData]
    public void GetLookupsImplementIefPocoLookup(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var results = rig.GetLookups();

        var assembly = Assembly.GetAssembly(typeof(QCashContext));
        assembly.ThrowIfNull();
        var badClassList = new List<string>();
        var resultDetails = new StringBuilder();
        foreach (var item in results.OrderBy(a => a.LookupType.ToString()))
        {
            Type? lookupTypeType = assembly.ExportedTypes.FirstOrDefault(a => a.Name == item.LookupType.ToString());
            if (item.HasIndividualListUi && item.HasIndividualListUi)
            {
                // this class has a specific custom UI, so it does not need to implement IEFPocoLookup to work correctly.
            }
            else if (lookupTypeType == null)
            {
                badClassList.Add(item.LookupType.ToString());
                resultDetails.AppendLine("Can't find type: " + item.LookupType.ToString());
            }
            else
            {
                lookupTypeType.ThrowIfNull();
                var isGood = lookupTypeType.IsAssignableTo(typeof(IEFPocoLookup));
                if (!isGood)
                {
                    badClassList.Add(lookupTypeType.Name);
                    resultDetails.AppendLine("Type doesn't implement IEFPocoLookup: " + lookupTypeType.Name);
                }
            }
        }
        if (badClassList.Any())
        {
            Assert.Fail(resultDetails.ToString());
        }
    }

    [Theory, AutoSubstituteData]
    public void GetSupportsForeignLanguageStatus(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        rig.GetSupportsForeignLanguageStatus(Enums.DecisionEngineLookupTypeEnum.Weekday).Should().BeTrue();
        rig.GetSupportsForeignLanguageStatus(Enums.DecisionEngineLookupTypeEnum.PurposeOfLoanType).Should().BeTrue();

        rig.GetSupportsForeignLanguageStatus(Enums.DecisionEngineLookupTypeEnum.ScoreType).Should().BeFalse();

        Assert.Throws<Exception>(() => rig.GetSupportsForeignLanguageStatus(Enums.DecisionEngineLookupTypeEnum.Unknown).Should().BeFalse());
    }

    [Theory, AutoSubstituteData]
    public void GetLookup(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var result = rig.GetLookup(Enums.DecisionEngineLookupTypeEnum.LoanCode);
        result.Should().NotBeNull();

        result.LinkText.Should().Be("Loan Codes");
        result.LookupType.Should().Be(Enums.DecisionEngineLookupTypeEnum.LoanCode);
        result.CssClass.Should().Be("loan-code");
        result.Title.Should().Be("Loan Codes lookup list");
        result.CreateTitle.Should().Be("Loan Code");
        result.ShowInUi.Should().Be(true);
    }

    [Theory, AutoSubstituteData]
    public void GetLookupTypeEnumFromScoreType(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        rig.GetLookupTypeEnumFromScoreType(Enums.ScoreTypesEnum.Unknown)
            .Should().Be(Enums.DecisionEngineLookupTypeEnum.Unknown);
        rig.GetLookupTypeEnumFromScoreType(Enums.ScoreTypesEnum.NSFTransactionCode)
            .Should().Be(Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode);
        rig.GetLookupTypeEnumFromScoreType(Enums.ScoreTypesEnum.DepositsExcludedForAggregateDepositTotal)
            .Should().Be(Enums.DecisionEngineLookupTypeEnum.DepositsToBeExcludedForAggregateDepositTotal);
        rig.GetLookupTypeEnumFromScoreType(Enums.ScoreTypesEnum.DepositsIncludedForAggregateDepositTotal)
            .Should().Be(Enums.DecisionEngineLookupTypeEnum.DepositsToBeIncludedForAggregateDepositTotal);
        rig.GetLookupTypeEnumFromScoreType(Enums.ScoreTypesEnum.VoidedTransaction)
            .Should().Be(Enums.DecisionEngineLookupTypeEnum.VoidedTransactions);
    }

    [Theory, AutoSubstituteData]
    public async Task SaveAsyncInvalidFilter(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var result = await rig.SaveAsync(filterIsValid:false);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Rule Detail did not pass validation. Please re-check your input.");
    }

    [Theory, AutoSubstituteData]
    public async Task SaveAsyncInvalidScoreType(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var result = await rig.SaveAsync(scoreTypeIsValid:false);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be($"No such score type found: ScoreTypeSlug1");
    }

    [Theory, AutoSubstituteData]
    public async Task SaveAsyncSaveFail(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var result = await rig.SaveAsync(saveIsSuccessful:false);
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Databases are just weird");
    }

    [Theory, AutoSubstituteData]
    public async Task SaveAsyncSuccessful(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var result = await rig.SaveAsync();
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
    }

    [Theory, AutoSubstituteData]
    public void ShouldHideScore(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        rig.ShouldHideScore(Enums.ScoreTypesEnum.DepositsExcludedForAggregateDepositTotal).Should().Be(true);
        rig.ShouldHideScore(Enums.ScoreTypesEnum.DepositsIncludedForAggregateDepositTotal).Should().Be(true);
        rig.ShouldHideScore(Enums.ScoreTypesEnum.VoidedTransaction).Should().Be(true);
        rig.ShouldHideScore(Enums.ScoreTypesEnum.NSFTransactionCode).Should().Be(true);
        rig.ShouldHideScore(Enums.ScoreTypesEnum.Unknown).Should().Be(false);
    }

    [Theory, AutoSubstituteData]
    public async Task DeleteItemAsyncSuccess(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var result = await rig.DeleteItemAsync();
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
        await rig.EfPocoService.Received().DeleteItemAsync<Weekday>(Arg.Any<DeleteItemDto>());
    }

    [Theory, AutoSubstituteData]
    public async Task GetItemAsync(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var result = await rig.GetBusinessRuleAsync();
        Assert.NotNull(result);
        result.Id.Should().Be(rig.BusinessRuleRecordId);
        result.Detail.Should().Be("Detail1");
    }

    [Theory, AutoSubstituteData]
    public async Task ValidateModelAsync(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        var resultGood = await rig.ValidateModelAsync(detailIsGood:true);
        resultGood.IsValid.Should().BeTrue();

        var resultBad = await rig.ValidateModelAsync(detailIsGood:false);
        resultBad.IsValid.Should().BeFalse();
    }

    [Theory, AutoSubstituteData]
    public void IsDvLookup(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        rig.IsDvLookup(Enums.DecisionEngineLookupTypeEnum.CheckingAccountType).Should().BeTrue();
        rig.IsDvLookup(Enums.DecisionEngineLookupTypeEnum.SavingAccountType).Should().BeTrue();
        rig.IsDvLookup(Enums.DecisionEngineLookupTypeEnum.IncludedNumberOfElectronicTransactionsTellerId).Should().BeTrue();
    }

    [Theory, AutoSubstituteData]
    public void GetPageTitle(IFixture fixture)
    {
        var rig = new DecisionEngineSettingsServiceTestRig(fixture);
        rig.GetPageTitle(Enums.DecisionEngineLookupTypeEnum.AccountWarningCode, true)
            .Should().Be("Create a new Account Warning Code lookup item");
        rig.GetPageTitle(Enums.DecisionEngineLookupTypeEnum.AccountWarningCode, false)
            .Should().Be("Edit Account Warning Code lookup list lookup item");
    }
}
