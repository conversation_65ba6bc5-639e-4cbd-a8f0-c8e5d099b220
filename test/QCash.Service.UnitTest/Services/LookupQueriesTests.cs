using AutoFixture;
using EntityFrameworkCore.Testing.NSubstitute;
using FluentAssertions;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Services;
using Xunit;

namespace QCash.Service.UnitTest.Services;

public class LookupQueriesTests
{
    private class LookupQueriesTestsTestRig
    {
        private QCashContext Context { get; set; }
        private LookupQueries LookupQueries { get; set; }

        private readonly Guid _appIdWrong = Guid.NewGuid();
        public readonly Guid _loanTypeId = Guid.NewGuid();
        private const string FiSlug = IFixtureExtensions.FiSlug;

        public LookupQueriesTestsTestRig(IFixture fixture)
        {
            Context = fixture.Freeze<QCashContext>();
            LookupQueries = new LookupQueries(Context);

            Context.FinancialInstitutions.Add(
                new FinancialInstitution
                {
                    Id = IFixtureExtensions.FinancialInstitutionId,
                    Slug = FiSlug,
                    Name = "Test1 Name",
                    NoReplySubdomain = "TestSubd1",
                    Address = "",
                    City = "",
                    ContactName = "",
                    ContactPhone = "",
                    GlLoanAccount = "",
                    MailingAddress = "",
                    State = "",
                    Zip = "12345",
                    TimeStamp = [],
                });
            Context.SaveChanges();

            Context.Set<LoanCategory>().AddRange(
                new LoanCategory() // this is the one good record that should be returned.
                {
                    Id = Guid.NewGuid(),
                    FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                    Slug = "LoanCategorySlug1",
                    IsDeleted = false,
                    Abrv = "",
                    AppAbrv = "",
                    Name = "Item1",
                    TimeStamp = [],
                },
                new LoanCategory()
                {
                    Id = Guid.NewGuid(),
                    FinancialInstitutionId = _appIdWrong,
                    Slug = "LoanCategorySlug2",
                    IsDeleted = false,
                    Abrv = "",
                    AppAbrv = "",
                    Name = "Item2",
                    TimeStamp = [],
                },
                new LoanCategory()
                {
                    Id = Guid.NewGuid(),
                    FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                    Slug = "LoanCategorySlug3",
                    IsDeleted = true,
                    Abrv = "",
                    AppAbrv = "",
                    Name = "Item3",
                    TimeStamp = [],
                });

            Context.Set<LoanType>().AddRange(
                new LoanType()      // this is the one good record that should be returned.
                {
                    Id = _loanTypeId,
                    FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
                    Slug = "LoanTypeSlug1",
                    Abrv = "FB",
                    AppAbrv = "",
                    Name = "LoanType1",
                    TimeStamp = [],
                },
                new LoanType()
                {
                    Id = Guid.NewGuid(),
                    FinancialInstitutionId = _appIdWrong,
                    Slug = "LoanTypeSlug2",
                    Abrv = "",
                    AppAbrv = "",
                    Name = "LoanType2",
                    TimeStamp = [],
                }
            );

            Context.Set<AspNetRole>().AddRange(
                new AspNetRole()
                {
                    Id = Guid.NewGuid(),
                    Name = "Role1"
                },
                new AspNetRole()
                {
                    Id = Guid.NewGuid(),
                    Name = "Role2",
                }
            );

            Context.SaveChanges();
        }

        public IQueryable<LoanCategory> GetLoanCategoriesByGuid() =>
            LookupQueries.GetLoanCategories(IFixtureExtensions.FinancialInstitutionId);

        public async Task<IQueryable<LoanCategory>> GetLoanCategoriesBySlug(Guid loanTypeId) =>
            await LookupQueries.GetLoanCategoriesQAsync(FiSlug, loanTypeId);

        public IQueryable<LoanType> GetLoanTypes() =>
            LookupQueries.GetLoanTypes(FiSlug);

        public IQueryable<AspNetRole> GetRolesQueryable() =>
            LookupQueries.GetRolesQueryable();
    }

    /// <summary>
    /// There are 3 records in the database, but only one should be returned.
    /// One is deleted, one is for another FI
    /// </summary>
    [Theory, AutoSubstituteData]
    public void GetLoanCategoriesByGuid(IFixture fixture)
    {
        var rig = new LookupQueriesTestsTestRig(fixture);
        var categoriesQ = rig.GetLoanCategoriesByGuid();
        var categories = categoriesQ.ToList();
        categories.Count().Should().Be(1);
        var category = categories.Single();
        category.Name.Should().Be("Item1");
        category.Slug.Should().Be("LoanCategorySlug1");
    }

    [Theory, AutoSubstituteData]
    public async Task GetLoanCategoriesBySlug(IFixture fixture)
    {
        var rig = new LookupQueriesTestsTestRig(fixture);
        var categoriesQ = await rig.GetLoanCategoriesBySlug(rig._loanTypeId);
        var categories = categoriesQ.ToList();
        categories.Count().Should().Be(1);
        var category = categories.Single();
        category.Name.Should().Be("Item1");
        category.Slug.Should().Be("LoanCategorySlug1");
    }

    [Theory, AutoSubstituteData]
    public void GetLoanTypes(IFixture fixture)
    {
        var rig = new LookupQueriesTestsTestRig(fixture);
        var typesQ = rig.GetLoanTypes();
        var types = typesQ.ToList();
        types.Count().Should().Be(1);
        var t = types.Single();
        t.Name.Should().Be("LoanType1");
        t.Slug.Should().Be("LoanTypeSlug1");
    }

    [Theory, AutoSubstituteData]
    public void GetRoles(IFixture fixture)
    {
        var rig = new LookupQueriesTestsTestRig(fixture);
        var roleQ = rig.GetRolesQueryable();
        roleQ.Count().Should().Be(2);
        roleQ.Any(r => r.Name == "Role1").Should().BeTrue();
        roleQ.Any(r => r.Name == "Role2").Should().BeTrue();
    }
}

