using System.Diagnostics.CodeAnalysis;
using AutoFixture;
using FluentAssertions;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NSubstitute;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.User;
using QCash.Service.Services.User.Interfaces;
using QCash.Utils.Core;
using Xunit;
using static QCash.Service.Models.FIConfiguration.UserManagement.Constants;

namespace QCash.Service.UnitTest.Services.User;

[ExcludeFromCodeCoverage]
public class UserServiceTests
{
    private class UserServiceTestRig
    {
        private ILogger<UserService> Logger { get; set; }
        public QCashContext Context { get; set; }
        private UserService UserService { get; set; }
        private ISystemClockService SystemClockService { get; set; }
        private ILookupQueries LookupQueries { get; set; }
        public IRoleService RoleService { get; set; }
        public IAuthUserService AuthUserService { get; set; }
        private IErrorNotificationService ErrorNotificationService { get; set; }
        public IGuidExtensionService GuidExtensionService { get; set; }
        public IPasswordHasher<AspNetUser> PasswordHasher { get; set; }
        public IEfPocoService EfPocoService { get; set; }
        public readonly Guid UserGuidDefault = Guid.NewGuid();
        public readonly Guid DeManagerId = Guid.NewGuid();
        public readonly Guid RaRecordId = Guid.NewGuid();

        public UserServiceTestRig(IFixture fixture)
        {
            Context = fixture.Create<QCashContext>();
            var fi1 = new FinancialInstitution
            {
                Id = IFixtureExtensions.FinancialInstitutionId,
                Name = "Test FI",
                Slug = "test-fi",
                Address = "123 Test St",
                City = "Test City",
                State = "TS",
                Zip = "12345",
                ContactName = "Test Contact",
                ContactPhone = "************",
                MailingAddress = "123 Test St",
                GlLoanAccount = "12345",
                DateCreatedUtc = DateTime.UtcNow,
                DateUpdatedUtc = DateTime.UtcNow,
                TimeStamp = [],
            };
            Context.FinancialInstitutions.Add(fi1);
            
            AspNetUser u = new()
            {
                Id = UserGuidDefault,
                FirstName = "Bob",
                LastName = "Jones",
                Email = "<EMAIL>",
                UserName = "BJones",
                PhoneNumber = "(*************",
                IsApproved = true,
                LockoutEnd = null,
                DateUpdatedUtc = DateTime.Parse("8/1/2000 9:00 am"),
                DateCreatedUtc = DateTime.Parse("3/1/2000 10:00 am"),
                TimeStamp = [0, 1, 2, 3],
                FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            };
            Context.AspNetUsers.Add(u);
            Context.AspNetRoles.Add(new AspNetRole
            {
                Id = DeManagerId,
                Name = DeManagerAbrv,
            });
            Context.SaveChanges();

            SystemClockService = fixture.Freeze<ISystemClockService>();
            SystemClockService.GetSystemTimeUtc()
                .Returns(DateTime.Parse("10/1/2024 8:00 am"));

            LookupQueries = fixture.Freeze<ILookupQueries>();
            RoleService = fixture.Freeze<IRoleService>();
            RoleService.IsSystemAdmin(Arg.Any<List<QListItem<Guid>>>() ).Returns(false);
            RoleService.IsDeManager(Arg.Any<List<QListItem<Guid>>>() ).Returns(false);
            RoleService.IsFIManager(Arg.Any<List<QListItem<Guid>>>() ).Returns(false);
            RoleService.IsSuperUser(Arg.Any<List<QListItem<Guid>>>() ).Returns(false);
            AuthUserService = fixture.Freeze<IAuthUserService>();
            ErrorNotificationService = fixture.Freeze<IErrorNotificationService>();
            GuidExtensionService = fixture.Freeze<IGuidExtensionService>();
            EfPocoService = fixture.Freeze<IEfPocoService>();
            Logger = fixture.Freeze<ILogger<UserService>>();
            PasswordHasher = fixture.Freeze<IPasswordHasher<AspNetUser>>();

            UserService = fixture.Create<UserService>();
        }

        public async Task<UserDetailsPageDTO?> GetPageDataAsync(string userName) =>
            await UserService.GetUserPageDataAsync(userName);

        public GenericActionResult GetUserCanUpdateRole(List<QListItem<Guid>> userRoles, string userNameBeingEdited, string loggedInUserRoles, string userNameLoggedIn) =>
            UserService.GetUserCanUpdateRole(userRoles, userNameBeingEdited, loggedInUserRoles, userNameLoggedIn);

        public async Task<GenericActionResult> SaveAsync(Guid financialInstitutionId, string userName,
            UserDetailsPageDTO dto) =>
            await UserService.SaveAsync(financialInstitutionId, userName, dto);

        public void MockPerformCreateOrUpdateAsyncAspNetUser(string? getUserError = null, Guid? userId = null)
        {
            GetOrCreateRecordResult<AspNetUser> result;
            if (!string.IsNullOrEmpty(getUserError))
            {
                result = new GetOrCreateRecordResult<AspNetUser>()
                {
                    Record = null,
                    CreatingNewRecord = false,
                    EditingExistingRecord = false,
                    FoundExistingRecord = true,
                    ErrorMessage = getUserError,
                    IsSuccessful = false,
                };
            } else
            {
                result = new GetOrCreateRecordResult<AspNetUser>()
                {
                    Record = new AspNetUser()
                    {
                        Id = Guid.NewGuid(),
                        UserName = "BJones",
                    },
                    CreatingNewRecord = false,
                    EditingExistingRecord = false,
                    FoundExistingRecord = true,
                    ErrorMessage = string.Empty,
                    IsSuccessful = true,
                };
            }
            EfPocoService.CreateOrUpdateAsync(userId ?? UserGuidDefault, IFixtureExtensions.FinancialInstitutionId, Arg.Any<byte[]>(),
                    Arg.Any<PerformCreateOrUpdateOptions<AspNetUser>>())
                .Returns(Task.FromResult(result));
        }
    }

    [Theory, AutoSubstituteData]
    public async Task GetPageDataAsyncNoSuchUser(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        var roles = new List<QListItem<Guid>>();
        var result = await rig.GetPageDataAsync("NOBODY");
        Assert.Null(result);
    }

    [Theory, AutoSubstituteData]
    public async Task GetPageDataAsyncBasics(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        var roles = new List<QListItem<Guid>>();
        var result = await rig.GetPageDataAsync("BJones");
        Assert.NotNull(result);
        result.Id.Should().Be(rig.UserGuidDefault);
        result.FirstName.Should().Be("Bob");
        result.LastName.Should().Be("Jones");
        result.Email.Should().Be("<EMAIL>");

        result.PhoneNumber.Should().Be("(*************");
        result.IsApproved.Should().Be(true);
        result.IsLockedOut.Should().Be(false);
        result.DateUpdatedUtc.Should().NotBe(DateTime.Parse("8/1/2000 9:00 am"));
        result.DateCreatedUtc.Should().NotBe(DateTime.Parse("3/1/2000 10:00 am"));
        result.TimeStamp.Should().BeEquivalentTo(new byte[] {0, 1, 2, 3});
    }

    [Theory, AutoSubstituteData]
    public void GetUserCanUpdateRoleAsyncOwnRecordFail(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        rig.RoleService.CanEditOwnRoles(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns(false);
        var result = rig.GetUserCanUpdateRole([], "", "", "");
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("User does not have permissions to edit their own record.");
    }

    [Theory, AutoSubstituteData]
    public void GetUserCanUpdateRoleAsyncRoleAdminAbrvDownChainOfCommandSuccess(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        rig.RoleService.CanEditOwnRoles(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns(true);
        var result = rig.GetUserCanUpdateRole([], "",
            RoleAdminAbrv, "");
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
    }

    [Theory, AutoSubstituteData]
    public void GetUserCanUpdateRoleAsyncRoleAdminAbrvUpChainOfCommandFail(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        rig.RoleService.CanEditOwnRoles(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns(true);
        rig.RoleService.IsFIManager(Arg.Any<List<QListItem<Guid>>>() )
            .Returns(true);
        var result = rig.GetUserCanUpdateRole([], "", RoleAdminAbrv, "");
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("Role Admins cannot edit super users / FI Managers / system admins.");
    }

    [Theory, AutoSubstituteData]
    public void GetUserCanUpdateRoleAsyncFiManagerDownChainOfCommandSuccess(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        rig.RoleService.CanEditOwnRoles(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns(true);
        var result = rig.GetUserCanUpdateRole([], "",
            FiManagerAbrv, "");
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
    }

    [Theory, AutoSubstituteData]
    public void GetUserCanUpdateRoleAsyncFiManagerUpChainOfCommandFail(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        rig.RoleService.CanEditOwnRoles(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns(true);
        rig.RoleService.IsDeManager(Arg.Any<List<QListItem<Guid>>>() ).Returns(true);
        var result = rig.GetUserCanUpdateRole([], "",
            FiManagerAbrv, "");
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("FI Managers cannot edit super users / system admins / DE Managers.");
    }

    [Theory, AutoSubstituteData]
    public void GetUserCanUpdateRoleAsyncSystemAdminDownChainOfCommandSuccess(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        rig.RoleService.CanEditOwnRoles(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns(true);
        rig.RoleService.IsDeManager(Arg.Any<List<QListItem<Guid>>>() ).Returns(true);
        var result = rig.GetUserCanUpdateRole([], "",
            SystemAdminAbrv, "");
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
    }

    [Theory, AutoSubstituteData]
    public void GetUserCanUpdateRoleAsyncSystemAdminAbrvUpChainOfCommandFail(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        rig.RoleService.CanEditOwnRoles(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns(true);
        rig.RoleService.IsSystemAdmin(Arg.Any<List<QListItem<Guid>>>() ).Returns(true);
        var result = rig.GetUserCanUpdateRole([], "",
            SystemAdminAbrv, "");
        result.IsSuccessful.Should().BeFalse();
        result.ErrorMessage.Should().Be("System admins cannot edit super users / other system admins.");
    }

    [Theory, AutoSubstituteData]
    public void GetUserCanUpdateRoleAsyncSuperUserDownChainOfCommandSuccess(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        rig.RoleService.CanEditOwnRoles(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns(true);
        var result = rig.GetUserCanUpdateRole([], "",
            SuperUserAbrv, "");
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
    }
    
    [Theory, AutoSubstituteData]
    public async Task SaveNewUser(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        var dto = new UserDetailsPageDTO()
        {
            Id = Guid.Empty,
            UserName = "nobodynobody",
            TimeStamp = [],
        };
        rig.MockPerformCreateOrUpdateAsyncAspNetUser(userId:Guid.Empty);
        var result = await rig.SaveAsync(IFixtureExtensions.FinancialInstitutionId, dto.UserName, dto);
        result.IsSuccessful.Should().BeTrue();
        result.ErrorMessage.Should().BeNull();
    }

    [Theory, AutoSubstituteData]
    public async Task SaveNoSuchUser(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        var dto = new UserDetailsPageDTO()
        {
            Id = rig.UserGuidDefault,
            UserName = "nobodynobody",
            TimeStamp = [],
        };
        rig.MockPerformCreateOrUpdateAsyncAspNetUser("Evil bad error: can't find the record");
        var result = await rig.SaveAsync(IFixtureExtensions.FinancialInstitutionId, dto.UserName, dto);
        result.ErrorMessage.Should().Be("Evil bad error: can't find the record");
    }

    /// <summary>
    /// Verify that the user service saves the user record and the RaEmail record
    /// </summary>
    [Theory, AutoSubstituteData]
    public async Task SaveBasics(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        var dto = new UserDetailsPageDTO()
        {
            Id = rig.UserGuidDefault,
            UserName = "BJones",
            FirstName = "Bob",
            LastName = "Jones",
            PhoneNumber = "(*************",
            TimeStamp = [0, 1, 2, 3],
        };
        rig.MockPerformCreateOrUpdateAsyncAspNetUser();

        await rig.SaveAsync(IFixtureExtensions.FinancialInstitutionId, dto.UserName, dto);
        await rig.Context.SaveChangesAsync();

        var userAfterEdit = rig.Context.AspNetUsers.Single(a => a.UserName == dto.UserName);
        userAfterEdit.PhoneNumber.Should().Be("(*************");        
        await rig.EfPocoService.Received().CreateOrUpdateAsync(rig.UserGuidDefault, IFixtureExtensions.FinancialInstitutionId, Arg.Any<byte[]>(),
            Arg.Any<PerformCreateOrUpdateOptions<AspNetUser>>());
    }

    [Theory, AutoSubstituteData]
    public async Task SaveRoles(IFixture fixture)
    {
        var rig = new UserServiceTestRig(fixture);
        var dto = new UserDetailsPageDTO()
        {
            Id = rig.UserGuidDefault,
            UserName = "UserBob1",
            PhoneNumber = "(*************",
            RoleTypeId = ManagerRoleTypeGuid,
            SelectedRoles =
            [
                new QListItemWithSelection<Guid>() { Value = rig.DeManagerId, Text = DeManagerAbrv, },
            ],
            TimeStamp = [0, 1, 2, 3],
        };
        rig.RoleService.CanEditOwnRoles(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<string>())
            .Returns(true);
        rig.AuthUserService.Roles
            .Returns(SystemAdminAbrv);

        rig.MockPerformCreateOrUpdateAsyncAspNetUser();
        await rig.SaveAsync(IFixtureExtensions.FinancialInstitutionId, dto.UserName, dto);
        await rig.Context.SaveChangesAsync();

        var userAfterEdit = rig.Context.AspNetUsers
            .Include(u => u.Roles)
            .Single(a => a.UserName == dto.UserName);
        userAfterEdit.PhoneNumber.Should().Be("(*************");

        // Can't unit test this, as the in-memory EF context doesn't seem to save/load relationship changes.  So .Roles comes back empty.
        //userAfterEdit.Roles.Count.Should().Be(1);
        // var role = userAfterEdit.Roles.Single();
        // role.Name.Should().Be(DeManagerAbrv);
    }
}
