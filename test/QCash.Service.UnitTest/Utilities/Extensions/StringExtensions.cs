using QCash.Service.Utilities.Extensions;
using Xunit;

namespace QCash.Service.UnitTest.Utilities.Extensions;

public class StringExtensions
{
    [Theory]
    [InlineData("1 and 2", "1", "2", "")]
    [InlineData("1, 2 and 3", "1", "2", "3")]
    [InlineData("2 and 3", "", "2", "3")]
    public void ReturnsCorrectResults(string expectedResult,
        string string1, string string2, string string3)
    {
        var input = new[] { string1, string2, string3 }.Where(r => !string.IsNullOrWhiteSpace(r));
        string actualResult = input.JoinWithConjunction(", ", " and ");
        Assert.Equal(expectedResult, actualResult);
    }
}