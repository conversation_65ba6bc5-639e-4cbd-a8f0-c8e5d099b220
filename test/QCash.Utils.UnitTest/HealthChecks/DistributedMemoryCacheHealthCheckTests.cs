using System.Globalization;
using System.Text;
using AutoFixture;
using FluentAssertions;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using NSubstitute;
using NSubstitute.ExceptionExtensions;
using QCash.Service.UnitTest;
using QCash.Utils.Core;
using QCash.Utils.HealthChecks;

namespace QCash.Utils.UnitTest.HealthChecks;

public class DistributedMemoryCacheHealthCheckTests
{
    private class TestRig(IFixture fixture)
    {
        private readonly IDistributedCache _distributedCache = fixture.Freeze<IDistributedCache>();
        private readonly ISystemClockService _systemClockService = fixture.Freeze<ISystemClockService>();
        private readonly HealthCheckContext _context = fixture.Freeze<HealthCheckContext>();
        private DistributedMemoryCacheHealthCheck DistributedMemoryCacheHealthCheck { get; set; } = fixture.Freeze<DistributedMemoryCacheHealthCheck>();

        public async Task<HealthCheckResult> CheckHealthAsync(bool cachedValueMatches = true, bool throwException = false)
        {
            var fakeUtcNowTimeString = "8/10/2020 1:00:00 PM";
            var fakeUtcNowTime = DateTime.Parse(fakeUtcNowTimeString);
            _systemClockService.GetSystemTimeUtc().Returns(fakeUtcNowTime);
            
            var fakeCacheTime = cachedValueMatches ? fakeUtcNowTime.ToString(CultureInfo.InvariantCulture) : "WRONG VALUE"; 
            var fakeTimeCacheString = fakeCacheTime.ToString(CultureInfo.InvariantCulture);
            var encodedVal = Encoding.UTF8.GetBytes(fakeTimeCacheString);

            if (throwException)
            {
                _distributedCache.GetAsync(DistributedMemoryCacheHealthCheck.TestKey, Arg.Any<CancellationToken>())
                    .ThrowsAsync(new Exception("Test exception"));
            }
            else
            {
                _distributedCache.GetAsync(DistributedMemoryCacheHealthCheck.TestKey, Arg.Any<CancellationToken>())
                    .Returns(encodedVal);
            }

            var token = CancellationToken.None;
            return await DistributedMemoryCacheHealthCheck.CheckHealthAsync(_context, token);
        }
    }
    
    [Theory, AutoSubstituteData]
    public async Task CheckHealthExactMatchAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.CheckHealthAsync();
        result.Status.Should().Be(HealthStatus.Healthy);
    }
    
    [Theory, AutoSubstituteData]
    public async Task CheckHealthCacheFailAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.CheckHealthAsync(cachedValueMatches: false);
        result.Status.Should().Be(HealthStatus.Degraded);
    }
    
    [Theory, AutoSubstituteData]
    public async Task CheckHealthExceptionAsync(IFixture fixture)
    {
        var rig = new TestRig(fixture);
        var result = await rig.CheckHealthAsync(throwException: true);
        result.Status.Should().Be(HealthStatus.Unhealthy);
    }
}