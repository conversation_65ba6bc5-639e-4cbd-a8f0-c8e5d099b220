namespace QCash.Data.UnitTest;

public class MultiTenantDbContextTestRig
{
    public QCashContext Context { get; }

    // Properties to store references to entities that tests might need to access
    public DecisionEngineScorePoint? DecisionEngineScorePoint1 { get; private set; }
    public DecisionEngineScorePoint? DecisionEngineScorePoint2 { get; private set; }

    public MultiTenantDbContextTestRig(QCashContext dbContext)
    {
        Context = dbContext;
        SetTenant(IFixtureExtensions.FinancialInstitutionId);
    }

    /// <summary>
    /// Sets the tenant in the HttpContext for a QCashContext instance.
    /// This is useful for testing tenant filtering in the QCashContext.
    /// </summary>
    /// <param name="tenantId">The tenant ID to set</param>
    /// <param name="tenantName">Optional name for the tenant</param>
    /// <param name="tenantSlug">Optional slug for the tenant</param>
    public void SetTenant(Guid tenantId, string tenantName = "Test Name", string tenantSlug = "TestSlug")
    {
        if (Context.GetType().GetField("_httpContextAccessor", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?.GetValue(Context) is IHttpContextAccessor httpContextAccessor)
        {
            // Set the tenant ID in the HttpContext
            var tenantInfo = new MultiTenantInfo
            {
                Id = tenantId,
                Name = tenantName,
                Slug = tenantSlug,
                IsResolved = true,
            };
                
            httpContextAccessor.HttpContext!.Items[nameof(MultiTenantInfo)] = tenantInfo;
        }
    }
    public async Task SeedTestAsync()
    {
        // Add FinancialInstitutions
        var fi1 = new FinancialInstitution
        {
            Id = IFixtureExtensions.FinancialInstitutionId,
            Name = "Test FI",
            Slug = "test-fi",
            Address = "123 Test St",
            City = "Test City",
            State = "TS",
            Zip = "12345",
            ContactName = "Test Contact",
            ContactPhone = "************",
            MailingAddress = "123 Test St",
            GlLoanAccount = "12345",
            DateCreatedUtc = DateTime.UtcNow,
            DateUpdatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };
        Context.FinancialInstitutions.Add(fi1);

        var fi2 = new FinancialInstitution
        {
            Id = IFixtureExtensions.OtherFinancialInstitutionId,
            Name = "Other FI",
            Slug = "other-fi",
            Address = "456 Other St",
            City = "Other City",
            State = "OS",
            Zip = "67890",
            ContactName = "Other Contact",
            ContactPhone = "************",
            MailingAddress = "456 Other St",
            GlLoanAccount = "67890",
            DateCreatedUtc = DateTime.UtcNow,
            DateUpdatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };
        Context.FinancialInstitutions.Add(fi2);

        // Add AanReasons for both tenants
        var aanReason1 = new AANReasonOverrideText()
        {
            Id = Guid.NewGuid(),
            Description = "Test Description 1",
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            TimeStamp = [],
        };
        Context.AANReasonOverrideTexts.Add(aanReason1);

        var aanReason2 = new AANReasonOverrideText
        {
            Id = Guid.NewGuid(),
            Description = "Test Description 2",
            FinancialInstitutionId = IFixtureExtensions.OtherFinancialInstitutionId,
            TimeStamp = [],
        };
        Context.AANReasonOverrideTexts.Add(aanReason2);


        // Add LoanTypes for both tenants
        var loanType = new LoanType
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            Name = "Test Loan Type 1",
            Abrv = "TLT1",
            AppAbrv = "TLT1",
            Slug = "test-loan-type-1",
            TimeStamp = [],
        };
        Context.LoanTypes.Add(loanType);

        var otherLoanType = new LoanType
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.OtherFinancialInstitutionId,
            Name = "Test Loan Type 2",
            Abrv = "TLT2",
            AppAbrv = "TLT2",
            Slug = "test-loan-type-2",
            TimeStamp = [],
        };
        Context.LoanTypes.Add(otherLoanType);


        // Add Products for both tenants
        var product1 = new Product
        {
            Id = Guid.NewGuid(),
            Name = "Test Product 1",
            Abrv = "TP1",
            AppAbrv = "TP1",
            Slug = "test-product-1",
            Description = "Test Product Description 1",
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            LoanTypeId = loanType.Id,
            IsActive = true,
            MinimumLoanAmount = 100,
            MaximumLoanAmount = 1000,
            MaximumNumberOfOpenLoan = 1,
            LoanAmount = 500,
            AnnualPercentage = 5.0m,
            LoanHistoryPeriod = 12,
            LoanPerBorrowed = 0.0m,
            NumberOfElectronicTransactionsHistoryPeriod = 12,
            PercentageOfAggregateDepositForLoanAmount = 0.0m,
            GlTranslationAccount = "12345",
            LengthOfRelationship = 0,
            MaprLoanOriginationFee = 0.0m,
            InvoiceId = "INV001",
            TimeStamp = [],
        };
        Context.Products.Add(product1);

        var product2 = new Product
        {
            Id = Guid.NewGuid(),
            Name = "Test Product 2",
            Abrv = "TP2",
            AppAbrv = "TP2",
            Slug = "test-product-2",
            Description = "Test Product Description 2",
            FinancialInstitutionId = IFixtureExtensions.OtherFinancialInstitutionId,
            LoanTypeId = otherLoanType.Id,
            IsActive = true,
            MinimumLoanAmount = 200,
            MaximumLoanAmount = 2000,
            MaximumNumberOfOpenLoan = 2,
            LoanAmount = 1000,
            AnnualPercentage = 6.0m,
            LoanHistoryPeriod = 24,
            LoanPerBorrowed = 0.0m,
            NumberOfElectronicTransactionsHistoryPeriod = 24,
            PercentageOfAggregateDepositForLoanAmount = 0.0m,
            GlTranslationAccount = "67890",
            LengthOfRelationship = 0,
            MaprLoanOriginationFee = 0.0m,
            InvoiceId = "INV002",
            TimeStamp = [],
        };
        Context.Products.Add(product2);

        // Add ScoreTypes for both tenants
        var scoreType1 = new ScoreType
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            Name = "Test Score Type 1",
            Abrv = "TST1",
            AppAbrv = "TST1",
            Slug = "test-score-type-1",
            Description = "Test Score Type Description 1",
            IsDeleted = false,
            DateCreatedUtc = DateTime.UtcNow,
            DateUpdatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };
        Context.ScoreTypes.Add(scoreType1);

        var scoreType2 = new ScoreType
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.OtherFinancialInstitutionId,
            Name = "Test Score Type 2",
            Abrv = "TST2",
            AppAbrv = "TST2",
            Slug = "test-score-type-2",
            Description = "Test Score Type Description 2",
            IsDeleted = false,
            DateCreatedUtc = DateTime.UtcNow,
            DateUpdatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };
        Context.ScoreTypes.Add(scoreType2);

        // Add FinancialInstitutionMembers for both tenants
        var member1 = new FinancialInstitutionMember
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            FirstName = "John",
            LastName = "Doe",
            MemberIdHash = "hash1",
            MemberIdMask = "mask1",
            TimeStamp = [],
        };
        Context.FinancialInstitutionMembers.Add(member1);

        var member2 = new FinancialInstitutionMember
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.OtherFinancialInstitutionId,
            FirstName = "Jane",
            LastName = "Smith",
            MemberIdHash = "hash2",
            MemberIdMask = "mask2",
            TimeStamp = [],
        };
        Context.FinancialInstitutionMembers.Add(member2);

        // Add FinancialInstitutionConfigurations for both tenants
        var config1 = new FinancialInstitutionConfiguration
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.FinancialInstitutionId,
            Description = "Test Configuration 1",
            DefaultProviderId = Guid.NewGuid(),
            Active = true,
            TimeStamp = [],
        };
        Context.FinancialInstitutionConfigurations.Add(config1);

        var config2 = new FinancialInstitutionConfiguration
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionId = IFixtureExtensions.OtherFinancialInstitutionId,
            Description = "Test Configuration 2",
            DefaultProviderId = Guid.NewGuid(),
            Active = true,
            TimeStamp = [],
        };
        Context.FinancialInstitutionConfigurations.Add(config2);


        // Add LoanApplications for both tenants
        var loanApp1 = new Models.LoanApplication
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionMemberId = member1.Id,
            AmountFinanced = 1000,
            AmountBorrowed = 1000,
            AnnualPercentageRate = 5.0m,
            FinanceCharge = 50,
            TotalOfPayments = 1050,
            IsInProgress = true,
            FeeRefundStep = "None",
            MatureDateUtc = DateTime.UtcNow.AddMonths(12),
            PaymentGuardReportStatusId = 1,
            TimeStamp = [],
        };
        Context.LoanApplications.Add(loanApp1);

        var loanApp2 = new Models.LoanApplication
        {
            Id = Guid.NewGuid(),
            FinancialInstitutionMemberId = member2.Id,
            AmountFinanced = 2000,
            AmountBorrowed = 2000,
            AnnualPercentageRate = 6.0m,
            FinanceCharge = 120,
            TotalOfPayments = 2120,
            IsInProgress = true,
            FeeRefundStep = "None",
            MatureDateUtc = DateTime.UtcNow.AddMonths(12),
            PaymentGuardReportStatusId = 1,
            TimeStamp = [],
        };
        Context.LoanApplications.Add(loanApp2);

        // Add TilaRequests for both tenants
        var tilaRequest1 = new TilaRequest
        {
            Id = Guid.NewGuid(),
            LoanApplicationId = loanApp1.Id,
            CorrelationId = Guid.NewGuid(),
            BaseAccountId = "12345",
            Abrv = "TR1",
            LoanFee = 10,
            PerBorrowed = 0.05m,
            AmountBorrowed = 1000,
            InterestRate = 5.0m,
            LoanOriginationFee = 25,
            FirstPaymentDayOfMonth = 15,
            LoanTermInMonths = 12,
            IsOpenEndedLoan = false,
            RoundToIndicator = "Nearest",
            AmortizationMethod = "UNITPERIOD",
            LoanOpenDate = DateTime.UtcNow,
            FirstPaymentDate = DateTime.UtcNow.AddMonths(1),
            TimeStamp = [],
        };
        Context.TilaRequests.Add(tilaRequest1);

        var tilaRequest2 = new TilaRequest
        {
            Id = Guid.NewGuid(),
            LoanApplicationId = loanApp2.Id,
            CorrelationId = Guid.NewGuid(),
            BaseAccountId = "67890",
            Abrv = "TR2",
            LoanFee = 20,
            PerBorrowed = 0.06m,
            AmountBorrowed = 2000,
            InterestRate = 6.0m,
            LoanOriginationFee = 50,
            FirstPaymentDayOfMonth = 20,
            LoanTermInMonths = 24,
            IsOpenEndedLoan = false,
            RoundToIndicator = "Nearest",
            AmortizationMethod = "UNITPERIOD",
            LoanOpenDate = DateTime.UtcNow,
            FirstPaymentDate = DateTime.UtcNow.AddMonths(1),
            TimeStamp = [],
        };
        Context.TilaRequests.Add(tilaRequest2);

        // Add TilaResponses for both tenants
        var tilaResponse1 = new TilaResponse
        {
            Id = Guid.NewGuid(),
            LoanApplicationId = loanApp1.Id,
            FinalPaymentAmount = 100,
            InterestRate = 5.0m,
            RegularPaymentAmount = 100,
            FinanceCharge = 50,
            TotalOfPayments = 1050,
            AdjustedApr = 5.0m,
            AmountFinanced = 1000,
            NumberOfPayments = 10,
            TimeStamp = [],
        };
        Context.TilaResponses.Add(tilaResponse1);

        var tilaResponse2 = new TilaResponse
        {
            Id = Guid.NewGuid(),
            LoanApplicationId = loanApp2.Id,
            FinalPaymentAmount = 200,
            InterestRate = 6.0m,
            RegularPaymentAmount = 200,
            FinanceCharge = 120,
            TotalOfPayments = 2120,
            AdjustedApr = 6.0m,
            AmountFinanced = 2000,
            NumberOfPayments = 10,
            TimeStamp = [],
        };
        Context.TilaResponses.Add(tilaResponse2);


        // Add CalcResult for current tenant
        var calcResult1 = new CalcResult
        {
            Id = Guid.NewGuid(),
            LoanApplicationId = loanApp1.Id,
            ProductId = product1.Id,
            TimeStamp = [],
        };
        Context.CalcResults.Add(calcResult1);

        // Add CalcResult for other tenant
        var calcResult2 = new CalcResult
        {
            Id = Guid.NewGuid(),
            LoanApplicationId = loanApp2.Id,
            ProductId = product2.Id,
            TimeStamp = [],
        };
        Context.CalcResults.Add(calcResult2);


        // Add CalcResultDecisionEngine for current tenant
        var calcResultDE1 = new CalcResultDecisionEngine
        {
            Id = Guid.NewGuid(),
            CalcResultId = calcResult1.Id,
            IsEligible = true,
            QualifiedAmount = 1000,
            MinimumAmount = 100,
            MaximumAmount = 1000,
            InterestRate = 5.0m,
            TimeStamp = [],
        };
        Context.CalcResultDecisionEngines.Add(calcResultDE1);

        // Add CalcResultDecisionEngine for other tenant
        var calcResultDecisionEngine2 = new CalcResultDecisionEngine
        {
            Id = Guid.NewGuid(),
            CalcResultId = calcResult2.Id,
            IsEligible = true,
            QualifiedAmount = 2000,
            MinimumAmount = 200,
            MaximumAmount = 2000,
            InterestRate = 6.0m,
            TimeStamp = [],
        };
        Context.CalcResultDecisionEngines.Add(calcResultDecisionEngine2);

        // Add DecisionEngineScorePoint for current tenant
        DecisionEngineScorePoint1 = new DecisionEngineScorePoint
        {
            Id = Guid.NewGuid(),
            CalcResultDecisionEngineId = calcResultDE1.Id,
            ScoreTypeId = scoreType1.Id,
            InputValue = 100,
            ScoreValue = 50,
            DateCreatedUtc = DateTime.UtcNow,
            DateUpdatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };
        Context.DecisionEngineScorePoints.Add(DecisionEngineScorePoint1);

        // Add DecisionEngineScorePoint for other tenant
        DecisionEngineScorePoint2 = new DecisionEngineScorePoint
        {
            Id = Guid.NewGuid(),
            CalcResultDecisionEngineId = calcResultDecisionEngine2.Id,
            ScoreTypeId = scoreType2.Id,
            InputValue = 200,
            ScoreValue = 75,
            DateCreatedUtc = DateTime.UtcNow,
            DateUpdatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };
        Context.DecisionEngineScorePoints.Add(DecisionEngineScorePoint2);

        await Context.SaveChangesAsync();

        // Store references to ScoreTypes for tests to use
    }
}