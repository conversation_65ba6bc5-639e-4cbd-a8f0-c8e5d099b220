using System.Diagnostics.CodeAnalysis;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using QCash.Middleware.Router.HealthChecks;
using QCash.Utils.Extensions;

namespace QCash.Middleware.Router;

[ExcludeFromCodeCoverage]
public class AppConfiguration(
    IWebHostEnvironment env,
    ILogger<AppConfiguration> logger,
    TelemetryConfiguration telemetryConfiguration,
    IHostApplicationLifetime applicationLifetime)
{
    public WebApplication ConfigureApp(WebApplication app)
    {
        logger.LogInformation("{ServiceName} starting up", $"{nameof(QCash)}.{nameof(Middleware)}.{nameof(Router)}");
        logger.LogInformation("Environment: {EnvironmentName}", env.EnvironmentName);

        // Configure the HTTP request pipeline
        ConfigureExceptionHandling(app);
        ConfigureSwagger(app);

        app.UseHttpsRedirection();

        // Configure routing
        app.UseRouting();

        // Add security headers
        _ = app.Use(async (context, next) =>
        {
            // Set security headers using indexer to avoid duplicate key exceptions
            context.Response.Headers.XContentTypeOptions = "nosniff";
            context.Response.Headers.XFrameOptions = "DENY";
            context.Response.Headers.XXSSProtection = "1; mode=block";
            context.Response.Headers["Referrer-Policy"] = "strict-origin-when-cross-origin";

            // Remove server header for security
            context.Response.Headers.Remove("Server");

            await next();
        });

        // Add CORS if configured
        var corsOrigins = app.Configuration.GetValue<string[]>("Cors:AllowedOrigins", []);
        if (corsOrigins.Length > 0)
        {
            app.UseCors(builder => builder.WithOrigins(corsOrigins)
                .AllowAnyMethod()
                .AllowAnyHeader()
                .AllowCredentials());
        }

        // Add authentication and authorization
        app.UseAuthentication();
        app.UseAuthorization();

        // Map controllers
        app.MapControllers();

        ConfigureHealthChecks(app);

        applicationLifetime.ApplicationStopped.Register(() => logger.LogInformation("{ServiceName} shutting down", $"{nameof(QCash)}.{nameof(Middleware)}.{nameof(Router)}"), true);
        return app;
    }

    private void ConfigureExceptionHandling(WebApplication app)
    {
        if (app.Environment.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
            telemetryConfiguration.DisableTelemetry = true;
        }
        else
        {
            app.UseExceptionHandler("/error");
            app.UseHsts();
        }
    }

    private static void ConfigureSwagger(WebApplication app)
    {
        if (app.Configuration.GetValue("Swagger:Enabled", false))
        {
            app.UseSwagger();
            app.UseSwaggerUI(options =>
            {
                options.SwaggerEndpoint("/swagger/v1/swagger.json", "QCash MiddlewareRouter API V1");
                options.DocumentTitle = "QCash Router API Documentation";
                // options.EnablePersistAuthorization();
                //
                // // Add OAuth2 configuration if needed
                // var clientId = app.Configuration.GetValue["Swagger:ClientId"];
                // if (!string.IsNullOrEmpty(clientId))
                // {
                //     options.OAuthClientId(clientId);
                //     options.OAuthAppName("QCash Router API - Swagger UI");
                //     options.OAuthUsePkce();
                // }
            });
        }
    }

    private static void ConfigureHealthChecks(WebApplication app)
    {
        var healthCheckPath = "/healthz";
        var healthCheckSettings = app.Configuration.GetSection("HealthChecks");

        if (healthCheckSettings.Exists())
        {
            healthCheckPath = healthCheckSettings.GetValue<string>("Path") ?? healthCheckPath;
        }

        // Configure health check endpoint with response writer
        app.MapHealthChecks(healthCheckPath).AllowAnonymous();

        // Detailed health check endpoint
        app.MapHealthChecks("/healthz/detail", new HealthCheckOptions
        {
            AllowCachingResponses = true,
            ResponseWriter = HttpResponseUtils.WriteHealthReportResponseAsync,
            Predicate = _ => true
        }).AllowAnonymous();

        // Add health check caching middleware
        var cacheDuration = app.Configuration.GetValue("HealthChecks:CacheDurationInSeconds", 10);
        app.UseMiddleware<HealthCheckCachingMiddleware>(TimeSpan.FromSeconds(cacheDuration));
    }
}
