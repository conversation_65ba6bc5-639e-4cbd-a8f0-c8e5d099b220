namespace QCash.Common.Extensions;

/// <summary>
/// DateTime class extension methods.
/// </summary>
public static class DateTimeExtensions
{
    /// <summary>
    /// Converts the DateTime UTC value in the specific zone. 
    /// </summary>
    /// <param name="value">The value.</param>
    /// <param name="destinationZone">The destination zone.</param>
    /// <returns>DateTime value in the specifice zone from provided UTC value.</returns>
    public static DateTime FromUTC(this DateTime value, TimeZoneInfo destinationZone)
    {
        var time = new DateTime(value.Ticks, DateTimeKind.Utc);
        return TimeZoneInfo.ConvertTimeFromUtc(time, destinationZone);
    }

    /// <summary>
    /// Converts the DateTime UTC value in the specific zone. 
    /// </summary>
    /// <param name="value">The value.</param>
    /// <param name="destinationZone">The destination zone.</param>
    /// <returns>DateTime value in the specifice zone from provided UTC value.</returns>
    public static DateTime? FromUTC(this DateTime? value, TimeZoneInfo destinationZone)
    {
        if (value.HasValue)
            return TimeZoneInfo.ConvertTimeFromUtc(value.GetValueOrDefault(), destinationZone);
        return null;
    }

    /// <summary>
    /// Converts provided date to the UTC DateTime.
    /// </summary>
    /// <param name="value">The value.</param>
    /// <param name="sourceZone">The source zone.</param>
    /// <returns>DateTime converted to the UTC.</returns>
    public static DateTime ToUTC(this DateTime value, TimeZoneInfo sourceZone)
    {
        var time = new DateTime(value.Ticks, DateTimeKind.Unspecified);
        return TimeZoneInfo.ConvertTimeToUtc(time, sourceZone);
    }

    /// <summary>
    /// Converts provided date to the UTC DateTime.
    /// </summary>
    /// <param name="value">The value.</param>
    /// <param name="sourceZone">The source zone.</param>
    /// <returns>DateTime converted to the UTC.</returns>
    public static DateTime? ToUTC(this DateTime? value, TimeZoneInfo sourceZone)
    {
        if (value.HasValue)
        {
            var time = new DateTime(value.GetValueOrDefault().Ticks, DateTimeKind.Unspecified);
            return TimeZoneInfo.ConvertTimeToUtc(time, sourceZone);
        }
        return null;
    }

    /// <summary>
    /// To nullable date time object.
    /// </summary>
    /// <param name="value">The value.</param>
    /// <returns>Null if value is DateTime.MinValue.</returns>
    public static DateTime? ToNullableDateTime(this DateTime value)
        => value == DateTime.MinValue ? null : value;

    /// <summary>
    /// First following day of week (excluding weekends). Return current date if it is not weekend day. 
    /// </summary>
    /// <param name="value">The value.</param>
    /// <returns>First following day of week (excluding weekends).</returns>
    public static DateTime FirstWeekday(this DateTime value) =>
        value.DayOfWeek switch
        {
            DayOfWeek.Saturday => value.AddDays(2),
            DayOfWeek.Sunday => value.AddDays(1),
            _ => value
        };
}