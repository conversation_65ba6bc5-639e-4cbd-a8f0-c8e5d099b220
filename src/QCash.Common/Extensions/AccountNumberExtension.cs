namespace QCash.Common.Extensions;

public static class AccountNumberExtension
{
    public static string AdjustAccountNumber(this string accountNumber, int requiredDigits)
    {
        if (string.IsNullOrWhiteSpace(accountNumber))
            return string.Empty;

        return requiredDigits switch
        {
            < 0 => accountNumber.TrimStart('0'),
            >= 0 when requiredDigits < accountNumber.Length => accountNumber,
            _ => $"{new string('0', requiredDigits - accountNumber.Length)}" + accountNumber
        };
    }
}