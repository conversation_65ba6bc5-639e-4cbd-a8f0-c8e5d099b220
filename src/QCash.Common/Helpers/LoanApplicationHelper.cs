using QCash.Common.Enums;

namespace QCash.Common.Helpers;

public class LoanApplicationHelper
{
    public static string GetAutopayOption(bool? isAutopaySelected, bool? isOptOutAutopaySelected)
    {
        if (!isAutopaySelected.HasValue)
        {
            return string.Empty;
        }

        if (isAutopaySelected.Value)
        {
            return AutopayOption.ForcedAutopay.ToString();
        }

        if (!isOptOutAutopaySelected.HasValue)
        {
            return string.Empty;
        }

        return isOptOutAutopaySelected.Value ? AutopayOption.OptOutOption.ToString() : AutopayOption.OptInOption.ToString();
    }
}