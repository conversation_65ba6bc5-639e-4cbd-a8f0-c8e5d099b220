namespace QCash.Models.Api;

/// <summary>
/// Represents a language with its associated properties.
/// </summary>
public class LanguageApiModel
{
    /// <summary>
    /// Gets or sets the unique identifier for the language.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the ISO code for the language.
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// Gets or sets the name of the language.
    /// </summary>
    public string? Name { get; set; }
}