namespace QCash.Models.Api;

/// <summary>
/// InterfaceTextAPIModel implementation.
/// </summary>
public class InterfaceTextApiModel
{
    /// <summary>
    /// Gets or sets the type.
    /// </summary>
    public int Type { get; set; }

    /// <summary>
    /// Gets or sets the abrv.
    /// </summary>
    public string? Abrv { get; set; }

    /// <summary>
    /// Gets or sets the name.
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Gets or sets the field value.
    /// </summary>
    public string? FieldValue { get; set; }
}