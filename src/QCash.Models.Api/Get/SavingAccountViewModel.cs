namespace QCash.Models.Api.Get;

/// <summary>
/// SavingAccountViewModel implementation.
/// </summary>
public class SavingAccountViewModel
{
    /// <summary>
    /// Gets or sets the identifier.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the account identifier.
    /// </summary>
    public string AccountId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the description.
    /// </summary>
    public string? Description { get; set; }
}