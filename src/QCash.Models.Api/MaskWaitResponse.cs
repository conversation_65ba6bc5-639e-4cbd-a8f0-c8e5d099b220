namespace QCash.Models.Api;

/// <summary>
/// MaskWaitResponse implementation.
/// </summary>
/// <seealso cref="Response" />
public class MaskWaitResponse : Response
{
    /// <summary>
    /// Gets or sets a value indicating whether is action.
    /// </summary>
    public bool Action { get; set; }

    /// <summary>
    /// Gets or sets the collection of display values.
    /// </summary>
    public IList<DisplayTextApiModel>? DisplayValues { get; set; }
}