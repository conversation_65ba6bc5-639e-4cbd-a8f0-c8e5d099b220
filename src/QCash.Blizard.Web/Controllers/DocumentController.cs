using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Mvc;
using QCash.Common.Enums;
using QCash.Common;
using FileContentResult = Microsoft.AspNetCore.Mvc.FileContentResult;

namespace QCash.Blizard.Web.Controllers;

[ApiController]
[Route("{fiSlug}/document")]
public class DocumentController(
    IHttpClientFactory clientFactory,
    TelemetryClient telemetryClient)
    : Controller
{
    [HttpGet("content")]
    public async Task<IActionResult> GetDocumentContentAsync(
        string fiSlug, [FromQuery] LoanApplicationDocumentType type, [FromQuery] bool printAction = false)
    {
        try
        {
            ReadHeaders(out var authorization, out var additionalHeaders, out var correlationId);
            var token = Request.Headers["Token"].FirstOrDefault() ?? string.Empty;

            telemetryClient.TrackEvent("Document.Request", new Dictionary<string, string>
            {
                { "fiSlug", fiSlug },
                { "documentType", type.ToString() },
                { "printAction", printAction.ToString() },
                { "correlationId", correlationId.ToString() }
            });

            var client = clientFactory.CreateClient("QCashApiClient");
            var documentEndpoint = GetDocumentEndpoint(type);
            var apiUrl = $"{fiSlug}/api/document/{documentEndpoint}?printAction={printAction}";

            var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            request.Headers.TryAddWithoutValidation("Authorization", authorization);
            request.Headers.TryAddWithoutValidation("Token", token);
            request.Headers.TryAddWithoutValidation("X-Correlation-ID", correlationId.ToString());

            // Add any additional headers
            foreach (var (key, value) in additionalHeaders)
            {
                request.Headers.TryAddWithoutValidation(key, value);
            }

            var response = await client.SendAsync(request).ConfigureAwait(false);
            response.EnsureSuccessStatusCode();

            var fileBytes = await response.Content.ReadAsByteArrayAsync();
            var contentType = response.Content.Headers.ContentType?.ToString() ?? "application/pdf";
            var fileName = response.Content.Headers.ContentDisposition?.FileName;
            var fcr = new FileContentResult(fileBytes, contentType)
            {
                FileDownloadName = fileName ?? $"{documentEndpoint}.pdf"
            };

            telemetryClient.TrackEvent("Document.Success", new Dictionary<string, string>
            {
                { "fiSlug", fiSlug },
                { "documentType", type.ToString() },
                { "fileSize", fcr.FileContents.Length.ToString() },
                { "correlationId", correlationId.ToString() }
            });

            return Ok(fcr);
        }
        catch (Exception ex)
        {
            var correlationId = Request.Headers["X-Correlation-ID"].FirstOrDefault();
            telemetryClient.TrackException(ex, new Dictionary<string, string>
            {
                { "fiSlug", fiSlug },
                { "documentType", type.ToString() },
                { "printAction", printAction.ToString() },
                { "correlationId", correlationId ?? "N/A" }
            });

            return StatusCode(500, "An error occurred while retrieving the document");
        }
    }

    private void ReadHeaders(
        out string authorization,
        out List<(string key, string value)> additionalHeaders,
        out Guid correlationId)
    {
        authorization = Request.Headers.Authorization.FirstOrDefault() ?? string.Empty;

        var correlationIdStr = Request.Headers["X-Correlation-ID"];
        if (!Guid.TryParse(correlationIdStr, out correlationId))
            correlationId = Guid.NewGuid();

        additionalHeaders = [];
        foreach (var (receiveKey, sendKey) in QCConstants.ADDITIONAL_HEADERS)
        {
            var headerValue = Request.Headers[receiveKey].ToString();
            if (!string.IsNullOrEmpty(headerValue))
            {
                additionalHeaders.Add((sendKey, headerValue));
            }
        }
    }

    private static string GetDocumentEndpoint(LoanApplicationDocumentType type) => type switch
    {
        LoanApplicationDocumentType.AdverseActionNotice => "adverse-action-notice",
        LoanApplicationDocumentType.EConsentDisclosure => "econsent-disclosure",
        LoanApplicationDocumentType.TILA => "tila",
        LoanApplicationDocumentType.PaymentGuard => "payment-guard",
        LoanApplicationDocumentType.Invoice => "invoice",
        _ => throw new ArgumentException($"Unsupported document type: {type}", nameof(type))
    };
}