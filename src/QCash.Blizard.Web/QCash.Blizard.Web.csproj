<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <UserSecretsId>2afa8c40-c462-4e5e-a101-289e8e53f349</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.AzureApplicationInsights" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.AzureKeyVault" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.System" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.Uris" Version="9.0.0" />
    <PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
    <PackageReference Include="DtronixPdf" Version="1.3.1" />
    <PackageReference Include="DtronixPdf.ImageSharp" Version="1.2.0" />
    <PackageReference Include="log4net" Version="3.1.0" />
    <PackageReference Include="Log4Net.Appenders" Version="1.0.5" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.Log4NetAppender" Version="2.23.0" />
    <PackageReference Include="Microsoft.Azure.SignalR" Version="1.30.3" />
    <PackageReference Include="Microsoft.Extensions.Caching.SqlServer" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.17" />
    <PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\QCash.Common\QCash.Common.csproj" />
    <ProjectReference Include="..\QCash.Models.Api\QCash.Models.Api.csproj" />
    <ProjectReference Include="..\QCash.Utils\QCash.Utils.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Development.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
    <Content Update="appsettings.dev.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
    <Content Update="log4net.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="log4net.dev.config">
      <DependentUpon>log4net.config</DependentUpon>
    </Content>
    <Content Update="log4net.prod.config">
      <DependentUpon>log4net.config</DependentUpon>
    </Content>
    <Content Update="log4net.qa.config">
      <DependentUpon>log4net.config</DependentUpon>
    </Content>
    <Content Update="log4net.train.config">
      <DependentUpon>log4net.config</DependentUpon>
    </Content>
    <Content Update="log4net.uat.config">
      <DependentUpon>log4net.config</DependentUpon>
    </Content>
    <Content Update="appsettings.Production.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
    <Content Update="appsettings.QA.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
    <Content Update="appsettings.Stage.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
    <Content Update="appsettings.Train.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Extensions\LoggerServiceExtensions.cs" />
  </ItemGroup>
</Project>
