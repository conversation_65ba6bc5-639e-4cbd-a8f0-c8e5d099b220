@attribute [Route(BlizardPages.Login)]
@using System.Text.Json
@using QCash.Blizard.Web.Enums
@using QCash.Blizard.Web.Extensions
@using QCash.Blizard.Web.Helpers
@using QCash.Blizard.Web.Models
@using QCash.Blizard.Web.Pages.Components.SmsConsent
@using QCash.Common.Crypto
@using QCash.Common.Enums
@using QCash.Common.Extensions
@using QCash.Models.Api.Get
@using QCash.Models.Api.Post
@using QCash.Models.Api

@implements IDisposable

@inject DataService DataService
@inject BlizardNavigationManager NavigationManager
@inject InitiationService InitiationService
@inject FiConfigurationService FiConfigurationService
@inject SessionService SessionService
@inject LoggerService LoggerService
@inject ITokenManagerService TokenManagerService
@inject ICryptoService CryptoService

@if (!_fraudControl && _loginPresentation != null) {
    <div class="qcash-connect-container">
        <div class="qcash-card qcash-login-presentation">
            <div class="qcash-login-text">
                <div class="text-h0 text-brand">@_loginPresentation.GetMarkupString("ExtAuthTitle")</div>
                <div class="text-standard">@_loginPresentation.GetMarkupString("ExtAuthIntroduction")</div>
            </div>
        </div>
        <div class="qcash-card qcash-login-action">
            <div class="qcash-action-text">
                <div class="text-h3 text-brand">@_loginPresentation.GetDisplayValue("LoginPageCardText").Replace("{FiSlug}", @UiContext.FiSlug).ToMarkupString()</div>
                <div class="qcash-text-grey">@_loginPresentation.GetMarkupString("ExtAuthInstructions")</div>
            </div>
            <div class="qcash-login-inputs">
                <QcashInputNumber AboveText="@_loginPresentation.GetDisplayValue("ExtAuthAccountNumber")" Required="true" @bind-StringValue="@_memberNumber" @bind-IsValid="@_isMemberNumberValid" MaxLength="10" MinLength="_loginPresentation.AccountIdRequiredDigits"></QcashInputNumber>
                <QcashInputNumber AboveText="@_loginPresentation.GetDisplayValue("ExtAuthZIPCode")" Required="true" @bind-StringValue="@_zipCode" @bind-IsValid="@_isZipcodeValid" MaxLength="5" MinLength="5"></QcashInputNumber>
            </div>
            @if (_loginPresentation?.PreferredDelivery != nameof(FraudControlPreferredDeliveryMethod.Email))
            {
                <QCashCheckbox
                    @bind-Value="_smsConsent"
                    Class="qcash-login-checkbox"
                    Text="@_loginPresentation?.GetDisplayValue("SmsConsentAcceptanceStatement")">
                    <Footer>
                        <PrivacyPolicyLink BaseGetModelTexts="_loginPresentation?.DisplayValues"/>
                    </Footer>
                </QCashCheckbox>
            }
            <button class="btn qcash-btn-primary qcash-lg-btn" @onclick="HandleSubmitAsync" disabled="@IsButtonDisabled()">@_loginPresentation?.GetDisplayValue("ExtAuthSubmit")</button>
        </div>
    </div>
}
@if (_fraudControl)
{
    <FraudControl PresentationModel="_fraudControlModel" OnClickCancel="HandleCancelFraudControlAsync" OnClickContinue="HandleContinueFraudControlAsync" OnClickResend="HandleResendFraudControlAsync"></FraudControl>
}

@code {
    [Parameter] public required string FiSlug { get; set; }
    [CascadingParameter] public UIContext UiContext { get; set; } = null!;

    private bool _isMemberNumberValid;
    private string? _memberNumber;
    private bool _isZipcodeValid;
    private string? _zipCode;
    private bool _smsConsent;

    private bool _fraudControl;

    private LoginPresentationModelGet? _loginPresentation;
    private FraudControlApiModelGet? _fraudControlModel;
    private LoginResponse? _loginResponse;
    private string? _fraudControlCodeId;
    private Guid _correlationId;

    protected override async Task OnInitializedAsync()
    {
        UiContext.ShowSpinner = true;
        UiContext.ShowProgressBar = false;

        // We set this early just to have it available for logging in case something goes wrong in the circuit before we log in
        _correlationId = Guid.NewGuid();
        await SessionService.SetCorrelationId(_correlationId);

        UiContext.ShowLanguageToggle = false;
        await InitializePageDataAsync();
        UiContext.ShowSpinner = false;
    }

    private async Task InitializePageDataAsync()
    {
        _loginPresentation = await DataService.GetLoginInterfaceTextsAsync();
    }

    private async Task HandleSubmitAsync()
    {
        UiContext.DismissAlert();
        UiContext.ShowSpinner = true;

        await SessionService.SetAccountNumber(_memberNumber!);
        await LoggerService.LogInfo("QCashConnect. Post AccountNumber and ZipCode", FiSlug);
        var postModel = new LoginApiModelPost
        {
            AccountNumber = _memberNumber ?? string.Empty,
            ZipCode = _zipCode ?? string.Empty,
        };

        postModel.AccountNumber = postModel.AccountNumber!.AdjustAccountNumber(_loginPresentation!.AccountIdRequiredDigits);

        _loginResponse = await DataService.LoginQCashConnectAsync(postModel);
        UiContext.ShowSpinner = false;

        if (_loginResponse.Errors?.Any() ?? false)
        {
            var logMessage = $"QCashConnect. Post AccountNumber and ZipCode failed {string.Join(", ", _loginResponse.Errors)}";
            await LoggerService.LogInfo(logMessage, FiSlug);
            var alertObject = new AlertArgs
            {
                AlertTitle = _loginResponse.Description ?? string.Empty,
                AlertList = _loginResponse.Errors.ToList(),
                AlertSource = "Login Page"
            };

            UiContext.ShowAlert(alertObject);
            return;
        }

        _fraudControlModel = _loginResponse.FraudControlApiModel;
        _fraudControlCodeId = _loginResponse.CodeId;
        _fraudControl = true;

        await LoggerService.LogInfo("QCashConnect. Post AccountNumber and ZipCode succeeded", FiSlug);
    }

    private async Task HandleContinueFraudControlAsync(FraudControlArgs args)
    {
        await LoggerService.LogInfo("QCashConnect. Post OTP code and SSN code.", FiSlug);

        UiContext.ShowSpinner = true;

        var postModel = new LoginFraudControlApiModelPost
        {
            ValidationCode = args.MobileCode,
            SSNCode = args.SsnValue,
            CodeId = _fraudControlCodeId
        };

        var fraudControlResponse = await DataService.LoginFraudControlAsync(postModel);

        if (fraudControlResponse.Status == "OK")
        {
            await LoggerService.LogInfo("QCashConnect. Post OTP code and SSN code succeeded.", FiSlug);
            var fiRoute = await FiConfigurationService.GetFiConfigurationBySlugAsync(UiContext.FiSlug, DataService);

            var cred = new SsoInitiateApiModel
            {
                OriginApp = (int)OriginApp.Web,
                Location = fraudControlResponse.Location,
                ReturnAsHtml = true,
                BaseAccount = _memberNumber,
                TellerId = fraudControlResponse.TellerId,
            };

            var applicationKey = await DataService.GetApplicationKeysAsync();
            var auth = CryptoService.GetAppAuthorizationToken(
                fiRoute.FinancialInstitutionId,
                JsonSerializer.Serialize(cred),
                TokenManagerService.Decrypt(applicationKey.ApplicationSsoKey),
                TokenManagerService.Decrypt(applicationKey.SharedKeyWeb),
                DateTime.UtcNow);

            var body = JsonSerializer.Serialize(cred);

            await LoggerService.LogInfo("QCashConnect: start SSO request", FiSlug);
            var initiateResponse = await InitiationService.InitiateMember(
                UiContext.FiSlug, auth, body, [], _correlationId, false, TrafficOrigin.QCashConnect, SsoType.SSOV2);

            if (!initiateResponse.IsSuccess)
            {
                await LoggerService.LogInfo("QCashConnect: SSO request failed", FiSlug);
                throw new InvalidOperationException("The SSO initiate request failed");
            }

            if (initiateResponse.Value!.RedirectUrl == null)
            {
                await LoggerService.LogWarning("QCashConnect: SSO request failed. RedirectUrl is empty", FiSlug);
                throw new InvalidOperationException("Could not get redirect URL");
            }

            await LoggerService.LogInfo("QCashConnect: SSO request succeeded", FiSlug);
            NavigationManager.NavigateTo(initiateResponse.Value.RedirectUrl, forceLoad: true);

            return;
        }

        if (fraudControlResponse.Errors?.Any() ?? false)
        {
            var logMessage = $"QCashConnect. Post OTP code and SSN code failed. Errors: {string.Join(", ", fraudControlResponse.Errors)}";
            await LoggerService.LogInfo(logMessage, FiSlug);
            var alertObject = new AlertArgs
            {
                AlertTitle = fraudControlResponse.Description ?? string.Empty,
                AlertList = fraudControlResponse.Errors.ToList(),
                AlertSource = "Login Fraud Control Page"
            };

            UiContext.ShowAlert(alertObject);
        }

        if (fraudControlResponse.ValidationResult == 5)
        {
            await LoggerService.LogInfo("QCashConnect: Submission threshold met", FiSlug);
            UiContext.ShowAlert(new AlertArgs
            {
                AlertTitle = _fraudControlModel!.GetDisplayValue("FraudControlSubmitThresholdMet"),
                AlertSource = "Login Fraud Control Page"
            });
        }

        UiContext.ShowSpinner = false;
    }

    private async Task HandleCancelFraudControlAsync()
    {
        await LoggerService.LogInfo("QCashConnect. Canceled fraud control", FiSlug);
        _fraudControl = false;
        _fraudControlModel = null;
        _memberNumber = null;
        _zipCode = null;
        _smsConsent = false;
        _isMemberNumberValid = false;
        _isZipcodeValid = false;

        await InvokeAsync(StateHasChanged);
    }

    private async Task HandleResendFraudControlAsync()
    {
        await LoggerService.LogInfo("QCashConnect: Resend OTP code", FiSlug);
        await HandleSubmitAsync();
    }

    private bool IsButtonDisabled()
    {
        return !_isMemberNumberValid || !_isZipcodeValid || (!_smsConsent && _loginPresentation?.PreferredDelivery != nameof(FraudControlPreferredDeliveryMethod.Email));
    }

    void IDisposable.Dispose()
    {
        UiContext.ShowSpinner = false;
        UiContext.DismissAlert();
    }
}
