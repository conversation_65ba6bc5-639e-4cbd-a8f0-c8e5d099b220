@using QCash.Blizard.Web.Enums
@using QCash.Blizard.Web.Extensions
@using QCash.Blizard.Web.Helpers
@using QCash.Blizard.Web.Models

@inherits LayoutComponentBase

@inject BlizardNavigationManager NavigationManager
@inject DataService DataService
@inject SessionService SessionService

<PageTitle>Loan Application</PageTitle>

<!-- This is here due to the fact that we need to call an API to get our styles -->
<style>
    :root {
        --primary-00: @_cssVariables.Primary0;
        --primary-10: @_cssVariables.Primary1;
        --primary-20: @_cssVariables.Primary2;
        --primary-30: @_cssVariables.Primary3;
        --secondary-00: @_cssVariables.Secondary0;
        --secondary-10: @_cssVariables.Secondary1;
        --secondary-20: @_cssVariables.Secondary2;
        --grey-00: @_cssVariables.Grey0;
        --grey-10: @_cssVariables.Grey1;
        --grey-20: @_cssVariables.Grey2;
        --grey-30: @_cssVariables.Grey3;
        --grey-40: @_cssVariables.Grey4;
        --grey-50: @_cssVariables.Grey5;
        --grey-60: @_cssVariables.Grey6;
        --greywhite: @_cssVariables.GreyWhite;
        --functionalmandatory-fields-errors: @_cssVariables.MandatoryFieldErrors;
        --shadow: @_cssVariables.Shadow;
    }

    .spinner-overlay-global{
        display: none;
    }
</style>


<CascadingValue Value="_uiContext">
    <ErrorBoundary>
        <ChildContent>
            <div class="wizard-container">
                <Spinner></Spinner>
                @if (_ranGetInterfacePresentation)
                {
                    <Banner></Banner>
                    <ProgressBar></ProgressBar>
                    <QCashAlert></QCashAlert>
                    @Body
                }
            </div>
        </ChildContent>
        <ErrorContent Context="error">
            <div class="wizard-container">
                <Banner></Banner>
                <Error Exception="error"></Error>
            </div>
        </ErrorContent>
    </ErrorBoundary>
</CascadingValue>

@code {
    private BlizardCssVariables _cssVariables = new();
    private bool _ranGetInterfacePresentation = false;
    private readonly UIContext _uiContext = new();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _uiContext.ShowSpinner = true;

            // Total steps is base 5, and 6 is data collection is enabled as per specifications.
            // If we ever get a more complicated progress bar we should have a more complex way of calculating this.
            _uiContext.ProgressBarTotalSteps = await SessionService.GetIsDataCollectionEnabled() ? 6 : 5;

            _uiContext.FiSlug = GetFiSlugFromUrl();
            if (_ranGetInterfacePresentation) return;

            try
            {
                SessionService.FiSlug = _uiContext.FiSlug;
                var memberInterfacePresentation = await DataService.GetMemberInterfacePresentationAsync();
                _cssVariables = memberInterfacePresentation.GetBlizardCssVariables();
                _uiContext.LogoFilePath = memberInterfacePresentation.LogoFilePath;
                _ranGetInterfacePresentation = true;
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception)
            {
                _ranGetInterfacePresentation = true;
                // TODO: if this method doesn't run, we can run with the base css variables do we throw an error here or just log it and move on?
            }

            _uiContext.ShowSpinner = false;
        }
        catch (Exception)
        {
            NavigationManager.NavigateTo(BlizardPages.Error, fiSlug: "QCashUIError");
        }
    }

    private string GetFiSlugFromUrl()
    {
        var uri = new Uri(NavigationManager.Uri);
        var path = uri.AbsolutePath;

        //TODO: if no FI slug is provided, redirect to some error page or login or something
        return path.Split('/', StringSplitOptions.RemoveEmptyEntries).FirstOrDefault() ?? throw new InvalidOperationException("No FI slug provided");
    }
}