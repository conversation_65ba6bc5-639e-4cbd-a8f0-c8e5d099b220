using Microsoft.AspNetCore.Components;
using QCash.Blizard.Web.Enums;
using QCash.Common.Enums;

namespace QCash.Blizard.Web.Services;

public class BlizardNavigationManager(NavigationManager navigationManager, SessionService sessionService)
{
    public void NavigateTo(string url, bool forceLoad = false, string? fiSlug = null)
    {
        fiSlug ??= sessionService.FiSlug;
        var processedUrl = url.Replace("{fiSlug}", fiSlug);
        navigationManager.NavigateTo(processedUrl, forceLoad);
    }

    public async Task NavigateByStep(LoanApplicationStep step)
    {
        var isLoanHub = await sessionService.GetIsLoanHubAsync();
        switch (step)
        {
            case LoanApplicationStep.LoanHub:
                NavigateTo(BlizardPages.LoanHub);
                break;
            case LoanApplicationStep.Application:
                NavigateTo(BlizardPages.Application);
                break;
            case LoanApplicationStep.ApplicationQualified:
                NavigateTo(isLoanHub ? BlizardPages.ApplicationQualifiedPaymentOptions : BlizardPages.ApplicationQualified);
                break;
            case LoanApplicationStep.AdverseAction or LoanApplicationStep.AANEmail:
                NavigateTo(BlizardPages.AdverseAction);
                break;
            case LoanApplicationStep.FundingComplete:
                NavigateTo(BlizardPages.FundingComplete);
                break;
            case LoanApplicationStep.FraudControl:
                NavigateTo(BlizardPages.FraudControl);
                break;
            case LoanApplicationStep.EConsentDisclosure:
                NavigateTo(BlizardPages.EConsentDisclosure);
                break;
            case LoanApplicationStep.Maintenance:
                NavigateTo(BlizardPages.Maintenance);
                break;
            case LoanApplicationStep.LoanLandingInitial:
                NavigateTo(BlizardPages.LoanLanding);
                break;
            case LoanApplicationStep.TILA or LoanApplicationStep.TILAEmail:
                NavigateTo(BlizardPages.LoanSummary);
                break;
            case LoanApplicationStep.DataCollection:
                NavigateTo(BlizardPages.DataCollection);
                break;
            case LoanApplicationStep.FundingPending:
                NavigateTo(BlizardPages.FundingPending);
                break;
            default:
                NavigateTo(BlizardPages.Error);
                break;
        }
    }

    public string Uri => navigationManager.Uri;
}
