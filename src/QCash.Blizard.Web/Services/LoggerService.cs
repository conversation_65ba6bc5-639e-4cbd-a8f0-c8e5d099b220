using QCash.Common;

namespace QCash.Blizard.Web.Services;

public class LoggerService(IConfiguration configuration, SessionService sessionService, ILogger<LoggerService> logger)
{
    private readonly string _environment = configuration["BlizardOptions:Environment"]!;
    private readonly string _location = configuration["BlizardOptions:Location"]!;

    public async Task LogTrace(string message, string fiSlug, params (string, string)[] parameters) =>
        await Log("trace", message, fiSlug, parameters);

    public async Task LogInfo(string message, string fiSlug, params (string, string)[] parameters) =>
        await Log("info", message, fiSlug, parameters);

    public async Task LogWarning(string message, string fiSlug, params (string, string)[] parameters) =>
        await Log("warning", message, fiSlug, parameters);

    private async Task Log(string logLevel, string message, string fiSlug, params (string, string)[] parameters)
    {
        var parametersDictionary = parameters.ToDictionary(x => x.Item1, x => x.Item2);
        parametersDictionary["logLevel"] = logLevel;
        parametersDictionary["fiSlug"] = fiSlug;
        parametersDictionary["environment"] = _environment;
        parametersDictionary["location"] = _location;
        parametersDictionary["application"] = "Blizard";
        var correlationId = await sessionService.GetCorrelationIdAsync();
        parametersDictionary["correlationId"] = correlationId?.ToString() ?? Guid.Empty.ToString();
        parametersDictionary["appId"] = await GetApplicationIdAsync();

        var baseAccountId = await sessionService.GetAccountNumberAsync();
        parametersDictionary["accountNumber"] = FormattingHelpers.GetMaskedNumber(baseAccountId);

        using var scope = logger.BeginScope(parametersDictionary);
        logger.LogInformation("{LogLevel}: {Message}", logLevel, message);
    }

    private async Task<string> GetApplicationIdAsync()
    {
        try
        {
            return await sessionService.GetApplicationIdAsync();
        }
        catch (Exception)
        {
            return string.Empty;
        }
    }
}
