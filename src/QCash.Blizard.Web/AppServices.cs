using System.Net.Mail;
using AspNetCoreRateLimit;
using QCash.Utils.RuntimeSetting;
using QCash.Utils.Settings;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Microsoft.AspNetCore.Components.Server.Circuits;
using Microsoft.Extensions.Options;
using QCash.Blizard.Web.HealthChecks;
using QCash.Blizard.Web.Models;
using QCash.Blizard.Web.Services;
using QCash.Common.Crypto;
using QCash.Common.Enums;
using QCash.Utils.Extensions;
using QCash.Utils.HealthChecks;

namespace QCash.Blizard.Web;

public static class AppServices
{
    public static IHostApplicationBuilder ConfigureAppServices(this IHostApplicationBuilder builder)
        => builder
            .ConfigureLogging()
            .ConfigureServiceProvider()
            .ConfigureAzureKeyVault()
            .ConfigureRuntimeSetting()
            .ConfigureApplicationInsights()
            .ConfigureRateLimiting()
            .ConfigureSignalR()
            .ConfigureServices()
            .ConfigureHealthChecks();

    private static IHostApplicationBuilder ConfigureLogging(this IHostApplicationBuilder builder)
    {
        builder.Logging.AddLog4NetWithAppenders();
        return builder;
    }

    private static IHostApplicationBuilder ConfigureServiceProvider(this IHostApplicationBuilder builder)
    {
        if (builder is WebApplicationBuilder webBuilder)
        {
            webBuilder.Host.UseDefaultServiceProvider((context, config) =>
            {
                config.ValidateScopes = context.HostingEnvironment.IsDevelopment();
                config.ValidateOnBuild = context.HostingEnvironment.IsDevelopment();
            });
        }
        return builder;
    }

    private static IHostApplicationBuilder ConfigureAzureKeyVault(this IHostApplicationBuilder builder)
    {
        var logger = builder.Services.BuildServiceProvider().GetRequiredService<ILogger<Program>>();

        var keyVaultSettings = new AzureKeyVaultSettings();
        builder.Configuration.Bind(AzureKeyVaultSettings.SectionName, keyVaultSettings);

        if (!keyVaultSettings.Enabled)
        {
            logger.LogInformation("Key Vault disabled");
            return builder;
        }

        var keyVaultOptions = new AzureKeyVaultConfigurationOptions { ReloadInterval = keyVaultSettings.ReloadInterval };
        var secretClient = new SecretClient(new Uri(keyVaultSettings.KeyVaultUrl), new DefaultAzureCredential());

        builder.Configuration.AddAzureKeyVault(secretClient, keyVaultOptions);

        logger.LogInformation("Azure Key Vault enabled");
        return builder;
    }

    private static IHostApplicationBuilder ConfigureRuntimeSetting(this IHostApplicationBuilder builder)
    {
        var runtimeSettingSettings = new RuntimeSettingSettings();
        builder.Configuration.Bind(RuntimeSettingSettings.SectionName, runtimeSettingSettings);

        if (!runtimeSettingSettings.Enabled)
        {
            return builder;
        }

        var connectionConfig = new ConnectionStringsOptions();
        builder.Configuration.GetSection(ConnectionStringsOptions.SectionName).Bind(connectionConfig);

        var runtimeSettingOptions = new RuntimeSettingOptions
        {
            ConnectionString = connectionConfig.QCashConnection,
            ReloadInterval = runtimeSettingSettings.ReloadInterval,
        };
        builder.AddRuntimeSettingConfiguration(runtimeSettingOptions);

        return builder;
    }

    private static IHostApplicationBuilder ConfigureSignalR(this IHostApplicationBuilder builder)
    {
        if (!builder.Environment.IsDevelopment())
        {
            builder.Services.AddSignalR()
                .AddAzureSignalR(options =>
                {
                    options.ServerStickyMode = Microsoft.Azure.SignalR.ServerStickyMode.Required;
                    options.ConnectionString = builder.Configuration.GetConnectionString("SignalR");
                });
        }
        else
        {
            builder.Services.AddSignalR();
        }
        return builder;
    }

    private static IHostApplicationBuilder ConfigureServices(this IHostApplicationBuilder builder)
    {
        builder.Services.AddRazorPages();
        builder.Services.AddServerSideBlazor()
            .AddCircuitOptions(options =>
            {
                options.DisconnectedCircuitRetentionPeriod = TimeSpan.FromMinutes(5);
                options.JSInteropDefaultCallTimeout = TimeSpan.FromSeconds(30);
            });
        builder.Services.AddScoped<CircuitHandler, LoggingCircuitHandler>();

        // Add CORS services using the BlizardOptions
        builder.Services.Configure<BlizardOptions>(builder.Configuration.GetSection(BlizardOptions.SectionName));

        builder.Services.AddCors(options =>
        {
            var allowedOrigins = builder.Configuration.GetSection("BlizardOptions:AllowedOrigins").Get<string[]>()
                                 ?? [];
            options.AddPolicy("BlizardCorsPolicy", policy =>
            {
                policy.WithOrigins(allowedOrigins)
                    .AllowAnyHeader()
                    .AllowAnyMethod();
            });
        });

        builder.Services
            .AddOptions<CryptoServiceOptions>()
            .Configure<IOptions<BlizardOptions>>((options, blizardOptions) =>
            {
                options.CryptoKeys = blizardOptions.Value.CryptoKeys;
            });

        builder.Services
            .AddOptions<TokenManagerServiceOptions>()
            .Configure<IOptions<BlizardOptions>>((options, blizardOptions) =>
            {
                options.TokenManagerKeys = blizardOptions.Value.TokenManagerKeys;
            });

        builder.Services.AddSingleton<ICryptoService, CryptoService>();
        builder.Services.AddSingleton<IAes256Service, Aes256Service>();
        builder.Services.AddSingleton<ITokenManagerService, TokenManagerService>();

        builder.Services.AddHttpClient("QCashApiClient", (serviceProvider, client) =>
        {
            var settings = serviceProvider.GetRequiredService<IOptions<BlizardOptions>>().Value;
            client.BaseAddress = new Uri(settings.ApiUrl);
            client.DefaultRequestHeaders.TryAddWithoutValidation("OriginApp", ((int)OriginApp.Web).ToString());
            client.Timeout = TimeSpan.FromMinutes(5);
        });

        if (!builder.Environment.IsDevelopment())
        {
            builder.Services.AddHsts(options =>
            {
                options.Preload = true;
                options.IncludeSubDomains = true;
                options.MaxAge = TimeSpan.FromDays(365);
            });

            builder.Services.AddDistributedSqlServerCache(options =>
            {
                options.ConnectionString = builder.Configuration.GetConnectionString("QCashConnection");
                options.SchemaName = "dbo";
                options.TableName = "_DistributedCacheBlizard";
            });
        }
        else
        {
            builder.Services.AddDistributedMemoryCache();
        }

        builder.Services.AddScoped<InitiationService>();
        builder.Services.AddScoped<DataService>();
        builder.Services.AddScoped<SessionService>();
        builder.Services.AddScoped<BlizardNavigationManager>();
        builder.Services.AddScoped<TelemetryService>();
        builder.Services.AddScoped<LoggerService>();
        builder.Services.AddSingleton<FiConfigurationService>();
        builder.Services.AddSingleton<AppConfiguration>();

        builder.Services.AddControllers();

        return builder;
    }

    private static IHostApplicationBuilder ConfigureRateLimiting(this IHostApplicationBuilder builder)
    {
        builder.Services.Configure<IpRateLimitOptions>(builder.Configuration.GetSection(IpRateLimitSettings.SectionName));
        builder.Services.Configure<IpRateLimitPolicies>(builder.Configuration.GetSection(nameof(IpRateLimitPolicies)));


        if (builder.Environment.IsDevelopment())
        {
            builder.Services.AddInMemoryRateLimiting();
        }
        else
        {
            builder.Services.AddDistributedRateLimiting<AsyncKeyLockProcessingStrategy>();
        }

        builder.Services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();

        return builder;
    }

    private static IHostApplicationBuilder ConfigureApplicationInsights(this IHostApplicationBuilder builder)
    {
        builder.Services.AddApplicationInsightsTelemetry();
        return builder;
    }

    private static IHostApplicationBuilder ConfigureHealthChecks(this IHostApplicationBuilder builder)
    {
        var instrumentationKey = builder.Configuration
            .GetSection("ApplicationInsights")
            .GetValue<string>("InstrumentationKey")
                ?? throw new InvalidOperationException("ApplicationInsights:InstrumentationKey not found");

        var connectionConfig = new ConnectionStringsOptions();
        builder.Configuration.GetSection(ConnectionStringsOptions.SectionName).Bind(connectionConfig);

        builder.Services.AddHealthChecks()
            .AddDistributedMemoryCacheHealthCheck(
                name: "Distributed Cache",
                timeout: TimeSpan.FromSeconds(2),
                tags: ["external"])
            .AddWebApiHealthCheck(
                name: "Web API",
                timeout: TimeSpan.FromSeconds(5),
                tags: ["external"])
            .AddAzureApplicationInsights(
                instrumentationKey,
                name: "Application Insights",
                tags: ["external"])
            .AddSqlServer(
                connectionString: connectionConfig.QCashConnection,
                healthQuery: RuntimeSettingConfigurationProvider.Query,
                name: "RuntimeSettings",
                timeout: TimeSpan.FromSeconds(2));

        var emailSettings = new EmailSettings();
        builder.Configuration.GetSection("Email").Bind(emailSettings);
        if (emailSettings.DeliveryMethod == SmtpDeliveryMethod.SpecifiedPickupDirectory)
        {
            builder.Services.AddHealthChecks()
                .AddFolder(
                    setup: setup => { setup.AddFolder(emailSettings.PickupDirectory); },
                    name: "Email pickup folder",
                    timeout: TimeSpan.FromSeconds(2),
                    tags: ["node", "external", "email"]
                )
                .AddDiskStorageHealthCheck(
                    setup: setup =>
                    {
                        var drive = Directory.GetDirectoryRoot(emailSettings.PickupDirectory);
                        setup.AddDrive(drive, minimumFreeMegabytes: 10L);
                    },
                    name: "Email pickup folder drive space",
                    timeout: TimeSpan.FromSeconds(2),
                    tags: ["node", "external", "email"]
                );
        }
        return builder;
    }
}
