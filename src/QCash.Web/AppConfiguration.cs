using System.Diagnostics.CodeAnalysis;
using AspNetCoreRateLimit;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.CookiePolicy;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using QCash.Service.Utils;
using QCash.Utils.Extensions;
using QCash.Web.HealthChecks;

namespace QCash.Web;

[ExcludeFromCodeCoverage]
public class AppConfiguration(
    IWebHostEnvironment env,
    ILogger<AppConfiguration> logger,
    TelemetryConfiguration telemetryConfiguration,
    IHostApplicationLifetime applicationLifetime)
{
    public void ConfigureApp(WebApplication app)
    {
        logger.LogInformation("{ServiceName} starting up", $"{nameof(QCash)}.{nameof(Web)}");
        logger.LogInformation("Environment: {EnvEnvironmentName}", env.EnvironmentName);

        app.UseIpRateLimiting();
        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
            telemetryConfiguration.DisableTelemetry = true;
        }
        else
        {
            app.UseExceptionHandler("/Home/Error");
            app.UseHsts();
        }

        app.UseHttpsRedirection();
        app.UseStaticFiles();

        app.UseCookiePolicy(new CookiePolicyOptions() { Secure = CookieSecurePolicy.Always, HttpOnly = HttpOnlyPolicy.Always });
        app.UseRouting();

        app.UseAuthentication();
        app.UseAuthorization();
        app.MapRazorPages();

        app.UseSession();

        app.MapControllerRoute(
            name: "Seed",
            pattern: "seed/{action=Index}",
            defaults: new { area = "Seed", controller = "Seed", action = "Index" });

        app.MapControllerRoute(
            name: "account",
            pattern: "Account/AccessDenied",
            defaults: new { controller = "Account", action = "AccessDenied" });

        app.MapControllerRoute(
            name: "default with tenant and area",
            pattern: "{FiSlug}/{area:exists}/{controller=Home}/{action=Index}/{id?}");

        app.MapControllerRoute(
            name: "default with tenant",
            pattern: "{FiSlug}/{controller=Home}/{action=Index}/{id?}");

        app.MapHealthChecks("/healthz").AllowAnonymous();

        app.MapHealthChecks("/healthz/detail", new HealthCheckOptions
        {
            // Use shared health check response writer from Utils
            ResponseWriter = HttpResponseUtils.WriteHealthReportResponseAsync,
        }).AllowAnonymous();
        //.RequireAuthorization();

        app.UseMiddleware<HealthCheckCachingMiddleware>(TimeSpan.FromSeconds(10));
        app.UseMiddleware<MultiTenantMiddleware>();

        applicationLifetime.ApplicationStopped.Register(() => { logger.LogInformation("{Service} shut down", $"{nameof(QCash)}.{nameof(Web)}"); }, true);
    }
}
