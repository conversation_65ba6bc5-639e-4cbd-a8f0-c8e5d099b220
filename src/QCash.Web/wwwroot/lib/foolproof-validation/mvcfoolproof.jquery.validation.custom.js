;//You must load the mvcfoolproof.core.js script before this.

if (!FoolProofCore)
    throw "You must load the mvcfoolproof.core.js script before this.";

FoolProofCore.registerValidators = function(jQuery) {
    if (!jQuery)
        throw "You must load jquery library before this.";

    jQuery.validator.addMethod("is", function(value, element, params) {
        var dependentProperty = FoolProofCore.getId(element, params["dependentproperty"]);
        var operator = params["operator"];
        var passOnNull = params["passonnull"];
        // Use jquery to get dependent property value within the current form! (Don't assume only 1 form in document)
        var dependentValue = jQuery(element).closest("form").find('[name="' + dependentProperty + '"]').val();

        if (FoolProofCore.is(value, operator, dependentValue, passOnNull))
            return true;

        return false;
    });

    jQuery.validator.addMethod("requiredif", function(value, element, params) {
        var dependentProperty = FoolProofCore.getName(element, params["dependentproperty"]);
        var dependentTestValue = params["dependentvalue"];
        var operator = params["operator"];
        var pattern = params["pattern"];
        // Use jquery to find the named element(s) within the current form, not the entire document! (Don't assume only 1 form in document)
        var dependentPropertyElement = jQuery(element).closest("form").find('[name="' + dependentProperty + '"]');
        var dependentValue = null;

        if (dependentPropertyElement.length > 1) {
            for (var index = 0; index !== dependentPropertyElement.length; index++)
                if (dependentPropertyElement[index]["checked"]) {
                    dependentValue = dependentPropertyElement[index].value;
                    break;
                }

            if (dependentValue == null)
                dependentValue = dependentPropertyElement.val();
        }
        else
            dependentValue = dependentPropertyElement.val();

        if (FoolProofCore.is(dependentValue, operator, dependentTestValue)) {
            if (pattern == null) {
                if (value != null && value.toString().replace(/^\s\s*/, '').replace(/\s\s*$/, '') != "")
                    return true;
            }
            else
                return (new RegExp(pattern)).test(value);
        }
        else
            return true;

        return false;
    });

    jQuery.validator.addMethod("requiredifempty", function(value, element, params) {
        var dependentProperty = FoolProofCore.getId(element, params["dependentproperty"]);
        // Use jquery to get dependent property value within the current form! (Don't assume only 1 form in document)
        var dependentValue = jQuery(element).closest("form").find('[name="' + dependentProperty + '"]').val();

        if (dependentValue == null || dependentValue.toString().replace(/^\s\s*/, '').replace(/\s\s*$/, '') == "") {
            if (value != null && value.toString().replace(/^\s\s*/, '').replace(/\s\s*$/, '') != "")
                return true;
        }
        else
            return true;

        return false;
    });

    jQuery.validator.addMethod("requiredifnotempty", function(value, element, params) {
        var dependentProperty = FoolProofCore.getId(element, params["dependentproperty"]);
        // Use jquery to get dependent property value within the current form! (Don't assume only 1 form in document)
        var dependentValue = jQuery(element).closest("form").find('[name="' + dependentProperty + '"]').val();

        if (dependentValue != null && dependentValue.toString().replace(/^\s\s*/, '').replace(/\s\s*$/, '') != "") {
            if (value != null && value.toString().replace(/^\s\s*/, '').replace(/\s\s*$/, '') != "")
                return true;
        }
        else
            return true;

        return false;
    });

    jQuery.validator.addMethod("requiredifequalto", function(value, element, params) {
        var dependentProperty = params.dependentproperty;
        var compareToProperty = params.comparetoproperty;

        var form = $(element).closest('form');

        var dependentValue = form.find('[name="' + dependentProperty + '"]').val();
        var compareToValue = form.find('[name="' + compareToProperty + '"]').val();

        if (dependentValue === compareToValue) {
            return $.validator.methods.required.call(this, value, element);
        }

        return true;
    });
};

(FoolProofCore.registerValidators)(jQuery);