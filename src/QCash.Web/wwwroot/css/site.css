html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
    background-color: #fafafa;
}

.hidden {
    display: none;
}

.navbar {
    background-color: #004b9752 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    box-shadow: rgba(0, 0, 0, 0.05) 0 1px 2px 0;
}

.k-breadcrumb {
    background-color: #fafafa;
}

#backBar {
    box-shadow: none;
    background-color: #fafafa;
    width: fit-content;
    margin-left: auto;
}

.title-row {
    margin-top: 25px;
    margin-bottom: 15px;
}

.title-row h4 {
    margin-bottom: 0!important;
    color: #0D1852;
}

.title-col {
    display: flex;
    align-items: flex-end;
}

ul#main-drawer li:not(.hidden) {
    display: inline-flex; /* make element size relative to content */
}

ul#main-drawer li {
    align-items: center;
    gap: 4px;
}

#main-drawer li {
    font-size: 1.2em;
    border-radius: 0;
    border-width: 0 0 1px;
    border-color: rgba(33, 37, 41, 0.125);
    line-height: 1.5em;
    padding: 0.25em 0;
}

/* Left Navigation Icon Styling */
#main-drawer li span i {
    display: inline-flex;
    width: 40px;
    justify-content: center;
    align-items: center;
}

/* Left Navigation - Wrap Text */
#main-drawer .k-item-text {
    white-space: normal;
}

#main-drawer .submenu-item {
    padding-left: 3em;
}

#main-drawer li:last-child {
    border: 0;
}

#main-drawer ul {
    margin-top: 0;
    margin-bottom: 1rem;
}

#main-drawer {
    padding: 15px;
}

#leftSideDrawer1 {
    border-right: rgba(0, 0, 0, 0.24);
    box-shadow: 0 .3rem .6rem 0 rgba(0, 0, 0, 0.24);
    height: 100vh;
    min-height: 100%;
}

.diagonal-text {
    position: absolute;
    top: 0;
    right: 0;
    display: inline-block;
    transform: translateX(50%) translateY(-50%) rotate(45deg);
    transform-origin: center;
    z-index: 1;
}

.diagonal-text::before {
    content: "";
    display: block;
    height: 0;
    padding-top: 100%;
}

.diagonal-text > span {
    display: inline-block;
    background: #eb0d01;
    padding: 6px 55px;
    transform: translateY(-50%);
    font-size: 18px;
    text-align: center;
    color: white;
    white-space: nowrap;
}

#appbarPrimaryApplication {
    border-bottom: 1px solid rgba(0, 0, 0, 0.24);
    height: 65px;
}

#QCashLogoInAppBar {
    padding-left:25px;
    width: 270px;
    height: 65px;
    border-right: 1px solid rgba(0, 0, 0, 0.24);
}

#NotificationsInAppBar {
    height: 65px;
    width: 50px;

    border-left: 1px solid rgba(0, 0, 0, 0.24);
    display: flex;
    align-items: center;
    justify-content: center;
}

#NotificationsInAppBar > a {
    height: 65px;
    width: 50px;
}

#UserInAppBar {
    height: 65px;
    width: 50px;
    border-left: 1px solid rgba(0, 0, 0, 0.24);
    display: flex;
    align-items: center;
    justify-content: center;
}

#NotificationsInAppBar > a {
    height: 65px;
    width: 50px;
}
.shadow-box {
    box-shadow: rgba(0, 0, 0, 0.24) 0 3px 8px;
}

.rounded-box {
    //@extend .flex-col;
    background-color: white;
    //border-style: solid;
    border-color: rgba(0, 0, 0, 0.24);
    border-radius: .5rem;
    padding: 1.5rem 2.5rem;
    box-shadow: 0 .3rem .6rem 0 rgba(0, 0, 0, 0.24);   // variables.$light-font
    width: 100%;
}

.switch-with-text-on-both-sides .form-switch {
    margin: 3px 0 0 15px;

}
div.switch-with-text-on-both-sides .form-text {
    user-select: none;
    cursor:pointer;
}

/*input[readonly='readonly'] {*/
/*    background-color: rgb(220,220,220);*/
/*}*/

.disabled-input {
    cursor:default;
    background-color: rgb(220,220,220);
}

.form-label {
    user-select: none;
    cursor:pointer;
    margin-top: 10px!important;
    margin-bottom: 0!important;
    font-weight: bold;
}

.btn-group.form-control {
    border:none;
}

.switch-with-text-on-both-sides.form-control {
    border:none;
}

.centered-cell {
    width:100%;
    display:flex;
    align-items:center;
    justify-content:center;
}


/*Kendo Grid/Table Styling*/

.k-grid {
    border-width: 0 !important;
}

.k-grid .k-table {
    width: inherit;
}

.k-grid-table {
    border-left-width: 1px !important;
    border-right-width: 1px !important;
    border-top-width: 1px !important;
    border-top-left-radius: 10px !important;
    border-top-right-radius: 10px !important;
    overflow: hidden;
}

.k-toolbar {
    border-style: hidden;
}

.k-table-th {
    border-width: 0 !important;
    background-color: #fafbff !important;
}

.k-grid-header .k-header
{
    font-weight: bold;
    color: #0D1852;
}

.k-table-td {
    border-inline-start-width: 0 !important;
    border-top-width: 1px !important;
}

.k-grid tbody>tr:not(.k-detail-row) {
    background-color: #ffffff !important;
}

.k-grid tbody>tr:not(.k-detail-row):hover {
    background-color: #f4f4f4 !important;
}

.k-grid-pager {
    border-width: 1px !important;
    background-color: #ffffff !important;
    border-bottom-left-radius: 10px !important;
    border-bottom-right-radius: 10px !important;
    overflow: hidden;
}

/*END Kendo Grid/Table Styling*/

.k-grid .k-grid-search {
    width: 300px;
}

.form-control.input-validation-error
{
    background: #FEF1EC;
    border: 1px solid #CD0A0A;
}

.text-truncate-container {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
