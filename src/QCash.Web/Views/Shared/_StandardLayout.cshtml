@using System.Security.Claims
@using QCash.Web.Areas.FIConfiguration.Controllers
@using QCash.Web.Areas.FIConfiguration.Models
@using QCash.Web.Areas.MemberInterface.Controllers
@using QCash.Web.Identity
@inject IApplicationSignInManager signInManager

<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="msapplication-config" content="/browserconfig"/>
    <meta http-equiv="Page-Enter" content="blendTrans(Duration=0.2)"/>
    <meta http-equiv="Page-Exit" content="blendTrans(Duration=0.1)"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>@ViewBag.Title - QCash Admin Portal</title>
    <link rel="shortcut icon" type="image/x-icon" href="/images/cropped-qcash-favicon-1-192x192.png"/>
    <link rel="manifest" type="application/json" href="/manifest.json"/>
    <link rel="stylesheet" href="~/lib/font-awesome/css/all.min.css" asp-append-version="true"/>
    <link rel="stylesheet" href="~/lib/bootstrap/css/bootstrap.min.css" asp-append-version="true"/>
    <link rel="stylesheet" href="~/lib/kendo/css/bootstrap-main.css" asp-append-version="true"/>
    <link rel="stylesheet" href="~/lib/kendo/css/font-icons/index.css" asp-append-version="true"/>
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true"/>
    <script src="~/lib/jquery/jquery.min.js" asp-append-version="true"></script>
    <script src="~/lib/jquery-validate/jquery.validate.min.js" asp-append-version="true"></script>
    <script src="~/lib/jquery-validate/additional-methods.min.js" asp-append-version="true"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js" asp-append-version="true"></script>
    <script src="~/lib/bootstrap/js/bootstrap.bundle.min.js" asp-append-version="true"></script>
    <script src="~/lib/foolproof-validation/mvcfoolproof.core.custom.js" asp-append-version="true"></script>
    <script src="~/lib/foolproof-validation/mvcfoolproof.jquery.validation.custom.js" asp-append-version="true"></script>
    <script src="~/lib/foolproof-validation/mvcfoolproof.unobtrusive.custom.js" asp-append-version="true"></script>
    <script src="~/lib/jszip/jszip.min.js" asp-append-version="true"></script>
    <script src="~/lib/kendo/js/kendo.all.min.js" asp-append-version="true"></script>
    <script src="~/lib/kendo/js/kendo.aspnetmvc.min.js" asp-append-version="true"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/qcash.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Head", false)
</head>
<body>
<header>
    <nav class="navbar navbar-expand-lg bg-body-tertiary" data-bs-theme="light">
        <div class="container-fluid">
            <a asp-area="" asp-controller="Home" asp-action="Index" asp-route-fislug="@Context.GetTenant().Slug" class="navbar-brand">
                <img src="/images/QCashLogo.png" width="100px" alt="Q-Cash Financial"/>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
                    aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            @await Html.PartialAsync("_SlugHeaderAndEnvironment")
            <div class="collapse navbar-collapse d-flex flex-column" id="navbarSupportedContent">
                <ul class="navbar-nav ms-auto mb-2 mb-lg-0" style="margin-top: 25px;">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fa-solid fa-user"></i>
                            Welcome @User.GetUsername()
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            @if (signInManager.IsSignedIn(User))
                            {
                                <li>
                                    <span class="dropdown-item-text">Hello @User.Identity?.Name!</span>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                @if (!User.HasClaim(c => c is { Type: ClaimTypes.AuthenticationMethod, Value: "AzureAD" }))
                                {
                                    <li>
                                        <a class="dropdown-item" asp-controller="Account" asp-action="ChangePassword" asp-route-fislug="@Context.GetTenant().Slug" asp-area="">Change Password</a>
                                    </li>
                                }
                                <li>
                                    <form asp-controller="Account" asp-action="Logout" asp-route-fislug="@Context.GetTenant().Slug" asp-area="" method="post">
                                        <button type="submit" class="dropdown-item">Logout</button>
                                    </form>
                                </li>
                            }
                            else
                            {
                                <li>
                                    <a class="dropdown-item" asp-controller="Account" asp-action="Login" asp-route-fislug="@Context.GetTenant().Slug" asp-area="">Login</a>
                                </li>
                            }
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
</header>

<kendo-notification name="kPopupNotification" on-show="QCash.centerKendoNotification"></kendo-notification>
<div id="toastContainer" class="toast-container position-fixed top-0 start-50 translate-middle p-3">
</div>

<script id="drawerItemsTemplate" type="text/x-kendo-template">
    <ul id='main-drawer'>
        <li data-role='drawer-item' class='k-selected'>
            <span><i class="fa-solid fa-users"></i></span>
            <span class='k-item-text'
                  data-id='homeDrawerItem'
                  data-url='@(Url.Action<UserManagementController>(a => a.List, new { fiSlug = Html.FiSlugPlaceholder(), area = Constants.FiConfigurationArea }))'>
                        User Management
                </span>
        </li>

        <li data-role='drawer-item'>
            <span><i class="fa-solid fa-landmark"></i></span>
            <span class='k-item-text' data-id='fiConfigurationItem' data-is-parent='true' data-is-expanded='false'>
                    FI Configuration
                </span>
            <span class='k-spacer'></span>
            <span><i class="fa-solid fa-angle-down"></i></span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='fiInfo' data-parent='fiConfigurationItem'
                          data-url='@(Url.Action<FiInfoController>(a => a.EditAsync, new { fiSlug = Html.FiSlugPlaceholder(), area = Constants.FiConfigurationArea }))'>
                        FI Info
                    </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='loanApplication'
                          data-parent='fiConfigurationItem'
                          data-url='@(Url.Action<LoanApplicationGeneralSettingsController>(a => a.EditAsync, new { fiSlug = Html.FiSlugPlaceholder(), area = Constants.FiConfigurationArea }))'>
                        Loan Application
                    </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='invoice'
                          data-parent='fiConfigurationItem'
                          data-url='@(Url.Action<InvoiceGeneralSettingsController>(a => a.EditAsync, new { fiSlug = Html.FiSlugPlaceholder(), area = Constants.FiConfigurationArea }))'>
                        Invoice
                    </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
            <span class='k-item-text submenu-item' data-id='general' data-parent='fiConfigurationItem'
                  data-url='@(Url.Action<GeneralController>(a => a.EditAsync, new { fiSlug = Html.FiSlugPlaceholder(), area = Constants.FiConfigurationArea }))'>
                General
            </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
            <span class='k-item-text submenu-item' data-id='templates' data-parent='fiConfigurationItem'
                  data-url='@(Url.Action<TemplatesController>(a => a.EditAsync, new { fiSlug = Html.FiSlugPlaceholder(), area = Constants.FiConfigurationArea }))'>
                Templates
            </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
            <span class='k-item-text submenu-item' data-id='preApprovalManagement' data-parent='fiConfigurationItem'
                  data-url=''>
                Pre-Approval Management*
            </span>
        </li>

        <li data-role='drawer-item'>
            <span><i class="fa-solid fa-scale-balanced"></i></span>
            <span class='k-item-text'
                  data-id='DecisionEngineSettingsMenuItem'
                  data-url='@Url.Action("Index", "DecisionEngine", new { fiSlug = Html.FiSlugPlaceholder(), area = "" })'>
                    Decision Engine Settings
                </span>
        </li>

        <li data-role='drawer-item'>
            <span><i class="fa-solid fa-layer-group"></i></span>
            <span class='k-item-text'
                  data-id='modelDashboardItem'
                  data-is-parent='true'
                  data-is-expanded='false'>
                    Model Manager
                </span>
            <span class='k-spacer'></span>
            <span><i class="fa-solid fa-angle-down"></i></span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='modelManager'
                          data-url=''
                          data-parent='modelDashboardItem'>
                        Model Dashboard*
                    </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='modelManager'
                          data-url=''
                          data-parent='modelDashboardItem'>
                        A:B Model Dashboard*
                    </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='modelManager'
                          data-url=''
                          data-parent='modelDashboardItem'>
                        Back Testing Dashboard*
                    </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='modelManager'
                          data-url=''
                          data-parent='modelDashboardItem'>
                        Model Archive*
                    </span>
        </li>

        <li data-role='drawer-item'>
            <span><i class="fa-solid fa-hand-holding-dollar"></i></span>
            <span class='k-item-text'
                  data-id='Products'
                  data-url='@Url.Action("List", "ProductGeneralProductInfo", new { fiSlug = Html.FiSlugPlaceholder(), area = "" })'>
                    Manage Products
                </span>
        </li>

        <li data-role='drawer-item'>
            <span><i class="fa-solid fa-ban"></i></span>
            <span class='k-item-text'
                  data-url='@Url.Action("Index", "ManageRestrictions", new { fiSlug = Html.FiSlugPlaceholder(), area = QCash.Web.Areas.LoanApplication.Models.Constants.LoanApplicationArea })'
                  data-id='ManageRestrictions'>
                    Manage Restrictions
                </span>
        </li>

        <li data-role='drawer-item'>
            <span><i class="fa-solid fa-file-lines"></i></span>
            <span class='k-item-text'
                  data-id='manageMemberInterface'
                  data-is-parent='true'
                  data-is-expanded='false'>
                    Member Interface
                </span>
            <span class='k-spacer'></span>
            <span><i class="fa-solid fa-angle-down"></i></span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='memberInterfaceLanguage'
                          data-url='@Url.Action("Index", "Language", new { fiSlug = Html.FiSlugPlaceholder(), area = QCash.Web.Areas.MemberInterface.Models.Constants.MemberInterfaceArea })'
                          data-parent='manageMemberInterface'>
                        Language
                    </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='memberInterfaceText'
                          data-url='@(Url.Action<TextListController>(a => a.List,
                            new { fiSlug = Html.FiSlugPlaceholder(), area = QCash.Web.Areas.MemberInterface.Models.Constants.MemberInterfaceArea }))'
                          data-parent='manageMemberInterface'>
                        Text
                    </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='memberInterfaceProductText'
                          data-url=''
                          data-parent='manageMemberInterface'>
                        Product Text
                    </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='memberInterfacePresentation'
                          data-url='@(Url.Action<PresentationController>(a => a.EditAsync, new { fiSlug = Html.FiSlugPlaceholder(), area = QCash.Web.Areas.MemberInterface.Models.Constants.MemberInterfaceArea }))'
                          data-parent='manageMemberInterface'>
                        Presentation
                    </span>
        </li>

        <li data-role='drawer-item'>
            <span><i class="fa-solid fa-flask"></i></span>
            <span class='k-item-text'
                  data-url='@(Url.Action<SsoTestSetupController>(a => a.EditAsync, new { fiSlug = Html.FiSlugPlaceholder(), area = "" }))'
                  data-id='QATestSuite'>
                    QA Test Suite
                </span>
        </li>

        <li data-role='drawer-item'>
            <span><i class="fa-solid fa-chart-line"></i></span>
            <span class='k-item-text'
                  data-id='Reports'
                  data-is-parent='true'
                  data-is-expanded='false'
                  data-url=''>
                    Reports*
                </span>
            <span class='k-spacer'></span>
            <span><i class="fa-solid fa-angle-down"></i></span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='ApplicationLogs'
                          data-parent='Reports'
                          data-url='@Url.Action("Index", "ApplicationLogs", new { fiSlug = Html.FiSlugPlaceholder(), area = "Reports" })'>
                        Application Logs
                    </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='UserLogs'
                          data-parent='Reports'
                          data-url='@Url.Action("Index", "AuditLogs", new { fiSlug = Html.FiSlugPlaceholder(), area = "Reports" })'>
                        User Logs
                    </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='ReportDownloads'
                          data-parent='Reports'
                          data-url='@Url.Action("Index", "ReportDownloads", new { fiSlug = Html.FiSlugPlaceholder(), area = "Reports" })'>
                        Download Reports
                    </span>
        </li>

        <li data-role='drawer-item' class='hidden'>
                    <span class='k-item-text submenu-item'
                          data-id='ReportScheduler'
                          data-parent='Reports'
                          data-url='@Url.Action("Index", "ReportScheduler", new { fiSlug = Html.FiSlugPlaceholder(), area = "Reports" })'>
                        Report Scheduler
                    </span>
        </li>

        <li data-role='drawer-item'>
            <span><i class="fa-solid fa-power-off"></i></span>
            <span class='k-item-text'
                  data-id='SystemStatus'>
                    System Status
                </span>
        </li>

    </ul>
</script>

<kendo-drawer name="leftSideDrawer1" template-id="drawerItemsTemplate" mode="push" expanded="true"
              auto-collapse="false" deferred="true" position="left" min-height="330" width="300"
              swipe-to-open="false" on-item-click="onDrawerItemClick">
    <mini enabled="false"/>
    <content>
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div id="breadcrumbs-container" class="flex-grow-1 me-3"
                 data-url="@Url.Action("GetBreadcrumbs", "Breadcrumbs", new { area = "", fiSlug = Context.GetTenant().Slug })"
                 data-controller="@ViewContext.RouteData.Values["controller"]"
                 data-action="@ViewContext.RouteData.Values["action"]"
                 data-product-id="@(Model.GetType().GetProperty("ProductId") != null ? $"{Model.ProductId}" : "null")">
            </div>
            <button class="k-button k-button-md k-rounded-md k-button-solid k-button-solid-light" onclick="clickBack()">
                <i class="fa-solid fa-arrow-left k-button-text"></i>
                <span class="k-button-text">Back</span>
            </button>
        </div>
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </content>
</kendo-drawer>

@await RenderSectionAsync("Scripts", required: false)
@await RenderSectionAsync("Styles", required: false)
@await RenderSectionAsync("Footer", false)
</body>
</html>

<script type="text/javascript" nonce="kendoInlineScript">
    @Html.Kendo().DeferredScripts(false)
</script>

<script type="text/javascript">
    $(document).ready(function () {
        setNavDrawerInitialSelectedItem('@(Html.FiSlugPlaceholder())');
    });

    function clickBack() {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = '@Url.Action("Index", "Home", new { area = "" })';
        }
    }

    function onDrawerItemClick(e) {
        const result = findAndShowDrawerItem(e.item, '@(Html.FiSlugPlaceholder())');
        if (!!result?.url) {
            redirectTo(result.url);
        }
    }
</script>