@using QCash.Web.Utils
@model QCash.Web.Models.Lookup.ListPageViewModel
@{ Layout = "_StandardLayout"; }

<script id="delete-list-item-template" type="text/x-kendo-template">
    @await Html.PartialAsync("_DeleteListItemButton")
</script>

<div class="container">
    @await Html.PartialAsync("_TitleRow", new TitleViewModel
    {
        Title = @Model.LookupListType.Title,
        EnableActionButton = true,
        ActionButtonText = "Add New Item",
        DisplayAddIcon = true,
        ActionButtonUrl = @Url.Action("Create","DvLookup", new {lookupTypeName=@Model.LookupTypeName, fiSlug=@Model.FiSlug})
    })

    @(Html.Kendo().Grid<GenericDescriptionListItemViewModel>()
        .Name("lookupListGrid1")
        .Columns(cols =>
        {
            cols
                .Template($"<a class='btn btn-light btn-sm' href='/{Model.FiSlug}/DvLookup/{Model.LookupTypeName}/Edit/#:Id#'><i class='fa-solid fa-pencil'></i></a>"
                ).Width(10);
            cols.Bound(p => p).ClientTemplateId("delete-list-item-template").Width(10);
            cols.Bound(p => p.Id).Visible(false);
            cols.Bound(p => p.Description);
            cols.Bound(p => p.Value);
        })
        .Sortable(s =>
        {
            s.SortMode(GridSortMode.SingleColumn);
            s.AllowUnsort(false);
            s.Enabled(true);
        })
        .Pageable(pageable => pageable
            .ButtonCount(TableSettings.DefaultButtonCount)
            .Refresh(TableSettings.ShowRefreshButton)
            .PageSizes(TableSettings.DefaultPageSizes))
        .DataSource(ds => ds
            .WebApi()
            .Model(m => { m.Id(p => p.Id); })
            .Read(read => read.Url(
                Url.Action<LookupApiController>(a => a.GetAsync, new {fiSlug = Model.FiSlug, lookupTypeName = Model.LookupTypeName, area = "" })))
            .Sort(sort =>
                {
                    sort.Add(nameof(GenericDescriptionListItemViewModel.Description));
                }
            )
        ))
</div>

@await Html.PartialAsync("_DeleteModalConfirmation",
    new DeleteModalViewModel()
    {
        FiSlug = Model.FiSlug,
        LookupTypeName = Model.LookupTypeName,
        GridListIdName = "lookupListGrid1",
        DeleteActionUrl = @Url.Action("Delete", "DvLookup", new { lookupTypeName = Model.LookupTypeName, area = "", fiSlug = Model.FiSlug, }),
    })

@await Html.PartialAsync("_ToastOnPageLoad", Model.SubmissionNotifications)
