@using QCash.Web.Models.Products
@model IProductSelectedLoanCategory

@if (!Model.IsActive)
{
    @Html.QLabelFor(a => a.SelectedLoanCategoryId, htmlAttributes: new {@class="form-label"})
    @Html.QDropDownListFor(a => Model.SelectedLoanCategoryId,
        Model.LoanCategoryOptions, new {disabled = "disabled"})

    @Html.HiddenFor(model => model.SelectedLoanCategoryId)
}
else
{
    @Html.QDropDownListFor(a => Model.SelectedLoanCategoryId,
        Model.LoanCategoryOptions, new {})


}

