@using QCash.Web.Models.Products
@model QCash.Web.Models.Products.ProductsPageViewModel
@{ Layout = "_StandardLayout"; }

<div class="container">
    @await Html.PartialAsync("_TitleRow", new TitleViewModel
    {
        Title = "Products",
        EnableSearch = true,
        SearchBar = new SearchBarViewModel
        {
            SearchPlaceholderText = "Products",
            SearchUrl = @Url.Action("FilterProducts", "ProductGeneralProductInfo"),
            SearchParameterName = "search",
            ListContainerIdForSearch = "productListContainer",
        },
        EnableActionButton = true,
        ActionButtonText = "Add Product",
        DisplayAddIcon = true,
        ActionButtonUrl = @Url.Action("Create","ProductGeneralProductInfo", new { fiSlug = @Model.FiSlug, area = "" })
    })

    <div id="productListContainer">
        @await Html.PartialAsync("_ProductListPartialView", Model)
    </div>
</div>

<script>
    function redirectToProductDetails(productSlug) {
        const url = getRelativeUrl(`/Product/${productSlug}/General/ProductInfo`);
        redirectTo(url);
    }

    function searchSubmitted(event) {
        event.preventDefault();
        const searchTextBox = $("#@nameof(ProductsPageViewModel.SearchValue)").data("kendoTextBox");
        const searchVal = searchTextBox.value();
        const baseUrl = getRelativeUrl(`/Product/List`);
        const url = baseUrl + (searchVal ? `?search=${searchVal}` : "");
        redirectTo(url);
        return false;
    }
</script>
