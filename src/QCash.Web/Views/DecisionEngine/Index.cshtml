@model QCash.Web.Models.DecisionEngine.DecisionEngineSettingsListPageViewModel
@{ Layout = "_StandardLayout"; }

<div class="container">
    @await Html.PartialAsync("_TitleRow", new TitleViewModel
    {
        Title = "Decision Engine Settings"
    })

    @foreach (var lookupGroup in Model.LookupGroups)
    {
        <div class="row" style="margin-top: 55px">
            <h5>
                @lookupGroup.Text
            </h5>
        </div>
        <div class="row rounded-box" style="padding-top:5px; padding-bottom: 20px">
            @foreach (var item in Model.Lookups
                          .Where(a => a.UIGroupEnum == lookupGroup.Value)
                          .OrderBy(a => a.<PERSON>IOrder))
            {
                <div class="col-6" style="margin-top: 0; padding-top: 10px; position: relative;">
                    <a class="btn btn-primary" style="line-height:35px; min-width:250px"
                           href="@Url.Action("List", "Lookup", new { fiSlug = Model.FiSlug, lookupTypeName = item.LookupType, area = "" })">
                            @(item.UIGroupDescription ?? item.LinkText)
                        </a>
                </div>
            }
        </div>
    }
</div>
