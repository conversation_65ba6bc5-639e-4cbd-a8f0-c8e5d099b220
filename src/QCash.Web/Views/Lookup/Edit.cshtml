@model QCash.Web.Models.Lookup.EditPageViewModel
@using QCash.Service.Services.General.Interfaces
@inject ILookupItemService LookupItemService

@{ Layout = "_StandardLayout"; }

<div class="container">
    <form id="lookupEditForm" method="post" class="custom needs-validation" novalidate  >
        @Html.AntiForgeryToken()
        @Html.HiddenFor(a => Model.Id)
        @Html.HiddenFor(a => Model.FiSlug)
        @Html.HiddenFor(a => Model.Abrv)
        @Html.HiddenFor(a => Model.LanguageCount)
        @Html.HiddenFor(a => Model.LanguageCode)
        @Html.HiddenFor(a => Model.Slug)
        @Html.HiddenFor(a => Model.LookupTypeName)
        @Html.HiddenFor(a => Model.TimeStamp)

        @await Html.PartialAsync("_TitleRow", new TitleViewModel
        {
            Title = @Model.PageTitle,
        })
        @await Html.PartialAsync("_ValidationSummary", Model)

        <div class="rounded-box">
            <div class="row">
                <div class="col">
                    @Html.QTextBoxFor(u => Model.Name, overrideLabelContent: Model.NameHeader,
                        maxLength: Model.NameFieldMaxLength)
                </div>
                @if (LookupItemService.ShouldShowTranslationName(Model.LookupTypeEnum, Model.ForeignLanguage))
                {
                    <div class="col">
                        @Html.QTextBoxFor(a => Model.TranslationName)
                    </div>
                }
                else
                {
                    @Html.HiddenFor(a => Model.TranslationName)
                }
            </div>
            <div class="row">
                <div class="col">
                    @Html.QTextAreaFor(u => Model.Description)
                </div>
                @if (LookupItemService.ShouldShowTranslationDescription(Model.LookupTypeEnum, Model.ForeignLanguage))
                {
                    <div class="col">
                        @Html.QTextAreaFor(u => Model.TranslationDescription)
                    </div>
                }
            </div>
        </div>

        @await Html.PartialAsync("_SubmitButton", new SubmitButtonViewModel())
    </form>
</div>
@await Html.PartialAsync("_ToastOnPageLoad", Model.SubmissionNotifications)

@section Scripts {
    <script type="text/javascript">
        $(function () {
            $('#lookupEditForm input').on('input', function () {
                const form = $(this.form);
                if (form.data('validator')) {
                    $(this).valid();    // Trigger validation for the changed field
                }
            });
        });
    </script>
}
