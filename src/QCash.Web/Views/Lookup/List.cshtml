@using QCash.Service.Models.DecisionEngine
@using QCash.Service.Services.General.Interfaces
@using QCash.Service.Utilities
@using QCash.Web.Utils
@model QCash.Web.Models.Lookup.ListPageViewModel
@inject ILookupListService LookupListService

@{ Layout = "_StandardLayout"; }

<script id="delete-list-item-template" type="text/x-kendo-template">
    @await Html.PartialAsync("_DeleteListItemButton")
</script>

<div class="container">
    @await Html.PartialAsync("_TitleRow", new TitleViewModel
    {
        Title = @Model.LookupListType.Title,
        EnableActionButton = true,
        ActionButtonText = "Add New Item",
        DisplayAddIcon = true,
        ActionButtonUrl = @Url.Action("Edit","Lookup", new {lookupTypeName = @Model.LookupTypeName, lookupItemSlug=@ItemConstants.CreateItemTag, area="", fiSlug=Model.FiSlug})
    })

    @(Html.Kendo().Grid<ListItemViewModel>()
        .Name("lookupListGrid1")
        .Columns(cols =>
        {
            cols
                .Template($"<a class='btn btn-light btn-sm' href='/{Model.FiSlug}/Lookup/{Model.LookupTypeName}/Edit/#:Slug#' > <i class='fa-solid fa-pencil'></i></a>"
                ).Width(10);
            cols.Bound(p => p).ClientTemplateId("delete-list-item-template").Width(10)
                .Visible(LookupListService.CanDelete(Model.LookupTypeEnum));
            cols.Bound(p => p.Id).Visible(false);
            cols.Bound(p => p.Name).Title(Model.NameHeader).Sortable(true).Width(200);
            cols.Bound(p => p.Description).Sortable(true);
        })
        .Sortable(s =>
        {
            s.SortMode(GridSortMode.SingleColumn);
            s.AllowUnsort(false);
            s.Enabled(true);
        })
        .Pageable(pageable => pageable
            .ButtonCount(TableSettings.DefaultButtonCount)
            .Refresh(TableSettings.ShowRefreshButton)
            .PageSizes(TableSettings.DefaultPageSizes))
        .DataSource(ds => ds
            .WebApi()
            .Model(m => { m.Id(p => p.Id); })
            .Read(read => read.Url($"/{Model.FiSlug}/api/LookupApi/{Model.LookupTypeName}"))
            .Sort(sort => { sort.Add(nameof(ListItemViewModel.Name)); }
            )
        ))
</div>

@await Html.PartialAsync("_DeleteModalConfirmation",
    new DeleteModalViewModel()
    {
        FiSlug = Model.FiSlug,
        LookupTypeName = Model.LookupTypeName,
        GridListIdName = "lookupListGrid1",
        DeleteActionUrl = @Url.Action("Delete", "Lookup", new { lookupTypeName = Model.LookupTypeName, area = "", fiSlug = Model.FiSlug, }),
    })

@await Html.PartialAsync("_ToastOnPageLoad", Model.SubmissionNotifications)
