@model QCash.Web.Models.Lookup.TrackingRecordDataMappingEditPageViewModel
@{ Layout = "_StandardLayout"; }

<div class="container">
    <form id="deTrackingRecordDataMappingItemForm" method="post" class="custom needs-validation"  novalidate>
        @Html.AntiForgeryToken()
        @Html.HiddenFor(a => Model.FiSlug)
        @Html.HiddenFor(a => Model.Id)
        @Html.HiddenFor(a => Model.Slug)
        @Html.HiddenFor(a => Model.TimeStamp)
        @Html.HiddenFor(a => Model.PageTitle)

        @await Html.PartialAsync("_TitleRow", new TitleViewModel
        {
            Title = Model.PageTitle ?? "Edit lookup",
        })
        @await Html.PartialAsync("_ValidationSummary", Model)

        <div class="rounded-box">
            <div class="row">
                <div class="col">
                    @Html.QTextBoxFor(u => Model.Name)
                </div>
                <div class="col">
                    @Html.QDropDownListFor(a => a.TrackingRecordLevel,
                    Model.TrackingRecordLevelChoices)
                </div>
            </div>
            <div class="row">
                <div class="col">
                    @Html.QTextBoxFor(u => Model.TrackingRecordType)
                </div>
                <div class="col">
                    @Html.QTextBoxFor(u => Model.TrackingRecordField)
                </div>
            </div>
            <div class="row">
                <div class="col">
                    @Html.QDropDownListFor(a => a.TrackingRecordPullOption,
                    Model.TrackingRecordPullOptionChoices)
                </div>
                <div class="col"></div>
            </div>
            <div class="row">
                <div class="col">
                    @Html.QTextAreaFor(u => Model.Description)
                </div>
            </div>
        </div>

        @await Html.PartialAsync("_SubmitButton", new SubmitButtonViewModel())
    </form>
</div>
@await Html.PartialAsync("_ToastOnPageLoad", Model.SubmissionNotifications)
