using QCash.Service.Models;

namespace QCash.Web.Models;

public class TenantSelectPageViewModel
{
    public List<TenantSelectPageTenantViewModel> Tenants { get; set; } = [];

    public static TenantSelectPageViewModel FromDto(TenantSelectPageDto dto)
    {
        var result = new TenantSelectPageViewModel
        {
            Tenants = dto.Tenants.Select(TenantSelectPageTenantViewModel.FromDto).ToList(),
        };
        return result;
    }
}

#pragma warning disable MA0048
public class TenantSelectPageTenantViewModel
#pragma warning restore MA0048
{
    public DateTime DateUpdated { get; private set; }
    public string? FiSlug { get; set; }
    public string Name { get; set; } = "";
    public string City { get; set; } = "";
    public string State { get; set; } = "";
    public string ContactName { get; set; } = "";
    public int Index { get; set; }

    private TenantSelectPageTenantViewModel(DateTime dateUpdated) =>
        DateUpdated = dateUpdated;

    public static TenantSelectPageTenantViewModel FromDto(TenantSelectPageTenantDto input)
    {
        var result = new TenantSelectPageTenantViewModel(input.DateUpdated)
        {
            Name = input.Name,
            FiSlug = input.FiSlug,
            ContactName = input.ContactName,
            City = input.City,
            State = input.State,
            Index = input.Index,
        };
        return result;
    }
}
