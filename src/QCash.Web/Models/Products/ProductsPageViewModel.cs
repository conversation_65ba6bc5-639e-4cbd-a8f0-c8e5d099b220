using QCash.Service.Models.Product;

namespace QCash.Web.Models.Products;

public class ProductsPageViewModel
{
    public List<ProductListItemViewModel> Products { get; private init; } = [];
    public required string FiSlug { get; init; } = null!;

    public required string? SearchValue { get; init; } = "";

    public static ProductsPageViewModel FromDto(ProductsPageDto input)
    {
        var result = new ProductsPageViewModel()
        {
            Products = input.Products
                .Select(ProductListItemViewModel.FromDto)
                .ToList(),
            FiSlug = input.FiSlug,
            SearchValue = input.SearchValue,
        };
        return result;
    }

}

#pragma warning disable MA0048
public class ProductListItemViewModel
#pragma warning restore MA0048
{
    public Guid Id { get; init; }
    public bool IsActive { get; init; } = true;
    public string Name { get; init; } = "";
    public string Description { get; init; } = "";
    public string LoanTypeName { get; init; } = "";
    public string Abrv { get; init; } = "";
    public bool IsArchived { get; init; }
    public string Slug { get; init; } = "";

    private ProductListItemViewModel() { }

    public static ProductListItemViewModel FromDto(ProductListItemDto input)
    {
        var result = new ProductListItemViewModel()
        {
            Id = input.Id,
            IsActive = input.IsActive,
            Name = input.Name,
            Description = input.Description,
            LoanTypeName = input.LoanTypeName,
            Abrv = input.Abrv,
            IsArchived = input.IsArchived,
            Slug = input.Slug,
        };
        return result;
    }
}

