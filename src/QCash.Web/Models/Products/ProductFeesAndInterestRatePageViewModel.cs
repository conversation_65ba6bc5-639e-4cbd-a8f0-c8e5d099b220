using System.ComponentModel.DataAnnotations;
using QCash.Service.Models;
using QCash.Service.Models.Product;

namespace QCash.Web.Models.Products;

public class ProductFeesAndInterestRatePageViewModel : BaseViewModel
{
    public required string ProductSlug { get; init; }
    public required Guid Id { get; init; }
    public required string TimeStamp { get; init; }


    [Display(Name = "MLA Interest Rate")]
    public required decimal? MLACoveredBorrowerInterestRate { get; init; }

    [Required]
    [Display(Name = "Auto Pay Rate")]
    public required decimal? AutoPayRate { get; init; }

    [Display(Name = "Automatic Payment")]
    public required int? AutoPayOption { get; init; }

    [Display(Name = "Personal Loan Campaign")]
    public required bool PersonalLoanCampaign { get; init; }

    [Display(Name = "PreApproved Card Campaign")]
    public required bool PreApprovedCardCampaign { get; init; }
    public required decimal? InterestRate { get; init; }
    public required Guid LoanTypeId { get; init; }

    // Metadata
    public List<QListItem<int?>> AutoPayOptionChoices { get; set; } = [];
    public bool IsInterestBased { get; set; }
    public ProductMainNavHeaderViewModel? NavHeader { get; set; }
    public ProductGeneralNavHeaderViewModel? NavHeaderSecondary { get; set; }

    public static ProductFeesAndInterestRatePageViewModel FromDto(ProductFeesAndInterestRateTabDto input)
    {
        var result = new ProductFeesAndInterestRatePageViewModel
        {
            FiSlug = input.FiSlug,
            ProductSlug = input.ProductSlug,
            Id = input.Id,
            TimeStamp = Convert.ToBase64String(input.TimeStamp),
            MLACoveredBorrowerInterestRate = input.MAPRInterestRate,
            AutoPayRate = input.AutoPayRate,
            AutoPayOption = input.AutoPayOption,
            PersonalLoanCampaign = input.PersonalLoanCampaign,
            PreApprovedCardCampaign = input.PreApprovedCardCampaign,
            LoanTypeId = input.LoanTypeId,
            InterestRate = input.InterestRate,
        };
        return result;
    }



    public ProductFeesAndInterestRateTabDto ToDto()
    {
        var result = new ProductFeesAndInterestRateTabDto()
        {
            ProductSlug = ProductSlug,
            FiSlug = FiSlug,
            Id = Id,
            TimeStamp = Convert.FromBase64String(TimeStamp ?? ""),
            MAPRInterestRate = MLACoveredBorrowerInterestRate,
            AutoPayRate = AutoPayRate ?? 0,
            AutoPayOption = AutoPayOption,
            PersonalLoanCampaign = PersonalLoanCampaign,
            PreApprovedCardCampaign = PreApprovedCardCampaign,
            InterestRate = InterestRate ?? 0,
            LoanTypeId = LoanTypeId,
        };
        return result;
    }
}
