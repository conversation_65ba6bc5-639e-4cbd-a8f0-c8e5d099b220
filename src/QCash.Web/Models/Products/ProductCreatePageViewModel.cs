using System.ComponentModel.DataAnnotations;
using QCash.Service.Models;
using QCash.Service.Models.Product;

namespace QCash.Web.Models.Products;

public class ProductCreatePageViewModel : IProductSelectedLoanCategory
{
    [Required]
    public required Guid FinancialInstitutionId { get; init; }

    [Required]
    [Display(Name = "Loan Category")]
    public required Guid SelectedLoanCategoryId { get; init; }

    [Required]
    [Display(Name = "Loan Type")]
    public required Guid? LoanTypeId { get; init; }

    [Required]
    [MaxLength(50, ErrorMessage = "The maximum length of Name is '50'")]
    public required string Name { get; init; } = "";

    [Required]
    [Display(Name = "Abbreviation")]
    [MaxLength(50, ErrorMessage = "The maximum length of Abrv is '50'")]
    public required string Abrv { get; init; } = "";

    [MaxLength(128), Display(Name = "Product Description")]
    public required string? Description { get; init; } = "";

    [Display(Name = "Personal Loan Campaign")]
    public required bool PersonalLoanCampaign { get; init; }

    public required bool IsArchived { get; init; }
    public required string Title { get; init; } = "";
    public required string FiSlug { get; init; } = "";
    public required bool IsActive { get; init; } = false;
    // Metadata
    public ToastModel? SubmissionNotifications { get; set; } = null;
    public List<QListItem<Guid>> LoanTypeOptions { get; init; } = [];
    public List<QListItem<Guid>> LoanCategoryOptions { get; init; } = [];


    public static ProductCreatePageViewModel FromDto(ProductCreatePageDto input)
    {
        var model = new ProductCreatePageViewModel()
        {
            FiSlug = input.FiSlug,
            FinancialInstitutionId = input.FinancialInstitutionId,
            SelectedLoanCategoryId = input.SelectedLoanCategoryId,
            LoanTypeId = input.LoanTypeId,
            Name = input.Name,
            Abrv = input.Abrv,
            Description = input.Description,
            PersonalLoanCampaign = input.PersonalLoanCampaign,
            IsArchived = input.IsArchived,
            Title = input.Title,
            LoanTypeOptions = input.LoanTypeOptions,
            LoanCategoryOptions = input.LoanCategoryOptions,
            IsActive = input.IsActive,
        };
        return model;
    }

    public ProductCreatePageDto ToDto()
    {
        var model = new ProductCreatePageDto()
        {
            FiSlug = FiSlug,
            FinancialInstitutionId = FinancialInstitutionId,
            SelectedLoanCategoryId = SelectedLoanCategoryId,
            LoanTypeId = LoanTypeId,
            Name = Name,
            Abrv = Abrv,
            Description = Description ?? "",
            PersonalLoanCampaign = PersonalLoanCampaign,
            IsArchived = IsArchived,
            Title = Title,
            IsActive = IsActive,
        };
        return model;
    }
}
