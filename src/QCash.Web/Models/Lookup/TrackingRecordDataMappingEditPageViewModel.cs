using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using static QCash.Service.Utilities.Extensions.EnumExtensions;

namespace QCash.Web.Models.Lookup;

// This is a viewmodel, so the properties we want to receive from a post cannot be private.
// Suppressing incorrect warnings with the lines below.
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable MemberCanBeProtected.Global
// ReSharper disable PropertyCanBeMadeInitOnly.Global
public class TrackingRecordDataMappingEditPageViewModel : TrackingRecordDataMappingPostingViewModel
{
    public ToastModel? SubmissionNotifications { get; set; }
    public List<QListItem<Enums.TrackingRecordLevelPull>> TrackingRecordLevelChoices { get; set; } = [];
    public List<QListItem<Enums.TrackingRecordPullOption>> TrackingRecordPullOptionChoices { get; set; } = [];

    public static TrackingRecordDataMappingEditPageViewModel FromDto(DETrackingRecordDataMappingDto input)
    {
        var result = new TrackingRecordDataMappingEditPageViewModel
        {
            Id = input.Id,
            Name = input.Name,
            Slug = input.Slug,
            FiSlug = input.FiSlug,
            Description = input.Description,
            TrackingRecordType = input.TrackingRecordType,
            TrackingRecordField = input.TrackingRecordField,
            TrackingRecordLevel = GetEnumByString<Enums.TrackingRecordLevelPull>(input.TrackingRecordLevel) ??
                                  Enums.TrackingRecordLevelPull.Unknown,
            TrackingRecordPullOption = GetEnumByString<Enums.TrackingRecordPullOption>(input.TrackingRecordPullOption) ??
                                       Enums.TrackingRecordPullOption.Unknown,
            TimeStamp = Convert.ToBase64String(input.TimeStamp),
            PageTitle = "Edit DE Tracking Record Data Mappings",
        };
        return result;
    }
}
