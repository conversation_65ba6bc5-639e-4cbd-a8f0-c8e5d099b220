using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using System.Text.Json;
using QCash.Service.Models.General;
using QCash.Service.Utilities;
using static QCash.Service.Utilities.Extensions.ExceptionExtensions;

namespace QCash.Web.Models.Lookup;

// This is a viewmodel, so the properties we want to receive from a post cannot be private.
// Suppressing incorrect warnings with the line below.
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable PropertyCanBeMadeInitOnly.Global
public class EditPostingViewModel : IValidatableObject
{
    #region Properties

    public string FiSlug { get; init; } = null!;

    /// <summary>
    /// Gets or sets Abrv.
    /// </summary>
    [DataMember]
    public string? Abrv { get; set; } = "";

    /// <summary>
    /// Gets or sets lookup item description.
    /// </summary>
    [DataMember, Required, MaxLength(500)]
    //[AllowHtml]       // TODO: review this attribute
    public string? Description { get; set; } = "";

    /// <summary>
    /// Gets or sets lookup item id.
    /// </summary>
    [DataMember]
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets IsDeleted.
    /// </summary>
    [DataMember]
    public bool IsDeleted { get; set; }

    public string? LanguageCode { get; set; } = "";

    public int LanguageCount { get; set; }

    /// <summary>
    /// Gets or sets lookup type.
    /// </summary>
    [DataMember]
    public string LookupTypeName { get; set; } = "";

    /// <summary>
    /// Gets or sets lookup item name.
    /// </summary>
    [DataMember, Required]  // MaxLength is dynamic, set in the view by NameFieldMaxLength
    public string? Name { get; set; } = "";

    /// <summary>
    /// Gets or sets lookup item slug.
    /// </summary>
    [DataMember] // Was Required in QCash6.  Not sure why.  Users don't have the ability to see or set this value.
    public string Slug { get; set; } = "";


    /// <summary>
    /// Gets or sets lookup item description.
    /// </summary>
    [DataMember, MaxLength(500), Display(Name = "Description")]
    //[AllowHtml]       // TODO: review this attribute
    public string? TranslationDescription { get; set; } = "";

    /// <summary>
    /// Gets or sets lookup item name.
    /// </summary>
    [DataMember, MaxLength(50), Display(Name = "Name")]
    public string? TranslationName { get; set; } = "";

    [DataMember]
    public string? Code { get; set; } = "";

    [DataMember] public string? TimeStamp { get; set; } = "";       // can be null when creating a new record

    public bool IsCreating => Id.Equals(Guid.Empty);

    #endregion Properties

    public EFPocoLookupDto ToDto()
    {
        var result = new EFPocoLookupDto()
        {
            Id = Id,
            Name = Name?.Trim() ?? string.Empty,
            Slug = Slug,
            IsDeleted = IsDeleted,
            Description = Description?.Trim() ?? string.Empty,
            Abrv = Abrv ?? string.Empty,
            AppAbrv = string.Empty,
            Code = Code?.Trim() ?? string.Empty,
            TranslationDescription = TranslationDescription?.Trim(),
            TranslationName = TranslationName?.Trim(),
            TimeStamp = Convert.FromBase64String(TimeStamp ?? string.Empty),
            FiSlug = FiSlug,
            LookupTypeName = LookupTypeName,
        };
        return result;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (!string.IsNullOrWhiteSpace(LanguageCode))
        {
            // A foreign language was set on the model, so we should validate those fields.
            if (string.IsNullOrWhiteSpace(TranslationName))
            {
                yield return new ValidationResult($"{nameof(TranslationName)} is required",
                    [nameof(TranslationName)]);
            }

            if (string.IsNullOrWhiteSpace(TranslationDescription))
            {
                yield return new ValidationResult($"{nameof(TranslationDescription)} is required",
                    [nameof(TranslationDescription)]);
            }
        }
    }

    public void CheckRoute(string fiSlug, string lookupTypeName, string lookupItemSlug)
    {
        ThrowIfUnequal(Slug, lookupItemSlug, "Potential XSS problem.");
        if (lookupItemSlug == ItemConstants.CreateItemTag && string.IsNullOrWhiteSpace(TimeStamp))
        {
            // Timestamp comes in NULL even if the page held an empty string.  It must be non-null to pass modelstate checks.
            TimeStamp = string.Empty;
        }
        CheckRoute(fiSlug, lookupTypeName);
    }

    public void CheckRoute(string fiSlug, string lookupTypeName)
    {
        ThrowIfUnequal(FiSlug, fiSlug, "Potential XSS problem.");
        ThrowIfUnequal(LookupTypeName, lookupTypeName, "Potential XSS problem.");
    }

    public override string ToString() => $"{GetType().Name} {JsonSerializer.Serialize(this)}";
}
