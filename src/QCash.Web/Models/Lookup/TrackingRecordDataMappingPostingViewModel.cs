using System.ComponentModel.DataAnnotations;
using QCash.Service.Models.DecisionEngine;
using static QCash.Service.Utilities.Extensions.ExceptionExtensions;

namespace QCash.Web.Models.Lookup;

// This is a viewmodel, so the properties we want to receive from a post cannot be private.
// Suppressing incorrect warnings with the lines below.
// ReSharper disable MemberCanBePrivate.Global
// ReSharper disable MemberCanBeProtected.Global
// ReSharper disable PropertyCanBeMadeInitOnly.Global
public class TrackingRecordDataMappingPostingViewModel
{
    public readonly string LookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping);
    public required string FiSlug { get; set; }
    public required Guid Id { get; set; }

    [Display(Name = "Name"), MaxLength(250)]
    public required string? Name { get; set; }
    public required string? PageTitle { get; set; }
    public required string Slug { get; set; }

    [Display(Name = "Description"), MaxLength(500)]
    public string? Description { get; set; }

    [Display(Name = "Tracking Record Type"), MaxLength(50)]
    public required string? TrackingRecordType { get; set; }

    [Display(Name = "Tracking Record Field"), MaxLength(500)]
    public required string? TrackingRecordField { get; set; }

    [Display(Name = "Tracking Record Level")]
    public Enums.TrackingRecordLevelPull TrackingRecordLevel { get; set; }

    [Display(Name = "Tracking Record Pull Option")]
    public Enums.TrackingRecordPullOption TrackingRecordPullOption { get; set; }

    public required string? TimeStamp { get; set; }


    public void CheckRoute(string fiSlug, string lookupTypeName, string lookupItemSlug)
    {
        ThrowIfUnequal(FiSlug, fiSlug, "Potential XSS problem.");
        ThrowIfUnequal(LookupTypeName, lookupTypeName, "Potential XSS problem.");
        ThrowIfUnequal(Slug, lookupItemSlug, "Potential XSS problem.");
    }

    public DETrackingRecordDataMappingDto ToDto()
    {
        var dto = new DETrackingRecordDataMappingDto()
        {
            Id = Id,
            Name = Name?.Trim() ?? string.Empty,
            Slug = Slug,
            Description = Description?.Trim() ?? string.Empty,
            Code = string.Empty,
            AppAbrv = string.Empty,
            FiSlug = FiSlug,
            TrackingRecordType = TrackingRecordType?.Trim() ?? string.Empty,
            TrackingRecordField = TrackingRecordField?.Trim() ?? string.Empty,
            TrackingRecordLevel = $"{TrackingRecordLevel}",
            TrackingRecordPullOption = $"{TrackingRecordPullOption}",
            TimeStamp = Convert.FromBase64String(TimeStamp ?? ""),
            LookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping),
        };
        return dto;
    }
}
