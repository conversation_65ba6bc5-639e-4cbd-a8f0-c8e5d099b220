using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;

namespace QCash.Web.Models.Lookup;

public class EditPageViewModel : EditPostingViewModel
{
    public string LookupItemSlug { get; init; } = null!;
    public required string NameHeader { get; init; } = null!;
    public Enums.DecisionEngineLookupTypeEnum LookupTypeEnum { get; init; } = Enums.DecisionEngineLookupTypeEnum.Unknown;
    public LanguageDto? ForeignLanguage { get; init; } = null!;
    public bool SupportsForeignLanguage { get; init; } = false;
    // Metadata:
    public string PageTitle { get; set; } = "";
    public ToastModel? SubmissionNotifications { get; set; }
    public int? NameFieldMaxLength { get; set; } = null;

    public static EditPageViewModel FromDto(EFPocoLookupDto dto)
    {
        var result = new EditPageViewModel
        {
            FiSlug = dto.FiSlug,
            Id = dto.Id,
            Name = dto.Name,
            Slug = dto.Slug,
            IsDeleted = dto.IsDeleted,
            Description = dto.Description,
            Abrv = dto.Abrv,
            Code = dto.Code,
            LanguageCode = dto.ForeignLanguage?.LanguageCode,
            LanguageCount = dto.ForeignLanguage == null ? 0 : 1,
            TranslationName = dto.TranslationName ?? "",
            TranslationDescription = dto.TranslationDescription ?? "",
            LookupTypeName = dto.LookupTypeEnum.ToString(),
            NameHeader = dto.NameHeader,
            ForeignLanguage = dto.ForeignLanguage,
            SubmissionNotifications = null,
            LookupTypeEnum = dto.LookupTypeEnum,
            SupportsForeignLanguage = dto.SupportsForeignLanguage,
            TimeStamp = Convert.ToBase64String(dto.TimeStamp),
        };
        return result;

    }

}
