using Microsoft.AspNetCore.Authentication;
using System.ComponentModel.DataAnnotations;

namespace QCash.Web.Models;

public class LoginViewModel
{
    [Required]
    [EmailAddress]
    [Display(Name = "Email")]
    public string Email { get; init; } = string.Empty;

    [Required]
    [DataType(DataType.Password)]
    [Display(Name = "Password")]
    public string Password { get; init; } = string.Empty;

    public string? ReturnUrl { get; init; }

    public ICollection<AuthenticationScheme> ExternalLogins { get; set; } = [];
}
