using System;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using System.Text.RegularExpressions;

namespace QCash.Web.Models.Validation
{
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = false)]
    public class PhoneUsAttribute : ValidationAttribute, IClientModelValidator
    {
        private const string DefaultErrorMessage = "Please enter a valid US phone number.";
        // Efficient regex for US phone numbers, avoids nested quantifiers, with timeout
        private static readonly Regex UsPhoneRegex = new(
            @"^(\+1\s?)?(\(\d{3}\)|\d{3})[\s.-]?\d{3}[\s.-]?\d{4}$",
            RegexOptions.Compiled | RegexOptions.ExplicitCapture,
            TimeSpan.FromMilliseconds(250)
        );

        public PhoneUsAttribute() : base(DefaultErrorMessage) { }

        protected override ValidationResult IsValid(object? value, ValidationContext validationContext)
        {
            if (value is null)
                return ValidationResult.Success!;
            var str = value as string;
            if (string.IsNullOrWhiteSpace(str))
                return ValidationResult.Success!;
            // Limit input length to 32 for safety
            if (str.Length > 32)
                return new ValidationResult(DefaultErrorMessage);
            if (UsPhoneRegex.IsMatch(str))
                return ValidationResult.Success!;
            return new ValidationResult(DefaultErrorMessage);
        }

        public void AddValidation(ClientModelValidationContext context)
        {
            MergeAttribute(context.Attributes, "data-val", "true");
            MergeAttribute(context.Attributes, "data-val-phoneus", DefaultErrorMessage);
        }

        private static bool MergeAttribute(System.Collections.Generic.IDictionary<string, string> attributes, string key, string value)
        {
            if (attributes.ContainsKey(key))
                return false;
            attributes.Add(key, value);
            return true;
        }
    }
}
