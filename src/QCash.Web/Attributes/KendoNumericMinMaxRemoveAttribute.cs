using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace QCash.Web.Attributes;

/// <summary>
/// Kendo Numeric Text Box Controls with this attribute and a Range attribute will remove the min/max values from
/// the kendo control allowing a Range validator to do its thing without <PERSON><PERSON> "adjusting" then entered number
/// to be within that range.
/// </summary>
[AttributeUsage(validOn: AttributeTargets.Property)]
[ExcludeFromCodeCoverage]
public class KendoNumericMinMaxRemoveAttribute : ValidationAttribute, IClientModelValidator
{
    public void AddValidation(ClientModelValidationContext context)
    {
        MergeAttribute(context.Attributes, "data-kendominmax", "true");
    }

    public override bool IsValid(object? value)
    {
        return true;
    }

    private static void MergeAttribute(IDictionary<string, string> attributes, string key, string value)
    {
        if (attributes.ContainsKey(key))
        {
            return;
        }

        attributes.Add(key, value);
    }
}