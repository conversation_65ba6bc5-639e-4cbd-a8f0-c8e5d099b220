@page "/home/<USER>"
@using QCash.Seed
@using QCash.Web.Areas.Seed.Controllers
@using QCash.Web.Extensions
@model QCash.Web.Pages.TenantSelectPage
@addTagHelper *, Kendo.Mvc
@inject SeedProcess SeedProcess


<div class="cards-container">
    <div class="k-card">
        <div class="k-card-header">
            <h5 class="k-card-title">Select a financial institution:</h5>
            <h6 class="k-card-subtitle">Probably some words go here...</h6>
        </div>
        <div class="k-card-body">
            @foreach (var item in Model.PageData.Tenants)
            {
                <div style="margin-top:20px">
                    @{ var homeUrl = $"/{item.FiSlug}";}
                    <a href="@homeUrl">@item.Name</a>
                </div>
            }
            @if (await SeedProcess.CanSeedInitialTestDataAsync())
            {
                <div style="margin-top:20px">
                    <a href="@(Url.Action<SeedController>(c => c.Index, new { area="seed" }))">Seed Test Tenants</a>
                </div>
            }
        </div>
        <div class="k-card-footer">
            <a class="k-button k-button-flat-base k-button-flat k-button-md k-rounded-md k-icon-button"><span class="facebook"></span></a>
            <a class="k-button k-button-flat-base k-button-flat k-button-md k-rounded-md k-icon-button"><span class="pinterest"></span></a>
            <a class="k-button k-button-flat-base k-button-flat k-button-md k-rounded-md k-icon-button"><span class="twitter"></span></a>
        </div>
    </div>
</div>



<style>
    .cards-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .k-card {
        width: 700px;
        margin: 5%;
    }

    .k-card-footer {
        text-align: center;
    }
</style>