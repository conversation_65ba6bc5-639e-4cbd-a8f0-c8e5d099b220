using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using QCash.Service.Services.Interfaces;
using QCash.Web.Models;
using QCash.Web.Utils;

namespace QCash.Web.Pages;

[ServiceFilter(typeof(GlobalTenantAttribute))]
public class TenantSelectPage(ITenantService tenantService) : PageModel
{
    public TenantSelectPageViewModel PageData { get; set; } = null!;

    public async Task OnGetAsync()
    {
        var dto = await tenantService.GetPageDataAsync();
        var viewModel = TenantSelectPageViewModel.FromDto(dto);
        PageData = viewModel;
    }
}
