{"AllowedHosts": "*", "AzureAd": {"IsEnabled": true, "Instance": "https://login.microsoftonline.com/", "Domain": "Alloyacorp.onmicrosoft.com", "Comment": "This is pointing to QC6 WebRole-App-Dev", "TenantId": "da9eee20-aa0f-4ad7-be80-9b5e9558b793", "ClientId": "d539484d-254b-4b4c-8d0f-2bf89f661665", "CallbackPath": "/", "SignedOutCallbackPath ": "/signout-callback-oidc"}, "AzureKeyVault": {"Enabled": false, "ReloadInterval": "00:05:00", "KeyVaultUrl": ""}, "BlizardOptions": {"TokenManagerKeys": "YjhjNDEwNjhhN2QyMzdlYTUxNmIyZWFiNzY4NWIwODg=;mZhZGQ2MmFhMjFlNTZiOD;lNGY3ODhkMmJlMTR"}, "ConnectionStrings": {"QCashConnection": "Server=127.0.0.1,1536;Initial Catalog=QCash;User Id=sa;Password=*********;TrustServerCertificate=true"}, "Email": {"DeliveryMethod": "SpecifiedPickupDirectory", "EmailSubjectSuffix": " (Development)", "EnableSSL": false, "FromAddress": "<EMAIL>", "Host": "localhost", "OverrideEmailRecipient": "", "Password": "", "PickupDirectory": "C:\\temp\\qcash7\\pickup", "Port": "25", "TemplateCacheMinutes": "1", "UseDefaultCredentials": true, "Username": ""}, "IpRateLimiting": {"_Comment": "see documentation here: https://github.com/stefanprodan/AspNetCoreRateLimit/wiki/IpRateLimitMiddleware#setup", "EnableEndpointRateLimiting": true, "StackBlockedRequests": false, "HttpStatusCode": 429, "GeneralRules": [{"Endpoint": "*", "Period": "10s", "Limit": 40}, {"Endpoint": "*:/*/Account/*", "Period": "10s", "Limit": 10}]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "RuntimeSetting": {"Enabled": true, "ReloadInterval": "00:05:00"}, "QCashApplication": {"Version": "7.0", "Build": {"BuildNumber": "", "SourceBranchName": ""}, "Release": {"ReleaseName": "", "DeploymentStartDate": "", "ReleaseDescription": ""}, "DisplayDiagnosticPages": true, "EnableHiddenPages": true, "ShowExceptionStackTrace": false, "WizardURL": "https://dev61.q-cash.com"}, "QCashContext": {"SlowQueryMilliseconds": 0, "SlowQueryLogging": true, "QueryTimeout": 31}}