<Project Sdk="Microsoft.NET.Sdk.Web">
    <PropertyGroup>
        <AnalysisLevel>latest</AnalysisLevel>
    </PropertyGroup>

    <ItemGroup>
      <Compile Remove="Views\Shared\Components\**" />
      <Content Remove="Views\Shared\Components\**" />
      <EmbeddedResource Remove="Views\Shared\Components\**" />
      <None Remove="Views\Shared\Components\**" />
      <Compile Remove="Controllers\SeedController.cs" />
      <Compile Remove="Controllers\CheckingAccountTypeController.cs" />
      <Compile Remove="Controllers\SavingAccountTypeController.cs" />
      <Compile Remove="Models\DecisionEngine\CheckingAccountTypeListItemViewModel.cs" />
      <Compile Remove="Models\SavingAccountTypeEditPageViewModel.cs" />
      <Compile Remove="Models\DecisionEngine\SavingAccountTypeListItemViewModel.cs" />
      <Compile Remove="Models\SavingAccountTypeEditPageViewModel.cs" />
      <Compile Remove="Controllers\CheckingAccountTypeController.cs" />
      <Compile Remove="Controllers\SavingAccountTypeController.cs" />
      <Compile Remove="Models\DecisionEngine\CheckingAccountTypeListItemViewModel.cs" />
      <Compile Remove="Models\SavingAccountTypeEditPageViewModel.cs" />
      <Content Remove="Areas\FIConfiguration\Views\FiInfo\Index.cshtml" />
      <Compile Remove="Areas\Reports\Controllers\GeneratedReportsController.cs" />
      <Compile Remove="Areas\Reports\Models\GeneratedReportsViewModel.cs" />
      <Content Update="appsettings.dev.json">
        <DependentUpon>appsettings.json</DependentUpon>
      </Content>
      <Compile Remove="Identity\CustomSignInManager.cs" />
      <Content Update="log4net.dev.config">
        <DependentUpon>log4net.config</DependentUpon>
      </Content>
      <Content Update="appsettings.Production.json">
        <DependentUpon>appsettings.json</DependentUpon>
      </Content>
      <Content Update="appsettings.QA.json">
        <DependentUpon>appsettings.json</DependentUpon>
      </Content>
      <Content Update="appsettings.Train.json">
        <DependentUpon>appsettings.json</DependentUpon>
      </Content>
      <Content Update="appsettings.Stage.json">
        <DependentUpon>appsettings.json</DependentUpon>
      </Content>
      <Content Update="log4net.Development.config">
        <DependentUpon>log4net.config</DependentUpon>
      </Content>
      <Compile Remove="RuntimeSetting\RuntimeSettingSettings.cs" />
      <Compile Remove="HealthChecks\DistributedMemoryCacheHealthCheck.cs" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
      <PackageReference Include="AspNetCore.HealthChecks.AzureApplicationInsights" Version="9.0.0" />
      <PackageReference Include="AspNetCore.HealthChecks.AzureKeyVault" Version="9.0.0" />
      <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="9.0.0" />
      <PackageReference Include="AspNetCore.HealthChecks.System" Version="9.0.0" />
      <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
      <PackageReference Include="AspNetCore.HealthChecks.Uris" Version="9.0.0" />
      <PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
      <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.3.2" />
      <PackageReference Include="FoolProof.Core" Version="1.3.1" />
      <PackageReference Include="log4net" Version="3.1.0" />
      <PackageReference Include="Log4Net.Appenders" Version="1.0.5" />
      <PackageReference Include="Meziantou.Analyzer" Version="2.0.201">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
      <PackageReference Include="Microsoft.ApplicationInsights.Log4NetAppender" Version="2.23.0" />
      <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.14" />
      <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.8" />
      <PackageReference Include="Microsoft.AspNetCore.SystemWebAdapters" Version="1.3.0" />
      <PackageReference Include="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="3.3.4">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="9.0.0">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.Extensions.Caching.SqlServer" Version="9.0.5" />
      <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.16" />
      <PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="8.0.0" />
      <PackageReference Include="Microsoft.Identity.Web.UI" Version="3.8.3" />
      <PackageReference Include="Microsoft.Web.Xdt" Version="3.1.0" />
      <PackageReference Include="Telerik.UI.for.AspNet.Core" Version="2024.3.806" />
      <PackageReference Include="Phoenix.Analyzer" Version="1.6.1">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
    </ItemGroup>

  <ItemGroup>
    <Content Include="Reports\TelerikDesigns\*.trdx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
      <ProjectReference Include="..\QCash.Seed\QCash.Seed.csproj" />
      <ProjectReference Include="..\QCash.Data\QCash.Data.csproj" />
      <ProjectReference Include="..\QCash.Service\QCash.Service.csproj" />
      <ProjectReference Include="..\QCash.Utils\QCash.Utils.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Content Update="appsettings.Development.json">
        <DependentUpon>appsettings.json</DependentUpon>
      </Content>
      <Content Update="log4net.Production.config">
        <DependentUpon>log4net.config</DependentUpon>
      </Content>
      <Content Update="log4net.QA.config">
        <DependentUpon>log4net.config</DependentUpon>
      </Content>
      <Content Update="log4net.Train.config">
        <DependentUpon>log4net.config</DependentUpon>
      </Content>
      <Content Update="log4net.Stage.config">
        <DependentUpon>log4net.config</DependentUpon>
      </Content>
      <Content Update="wwwroot\images\QCashLogo.png">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
      <Content Update="log4net.config">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <_ContentIncludedByDefault Remove="Views\Products\Index.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Product\Create.cshtml" />
      <_ContentIncludedByDefault Remove="wwwroot\lib\jquery-validation\LICENSE.md" />
      <_ContentIncludedByDefault Remove="Pages\Shared\Components\ProductDetailsGeneralTab\Default.cshtml" />
      <_ContentIncludedByDefault Remove="Pages\LoanApplication\ApplicationSettingsPage.cshtml" />
      <_ContentIncludedByDefault Remove="Pages\FIConfiguration\UserManagement\UserDetailsPage.cshtml" />
      <_ContentIncludedByDefault Remove="Views\SavingAccountType\Edit.cshtml" />
      <_ContentIncludedByDefault Remove="Views\SavingAccountType\List.cshtml" />
    </ItemGroup>

  <ItemGroup>
    <AdditionalFiles Include="..\..\BannedSymbols.txt" />
    <AdditionalFiles Include="Areas\ModelManager\Views\ModelDashboard\List.cshtml" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Areas\Reports\Views\GeneratedReports\" />
    <Folder Include="Models\Infrastructure\ApplicationContext\" />
  </ItemGroup>

  <PropertyGroup>
    <MvcBuildViews>true</MvcBuildViews>
    <UserSecretsId>472e0beb-f442-4dd3-9517-de04277e3353</UserSecretsId>
  </PropertyGroup>

</Project>
