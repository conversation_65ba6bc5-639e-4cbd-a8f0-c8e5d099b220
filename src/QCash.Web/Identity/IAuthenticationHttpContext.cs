using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;

namespace QCash.Web.Identity;

public interface IAuthenticationHttpContext
{
    Task<AuthenticateResult> AuthenticateAsync();
    Task<AuthenticateResult> AuthenticateAsync(string? scheme);
    Task ChallengeAsync();
    Task ChallengeAsync(AuthenticationProperties? properties);
    Task ChallengeAsync(string? scheme);
    Task ChallengeAsync(string? scheme, AuthenticationProperties? properties);
    Task ForbidAsync();
    Task ForbidAsync(AuthenticationProperties? properties);
    Task ForbidAsync(string? scheme);
    Task ForbidAsync(string? scheme, AuthenticationProperties? properties);
    Task<string?> GetTokenAsync(string tokenName);
    Task<string?> GetTokenAsync(string? scheme, string tokenName);
    Task SignInAsync(ClaimsPrincipal principal);
    Task SignInAsync(ClaimsPrincipal principal, AuthenticationProperties? properties);
    Task SignInAsync(string? scheme, ClaimsPrincipal principal);
    Task SignInAsync(string? scheme, ClaimsPrincipal principal, AuthenticationProperties? properties);
    Task SignOutAsync();
    Task SignOutAsync(AuthenticationProperties? properties);
    Task SignOutAsync(string? scheme);
    Task SignOutAsync(string? scheme, AuthenticationProperties? properties);
}