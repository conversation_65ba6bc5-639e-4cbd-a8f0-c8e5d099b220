using System.Linq.Expressions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using QCash.Service.Models.Core;
using QCash.Web.Models;
using QCash.Web.Models.General;
using QCash.Web.Models.Lookup;

namespace QCash.Web.Extensions;

// ReSharper disable once CheckNamespace
//namespace System.Web.Mvc;

public static class UrlExtensions
{
    // Generic extensions
    // Usage : <a href="@(Url.Action<MyController>(x=>x.MyActionNoVars, new {myroutevalue = 1}))"></a>
    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    // Usage : <a href="@(Url.Action<MyController,vartype1>(x=>x.MyActionWithOneVar, new {myroutevalue = 1}))"></a>
    public static string Action<TController, TParam1>(this IUrlHelper helper, Expression<Func<TController, Func<TParam1, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>(expression, routeValues);

    // Usage : <a href="@(Url.Action<MyController,vartype1,vartype2>(x=>x.MyActionWithTwoVars, new {myroutevalue = 1}))"></a>
    public static string Action<TController, TParam1, TParam2>(this IUrlHelper helper, Expression<Func<TController, Func<TParam1, TParam2, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>(expression, routeValues);

    // Usage : <a href="@(Url.Action<MyController,vartype1,vartype2,vartype3>(x=>x.MyActionWithTwoVars, new {myroutevalue = 1}))"></a>
    public static string Action<TController, TParam1, TParam2, TParam3>(this IUrlHelper helper, Expression<Func<TController, Func<TParam1, TParam2, TParam3, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>(expression, routeValues);

    // Generic extensions that return Task<IActionResult>:
    // Usage : <a href="@(Url.Action<MyController>(x=>x.MyActionNoVars, new {myroutevalue = 1}))"></a>
    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    // Usage : <a href="@(Url.Action<MyController,vartype1>(x=>x.MyActionWithOneVar, new {myroutevalue = 1}))"></a>
    public static string Action<TController, TParam1>(this IUrlHelper helper, Expression<Func<TController, Func<TParam1, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>(expression, routeValues);

    // Usage : <a href="@(Url.Action<MyController,vartype1,vartype2>(x=>x.MyActionWithTwoVars, new {myroutevalue = 1}))"></a>
    public static string Action<TController, TParam1, TParam2>(this IUrlHelper helper, Expression<Func<TController, Func<TParam1, TParam2, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>(expression, routeValues);

    // Usage : <a href="@(Url.Action<MyController,vartype1,vartype2,vartype3>(x=>x.MyActionWithTwoVars, new {myroutevalue = 1}))"></a>
    public static string Action<TController, TParam1, TParam2, TParam3>(this IUrlHelper helper, Expression<Func<TController, Func<TParam1, TParam2, TParam3, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>(expression, routeValues);

    // Implementations with concrete parameters are below.  These allow us to call methods without specifying every parameter type for the ControllerBase method

    // Usage : <a href="@(Url.Action<MyController>(x=>x.MyActionWithOneInt, new {myroutevalue = 1}))"></a>
    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<int, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string?, ToastMsgTypeEnum?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<Enums.WhiteListIpTypeEnum, string?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, Enums.WhiteListIpTypeEnum, DeleteItemViewModel, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, string, string?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, Guid, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<DataSourceRequest, string, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, string?, ToastMsgTypeEnum?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<DataSourceRequest, Enums.WhiteListIpTypeEnum, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, DeleteLookupViewModel, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, DeleteItemViewModel, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, string?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, string, DeleteLookupViewModel, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, string, Guid?, string?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, string, string?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, string, Guid, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<DataSourceRequest, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string?, ToastMsgTypeEnum?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, string, string?, ToastMsgTypeEnum?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<IEnumerable<IFormFile>?, string?, string?, Guid?, string?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<Enums.LoanApplicationDocumentType, string?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, string, bool, string, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<Guid?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<string, string, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    public static string Action<TController>(this IUrlHelper helper, Expression<Func<TController, Func<Guid?, string?, ToastMsgTypeEnum?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => helper.Action<TController>((LambdaExpression)expression, routeValues);

    //Support functions
    private static string Action<TController>(this IUrlHelper helper, LambdaExpression expression, object? routeValues = null) where TController : ControllerBase
    {
        var actionName = ControllerExtensions.GetControllerActionName(expression);
        var controllerName = ControllerExtensions.GetControllerName<TController>();
        var action = helper.Action(actionName, controllerName, routeValues);
        if (string.IsNullOrWhiteSpace(action))
        {
            throw new Exception("Invalid action");
        }
        return action;
    }
}
