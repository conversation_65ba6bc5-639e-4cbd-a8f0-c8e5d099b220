using System.Linq.Expressions;
using Microsoft.AspNetCore.Mvc;
using QCash.Service.Models.Core;
using QCash.Web.Models;

namespace QCash.Web.Extensions;

public static class RedirectToActionExtensions
{
    // Generic extensions

    public static RedirectToActionResult RedirectToAction<TController, TParam1>(
        this Controller controller,
        Expression<Func<TController, Func<TParam1, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    // Implementations with concrete parameters are below.  These allow us to call methods without specifying every parameter type for the ControllerBase method

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<string?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<string?, ToastMsgTypeEnum?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<string?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<string?, ToastMsgTypeEnum?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<string, string?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<string, string, string?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<Enums.WhiteListIpTypeEnum, string?, ActionResult?>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<Enums.WhiteListIpTypeEnum, string?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<string, string?, ToastMsgTypeEnum?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<string, string, string?, ToastMsgTypeEnum?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToAction<TController>(
        this Controller controller,
        Expression<Func<TController, Func<bool, string?, ToastMsgTypeEnum?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => controller.RedirectToActionHelper<TController>(expression, routeValues);

    public static RedirectToActionResult RedirectToLocalAction<TController>(
        this TController controller,
        Expression<Func<TController, Func<string?, ToastMsgTypeEnum?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => (controller as Controller)!.RedirectToActionHelper(expression, routeValues);

    public static RedirectToActionResult RedirectToLocalAction<TController>(
        this TController controller,
        Expression<Func<TController, Func<Guid?, string?, ToastMsgTypeEnum?, IActionResult>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => (controller as Controller)!.RedirectToActionHelper(expression, routeValues);

    public static RedirectToActionResult RedirectToLocalAction<TController>(
        this TController controller,
        Expression<Func<TController, Func<Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => (controller as Controller)!.RedirectToActionHelper(expression, routeValues);

    public static RedirectToActionResult RedirectToLocalAction<TController>(
        this TController controller,
        Expression<Func<TController, Func<string, string, string?, ToastMsgTypeEnum?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => (controller as Controller)!.RedirectToActionHelper(expression, routeValues);

    public static RedirectToActionResult RedirectToLocalAction<TController>(
        this TController controller,
        Expression<Func<TController, Func<string, string?, ToastMsgTypeEnum?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => (controller as Controller)!.RedirectToActionHelper(expression, routeValues);

    public static RedirectToActionResult RedirectToLocalAction<TController>(
        this TController controller,
        Expression<Func<TController, Func<bool, Guid?, string?, ToastMsgTypeEnum?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => (controller as Controller)!.RedirectToActionHelper(expression, routeValues);

    public static RedirectToActionResult RedirectToLocalAction<TController>(
        this TController controller,
        Expression<Func<TController, Func<Guid, string?, ToastMsgTypeEnum?, Task<IActionResult>>>> expression,
        object? routeValues = null) where TController : ControllerBase
        => (controller as Controller)!.RedirectToActionHelper(expression, routeValues);

    //Support functions
    private static RedirectToActionResult RedirectToActionHelper<TController>(
        this Controller controller,
        LambdaExpression expression,
        object? routeValues = null)
        where TController : ControllerBase
    {
        var controllerName = ControllerExtensions.GetControllerName<TController>();
        var actionName = ControllerExtensions.GetControllerActionName(expression);
        return controller.RedirectToAction(actionName, controllerName, routeValues);
    }

    private static RedirectToActionResult RedirectToActionHelper(
        this Controller controller,
        LambdaExpression expression,
        object? routeValues = null)
    {
        var controllerName = ControllerExtensions.GetControllerName(controller);
        var actionName = ControllerExtensions.GetControllerActionName(expression);
        return controller.RedirectToAction(actionName, controllerName, routeValues);
    }
}
