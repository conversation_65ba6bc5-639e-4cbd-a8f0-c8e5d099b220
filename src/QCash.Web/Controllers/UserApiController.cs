using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Services.User.Interfaces;
using QCash.Web.Models.FIConfiguration.UserManagement;

namespace QCash.Web.Controllers;

[ApiController]
public class UserApiController(
    IUserService userService
) : Controller
{
    [HttpGet]
    [Route("{fiSlug}/api/[controller]")]
    public async Task<IActionResult> GetAsync([DataSourceRequest] DataSourceRequest request, string fiSlug)
    {
        var result = await userService.GetUsersQueryableAsync(fiSlug, request);

        result.Data = result.Data
            .Cast<UserListPageItemDTO>()
            .Select(UserListItemViewModel.FromDTO);

        return Json(result);
    }
}
