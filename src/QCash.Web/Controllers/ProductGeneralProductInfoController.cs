using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Extensions;
using QCash.Data.Models.Exceptions;
using QCash.Service.Core;
using QCash.Service.Models.Product;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Product.Interfaces;
using QCash.Web.Models.Products;
using QCash.Web.Extensions;
using QCash.Web.Models;

namespace QCash.Web.Controllers;

public class ProductGeneralProductInfoController(
    ILogger<ProductGeneralProductInfoController> logger,
    IProductService productService,
    IErrorNotificationService errorNotificationService,
    IUnitOfWork unitOfWork
) : Controller
{
    public async Task<IActionResult> ListAsync(string? search = "")
    {
        var model = await GetProductsViewModelAsync(search);
        return View(model);
    }

    [HttpGet]
    public async Task<IActionResult> FilterProductsAsync(string? search = "")
    {
        var model = await GetProductsViewModelAsync(search);
        return PartialView("_ProductListPartialView", model);
    }

    private async Task<ProductsPageViewModel> GetProductsViewModelAsync(string? searchQuery)
    {
        var slug = HttpContext.GetTenant().Slug;
        var dto = await productService.GetListAsync(slug, searchQuery);
        return ProductsPageViewModel.FromDto(dto);
    }

    public async Task<IActionResult> CreateAsync()
    {
        var dto = await productService.GetCreatePageModelAsync(HttpContext.GetTenant().Slug, HttpContext.GetTenant().Id);
        var model = ProductCreatePageViewModel.FromDto(dto);
        return View(model);
    }

    private async Task<ActionResult> PrepCreatePageAsync(ProductCreatePageDto productDto, string? message = null, ToastMsgTypeEnum? msgType = null)
    {
        await productService.PopulateCreatePageDropdownsAsync(productDto);
        var model = ProductCreatePageViewModel.FromDto(productDto);

        if (!string.IsNullOrWhiteSpace(message))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(message),
                MsgType = msgType ?? ToastMsgTypeEnum.Success,
            };
        }
        return View(model);
    }

    [HttpPost]
    public async Task<IActionResult> CreateAsync(ProductCreatePageViewModel model)
    {
        var productDto = model.ToDto();
        if (!ModelState.IsValid)
        {
            return await PrepCreatePageAsync(productDto);
        }

        var createResult = await productService.CreateNewProductAsync(productDto);
        if (!createResult.IsSuccessful)
        {
            return await PrepCreatePageAsync(productDto, createResult.ErrorMessage, ToastMsgTypeEnum.Error);
        }
        try
        {
            await unitOfWork.CommitAsync(HttpContext.RequestAborted);

            return RedirectToAction("Edit", new
            {
                FiSlug = HttpContext.GetTenant().Slug,
                productSlug = createResult.ProductSlug,
                msg = "Product Created Successfully",
                msgType = ToastMsgTypeEnum.Success,
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error attempting to create a product.  Name={ProductName}", model.Name);
            return await PrepCreatePageAsync(productDto, "Error while creating product", ToastMsgTypeEnum.Error);
        }
    }

    [HttpPost]
    public async Task<PartialViewResult> GetLoanCategoryPartialAsync(string loanTypeId, Guid oldSelectedCategoryId)
    {
        var dto = await productService.GetCreatePageModelOnLoanTypeChangeAsync(HttpContext.GetTenant().Slug, Guid.Parse(loanTypeId), oldSelectedCategoryId);
        var viewModel = ProductCategoryPartialViewModel.FromDto(dto);
        return PartialView("_ProductInfoLoanCategory", viewModel);
    }

    private void PrepareGeneralEditPage(ProductInfoPageViewModel model, string? msg = null, ToastMsgTypeEnum? msgType = null)
    {
        if (!string.IsNullOrWhiteSpace(msg))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Error,
            };
        }
        model.NavHeader = ProductMainNavHeaderViewModel.FromDto(model.FiSlug, Constants.ProductMainHeaderTabEnum.GeneralTab, model.Slug);
        model.NavHeaderSecondary = ProductGeneralNavHeaderViewModel.FromDto(model.FiSlug, Constants.ProductGeneralHeaderTabEnum.ProductInfoTab, model.Slug);
    }

    [HttpGet, Route("{FiSlug}/Product/{productSlug}/General/ProductInfo")]
    public async Task<IActionResult> EditAsync([FromRoute] string productSlug, string? msg, ToastMsgTypeEnum? msgType = null)
    {
        var pageDto = await productService.GetProductInfoPageDataAsync(HttpContext.GetTenant().Slug, productSlug);
        if (pageDto == null)
        {
            throw new ObjectNotFoundException();
        }
        var model = ProductInfoPageViewModel.FromDto(pageDto);
        PrepareGeneralEditPage(model, msg, msgType);
        return View(model);
    }

    [HttpPost, Route("{FiSlug}/Product/{productSlug}/General/ProductInfo")]
    public async Task<IActionResult> EditAsync([Bind] ProductInfoPagePostingViewModel userSubmittedModel,
        [FromRoute] string fiSlug, [FromRoute] string productSlug)
    {
        userSubmittedModel.CheckRoute(fiSlug, productSlug);
        var dto = userSubmittedModel.ToDto();
        if (!ModelState.IsValid)
        {
            return await GetPageForPostbackAsync(userSubmittedModel, dto);
        }

        try
        {
            var saveResult = await productService.SaveGeneralProductInfoTabAsync(dto);
            if (saveResult.IsSuccessful)
            {
                await unitOfWork.CommitAsync();
                return this.RedirectToAction<ProductGeneralProductInfoController>(a => a.EditAsync, new
                {
                    area = "",
                    msg = errorNotificationService.GetSuccessfulDatabaseActionMessage(productService.RecordName,
                        EntityState.Modified),
                    msgType = ToastMsgTypeEnum.Success,
                });
            }
            return await GetPageForPostbackAsync(userSubmittedModel, dto, saveResult.ErrorMessage, ToastMsgTypeEnum.Error);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error attempting to save a product.  Slug={ProductSlug}", productSlug);
            return await GetPageForPostbackAsync(userSubmittedModel, dto, "Error while saving product", ToastMsgTypeEnum.Error);
        }
    }

    private async Task<IActionResult> GetPageForPostbackAsync(ProductInfoPagePostingViewModel userSubmittedModel, ProductInfoTabDto dto, string? msg = null, ToastMsgTypeEnum? msgType = null)
    {
        await productService.PopulateMetadataForGeneralProductInfoTabAsync(HttpContext.GetTenant().Slug, dto);
        userSubmittedModel.OverlayPageMetadata(dto);
        var model = ProductInfoPageViewModel.FromDto(dto);
        PrepareGeneralEditPage(model, msg, msgType);
        return View(model);
    }

    [HttpPost, Route("{FiSlug}/[controller]/{productSlug}/SetActive")]
    public async Task<IActionResult> SetActiveAsync([FromRoute] string fiSlug, [FromRoute] string productSlug,
        [FromQuery] bool value, [FromQuery] string timeStamp)
    {
        var result = await productService.SetActiveAsync(productSlug, value, timeStamp);
        string msg;
        ToastMsgTypeEnum msgType;
        if (result.IsSuccessful)
        {
            await unitOfWork.CommitAsync();
            msg = $"Product status changed successfully";
            msgType = ToastMsgTypeEnum.Success;
        }
        else
        {
            msg = $"Error while updating product status:  " + result.ErrorMessage;
            msgType = ToastMsgTypeEnum.Error;
        }
        return this.RedirectToAction<ProductGeneralProductInfoController>(a => a.EditAsync,
            new { FiSlug = HttpContext.GetTenant().Slug, productSlug, msg, msgType, }
        );
    }

}
