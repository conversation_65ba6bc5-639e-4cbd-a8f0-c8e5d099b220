using System.Diagnostics.CodeAnalysis;
using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Extensions;
using QCash.Data.Models;
using QCash.Service.Core;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.User.Interfaces;
using QCash.Web.Identity;
using QCash.Web.Models;
using QCash.Web.Models.Account;
using QCash.Web.Utils;

namespace QCash.Web.Controllers;

[ExcludeFromCodeCoverage]
public class AccountController(
    ILogger<AccountController> logger,
    IApplicationSignInManager signInManager,
    IUserService userService,
    IUnitOfWork unitOfWork,
    IErrorNotificationService errorNotificationService)
    : Controller
{

    [AllowAnonymous]
    [ServiceFilter(typeof(GlobalTenantAttribute))] // This needs to be global in case user tries to login to a different tenant
    public async Task<IActionResult> LoginAsync(string? returnUrl = null)
    {
        returnUrl ??= Url.Content($"~/{HttpContext.GetTenant().Slug}");

        // Clear the existing external cookie to ensure a clean login process
        await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);

        var externalLogins = (await signInManager.GetExternalAuthenticationSchemesAsync()).ToList();

        var model = new LoginViewModel
        {
            ReturnUrl = returnUrl,
            ExternalLogins = externalLogins,
        };

        return View(model);
    }

    [AllowAnonymous]
    [HttpPost]
    [ValidateAntiForgeryToken]
    [ServiceFilter(typeof(GlobalTenantAttribute))] // This needs to be global in case user tries to login to a different tenant
    public async Task<IActionResult> LoginAsync(LoginViewModel model, string? returnUrl = null)
    {
        returnUrl ??= Url.Content($"~/{HttpContext.GetTenant().Slug}");

        if (ModelState.IsValid)
        {
            try
            {
                var user = await userService.FindByEmailWithRolesAsync(model.Email);

                if (user != null && !user.IsDeleted)
                {
                    // Attempt to sign in with password
                    var result = await signInManager.PasswordSignInAsync(user, model.Password);
                    if (result.Succeeded)
                    {
                        logger.LogInformation("User {Email} logged in with password", model.Email);
                        return LocalRedirect(returnUrl);
                    }
                    if (result.IsLockedOut)
                    {
                        logger.LogWarning("User account locked out: {Email}", model.Email);
                        return RedirectToAction(nameof(AccessDenied));
                    }
                }

                // If we got this far, something failed
                ModelState.AddModelError(string.Empty, "Provided email or password is incorrect.");
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Error attempting to authenticate user");
                ModelState.AddModelError(string.Empty, "An unexpected error occurred.  Please try again later.");
            }
        }

        // If we got this far, something failed, redisplay form
        model.ExternalLogins = (await signInManager.GetExternalAuthenticationSchemesAsync()).ToList();
        return View(model);
    }

    [AllowAnonymous]
    [HttpPost]
    [ValidateAntiForgeryToken]
    [ServiceFilter(typeof(GlobalTenantAttribute))]
    public IActionResult AuthenticateExternal(string provider, string? returnUrl = null)
    {
        return Challenge(provider, returnUrl);
    }

    [Authorize(AuthenticationSchemes = "Identity.External")]//This should be IdentityConstants.ExternalScheme, but that is not a constant
    [HttpGet]
    [ServiceFilter(typeof(GlobalTenantAttribute))]
    public async Task<IActionResult> CallbackAsync(string? returnUrl = null, string? remoteError = null)
    {
        returnUrl ??= Url.Content($"~/{HttpContext.GetTenant().Slug}");

        if (remoteError != null)
        {
            logger.LogError("Error from external provider: {RemoteError}", remoteError);
            return RedirectToAction("AccessDenied");
        }

        var info = await signInManager.GetExternalLoginInfoAsync();
        if (info is null)
        {
            logger.LogError("Error loading external login information");
            return RedirectToAction("AccessDenied");
        }

        var firstName = GetFirstName(info.Principal) ?? "";
        var lastName = GetLastName(info.Principal) ?? "";

        var email = GetEmailAddress(info.Principal);
        var userName = GetUserName(info.Principal);

        if (string.IsNullOrEmpty(email))
        {
            logger.LogError("Error finding email address claim ({FirstName} {LastName})", firstName, lastName);
            return RedirectToAction("AccessDenied");
        }

        var user = await userService.FindByEmailWithRolesAsync(email) ?? userService.CreateUserForTenant(HttpContext.GetTenant().Id);

        // Always update user fields from provider
        user.UserName = userName;
        user.FirstName = firstName;
        user.LastName = lastName;
        user.Email = email;
        user.IsApproved = true;
        userService.UpdateUser(user);
        //await unitOfWork.CommitAsync(); // Commit happens in SignInAsync

        // Remove existing roles to avoid duplicates
        user.Roles.Clear();
        // Add roles from SSO claims (adapt claim type if needed)
        foreach (var r in info.Principal.Claims.Where(c => c.Type == ClaimTypes.Role))
        {
            user.Roles.Add(new AspNetRole { Name = r.Value });
        }
        //await unitOfWork.CommitAsync(); // Commit happens in SignInAsync

        await signInManager.SignInAsync(user, info.LoginProvider);

        logger.LogInformation("{Email} logged in with {InfoLoginProvider} provider", email, info.LoginProvider);
        return LocalRedirect(returnUrl);
    }

    [Authorize]
    [ServiceFilter(typeof(GlobalTenantAttribute))]
    public async Task<IActionResult> LogoutAsync(string? returnUrl = null)
    {
        var user = User.Identity?.Name ?? "unknown";

        await signInManager.SignOutAsync();

        logger.LogInformation("User {User} logged out", user);
        if (returnUrl != null)
        {
            return LocalRedirect(returnUrl);
        }

        return View();
    }

    [AllowAnonymous]
    [ServiceFilter(typeof(GlobalTenantAttribute))] // This needs to be global so we ignore tenant restrictions
    public IActionResult AccessDenied(string? returnUrl = null)
    {
        //the model is a string, so we need to name the View in order to stop routing confusion
        return View("AccessDenied", returnUrl ?? string.Empty);
    }

    [AllowAnonymous]
    [ServiceFilter(typeof(GlobalTenantAttribute))]
    public IActionResult Challenge(string provider, string? returnUrl = null)
    {
        // Request a redirect to the external login provider.
        var redirectUrl = Url.Action("Callback", new { returnUrl })!;
        var properties = signInManager.ConfigureExternalAuthenticationProperties(provider, redirectUrl);
        return new ChallengeResult(provider, properties);
    }

    [AllowAnonymous]
    [ServiceFilter(typeof(GlobalTenantAttribute))]
    public IActionResult KeepAlive()
    {
        return Json("Success");
    }

    [Authorize]
    public IActionResult ChangePassword([FromQuery] string? msg, [FromQuery] ToastMsgTypeEnum? msgType = null)
    {
        var model = new ChangePasswordViewModel();

        if (!string.IsNullOrWhiteSpace(msg))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Error,
            };
        }

        return View(model);
    }

    [Authorize]
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ChangePasswordAsync(ChangePasswordViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        try
        {
            var userEmailAddress = User.GetEmailAddress();
            var user = await userService.FindByEmailWithRolesAsync(userEmailAddress);
            if (user == null)
            {
                logger.LogWarning("Unable to determine User - Email '{Email}' not found in user base", User.GetEmailAddress());
                return CreateRedirectToChangePasswordPage(errorNotificationService.DefaultErrorMessage, ToastMsgTypeEnum.Error);
            }

            var result = userService.ChangePassword(user, model.OldPassword, model.NewPassword);
            if (!result.IsSuccessful)
            {
                ModelState.AddModelError(nameof(ChangePasswordViewModel.OldPassword), result.ErrorMessage ?? errorNotificationService.DefaultErrorMessage);
                return View(model);
            }

            await unitOfWork.CommitAsync();
            var successMessage = errorNotificationService.GetSuccessfulDatabaseActionMessage("Password", EntityState.Modified);
            return CreateRedirectToChangePasswordPage(successMessage, ToastMsgTypeEnum.Success);
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Error updating password for user");
            return CreateRedirectToChangePasswordPage(errorNotificationService.DefaultErrorMessage, ToastMsgTypeEnum.Error);
        }
    }

    private RedirectToActionResult CreateRedirectToChangePasswordPage(string message, ToastMsgTypeEnum messageType) =>
        RedirectToAction("ChangePassword", routeValues: new
        {
            msg = message,
            msgType = messageType,
        });

    private static string? GetEmailAddress(ClaimsPrincipal principal)
    {
        var email = principal.FindFirstValue(ClaimTypes.Email);

        if (string.IsNullOrEmpty(email))
        {
            email = principal.FindFirstValue("preferred_username");
        }

        if (string.IsNullOrEmpty(email))
        {
            email = principal.FindFirstValue("email");
        }

        return email;
    }

    private static string? GetFirstName(ClaimsPrincipal principal)
    {
        var name = principal.FindFirstValue(ClaimTypes.GivenName);

        if (string.IsNullOrEmpty(name))
        {
            name = principal.FindFirstValue("given_name");
        }

        return name;
    }

    private static string? GetLastName(ClaimsPrincipal principal)
    {
        var name = principal.FindFirstValue(ClaimTypes.Surname);

        if (string.IsNullOrEmpty(name))
        {
            name = principal.FindFirstValue("family_name");
        }

        return name;
    }

    private static string GetUserName(ClaimsPrincipal principal)
    {
        var name = principal.FindFirstValue(ClaimTypes.Upn);

        if (string.IsNullOrEmpty(name))
        {
            name = principal.FindFirstValue("preferred_username");
            if (string.IsNullOrEmpty(name))
            {
                name = principal.FindFirstValue("name") ?? "";
            }
        }

        return name;
    }
}
