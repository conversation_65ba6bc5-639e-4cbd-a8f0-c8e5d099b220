using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Extensions;
using QCash.Data.Models.Exceptions;
using QCash.Service.Core;
using QCash.Service.Models.Product;
using QCash.Service.Services;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.Product.Interfaces;
using QCash.Web.Extensions;
using QCash.Web.Models;
using QCash.Web.Models.Products;

namespace QCash.Web.Controllers;

public class ProductFeesAndInterestRateController(
    ILogger<ProductFeesAndInterestRateController> logger,
    IProductService productService,
    IErrorNotificationService errorNotificationService,
    IUnitOfWork unitOfWork,
    ILookupQueries lookupQueries
    ) : Controller
{
    [HttpGet, Route("{FiSlug}/Product/{productSlug}/General/FeesAndInterestRate")]
    public async Task<IActionResult> EditAsync([FromRoute] string productSlug, string? msg, ToastMsgTypeEnum? msgType = null)
    {
        var dto = await productService.GetProductFeesAndInterestRatePageDataAsync(HttpContext.GetTenant().Slug, productSlug);
        if (dto == null)
        {
            throw new ObjectNotFoundException();
        }
        var model = await PrepareEditPageAsync(dto, msg, msgType);
        return View(model);
    }

    private async Task<ProductFeesAndInterestRatePageViewModel> PrepareEditPageAsync(ProductFeesAndInterestRateTabDto dto, string? msg = null, ToastMsgTypeEnum? msgType = null)
    {
        var model = ProductFeesAndInterestRatePageViewModel.FromDto(dto);
        model.AutoPayOptionChoices = productService.GetAutoPayChoices();
        if (!string.IsNullOrWhiteSpace(msg))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Error,
            };
        }
        model.NavHeader = ProductMainNavHeaderViewModel.FromDto(model.FiSlug, Constants.ProductMainHeaderTabEnum.GeneralTab, model.ProductSlug);
        model.NavHeaderSecondary = ProductGeneralNavHeaderViewModel.FromDto(model.FiSlug, Constants.ProductGeneralHeaderTabEnum.FeesAndInterestRateTab, model.ProductSlug);

        var loanTypes = lookupQueries.GetLoanTypes(model.FiSlug);
        var interestBasedLoanType = await loanTypes.FirstOrDefaultAsync(a => a.Abrv == LookupService.LoanTypeInterestBasedAbrv);
        if (interestBasedLoanType != null)
        {
            model.IsInterestBased = (interestBasedLoanType.Id == dto.LoanTypeId);
        }
        return model;
    }

    [HttpPost, Route("{FiSlug}/Product/{productSlug}/General/FeesAndInterestRate")]
    public async Task<IActionResult> EditAsync([Bind] ProductFeesAndInterestRatePageViewModel userSubmittedModel,
        [FromRoute] string fiSlug, [FromRoute] string productSlug)
    {
        var dto = userSubmittedModel.ToDto();
        if (!ModelState.IsValid)
        {
            return await GetPageForPostbackAsync(userSubmittedModel, dto);
        }

        try
        {
            var saveResult = await productService.SaveGeneralFeesAndInterestRateTabAsync(dto, HttpContext.GetTenant().Id);
            if (!saveResult.IsSuccessful)
            {
                return await GetPageForPostbackAsync(userSubmittedModel, dto, saveResult.ErrorMessage, ToastMsgTypeEnum.Error);
            }
            await unitOfWork.CommitAsync();
            return this.RedirectToAction<ProductFeesAndInterestRateController>(a => a.EditAsync, new
            {
                area = "",
                msg = errorNotificationService.GetSuccessfulDatabaseActionMessage(productService.RecordName, EntityState.Modified),
                msgType = ToastMsgTypeEnum.Success,
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error attempting to save a product.  Slug={ProductSlug}", productSlug);
            return await GetPageForPostbackAsync(userSubmittedModel, dto, "Error while saving product");
        }
    }

    private async Task<IActionResult> GetPageForPostbackAsync(ProductFeesAndInterestRatePageViewModel userSubmittedModel, ProductFeesAndInterestRateTabDto dto, string? msg = null, ToastMsgTypeEnum? msgType = null)
    {
        var model = await PrepareEditPageAsync(dto, msg, msgType);
        return View(model);
    }
}
