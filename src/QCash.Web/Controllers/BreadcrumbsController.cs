using Microsoft.AspNetCore.Mvc;
using QCash.Service.Services.Interfaces;
using QCash.Web.Models;

namespace QCash.Web.Controllers;

public class BreadcrumbsController(ISitemapService sitemapService) : Controller
{
    [HttpGet(Name = "GetBreadcrumbs")]
    public async Task<IActionResult> GetBreadcrumbsAsync(
        string? sourceController = null,
        string? sourceAction = null,
        string? productId = null)
    {
        var currentUrl = Request.Headers.Referer.ToString();
        var absolutePath = new Uri(currentUrl).AbsolutePath;

        if (!string.IsNullOrEmpty(sourceController) &&
            (sourceController.Equals("Lookup", StringComparison.OrdinalIgnoreCase)
             || sourceController.Equals("DvLookup", StringComparison.OrdinalIgnoreCase)
             || sourceController.Equals("DecisionEngineSettings", StringComparison.OrdinalIgnoreCase)))
        {
            var parts = absolutePath.Split('/');
            sourceController = parts[2] + "/" + parts[3];
        }

        var viewModel = new BreadcrumbsViewModel
        {
            BreadcrumbItems =
            [.. (await sitemapService.GetBreadcrumbTrailAsync(sourceController, sourceAction, productId))
                .Select(BreadcrumbItem.FromSitemap),],
        };

        if (viewModel.BreadcrumbItems.Count == 0)
        {
            throw new InvalidOperationException(
            """
            Breadcrumbs navigation is needed for this page. Developer action needed: Make sure to update the SitemapService when creating a new page.  Example:
             AddNode(fiConfigurationGeneralSection,
                    "add a title...",
                    "{sourceController}",
                    "{sourceAction}");   "
            """);
        }
        return PartialView("_Breadcrumbs", viewModel);
    }
}
