using System.Diagnostics;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QCash.Data.Models.Exceptions;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Utilities.Extensions;
using QCash.Web.Models;
using QCash.Web.Models.Home;
using QCash.Web.Utils;

namespace QCash.Web.Controllers;

[Authorize]
public class HomeController(
    IFiService fiService
    ) : Controller
{
    [ServiceFilter(typeof(GlobalTenantAttribute))]
    public async Task<IActionResult> IndexAsync()
    {
        var dto = await fiService.GetPageDataAsync();
        dto.ThrowIfNull(new ObjectNotFoundException());
        var model = HomePageViewModel.CreateFromDto(dto);
        return View(model);
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}

