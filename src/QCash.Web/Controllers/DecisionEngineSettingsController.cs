using Microsoft.AspNetCore.Mvc;
using QCash.Data.Extensions;
using QCash.Service.Core;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Web.Models.Lookup;
using QCash.Service.Utilities.Extensions;
using QCash.Web.Models.DecisionEngine;
using static QCash.Web.Extensions.UrlExtensions;
using ListPageViewModel = QCash.Web.Models.DecisionEngine.ListPageViewModel;

namespace QCash.Web.Controllers;

public class DecisionEngineSettingsController(
    IDecisionEngineSettingsService decisionEngineSettingsService,
    IErrorNotificationService errorNotificationService,
    IUnitOfWork unitOfWork,
    ILogger<DecisionEngineSettingsController> logger
    ) : Controller
{
    private ListPageViewModel PrepareListPage(string scoreTypeSlug, string? msg)
    {
        var scoreTypeEnum = EnumExtensions.GetEnumValueFromDescription<Enums.ScoreTypesEnum>(scoreTypeSlug);
        var lookupTypeEnum = decisionEngineSettingsService.GetLookupTypeEnumFromScoreType(scoreTypeEnum);
        var lookupTypeName = lookupTypeEnum.ToString();
        var model = new ListPageViewModel()
        {
            FiSlug = HttpContext.GetTenant().Slug,
            ScoreTypeSlug = scoreTypeSlug,
            LookupTypeEnum = lookupTypeEnum,
            LookupTypeName = lookupTypeName,
            LookupListType = decisionEngineSettingsService.GetLookup(lookupTypeEnum),
            SubmissionNotifications = errorNotificationService.GetNotyErrorNotification(msg),
        };
        return model;
    }

    [HttpGet, Route("{fiSlug}/[controller]/{scoreTypeSlug}/List")]
    public IActionResult ListScoreTypes([FromRoute] string scoreTypeSlug, [FromQuery] string? msg)
    {
        var model = PrepareListPage(scoreTypeSlug, msg);
        return View("List", model);
    }

    private BusinessRuleViewModel GetBlankViewModel(Guid id, string fiSlug, string scoreTypeSlug, string? submissionNotifications) =>
        new()
        {
            Id = id,
            FiSlug = fiSlug,
            Name = "",
            Detail = "",
            Score = 0,
            ScoreTypeSlug = scoreTypeSlug,
            TimeStamp = "",
            SubmissionNotifications = submissionNotifications,
        };

    [HttpGet]
    [Route("{fiSlug}/[controller]/{scoreTypeSlug}/[action]")]
    public async Task<IActionResult> CreateBusinessRuleAsync([FromRoute] string fiSlug, [FromRoute] string scoreTypeSlug, [FromQuery] string? msg)
    {
        var model = GetBlankViewModel(Guid.Empty, fiSlug, scoreTypeSlug, null);
        PrepareEditPage(model);
        return await Task.FromResult(View("Edit", model));
    }

    private void PrepareEditPage(BusinessRuleViewModel model)
    {
        var scoreTypeEnum = EnumExtensions.GetEnumValueFromDescription<Enums.ScoreTypesEnum>(model.ScoreTypeSlug);
        var lookupTypeEnum = decisionEngineSettingsService.GetLookupTypeEnumFromScoreType(scoreTypeEnum);
        model.NameFieldMaxLength = decisionEngineSettingsService.GetNameFieldMaxLength(lookupTypeEnum);
        model.ShouldHideScore = decisionEngineSettingsService.ShouldHideScore(scoreTypeEnum);
    }

    private IActionResult GetEditPageForPostback(BusinessRuleViewModel userSubmittedModel, string? errorMessage = null)
    {
        var dto = userSubmittedModel.ToDto();
        var model = BusinessRuleViewModel.FromDto(dto);
        PrepareEditPage(model);
        if (!string.IsNullOrWhiteSpace(errorMessage))
        {
            model.SubmissionNotifications = errorNotificationService.GetNotyErrorNotification(errorMessage);
        }
        return View("Edit", model);
    }

    [HttpGet, Route("{fiSlug}/[controller]/{scoreTypeSlug}/[action]/{id}")]
    public async Task<IActionResult> EditAsync([FromRoute] string fiSlug, [FromRoute] string scoreTypeSlug, [FromRoute] Guid id)
    {
        var scoreTypeEnum = EnumExtensions.GetEnumValueFromDescription<Enums.ScoreTypesEnum>(scoreTypeSlug);
        var dto = await decisionEngineSettingsService.GetBusinessRuleAsync(scoreTypeEnum, id);
        if (dto == null)
        {
            var model = GetBlankViewModel(id, fiSlug, scoreTypeSlug, "No such item found");
            return View("Edit", model);
        }
        else
        {
            var model = BusinessRuleViewModel.FromDto(dto);
            PrepareEditPage(model);
            return View("Edit", model);
        }
    }

    [HttpPost, Route("{fiSlug}/[controller]/{scoreTypeSlug}/[action]/{id}")]
    public async Task<IActionResult> EditAsync([FromForm] BusinessRuleViewModel model,
        [FromRoute] string fiSlug, [FromRoute] string scoreTypeSlug, [FromRoute] Guid id)
    {
        model.CheckRoute(fiSlug, scoreTypeSlug);
        var dto = model.ToDto();
        await decisionEngineSettingsService.ValidateModelAsync(dto, ModelState);
        if (!ModelState.IsValid)
        {
            return GetEditPageForPostback(model);
        }
        try
        {
            var result = await decisionEngineSettingsService.SaveAsync(HttpContext.GetTenant().Id, dto);
            if (result.IsSuccessful)
            {
                await unitOfWork.CommitAsync();
                return Redirect(Url.Action<DecisionEngineSettingsController>(a => a.ListScoreTypes,
                    new { scoreTypeSlug, fiSlug, area = "" }));
            }
            return GetEditPageForPostback(model, result.ErrorMessage);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error attempting to save a setting item.  Type={ScoreTypeSlug}  Id={Id}", scoreTypeSlug, id);
            return GetEditPageForPostback(model, $"Error while saving lookup item {scoreTypeSlug}.{id}");
        }
    }

    /// <summary>
    /// Delete action comes from the list page.  If it fails, the user will return to the list page.
    /// </summary>
    /// <returns></returns>
    [HttpPost, Route("{fiSlug}/[controller]/{scoreTypeSlug}/Delete")]
    public async Task<IActionResult> DeleteScoreTypeAsync([FromRoute] string fiSlug, [FromRoute] string scoreTypeSlug,
        [FromForm] DeleteLookupViewModel userSubmittedModel)
    {
        var scoreTypeEnum = EnumExtensions.GetEnumValueFromDescription<Enums.ScoreTypesEnum>(scoreTypeSlug);
        var lookupTypeEnum = decisionEngineSettingsService.GetLookupTypeEnumFromScoreType(scoreTypeEnum);
        var lookupTypeName = lookupTypeEnum.ToString();
        userSubmittedModel.CheckRoute(fiSlug, lookupTypeName);
        if (userSubmittedModel.DeleteItemId.Equals(Guid.Empty))
        {
            return Redirect(Url.Action<DecisionEngineSettingsController>(a => a.ListScoreTypes, new
            {
                scoreTypeSlug,
                fiSlug,
                area = "",
                msg = "Item Id is missing.",
            }));
        }

        var dto = userSubmittedModel.ToDto();
        var result = await decisionEngineSettingsService.DeleteItemAsync(lookupTypeEnum, dto);
        if (result.IsSuccessful)
        {
            await unitOfWork.CommitAsync();
            return Redirect(Url.Action<DecisionEngineSettingsController>(a => a.ListScoreTypes, new
            {
                scoreTypeSlug,
                fiSlug,
                area = "",
                msg = "Item has been deleted.",
            }));
        }

        return Redirect(Url.Action<DecisionEngineSettingsController>(a => a.ListScoreTypes, new
        {
            scoreTypeSlug,
            fiSlug,
            area = "",
            msg = result.ErrorMessage,
        }));
    }
}
