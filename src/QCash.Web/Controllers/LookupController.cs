using EntityFramework.Exceptions.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Extensions;
using QCash.Data.Models;
using QCash.Data.Models.Interfaces;
using QCash.Service.Core;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Utilities;
using QCash.Service.Utilities.Extensions;
using QCash.Web.Models;
using QCash.Web.Models.Lookup;
using static QCash.Service.Utilities.Extensions.EnumExtensions;
using static QCash.Web.Extensions.RedirectToActionExtensions;

namespace QCash.Web.Controllers;

public class LookupController(
    ILogger<LookupController> logger,
    IDecisionEngineSettingsService decisionEngineSettingsService,
    IUnitOfWork unitOfWork,
    ILanguageService languageService,
    ILookupItemService lookupItemService,
    IDETrackingRecordDataMappingService deTrackingRecordDataMappingService,
    ILookupService lookupService,
    IErrorNotificationService errorNotificationService
    ) : Controller
{
    private ListPageViewModel PrepareListPage(string lookupTypeName, string? msg, ToastMsgTypeEnum? msgType)
    {
        var lookupTypeEnum = GetEnumByString<Enums.DecisionEngineLookupTypeEnum>(lookupTypeName) ??
                             Enums.DecisionEngineLookupTypeEnum.Unknown;
        var model = new ListPageViewModel()
        {
            FiSlug = HttpContext.GetTenant().Slug,
            LookupTypeName = lookupTypeEnum.ToString(),
            LookupTypeEnum = lookupTypeEnum,
            LookupListType = decisionEngineSettingsService.GetLookup(lookupTypeEnum),
            NameHeader = decisionEngineSettingsService.GetNameHeader(lookupTypeEnum),
            SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Error,
            },
        };
        return model;
    }

    [HttpGet, Route("{fiSlug}/[controller]/{lookupTypeName}/List")]
    public IActionResult List([FromRoute] string fiSlug, [FromRoute] string lookupTypeName, [FromQuery] string? msg,
        [FromQuery] ToastMsgTypeEnum? msgType)
    {
        var model = PrepareListPage(lookupTypeName, msg, msgType);
        if (model.LookupListType.HasIndividualListUi)
        {
            return GetIndividualListView(model);
        }
        return View(model);
    }

    private IActionResult GetIndividualListView(ListPageViewModel model)
    {
        var isDvLookup = decisionEngineSettingsService.IsDvLookup(model.LookupListType.LookupType);
        if (isDvLookup)
        {
            return RedirectToAction(
                actionName: "List",
                controllerName: "DvLookup",
                routeValues: new
                {
                    fiSlug = model.FiSlug,
                    lookupTypeName = model.LookupListType.LookupType.ToString(),
                    area = "",
                }
            );
        }

        var scoreType = decisionEngineSettingsService.GetScoreTypeFromLookupType(model.LookupListType.LookupType);
        if (scoreType != null)
        {
            return this.RedirectToAction<DecisionEngineSettingsController>(a => a.ListScoreTypes,
                new { fiSlug = model.FiSlug, scoreTypeSlug = scoreType.ToDescriptionString(), area = "", });
        }

        return model.LookupListType.LookupType switch
        {
            Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping =>
                View("ListTrackingRecordDataMapping", model),
            _ => throw new Exception($"Lookup type {model.LookupListType.LookupType} does not have logic for its own list page."),
        };
    }

    /// <summary>
    /// Delete action comes from the list page.  If it fails, the user will return to the list page.
    /// </summary>
    /// <returns></returns>
    [HttpPost, Route("{fiSlug}/[controller]/{lookupTypeName}/Delete")]
    public async Task<IActionResult> DeleteAsync([FromRoute] string fiSlug, [FromRoute] string lookupTypeName,
        [FromForm] DeleteLookupViewModel userSubmittedModel)
    {
        userSubmittedModel.CheckRoute(fiSlug, lookupTypeName);
        var lookupTypeEnum = decisionEngineSettingsService.GetLookupTypeEnum(lookupTypeName);
        if (userSubmittedModel.DeleteItemId.Equals(Guid.Empty))
        {
            return RedirectToAction("List", routeValues: BuildRedirectRouteValues(fiSlug, lookupTypeName, "Item Id is missing.", ToastMsgTypeEnum.Error));
        }

        var dto = userSubmittedModel.ToDto();
        var result = await decisionEngineSettingsService.DeleteItemAsync(lookupTypeEnum, dto);
        if (result.IsSuccessful)
        {
            await unitOfWork.CommitAsync();

            return RedirectToAction("List", routeValues: BuildRedirectRouteValues(fiSlug, lookupTypeName, "Item has been deleted.", ToastMsgTypeEnum.Success));
        }

        return RedirectToAction("List", routeValues: BuildRedirectRouteValues(fiSlug, lookupTypeName, result.ErrorMessage, ToastMsgTypeEnum.Error));
    }

    private async Task<EditPageViewModel> PrepareEditPageAsync(EFPocoLookupDto dto, string lookupTypeName, string? msg, ToastMsgTypeEnum? msgType)
    {
        var lookupTypeEnum = decisionEngineSettingsService.GetLookupTypeEnum(lookupTypeName);
        dto.LookupTypeEnum = lookupTypeEnum;
        dto.NameHeader = decisionEngineSettingsService.GetNameHeader(lookupTypeEnum);
        dto.SupportsForeignLanguage = decisionEngineSettingsService.GetSupportsForeignLanguageStatus(lookupTypeEnum);
        dto.ForeignLanguage = dto.SupportsForeignLanguage ? await languageService.GetForeignLanguageAsync() : null;
        var model = EditPageViewModel.FromDto(dto);
        model.PageTitle = decisionEngineSettingsService.GetPageTitle(lookupTypeEnum, dto.Id.Equals(Guid.Empty));
        model.NameFieldMaxLength = decisionEngineSettingsService.GetNameFieldMaxLength(lookupTypeEnum);
        if (!string.IsNullOrWhiteSpace(msg))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Error,
            };
        }

        return model;
    }

    [HttpGet, Route("{fiSlug}/[controller]/{lookupTypeName}/Edit/{lookupItemSlug}")]
    public async Task<IActionResult> EditAsync([FromRoute] string lookupTypeName, [FromRoute] string lookupItemSlug,
        [FromQuery] string? msg, [FromQuery] ToastMsgTypeEnum? msgType)
    {
        EFPocoLookupDto? dto;
        if (lookupItemSlug == ItemConstants.CreateItemTag)
        {
            dto = lookupItemService.CreateBlankItem(HttpContext.GetTenant().Slug, lookupTypeName, lookupItemSlug);
        }
        else
        {
            dto = await lookupItemService.GetItemAsync(HttpContext.GetTenant().Slug, lookupTypeName, lookupItemSlug);
        }
        dto.ThrowIfNull();
        var model = await PrepareEditPageAsync(dto, lookupTypeName, msg, msgType);
        return View(model);
    }

    private async Task<IActionResult> GetEditPageForPostbackAsync(EditPostingViewModel userSubmittedModel, string? errorMessage = null)
    {
        var dto = userSubmittedModel.ToDto();
        dto.LookupTypeEnum = decisionEngineSettingsService.GetLookupTypeEnum(userSubmittedModel.LookupTypeName);
        var model = await PrepareEditPageAsync(dto, userSubmittedModel.LookupTypeName, errorMessage, ToastMsgTypeEnum.Error);
        return View(model);
    }

    [HttpPost, Route("{fiSlug}/[controller]/{lookupTypeName}/Edit/{lookupItemSlug}")]
    public async Task<IActionResult> EditAsync([FromForm] EditPostingViewModel model,
        [FromRoute] string fiSlug, [FromRoute] string lookupTypeName, [FromRoute] string lookupItemSlug)
    {
        model.CheckRoute(fiSlug, lookupTypeName, lookupItemSlug);
        if (!ModelState.IsValid)
        {
            return await GetEditPageForPostbackAsync(model);
        }

        IEFPocoLookup? record = null;
        try
        {
            var dto = model.ToDto();
            var result = await lookupItemService.SaveAsync(HttpContext.GetTenant().Id, lookupTypeName, dto);
            record = result.Record as IEFPocoLookup;
            if (result.IsSuccessful)
            {
                await unitOfWork.CommitAsync();
                if (lookupItemSlug == ItemConstants.CreateItemTag)
                {
                    lookupItemSlug = $"{record?.Slug}";
                }

                var displayName = decisionEngineSettingsService.GetLookupTypeEnum(lookupTypeName).GetDisplayName();

                var successfulDatabaseMessage = errorNotificationService.GetSuccessfulDatabaseActionMessage(
                    displayName,
                    (dto.Slug == ItemConstants.CreateItemTag) ? EntityState.Added : EntityState.Modified);

                return RedirectToAction("List", new
                {
                    LookupTypeName = lookupTypeName,
                    LookupItemSlug = lookupItemSlug,
                    FiSlug = fiSlug,
                    area = "",
                    msg = successfulDatabaseMessage,
                    msgType = ToastMsgTypeEnum.Success,
                });
            }

            return await GetEditPageForPostbackAsync(model, result.ErrorMessage);
        }
        catch (UniqueConstraintException ex) when (ex.ConstraintProperties?.Contains(nameof(EditPostingViewModel.Name)) == true)
        {
            return await HandleUniqueConstraintExceptionAsync(model, ex, record);
        }
        catch (Exception ex)
        {
            var errMsg = errorNotificationService.GetDatabaseErrorMessage(ex, model);
            logger.LogWarning(ex, "Error attempting to save a lookup item.  Type={LookupTypeName}  Slug={LookupItemSlug}", lookupTypeName, lookupItemSlug);
            return await GetEditPageForPostbackAsync(model, errMsg);
        }
    }

    private async Task<IActionResult> HandleUniqueConstraintExceptionAsync(EditPostingViewModel model, UniqueConstraintException ex,
        IEFPocoLookup? record)
    {
        if (record != null)
        {
            // Tell EFCore to ignore the record that we attempted to add.  We now know we cannot create that record.
            unitOfWork.RevertChangedFlag(record);
        }

        var dto = model.ToDto();
        var result = await lookupItemService.ResurrectAndOverwriteDeletedRecordAsync(dto, record);
        if (result.IsSuccessful)
        {
            await unitOfWork.CommitAsync();

            // the deleted record has been resurrected.  redirect to the list.
            return this.RedirectToAction<LookupController>(a => a.List,
                new
                {
                    dto.LookupTypeName,
                    FiSlug = HttpContext.GetTenant().Slug,
                    area = "",
                    msg = errorNotificationService.GetSuccessfulDatabaseActionMessage(lookupItemService.RecordName, EntityState.Modified),
                    msgType = ToastMsgTypeEnum.Success,
                });
        }

        // We were unable to find/resurrect a deleted item.  Give the normal unique conflict message.
        logger.LogError(ex, "Duplicate name when attempting to save a lookup item.  Model={@Model}", model);
        ModelState.AddModelError(nameof(EditPostingViewModel.Name), "Code must be unique - Another record already has this code");
        return await GetEditPageForPostbackAsync(model);
    }

    private async Task<IActionResult> HandleUniqueConstraintExceptionAsync(TrackingRecordDataMappingPostingViewModel model, UniqueConstraintException ex,
        DecisionEngineTrackingRecordDataMapping? record)
    {
        if (record != null)
        {
            // Tell EFCore to ignore the record that we attempted to add.  We now know we cannot create that record.
            unitOfWork.RevertChangedFlag(record);
        }

        var dto = model.ToDto();
        var result = await deTrackingRecordDataMappingService.ResurrectAndOverwriteDeletedRecordAsync(dto, record);
        if (result.IsSuccessful)
        {
            await unitOfWork.CommitAsync();

            // the deleted record has been resurrected.  redirect to the list.
            return this.RedirectToAction<LookupController>(a => a.List,
                new
                {
                    dto.LookupTypeName,
                    FiSlug = HttpContext.GetTenant().Slug,
                    area = "",
                    msg = errorNotificationService.GetSuccessfulDatabaseActionMessage(lookupItemService.RecordName, EntityState.Modified),
                    msgType = ToastMsgTypeEnum.Success,
                });
        }

        // We were unable to find/resurrect a deleted item.  Give the normal unique conflict message.
        logger.LogError(ex, "Duplicate name when attempting to save a lookup item.  Model={@Model}", model);
        ModelState.AddModelError(nameof(EditPostingViewModel.Name), "Code must be unique - Another record already has this code");
        return await GetEditPageForPostbackAsync(model);
    }

    public TrackingRecordDataMappingEditPageViewModel PrepareEditTrackingRecordDataMappingPage(DETrackingRecordDataMappingDto dto, string? msg, ToastMsgTypeEnum? msgType)
    {
        var model = TrackingRecordDataMappingEditPageViewModel.FromDto(dto);

        if (!string.IsNullOrWhiteSpace(msg))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Error,
            };
        }

        return model;
    }

    [HttpGet, Route("{fiSlug}/[controller]/DecisionEngineTrackingRecordDataMapping/Edit/{lookupItemSlug}")]
    public async Task<IActionResult> EditTrackingRecordDataMappingAsync([FromRoute] string lookupItemSlug, [FromQuery] string? msg, [FromQuery] ToastMsgTypeEnum? msgType)
    {
        TrackingRecordDataMappingEditPageViewModel model;
        if (lookupItemSlug == ItemConstants.CreateItemTag)
        {
            model = new TrackingRecordDataMappingEditPageViewModel
            {
                Id = Guid.Empty,
                Name = "",
                Slug = lookupItemSlug,
                FiSlug = HttpContext.GetTenant().Slug,
                TimeStamp = "",
                PageTitle = "Create DE Tracking Record Data Mappings",
                TrackingRecordType = "",
                TrackingRecordField = "",
            };
        }
        else
        {
            var dto = await deTrackingRecordDataMappingService.GetItemAsync(HttpContext.GetTenant().Id, lookupItemSlug);
            dto.ThrowIfNull();
            model = PrepareEditTrackingRecordDataMappingPage(dto, msg, msgType);
        }
        PopulateTrackingRecordChoices(model);
        return View(model);
    }

    private void PopulateTrackingRecordChoices(TrackingRecordDataMappingEditPageViewModel model)
    {
        model.TrackingRecordLevelChoices = [.. lookupService.GetItemsFromEnum<Enums.TrackingRecordLevelPull>().Where(a => a.Value != Enums.TrackingRecordLevelPull.Unknown)];
        model.TrackingRecordPullOptionChoices = [.. lookupService.GetItemsFromEnum<Enums.TrackingRecordPullOption>().Where(a => a.Value != Enums.TrackingRecordPullOption.Unknown)];
    }

    private async Task<IActionResult> GetEditPageForPostbackAsync(TrackingRecordDataMappingPostingViewModel userSubmittedModel, string? errorMessage = null)
    {
        var dto = userSubmittedModel.ToDto();
        dto.LookupTypeEnum = decisionEngineSettingsService.GetLookupTypeEnum(userSubmittedModel.LookupTypeName);
        var model = await Task.FromResult(PrepareEditTrackingRecordDataMappingPage(dto, errorMessage, ToastMsgTypeEnum.Error));
        PopulateTrackingRecordChoices(model);
        return View("EditTrackingRecordDataMapping", model);
    }

    [HttpPost, Route("{fiSlug}/[controller]/DecisionEngineTrackingRecordDataMapping/Edit/{lookupItemSlug}")]
    public async Task<IActionResult> EditDecisionEngineTrackingRecordDataMappingAsync([FromForm] TrackingRecordDataMappingPostingViewModel model,
        [FromRoute] string fiSlug, [FromRoute] string lookupItemSlug)
    {
        var lookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping);
        model.CheckRoute(fiSlug, lookupTypeName, lookupItemSlug);
        if (!ModelState.IsValid)
        {
            return await GetEditPageForPostbackAsync(model);
        }

        DecisionEngineTrackingRecordDataMapping? record = null;
        try
        {
            var dto = model.ToDto();
            var result = await deTrackingRecordDataMappingService.SaveAsync(HttpContext.GetTenant().Id, dto);
            record = result.Value;
            if (result.IsSuccessful)
            {
                await unitOfWork.CommitAsync();

                var displayName = decisionEngineSettingsService.GetLookupTypeEnum(lookupTypeName).GetDisplayName();

                var successfulDatabaseMessage = errorNotificationService.GetSuccessfulDatabaseActionMessage(
                    displayName,
                    (dto.Id == Guid.Empty && dto.Slug == ItemConstants.CreateItemTag) ? EntityState.Added : EntityState.Modified);

                return RedirectToAction("List", BuildRedirectRouteValues(fiSlug, lookupTypeName, successfulDatabaseMessage, ToastMsgTypeEnum.Success));
            }
            return await GetEditPageForPostbackAsync(model, result.ErrorMessage);
        }
        catch (UniqueConstraintException ex) when (ex.ConstraintProperties?.Contains(nameof(TrackingRecordDataMappingPostingViewModel.Name)) == true)
        {
            return await HandleUniqueConstraintExceptionAsync(model, ex, record);
        }
        catch (Exception ex)
        {
            var errMsg = errorNotificationService.GetDatabaseErrorMessage(ex, model);
            logger.LogWarning(ex, "Error attempting to save a lookup item.  Type={LookupTypeName}, Slug={LookupItemSlug}", lookupTypeName, lookupItemSlug);
            return await GetEditPageForPostbackAsync(model, errMsg);
        }
    }

    private static object BuildRedirectRouteValues(string fiSlug, string lookupTypeName, string? message, ToastMsgTypeEnum? msgType) =>
        new
        {
            FiSlug = fiSlug,
            LookupTypeName = lookupTypeName,
            area = "",
            msg = message,
            msgType,
        };
}
