using Microsoft.AspNetCore.Mvc;
using QCash.Data.Extensions;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Web.Models.DecisionEngine;

namespace QCash.Web.Controllers;

public class DecisionEngineController(
    IDecisionEngineSettingsService decisionEngineSettingsService,
    ILookupService lookupService
    ) : Controller
{
    public IActionResult Index()
    {
        var model = new DecisionEngineSettingsListPageViewModel()
        {
            FiSlug = HttpContext.GetTenant().Slug,
            Lookups = decisionEngineSettingsService.GetLookups(),
            LookupGroups = lookupService.GetItemsFromEnum<Enums.DecisionEngineUIGroupEnum>()
                .Where(a => a.Value != Enums.DecisionEngineUIGroupEnum.Unknown)
                .ToList(),
        };
        return View(model);
    }
}
