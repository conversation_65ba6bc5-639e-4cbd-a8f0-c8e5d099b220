using EntityFramework.Exceptions.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Extensions;
using QCash.Service.Core;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.LoanApplication.Interfaces;
using QCash.Service.Utilities.Extensions;
using QCash.Web.Areas.LoanApplication.Models;
using QCash.Web.Extensions;
using QCash.Web.Models;

namespace QCash.Web.Areas.LoanApplication.Controllers;

[Area(Constants.LoanApplicationArea)]
public class ManageRestrictionsController(
    IManageRestrictionsPageService manageRestrictionsPageService,
    ILookupService lookupService,
    IErrorNotificationService errorNotificationService,
    IUnitOfWork unitOfWork
    ) : Controller
{
    private async Task PrepareEditPageAsync(ManageRestrictionsPageViewModel model, bool includeInactiveProducts = false, string? msg = null, ToastMsgTypeEnum? msgType = null)
    {
        model.StateChoices = await lookupService.GetStatesAsync();
        model.ProductChoices = await lookupService.GetProductDropdownChoicesAsync();
        model.IncludeInactiveProducts = includeInactiveProducts;
        foreach (var item in model.Products ?? [])
        {
            item.ProductName = model.ProductChoices.SingleOrDefault(a =>
                a.Value == item.ProductId)?.Text ?? "Unknown product";
        }

        if (!string.IsNullOrWhiteSpace(msg))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Success,
            };
        }
    }

    [HttpGet]
    public async Task<IActionResult> IndexAsync([FromQuery] bool includeInactiveProducts = false,
        [FromQuery] string? msg = null, [FromQuery] ToastMsgTypeEnum? msgType = null)
    {
        var dto = await manageRestrictionsPageService.GetAsync(includeInactiveProducts);
        dto.ThrowIfNull();
        var model = ManageRestrictionsPageViewModel.FromDto(dto);
        await PrepareEditPageAsync(model, includeInactiveProducts, msg, msgType);
        return View(model);
    }

    [HttpGet]
    public async Task<IActionResult> FilterRestrictionsAsync(bool includeInactiveProducts)
    {
        var dto = await manageRestrictionsPageService.GetAsync(includeInactiveProducts);
        dto.ThrowIfNull();
        var model = ManageRestrictionsPageViewModel.FromDto(dto);
        await PrepareEditPageAsync(model, includeInactiveProducts);
        return PartialView("_ManageRestrictionsPartialView", model);
    }

    public async Task<IActionResult> GetPageForPostbackAsync(ManageRestrictionsPostingViewModel postingModel, string? errorMessage = null, ToastMsgTypeEnum? msgType = null)
    {
        var model = ManageRestrictionsPageViewModel.FromPostingModel(postingModel);
        return await GetPageForPostbackAsync(model, errorMessage, msgType);
    }

    public async Task<IActionResult> GetPageForPostbackAsync(ManageRestrictionsPageViewModel model, string? errorMessage = null, ToastMsgTypeEnum? msgType = null)
    {
        await PrepareEditPageAsync(model, msg: errorMessage, msgType: msgType ?? ToastMsgTypeEnum.Error);
        return View("Index", model);
    }

    [HttpPost]
    public async Task<IActionResult> IndexAsync([FromForm] ManageRestrictionsPostingViewModel model, [FromForm] bool includeInactiveProducts)
    {
        if (!ModelState.IsValid)
        {
            return await GetPageForPostbackAsync(model);
        }
        model.ThrowIfNull("model is null");
        var dto = model.ToDto();
        model.CheckRoute(HttpContext.GetTenant().Slug);
        try
        {
            var result = await manageRestrictionsPageService.SaveAsync(dto);
            if (result.IsSuccessful)
            {
                await unitOfWork.CommitAsync();
                return this.RedirectToAction<ManageRestrictionsController>(a => a.IndexAsync, new
                {
                    area = Constants.LoanApplicationArea,
                    FiSlug = HttpContext.GetTenant().Slug,
                    includeInactiveProducts = includeInactiveProducts,
                    msg = errorNotificationService.GetSuccessfulDatabaseActionMessage(manageRestrictionsPageService.RecordName, EntityState.Modified),
                    msgType = ToastMsgTypeEnum.Success,
                });
            }

            return await GetPageForPostbackAsync(model, result.ErrorMessage);
        }
        catch (UniqueConstraintException uce) when (uce.ConstraintName == "UX_StateRestrictedInterestRate_ProductIdState")
        {
            return await GetErrorFeedbackForUniqueConstraintAsync(model, uce);
        }
        catch (Exception ex)
        {
            var errMsg = errorNotificationService.GetDatabaseErrorMessage(ex, model);
            return await GetPageForPostbackAsync(model, errMsg);
        }
    }

    private async Task<IActionResult> GetErrorFeedbackForUniqueConstraintAsync(ManageRestrictionsPostingViewModel model,
        UniqueConstraintException uce)
    {
        var errMsg = errorNotificationService.GetDatabaseErrorMessage(uce, model);
        var entry = uce.Entries.Count > 0 ? uce.Entries[0] : null;
        if (entry == null)
        {
            // Can't find the entry that caused the exception?  Just return a generic error message.
            return await GetPageForPostbackAsync(model, errMsg);
        }
        var productId = (Guid?)entry.CurrentValues[nameof(ManageRestrictionsProductViewModel.ProductId)];
        var state = (string?)entry.CurrentValues[nameof(ManageRestrictionsStateViewModel.State)];
        var productContainer = model.Products?
            .Select((p, index) => (p, index))
            .SingleOrDefault(a => a.p.ProductId == productId);
        if (productContainer == null || productId == null || state == null)
        {
            return await GetPageForPostbackAsync(model, errMsg);
        }
        foreach (var rateRecord in productContainer.Value.p.Rates.Select((r, index) => (r, index)))
        {
            if (rateRecord.r.RestrictedStateAbbreviations.Contains(state))
            {
                var productIndex = productContainer.Value.index;
                var rateIndex = rateRecord.index;
                // Example: Products[0].Rates[1].RestrictedStateAbbreviations
                var fieldDesc = $"Products[{productIndex}].Rates[{rateIndex}].RestrictedStateAbbreviations";
                ModelState.AddModelError(fieldDesc, $"Duplicate state entry: {state}");
            }
        }
        return await GetPageForPostbackAsync(model, "");
    }
}
