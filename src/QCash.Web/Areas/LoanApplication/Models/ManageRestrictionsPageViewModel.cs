using System.ComponentModel.DataAnnotations;
using System.Globalization;
using QCash.Service.Models;
using QCash.Service.Models.LoanApplication;
using QCash.Web.Models;

namespace QCash.Web.Areas.LoanApplication.Models;

public class ManageRestrictionsPageViewModel : ManageRestrictionsPostingViewModel
{
    public List<QListItem<string>> StateChoices { get; set; } = [];
    public List<QListItem<Guid>> ProductChoices { get; set; } = [];
    public ToastModel? SubmissionNotifications { get; set; } = null;
    public string CurrencySymbol = CultureInfo.CurrentCulture.NumberFormat.CurrencySymbol;

    [Display(Name = "Include Inactive Products")]
    public bool IncludeInactiveProducts { get; set; } = false;

    public Guid? AddingRateToProductId { get; init; }

    public static ManageRestrictionsPageViewModel FromDto(ManageRestrictionsPageDto dto)
    {
        var result = new ManageRestrictionsPageViewModel()
        {
            FiSlug = dto.FiSlug,
            MilitaryAnnualPercentageRate = dto.MilitaryAnnualPercentageRate,
            SubmissionNotifications = null,
            IncludeInactiveProducts = dto.IncludeInactiveProducts,
            SettingId = dto.SettingId,
            SettingTimeStamp = Convert.ToBase64String(dto.SettingTimeStamp),
            Products = (
                from p in dto.Products
                join stateGroupMaker in dto.ExcludedStates.GroupBy(p =>
                    new { p.ProductId, },
                    p => new ManageRestrictionsStateViewModel()
                    {
                        Id = p.Id,
                        State = p.State,
                        TimeStamp = Convert.ToBase64String(p.TimeStamp),
                    },
                    (key, g) => new ManageRestrictionsProductViewModel()
                    {
                        ProductId = key.ProductId,
                        ExcludedStateOriginalDetails = g.ToList(),
                    }) on p.Id equals stateGroupMaker.ProductId into groupedStates
                from pExcludedStates in groupedStates.DefaultIfEmpty()
                join rateGroupMaker in dto.RestrictedRates.GroupBy(r =>
                    new { r.ProductId, },
                    r => new
                    {
                        r.Id,
                        r.ProductId,
                        r.Rate,
                        r.State,
                        r.DeleteRequested,
                        TimeStamp = Convert.ToBase64String(r.TimeStamp),
                        IsNewRecord = r.CreateRequested,
                    },
                    (key, g) => new { key.ProductId, Data = g.ToList(), })
                        on p.Id equals rateGroupMaker.ProductId into groupedProductRates
                from pRestrictedRates in groupedProductRates.DefaultIfEmpty()
                select new ManageRestrictionsProductViewModel()
                {
                    ProductId = p.Id,
                    ExcludedStateOriginalDetails = pExcludedStates?.ExcludedStateOriginalDetails ?? [],
                    Rates = (pRestrictedRates?.Data.GroupBy(r => new { r.ProductId, r.Rate, },
                            r => new { r.Id, r.State, r.Rate, r.TimeStamp, r.IsNewRecord, r.DeleteRequested, },
                            (key, g) => new { key, g = g.ToList() })
                        ?? [])
                        .Select(proj => new ManageRestrictionsProductRateParentViewModel()
                        {
                            Rate = proj.key.Rate,
                            RestrictedStateAbbreviations = proj.g
                                .Where(a => !a.DeleteRequested)
                                .Select(a => a.State).GroupBy(a => a).Select(a => a.Key)
                                .OrderBy(a => a).ToArray(),
                            RestrictedStateOriginalDetails = proj.g
                                .Select(s =>
                                new ManageRestrictionsStateRateViewModel() { Id = s.Id, State = s.State, Rate = s.Rate, TimeStamp = s.TimeStamp }).ToList(),
                            IsNewRecord = proj.g.All(record => record.IsNewRecord),
                        }).ToList(),
                }).ToArray(),
        };
        foreach (var item in result.Products)
        {
            item.ExcludedStateAbbreviations = item.ExcludedStateOriginalDetails
                .Select(a => a.State)
                .GroupBy(a => a)
                .Select(a => a.Key)
                .ToArray();
        }
        return result;
    }

    public static ManageRestrictionsPageViewModel FromPostingModel(ManageRestrictionsPostingViewModel postingModel)
    {
        var pageViewModel = new ManageRestrictionsPageViewModel
        {
            FiSlug = postingModel.FiSlug,
            MilitaryAnnualPercentageRate = postingModel.MilitaryAnnualPercentageRate,
            SettingTimeStamp = postingModel.SettingTimeStamp,
            SettingId = postingModel.SettingId,
            Products = postingModel.Products,
        };
        return pageViewModel;
    }
}
