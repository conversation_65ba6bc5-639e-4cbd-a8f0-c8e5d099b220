using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Runtime.Serialization;
using QCash.Service.Models.LoanApplication;
using QCash.Service.Utilities.Extensions;
using static QCash.Service.Utilities.Extensions.ExceptionExtensions;

namespace QCash.Web.Areas.LoanApplication.Models;

public class ManageRestrictionsPostingViewModel : IValidatableObject
{
    [DataMember]
    public required string FiSlug { get; init; }

    [DataMember, Required]
    [DisplayName("Max Interest Rate for MLA Covered Borrowers")]
    public required decimal MilitaryAnnualPercentageRate { get; init; }

    public required string? SettingTimeStamp { get; init; } = "";
    public required Guid SettingId { get; init; }

    [DataMember]
    public required ManageRestrictionsProductViewModel[]? Products { get; set; } = [];

    public ManageRestrictionsPageDto ToDto()
    {
        foreach (var product in Products ?? [])
        {
            product.Rates.RemoveAll(rate => rate is { ShouldDeleteRecord: true, IsNewRecord: true });

            foreach (var rate in product.Rates.Where(rate => rate is { ShouldDeleteRecord: true, IsNewRecord: false }))
            {
                rate.RestrictedStateAbbreviations = [];
            }
        }

        var dto = new ManageRestrictionsPageDto()
        {
            FiSlug = FiSlug,
            Products = Products?.Select(p => new ManageRestrictionProductDto()
            {
                Id = p.ProductId,
                Name = p.ProductName,
            }).ToList() ?? [],
            SettingId = SettingId,
            SettingTimeStamp = Convert.FromBase64String(SettingTimeStamp ?? ""),
            MilitaryAnnualPercentageRate = MilitaryAnnualPercentageRate,
            ExcludedStates = ConvertStatesFromModelToDto(),
            RestrictedRates = ConvertRatesFromModelToDto(),
        };
        return dto;
    }

    private List<ManageRestrictionRateDto> ConvertRatesFromModelToDto()
    {
        var ratesFlattenedStep1 = (Products?
            .SelectMany(m =>
                m.Rates.Select(r => new
                {
                    m.ProductId,
                    r.Rate,
                    r.RestrictedStateAbbreviations,
                    r.RestrictedStateOriginalDetails,
                })
            ) ?? []).ToList();
        var ratesFlattened = ratesFlattenedStep1
            .SelectMany(g =>
                g.RestrictedStateAbbreviations.Select(s => new { g.ProductId, g.Rate, State = s, })
            ).Distinct();
        var originalDataList = ratesFlattenedStep1
            .SelectMany(a =>
                a.RestrictedStateOriginalDetails.Select(d => new
                {
                    a.ProductId,
                    Data = d,
                })
            );

        var data = ratesFlattened.FullOuterJoin(originalDataList,
            rateJoin => new { rateJoin.ProductId, rateJoin.State },
            origDataJoin => new { origDataJoin.ProductId, origDataJoin.Data.State },
            (newData, origData) =>
                new ManageRestrictionRateDto()
                {
                    Id = origData?.Data.Id ?? Guid.Empty,
                    ProductId = newData?.ProductId ?? origData?.ProductId ?? Guid.Empty,
                    State = origData?.Data.State ?? newData?.State ?? "",
                    Rate = newData?.Rate ?? origData?.Data.Rate ?? 0,
                    CreateRequested = origData == null,
                    DeleteRequested = newData == null,
                    UpdateRequested = origData != null && newData != null && origData.Data.Rate != newData.Rate,
                    TimeStamp = !string.IsNullOrEmpty(origData?.Data.TimeStamp) ? Convert.FromBase64String(origData?.Data.TimeStamp ?? "") : [],
                }
            ).ToList();
        return data;
    }

    private List<ManageRestrictionStateDto> ConvertStatesFromModelToDto()
    {
        var data = Products?
            .Where(a => a.ExcludedStateAbbreviations != null)
            .SelectMany(a =>
                    (from editedItem in a.ExcludedStateAbbreviations!.Select(b => new { State = b })
                     join tempOriginalItem in a.ExcludedStateOriginalDetails on editedItem.State equals tempOriginalItem.State into gj
                     from originalData in gj.DefaultIfEmpty()
                     select new { editedItem.State, Id = originalData?.Id ?? Guid.Empty, TimeStamp = originalData?.TimeStamp ?? string.Empty, })
                    .Union(
                        // If there are any items deleted, they will not be part of the join above
                        from originalData in a.ExcludedStateOriginalDetails
                        where !a.ExcludedStateAbbreviations!.Contains(originalData.State)
                        select new { originalData.State, originalData.Id, originalData.TimeStamp }
                    ),
                (x, y) => new
                {
                    x.ProductId,
                    y.Id,
                    y.State,
                    y.TimeStamp,
                    DeleteRequested = !x.ExcludedStateAbbreviations!.Any(a => a == y.State),
                    CreateRequested = y.Id == Guid.Empty,
                })
            .Select(a => new ManageRestrictionStateDto()
            {
                Id = a.Id,
                ProductId = a.ProductId,
                State = a.State,
                CreateRequested = a.CreateRequested,
                DeleteRequested = a.DeleteRequested,
                TimeStamp = Convert.FromBase64String(SettingTimeStamp ?? ""),
            })
            .ToList() ?? [];
        return data;
    }

    public void CheckRoute(string fiSlug) =>
        ThrowIfUnequal(FiSlug, fiSlug, "Potential XSS problem.");

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        foreach (var p in ((Products ?? []).Select((product, productIndex) => (product, productIndex))))
        {
            // For this product, gather a list of the states with the associated rate for that line item.
            // If a certain state is duplicated, but with the same rate, we will merge them, so that is not an error.
            // If a state is duplicated with a different rate, that is an error.

            // First, create a list of states and rates, grouped so we will ignore an acceptable duplicate.
            var allStatesGroupedByRate = p.product.Rates!
                .Where(rate => !rate.ShouldDeleteRecord)
                .SelectMany(a => a.RestrictedStateAbbreviations, (rate, state) => new { rate.Rate, state })
                .GroupBy(a => new { a.Rate, a.state })
                .Select(a => new { a.Key.Rate, a.Key.state });
            var duplicateStatesWithRates = allStatesGroupedByRate.GroupBy(a => a.state)
                .Where(a => a.Count() > 1).Select(a => a.Key);
            var duplicateStates = duplicateStatesWithRates.Select(a => a);
            foreach (var duplicateState in duplicateStates)
            {
                // There is a problem.  The same state is listed more than one time.
                // We don't know which one is correct, so highlight all records that have the duplicate state
                foreach (var item in p.product.Rates?.Select((rateItem, rateIndex) => (rateItem, rateIndex)) ?? [])
                {
                    if (item.rateItem.RestrictedStateAbbreviations.Contains(duplicateState))
                    {
                        var fieldName = $"{nameof(Products)}[{p.productIndex}].{nameof(ManageRestrictionsProductViewModel.Rates)}[{item.rateIndex}].{nameof(ManageRestrictionsProductRateParentViewModel.RestrictedStateAbbreviations)}";
                        yield return new ValidationResult($"Duplicate state entry: {duplicateState}", [fieldName]);
                    }
                }
            }
        }
    }
}
