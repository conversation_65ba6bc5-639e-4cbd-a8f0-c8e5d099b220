@model QCash.Web.Areas.LoanApplication.Models.ManageRestrictionsPageViewModel
@using QCash.Service.Models
@using QCash.Web.Areas.LoanApplication.Models

@for (var productIndexIterator = 0; productIndexIterator < (Model.Products?.Length ?? 0); productIndexIterator++)
{
    int productIndex = productIndexIterator;
    @Html.HiddenFor(a => Model.Products![productIndex].ProductId)
    @Html.HiddenFor(a => Model.Products![productIndex].ProductName)
    @for (var stateDetailIndex = 0; stateDetailIndex < Model.Products![productIndex].ExcludedStateOriginalDetails.Count; stateDetailIndex++)
    {
        @Html.HiddenFor(a => Model.Products[productIndex].ExcludedStateOriginalDetails[stateDetailIndex].Id)
        @Html.HiddenFor(a => Model.Products[productIndex].ExcludedStateOriginalDetails[stateDetailIndex].State)
        @Html.HiddenFor(a => Model.Products[productIndex].ExcludedStateOriginalDetails[stateDetailIndex].TimeStamp)
    }

    <div class="card product-card" style="margin-bottom: 45px">
        <div class="card-header">
            <div class="row k-card-title align-text-bottom" style="margin-bottom:0" >
                <h5 class="col align-text-bottom" style="margin-bottom:0">Product: @Model.Products[productIndex].ProductName</h5>
            </div>
        </div>
        <div class="card-body" style="padding-top:5px; ">
            @Html.QLabelFor(a => a.Products![productIndex].ExcludedStateAbbreviations)
            @(Html.Kendo().MultiSelectFor(a => a.Products![productIndex].ExcludedStateAbbreviations)
                .Placeholder("")
                .DownArrow(true)
                .HeaderTemplate($"<div class='select-all-states'><label><input type='checkbox' id='Products_{productIndex}__ExcludedStateAbbreviations_selectAll' data-product-index='Products_{productIndex}__ExcludedStateAbbreviations'> Select All</label></div>")
                .DataTextField(nameof(QListItem<string>.Text))
                .DataValueField(nameof(QListItem<string>.Value))
                .Filter("contains")
                .BindTo(Model.StateChoices)
                .Events(e => e
                    .Change("stateSelectionChanged")
                    .DataBound("stateSelectionDataBound")))
            <div style="height:5px"></div>
            <div class="row">
                <div class="col">
                    @Html.QLabelFor(a => a.Products![productIndex].Rates)
                </div>
                <div class="col-2 text-end">
                    @if (!Model.Products[productIndex].ProductId.Equals(Model.AddingRateToProductId))
                    {
                        <a role="button" class="btn btn-primary btn-sm" onclick="addRateGroup(@productIndex)">
                            <i class='fa-solid fa-plus'></i>
                            Add Restriction
                        </a>
                    }
                </div>
            </div>
            @{
                var hideRateTable = !(Model.Products?[productIndex].Rates ?? []).Any();
            }

            <div id="empty-rates-for-product-@productIndex" class="k-grid table @(hideRateTable ? "" : "hidden")">No restrictions</div>

            <table id="rateTable_@productIndex" class="k-grid table @(hideRateTable ? "hidden" : "")">
                <thead>
                <tr>
                    <th></th>
                <th class="col-2">@Html.QLabelFor(a => Model.Products![productIndex].Rates[0].Rate)</th>
                <th class="col-10">@Html.QLabelFor(a => Model.Products![productIndex].Rates[0].RestrictedStateAbbreviations)</th>
                </tr>
                </thead>
                <tbody>
                @for (var rateGroupIndexIterator = 0; rateGroupIndexIterator < Model.Products![productIndex].Rates.Count; rateGroupIndexIterator++)
                {
                    int rateGroupIndex = rateGroupIndexIterator;
                    @Html.HiddenFor(a => Model.Products![productIndex].Rates[rateGroupIndex].IsNewRecord)
                    @Html.HiddenFor(a => Model.Products![productIndex].Rates[rateGroupIndex].ShouldDeleteRecord)


                @for (int stateDetailIndex = 0; stateDetailIndex < Model.Products[productIndex].Rates[rateGroupIndex].RestrictedStateOriginalDetails.Count; stateDetailIndex++)
                {
                    if (Model.Products[productIndex].Rates[rateGroupIndex].IsNewRecord &&
                        string.IsNullOrEmpty(Model.Products[productIndex].Rates[rateGroupIndex].RestrictedStateOriginalDetails[stateDetailIndex].State))
                    {
                        continue;
                    }
                    @Html.HiddenFor(a => Model.Products[productIndex].Rates[rateGroupIndex].RestrictedStateOriginalDetails[stateDetailIndex].Id)
                    @Html.HiddenFor(a => Model.Products[productIndex].Rates[rateGroupIndex].RestrictedStateOriginalDetails[stateDetailIndex].Rate)
                    @Html.HiddenFor(a => Model.Products[productIndex].Rates[rateGroupIndex].RestrictedStateOriginalDetails[stateDetailIndex].State)
                    @Html.HiddenFor(a => Model.Products[productIndex].Rates[rateGroupIndex].RestrictedStateOriginalDetails[stateDetailIndex].TimeStamp)
                }

                    <tr id="rateGroup_Product_@(productIndex)_Rate_@(rateGroupIndex)">
                        <td>
                            <button type="button"
                                    class='btn btn-light btn-sm'
                                    onclick="deleteRateGroup(@productIndex, @rateGroupIndex)">
                                <i class='fa-solid fa-x'></i>
                            </button>
                            <div class="spacer"></div>
                        </td>
                        <td>
                            @(Html.Kendo().NumericTextBoxFor(a => Model.Products![productIndex].Rates[rateGroupIndex].Rate)
                                .Format("##.## '%'")
                                .Min(0)
                                .Max(100)
                                .Spinners(false)
                                .HtmlAttributes(new { @class = "form-control", data_val = "true", data_val_required = "The Rate field is required." })
                            )
                            <div class="text-danger">
                                @Html.ValidationMessageFor(a => a.Products![productIndex].Rates[rateGroupIndex].Rate)
                            </div>
                        </td>
                        <td>
                            <span class="form-control" style="border:none; padding: 0">
                                @(Html.Kendo().MultiSelectFor(a => a.Products![productIndex].Rates[rateGroupIndex].RestrictedStateAbbreviations)
                                    .Placeholder("Select States...")
                                    .DownArrow(true)
                                    .HeaderTemplate($@"<div class='select-all-states'><label><input type='checkbox'
id='{nameof(ManageRestrictionsPostingViewModel.Products)}_{productIndex}__{nameof(ManageRestrictionsProductViewModel.Rates)}_{rateGroupIndex}__{nameof(ManageRestrictionsProductRateParentViewModel.RestrictedStateAbbreviations)}_selectAll'
data-product-index='{nameof(ManageRestrictionsPostingViewModel.Products)}_{productIndex}__{nameof(ManageRestrictionsProductViewModel.Rates)}_{rateGroupIndex}__{nameof(ManageRestrictionsProductRateParentViewModel.RestrictedStateAbbreviations)}'> Select All</label></div>")
                                    .DataTextField(nameof(QListItem<string>.Text))
                                    .DataValueField(nameof(QListItem<string>.Value))
                                    .Filter("contains")
                                    .BindTo(Model.StateChoices)
                                    .HtmlAttributes(new { @class = "form-control" })
                                    .Events(e => e
                                        .Change("stateSelectionChanged")
                                        .DataBound("stateSelectionDataBound")))
                                @Html.ValidationMessageFor(a => a.Products![productIndex].Rates[rateGroupIndex].RestrictedStateAbbreviations, "",
                                    htmlAttributes: new { @class = "text-danger" })
                            </span>
                        </td>
                    </tr>
                    <tr id="undoRateDeletionGroup_Product_@(productIndex)_Rate_@(rateGroupIndex)" class="hidden">
                        <td colspan="3">
                            <button type="button"
                                    class='btn btn-light btn-sm'
                                    onclick="undoRateDeletion(@productIndex, @rateGroupIndex)">
                                <i class='fa fa-undo'></i> Undo
                            </button>
                            <small class="text-muted" style='color: red !important; padding-left: 5px;'>This rate group is set to be removed. Click "Save" to confirm the change.</small>
                        </td>
                    </tr>
                }
                </tbody>
            </table>
        </div>
    </div>
}