@model QCash.Web.Areas.LoanApplication.Models.ManageRestrictionsPageViewModel
@{ Layout = "_StandardLayout"; }
@using QCash.Service.Models
@using QCash.Web.Areas.LoanApplication.Models
@using QCash.Web.Models

<script src="~/js/validator-default-override-for-kendo.js" asp-append-version="true"></script>

@section Styles {
    <style>
        .k-grid.table {
            display: table;
            border: none;
            width: 100%;
        }

        .k-grid.table tr {
            display: table-row;
            width: 100%;
        }

        .k-grid.table td,
        .k-grid.table th {
            border: none;
        }

        .hidden {
            display: none !important;
        }

        .text-danger, .spacer {
            display: block;
            transition: min-height 0.2s ease-out, margin-top 0.2s ease-out;
            min-height: 0;
        }

        /* When validation error exists in any cell of the row, expand all validation containers and spacers */
        tr:has(.field-validation-error) .text-danger,
        tr:has(.field-validation-error) .spacer {
            min-height: 20px;
        }

        .select-all-states * {
            cursor: pointer;
        }

        .select-all-states {
            cursor: pointer;
            border-bottom: 2px solid #dee2e6;
        }

        .select-all-states:hover {
            background-color: #dee2e6;
        }

        .select-all-states label {
            margin-left: 12px;
            margin-top: 3px;
            margin-bottom: 3px;
        }
    </style>
}

<script id="rate-group-template" type="text/x-kendo-template">
    <tr id="rateGroup_Product_#: ProductIndex #_Rate_#: RateGroupIndex #">
        <td>
            <button type="button"
                    class='btn btn-light btn-sm'
                    onclick="deleteRateGroup('#: ProductIndex #', '#: RateGroupIndex #')">
                <i class='fa-solid fa-x'></i>
            </button>
            <div class="spacer"></div>
        </td>
        <td>
            <span class="form-control" style="border:none; padding: 0">
                <input type="number"
                       id="Products_#: ProductIndex #__Rates_#: RateGroupIndex #__Rate"
                       name="Products[#: ProductIndex #].Rates[#: RateGroupIndex #].Rate"
                       class="kendo-numerictextbox form-control"
                       style="width: 100%;"/>
            </span>
            <span class="text-danger field-validation-valid" data-valmsg-for="Products[#: ProductIndex #].Rates[#: RateGroupIndex #].Rate" data-valmsg-replace="true"></span>
        </td>
        <td>
            <span class="form-control" style="border:none; padding: 0">
                <select name="Products[#: ProductIndex #].Rates[#: RateGroupIndex #].@(nameof(ManageRestrictionsProductRateParentViewModel.RestrictedStateAbbreviations))"
                        id="Products_#: ProductIndex #__Rates_#: RateGroupIndex #__@(nameof(ManageRestrictionsProductRateParentViewModel.RestrictedStateAbbreviations))"
                        class="kendo-multiselect  form-control"
                        data-role="multiselect"
                        style="width: 100%;">
                </select>
            </span>
            <span class="text-danger field-validation-valid" data-valmsg-for="Products[#: ProductIndex #].Rates[#: RateGroupIndex #].RestrictedStateAbbreviations" data-valmsg-replace="true"></span>
        </td>
    </tr>
</script>

<div class="container">
    <form id="restrictionsForm" method="post" class="custom needs-validation"
          asp-area="LoanApplication" asp-controller="ManageRestrictions" asp-action="Index" asp-route-fiSlug="@Model.FiSlug" novalidate>
        @Html.AntiForgeryToken()
        @Html.HiddenFor(a => Model.SettingId)
        @Html.HiddenFor(a => Model.SettingTimeStamp)
        @Html.HiddenFor(a => Model.FiSlug)

        @await Html.PartialAsync("_TitleRow", new TitleViewModel
        {
            Title = "Manage Restrictions",
        })
        @await Html.PartialAsync("_ValidationSummary", Model)

        <div class="rounded-box" style="padding-top:5px; padding-bottom:10px">
            <div class="row">
                <div class="col">
                    @Html.QTextBoxFor(u => Model.MilitaryAnnualPercentageRate, format: HtmlHelperExtensions.QTextBoxFormat.Percentage3)
                </div>
            </div>
        </div>

        <div style="height:15px"></div>

        <div class="" style="padding-top:20px; padding-bottom:10px">
            <div class="row">
                <div class="col">
                </div>
                <div class="col-3 text-end">
                    <div class="float-end">
                        @Html.QSwitchFor(a => a.IncludeInactiveProducts, "Include Inactive Products", overrideLabelContent: "",
                            htmlAttributes: new { onclick = "includeInactiveProductsToggleClicked()" })
                    </div>
                </div>
            </div>
        </div>

        <div id="manageRestrictionsListContainer">
            @await Html.PartialAsync("_ManageRestrictionsPartialView", Model)
        </div>

        @await Html.PartialAsync("_SubmitButton", new SubmitButtonViewModel())
    </form>
</div>

@await Html.PartialAsync("_ToastOnPageLoad", Model.SubmissionNotifications)

@section Scripts {
    <script type="text/javascript">
        function includeInactiveProductsToggleClicked() {
            const searchValue = $('#IncludeInactiveProducts').prop('checked');
            const url = `@Url.Action("FilterRestrictions", "ManageRestrictions")`;
            const parameter = "includeInactiveProducts";
            const containerId = "#manageRestrictionsListContainer";
            performSearch(searchValue, url, parameter, containerId); //site.js
        }

        function addRateGroup(productIndex) {
            const tableBody = $(`#rateTable_${productIndex} tbody`);
            const rateGroupIndex = $(tableBody).children('tr:visible').length;

            if (rateGroupIndex === 0) {
                $(`#empty-rates-for-product-${productIndex}`).addClass('hidden');
                $(`#rateTable_${productIndex}`).removeClass('hidden');
            }

            const template = kendo.template($("#rate-group-template").html());

            const data = {
                ProductIndex: productIndex,
                RateGroupIndex: rateGroupIndex
            };

            const renderedRowTemplate = template(data);
            const $rowElement = $(renderedRowTemplate);

            tableBody.append(`<input type="hidden"
                                name="Products[${productIndex}].Rates[${rateGroupIndex}].IsNewRecord"
                                value="true" />`);
            tableBody.append(`<input type="hidden"
                                name="Products[${productIndex}].Rates[${rateGroupIndex}].ShouldDeleteRecord"
                                value="false" />`)

            tableBody.append($rowElement);

            $rowElement.find(".kendo-numerictextbox").kendoNumericTextBox({
                spinners: false,
                format: "##.## '%'",
                min: 0,
                max: 100,
                value: 0,
            });

            $rowElement.find(".kendo-multiselect").kendoMultiSelect({
                placeholder: "Select States...",
                dataTextField: "Text",
                dataValueField: "Value",
                dataSource: @Html.Raw(Json.Serialize(Model.StateChoices)),
                filter: "contains",
                downArrow: true,
                clearButton: true,
                headerTemplate: `<div class='select-all-states'><label><input type='checkbox'
                id='@(nameof(ManageRestrictionsPostingViewModel.Products))_${productIndex}__@(nameof(ManageRestrictionsProductViewModel.Rates))_${rateGroupIndex}__@(nameof(ManageRestrictionsProductRateParentViewModel.RestrictedStateAbbreviations))_selectAll'
                data-product-index='@(nameof(ManageRestrictionsPostingViewModel.Products))_${productIndex}__@(nameof(ManageRestrictionsProductViewModel.Rates))_${rateGroupIndex}__@(nameof(ManageRestrictionsProductRateParentViewModel.RestrictedStateAbbreviations))'> Select All</label></div>`,
                change: stateSelectionChanged,
                dataBound: stateSelectionDataBound,
            });

            updateValidationRulesForRateGroup(productIndex, rateGroupIndex, true);
        }

        function updateValidationRule(fieldName, addRule, rules, messages) {
            const form = $("#restrictionsForm");

            if (addRule) {
                form.validate().settings.rules[fieldName] = rules;
                form.validate().settings.messages[fieldName] = messages;
            }
            else {
                if (form.validate().settings.rules[fieldName]) {
                    delete form.validate().settings.rules[fieldName];
                    delete form.validate().settings.messages[fieldName];
                }
            }
        }

        function updateValidationRulesForRateGroup(productIndex, rateIndex, addRule) {
            const rateField = `Products[${productIndex}].Rates[${rateIndex}].Rate`;
            const excludedStateField = `Products[${productIndex}].Rates[${rateIndex}].@(nameof(ManageRestrictionsProductRateParentViewModel.RestrictedStateAbbreviations))`;
            const rateFieldRules = {
                required: true,
                number: true
            };
            const excludedStateFieldRules = {
                required: true
            };
            const rateFieldMessages = {
                required: "The Rate field is required.",
                number: "The field Rate must be a number."
            }
            const excludedStateFieldMessages = {
                required: "At least one state must be selected."
            };

            updateValidationRule(rateField, addRule, rateFieldRules, rateFieldMessages);
            updateValidationRule(excludedStateField, addRule, excludedStateFieldRules, excludedStateFieldMessages);
        }

        function toggleRateGroupVisibility(productIndex, rateIndex, isDeleting) {
            const rateGroup = document.getElementById(`rateGroup_Product_${productIndex}_Rate_${rateIndex}`);
            const deleteRateMessage = document.getElementById(`undoRateDeletionGroup_Product_${productIndex}_Rate_${rateIndex}`);
            const hiddenDeleteRecordInput = document.querySelector(`input[name='Products[${productIndex}].Rates[${rateIndex}].ShouldDeleteRecord']`);
            const hiddenIsNewRecordInput = document.querySelector(`input[name='Products[${productIndex}].Rates[${rateIndex}].IsNewRecord']`);

            if (isDeleting) {
                updateValidationRulesForRateGroup(productIndex, rateIndex, false);
            }
            else {
                updateValidationRulesForRateGroup(productIndex, rateIndex, true);
            }

            if (hiddenIsNewRecordInput.value === "true" && isDeleting === true && !deleteRateMessage) {
                if (rateGroup) {
                    rateGroup.remove();
                }
                if (hiddenDeleteRecordInput) {
                    hiddenDeleteRecordInput.remove();
                }
                if (hiddenIsNewRecordInput) {
                    hiddenIsNewRecordInput.remove();
                }

                if (parseInt(rateIndex) === 0) {
                    $(`#rateTable_${productIndex}`).addClass('hidden');
                    $(`#empty-rates-for-product-${productIndex}`).removeClass('hidden');
                }

                return;
            }

            if (rateGroup) {
                rateGroup.classList.toggle("hidden", isDeleting);
            }
            if (deleteRateMessage) {
                deleteRateMessage.classList.toggle("hidden", !isDeleting);
            }
            if (hiddenDeleteRecordInput) {
                hiddenDeleteRecordInput.value = isDeleting ? "true" : "false";
            }
        }

        function deleteRateGroup(productIndex, rateIndex) {
            toggleRateGroupVisibility(productIndex, rateIndex, true);
        }

        function undoRateDeletion(productIndex, rateIndex) {
            toggleRateGroupVisibility(productIndex, rateIndex, false);
        }

        function stateSelectionChanged(e) {
            const id = this.element.attr("id");
            const multiSelect = $(e.sender.element).data("kendoMultiSelect");
            const selectAll = $(`#${id}_selectAll`);

            selectAll.prop("checked", multiSelect.value().length === multiSelect.dataSource.data().length);
            unHighlightStateDropDownIfValidationIssueResolved(multiSelect);
        }

        function stateSelectionDataBound(e) {
            const id = this.element.attr("id");
            const multiSelect = $(e.sender.element).data("kendoMultiSelect");
            const selectAll = $(`#${id}_selectAll`);

            selectAll.prop("checked", multiSelect.value().length === multiSelect.dataSource.data().length);
        }

        $(document).ready(function() {
            $(document).on("click", ".select-all-states", function() {
                const checkbox = $(this).find("input");
                checkbox.prop("checked", !checkbox.prop("checked"));
                checkbox.trigger("change");
            });

            $(document).on("click", ".select-all-states label", function(e) {
                e.stopPropagation();
            });

            $(document).on("change", ".select-all-states input[type='checkbox']", function() {
                const id = this.dataset.productIndex;
                const multiSelect = $(`#${id}`).data("kendoMultiSelect");

                if (this.checked) {
                    multiSelect.value(multiSelect.dataSource.data().map(item => item.Value));
                } else {
                    multiSelect.value(null);
                }

                unHighlightStateDropDownIfValidationIssueResolved(multiSelect);

                multiSelect.close();
            });
        });

        function unHighlightStateDropDownIfValidationIssueResolved(multiSelectElement) {
            if (multiSelectElement.value().length > 0) {
                $("#restrictionsForm").validate().element(multiSelectElement.element);
            }
        }
    </script>
}