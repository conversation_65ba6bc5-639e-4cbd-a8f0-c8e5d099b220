@using QCash.Web.Models
@model QCash.Web.Areas.FIConfiguration.Models.UserEditPageViewModel

@{ Layout = "_StandardLayout"; }

<div class="container">
    <form id="userDetailsForm" method="post" class="custom needs-validation" action novalidate="novalidate">
        @{
            var isNewUser = Model.CreatingUser();
            var title = isNewUser ? "Add User" : $"Edit user - {Model.FirstName} {Model.LastName}";
        }

        @await Html.PartialAsync("_TitleRow", new TitleViewModel
        {
            Title = title,
        })
        @await Html.PartialAsync("_ValidationSummary", Model)

        @Html.AntiForgeryToken()
        @Html.HiddenFor(a => Model.UserId)
        @Html.HiddenFor(a => Model.UserName)
        @Html.HiddenFor(a => Model.TimeStamp)
        @Html.HiddenFor(a => Model.FiSlug)

        <div class="rounded-box">
            <div class="row">
                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.FirstName, enabled: !Model.FormIsDisabled)
                </div>
                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.LastName, enabled: !Model.FormIsDisabled)
                </div>

                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.Email, enabled: !Model.FormIsDisabled)
                </div>

                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.PhoneNumber, enabled: !Model.FormIsDisabled)
                </div>
            </div>
            <div class="row">
                @if (Model.RoleTypeOptions.Any())
                {
                    <div class="col-12 col-xl-6">
                        @Html.QRadioButtonTogglesFor(u => Model.RoleTypeId,
                            Model.RoleTypeOptions,
                            enabled: !Model.FormIsDisabled && Model.CanUpdateRoles)
                        @Html.QCheckboxListFor(u => Model.RoleSubTypeManagerOptions!,
                            htmlAttributes: new Dictionary<string, object>()
                            {
                                { "class", "hidden" },
                                { "data-parent-toggle-visibility", "true" },
                                { "data-parent-property", @Html.NameFor(u => Model.RoleTypeId) },
                                { "data-parent-id", QCash.Service.Models.FIConfiguration.UserManagement.Constants.ManagerRoleTypeGuid.ToString() },
                            },
                            enabled: !Model.FormIsDisabled && Model.CanUpdateRoles,
                            labelToolTip: Model.CanUpdateRolesExplanation)
                        @Html.QCheckboxListFor(u => Model.RoleSubTypeOtherOptions!,
                            htmlAttributes: new Dictionary<string, object>()
                            {
                                { "class", "hidden" },
                                { "data-parent-toggle-visibility", "true" },
                                { "data-parent-property", @Html.NameFor(u => Model.RoleTypeId) },
                                { "data-parent-id", QCash.Service.Models.FIConfiguration.UserManagement.Constants.OtherRoleTypeGuid.ToString() },
                            },
                            enabled: !Model.FormIsDisabled && Model.CanUpdateRoles,
                            labelToolTip: Model.CanUpdateRolesExplanation)
                    </div>
                }
                <div class="col-6">
                    @Html.QSwitchFor(u => Model.IsActive, "Inactive", "Active",
                        enabled: !Model.FormIsDisabled)

                    @if (Model.IsLockedOut)
                    {
                        <div class="form-label">Lockout Status</div>
                        <div class="form-text">User is locked out.</div>
                        <DisabledTagHelper type="submit"
                                           asp-page-handler="UnlockUser"
                                           class="btn btn-primary button-sm"
                                           style="width:150px">
                            Unlock user
                        </DisabledTagHelper>
                    }
                    else
                    {
                        <div class="form-label">Lockout Status</div>
                        <div class="form-text">User is unlocked.</div>
                    }
                </div>
            </div>
            <div class="row">
                <div class="col-6">
                    @if (Model.DateCreatedLocal.HasValue)
                    {
                        @Html.QTextBoxFor(u => Model.DateCreatedLocal, enabled: false)
                    }
                </div>
                <div class="col-6">
                    @if (Model.DateUpdatedLocal.HasValue)
                    {
                        @Html.QTextBoxFor(u => Model.DateUpdatedLocal, enabled: false)
                    }
                </div>
            </div>
        </div>

        @await Html.PartialAsync("_SubmitButton", new SubmitButtonViewModel
        {
            FormIsEnabled = !Model.FormIsDisabled,
        })
    </form>
</div>

@await Html.PartialAsync("_ToastOnPageLoad", Model.SubmissionNotifications)

<script type="text/javascript">
    $(document).ready(function () {
        QCash.initializeInputControls();

        //Margin percentage focus and blur - add/remove percentage sign
        QCash.initializeBoxFocusFormatter(true);

        const roleTypeId = `@Html.ValueFor(u => Model.RoleTypeId)`;
        const roleTypeFieldName = `@Html.NameFor(u => Model.RoleTypeId)`;
        QCash.setVisibilityOfChildControls(roleTypeFieldName, roleTypeId);
    });

</script>
