@using QCash.Web.Models
@model QCash.Web.Areas.FIConfiguration.Models.UserCreatePageViewModel
@addTagHelper *, Kendo.Mvc
@addTagHelper *, QCash.Web

@{ Layout = "_StandardLayout"; }

<div class="container">
    <form id="userDetailsForm" method="post" class="custom needs-validation" onsubmit="QCash.onFormSubmit()" novalidate>
        <div class="text-danger">
            @Html.ValidationMessageFor(a => a.UserId)
            @Html.ValidationMessageFor(a => a.UserName)
            @Html.ValidationMessageFor(a => a.TimeStamp)
        </div>

        @Html.AntiForgeryToken()
        @Html.HiddenFor(a => Model.UserId)
        @Html.HiddenFor(a => Model.UserName)
        @Html.HiddenFor(a => Model.TimeStamp)
        @Html.HiddenFor(a => Model.FiSlug)

        <div style="height:15px"></div>
        @if (Model.CreatingUser())
        {
            <h4>Add User</h4>
        }
        else
        {
            <h4>Edit user - @Model.FirstName @Model.LastName</h4>
        }

        @await Html.PartialAsync("_ValidationSummary", Model)

        <div class="hidden">
            <div class="text-danger">Validation Errors:</div>
            @Html.ValidationSummary()
        </div>

        <div class="rounded-box">
            <div class="row row-cols-12">
                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.FirstName, enabled: !Model.FormIsDisabled)
                </div>
                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.LastName, enabled: !Model.FormIsDisabled)
                </div>

                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.Password, enabled: !Model.FormIsDisabled, maskPassword:true)
                </div>
                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.PasswordConfirm, enabled: !Model.FormIsDisabled, maskPassword:true)
                </div>

                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.Email, enabled: !Model.FormIsDisabled)
                </div>
                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.EmailConfirm, enabled: !Model.FormIsDisabled)
                </div>
                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.PhoneNumber, enabled: !Model.FormIsDisabled)
                </div>
            </div>
            <div class="row row-cols-12">
                @if (Model.RoleTypeOptions.Any())
                {
                    <div class="col-6" style="min-width: 250px;">
                        @Html.QRadioButtonTogglesFor(u => Model.RoleTypeId,
                            Model.RoleTypeOptions,
                            enabled: !Model.FormIsDisabled && Model.CanUpdateRoles)
                        @Html.QCheckboxListFor(u => Model.RoleSubTypeManagerOptions!,
                            htmlAttributes: new Dictionary<string, object>()
                            {
                                { "class", "hidden" },
                                { "data-parent-toggle-visibility", "true" },
                                { "data-parent-property", @Html.NameFor(u => Model.RoleTypeId) },
                                { "data-parent-id", QCash.Service.Models.FIConfiguration.UserManagement.Constants.ManagerRoleTypeGuid.ToString() },
                            },
                            enabled: !Model.FormIsDisabled && Model.CanUpdateRoles,
                            labelToolTip: Model.CanUpdateRolesExplanation)
                        @Html.QCheckboxListFor(u => Model.RoleSubTypeOtherOptions!,
                            htmlAttributes: new Dictionary<string, object>()
                            {
                                { "class", "hidden" },
                                { "data-parent-toggle-visibility", "true" },
                                { "data-parent-property", @Html.NameFor(u => Model.RoleTypeId) },
                                { "data-parent-id", QCash.Service.Models.FIConfiguration.UserManagement.Constants.OtherRoleTypeGuid.ToString() },
                            },
                            enabled: !Model.FormIsDisabled && Model.CanUpdateRoles,
                            labelToolTip: Model.CanUpdateRolesExplanation)
                    </div>
                }
                <div class="col-6">
                    @Html.QSwitchFor(u => Model.IsActive, "Inactive", "Active",
                        enabled: !Model.FormIsDisabled)
                </div>
                <div class="col">
                    @if (Model.DateCreatedLocal.HasValue)
                    {
                        @Html.QTextBoxFor(u => Model.DateCreatedLocal, enabled: false)
                    }
                </div>
                <div class="col">
                    @if (Model.DateUpdatedLocal.HasValue)
                    {
                        @Html.QTextBoxFor(u => Model.DateUpdatedLocal, enabled: false)
                    }
                </div>
            </div>
        </div>

        @await Html.PartialAsync("_SubmitButton", new SubmitButtonViewModel())
    </form>
</div>

@await Html.PartialAsync("_ToastOnPageLoad", Model.SubmissionNotifications)

<script type="text/javascript">
    $(document).ready(function () {
        QCash.initializeInputControls();

        //Margin percentage focus and blur - add/remove percentage sign
        QCash.initializeBoxFocusFormatter(true);

        const roleTypeId = `@Html.ValueFor(u => Model.RoleTypeId)`;
        const roleTypeFieldName = `@Html.NameFor(u => Model.RoleTypeId)`;
        QCash.setVisibilityOfChildControls(roleTypeFieldName, roleTypeId);
    });
</script>
