@{ Layout = "_StandardLayout"; }
@using QCash.Web.Models
@model QCash.Web.Areas.FIConfiguration.Models.ApplicationKeyViewModel

<div class="container">
    <form id="applicationKeyForm" method="post" class="custom needs-validation"
          asp-controller="FiInfo" asp-action="Edit" novalidate >
        @Html.AntiForgeryToken()
        @Html.HiddenFor(a => Model.FiSlug)
        @Html.HiddenFor(a => Model.Id)
        @Html.HiddenFor(a => Model.TimeStamp)

        @await Html.PartialAsync("_TitleRow", new TitleViewModel
        {
            Title = "Application Info",
        })
        @await Html.PartialAsync("_ValidationSummary", Model)

        <div class="rounded-box">
            <div class="row">
                <div class="col">
                    @Html.QTextBoxFor(u => Model.ApplicationId, enabled: false,
                        buttonIconName:"fa-solid fa-clipboard", buttonOnClickEvent:"QCash.copyTextBoxToClipboard(event)")
                </div>
            </div>
            <div class="row">
                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.SharedKey, enabled: false)
                </div>
                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.SecondarySharedKey, enabled: false)
                </div>
            </div>
            <div class="row">
                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.ApplicationSsoKey, enabled: false)
                </div>
                <div class="col-6">
                    @Html.QTextBoxFor(u => Model.SecondaryApplicationSsoKey, enabled: false)
                </div>
            </div>
            <div class="row">
                <div class="col">
                    @Html.QSwitchFor(u => Model.SsoSecondaryEnabled, textWhenOn:"Enabled", textWhenOff:"Disabled")
                </div>
            </div>
        </div>

        <div class="rounded-box" style="margin-top: 20px;">
            <div class="row">
                <div class="col-3">
                    <button type="submit" asp-action="GenerateSecondaryKeys" class="btn btn-secondary button-large">
                        Generate Secondary Keys
                    </button>
                </div>
                <div class="col-3">
                    <button id="swapPrimaryKeysButtonId" type="button" class="btn btn-secondary button-large"
                            onclick="swapPrimaryAndSecondaryKeys()" disabled="@string.IsNullOrWhiteSpace(Model.SecondarySharedKey)">
                        Swap Primary Keys
                    </button>
                </div>
                <div class="col-3">
                    <button type="button" class="btn btn-secondary button-large" onclick="deleteSecondaryKeys()">
                        Delete Secondary Keys
                    </button>
                </div>

                <div class="col-3">
                    <div class="text-end">
                        <button type="submit" asp-action="Save" class="btn btn-primary button-large" style="width:150px">
                            Save
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@await Html.PartialAsync("_ToastOnPageLoad", Model.SubmissionNotifications)

<script type="text/javascript">
    function deleteSecondaryKeys() {
        $('input[name=@nameof(ApplicationKeyViewModel.SecondarySharedKey)]').val("");
        $('input[name=@nameof(ApplicationKeyViewModel.SecondaryApplicationSsoKey)]').val("");
        $('button#swapPrimaryKeysButtonId').prop('disabled', true);
    }

    function swapPrimaryAndSecondaryKeys() {
        QCash.swapInputValuesBySelector('input[name=@nameof(ApplicationKeyViewModel.SharedKey)]','input[name=@nameof(ApplicationKeyViewModel.SecondarySharedKey)]');
        QCash.swapInputValuesBySelector('input[name=@nameof(ApplicationKeyViewModel.ApplicationSsoKey)]','input[name=@nameof(ApplicationKeyViewModel.SecondaryApplicationSsoKey)]');
    }
</script>
