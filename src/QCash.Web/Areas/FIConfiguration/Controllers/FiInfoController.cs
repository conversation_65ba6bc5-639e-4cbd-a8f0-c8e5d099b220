using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Extensions;
using QCash.Service.Core;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Utilities.Extensions;
using QCash.Web.Areas.FIConfiguration.Models;
using QCash.Web.Extensions;
using QCash.Service.Services.Interfaces;
using QCash.Web.Models;

namespace QCash.Web.Areas.FIConfiguration.Controllers;

[Area(Constants.FiConfigurationArea)]
public class FiInfoController(
    IFiService fiService,
    IErrorNotificationService errorNotificationService,
    IUnitOfWork unitOfWork,
    ILookupService lookupService,
    IAuthUserService authUserService
    ) : Controller
{
    public async Task<IActionResult> EditAsync([FromQuery] string? msg, [FromQuery] ToastMsgTypeEnum? msgType)
    {
        var dto = await fiService.GetPageDataAsync();
        dto.ThrowIfNull();
        var model = FiInfoPageViewModel.FromDto(dto, HttpContext.GetTenant().Slug);
        await PrepareEditPageAsync(model, msg, msgType);
        return View("Edit", model);
    }

    private async Task PrepareEditPageAsync(FiInfoPageViewModel model, string? msg = null, ToastMsgTypeEnum? msgType = null)
    {
        model.SetMetadata(lookupService.GetTimeZoneChoices(),
            await lookupService.GetStatesAsync(),
            authUserService.IsQcfFinance());
        model.SetNotificationInfo(errorNotificationService, msg, msgType);
    }

    [HttpPost]
    public async Task<IActionResult> EditAsync([FromForm] FiInfoPostingViewModel model, [FromRoute] string fiSlug)
    {
        var dto = model.ToDto();
        if (!ModelState.IsValid)
        {
            return await GetEditPageForPostbackAsync(model);
        }
        try
        {
            var result = await fiService.SaveAsync(HttpContext.GetTenant().Id, dto);
            if (result.IsSuccessful)
            {
                await unitOfWork.CommitAsync();
                return this.RedirectToLocalAction(a => a.EditAsync, new
                {
                    area = Constants.FiConfigurationArea,
                    fiSlug = model.FiSlug,
                    msg = errorNotificationService.GetSuccessfulDatabaseActionMessage(fiService.RecordName, EntityState.Modified),
                    msgType = ToastMsgTypeEnum.Success,
                });
            }
            return await GetEditPageForPostbackAsync(model, result.ErrorMessage);
        }
        catch (Exception ex)
        {
            var errMsg = errorNotificationService.GetDatabaseErrorMessage(ex, model);
            return await GetEditPageForPostbackAsync(model, errMsg);
        }
    }

    private async Task<ViewResult> GetEditPageForPostbackAsync(FiInfoPostingViewModel userSubmittedModel, string? errorMessage = null)
    {
        var dto = userSubmittedModel.ToDto();
        var model = FiInfoPageViewModel.FromDto(dto, HttpContext.GetTenant().Slug);
        await PrepareEditPageAsync(model, errorMessage, ToastMsgTypeEnum.Error);
        return View("Edit", model);
    }
}
