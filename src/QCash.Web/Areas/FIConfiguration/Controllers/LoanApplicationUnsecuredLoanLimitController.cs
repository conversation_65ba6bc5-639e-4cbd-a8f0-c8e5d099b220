using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Extensions;
using QCash.Web.Extensions;
using QCash.Service.Core;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Web.Areas.FIConfiguration.Models;
using QCash.Web.Models;

namespace QCash.Web.Areas.FIConfiguration.Controllers;


[Area(Constants.FiConfigurationArea)]
[Route($"{{fiSlug}}/{Constants.FiConfigurationArea}/LoanApplication/UnsecuredLoanLimit")]
public class LoanApplicationUnsecuredLoanLimitController(
    IUnsecuredLoanLimitService unsecuredLoanLimitService,
    IUnitOfWork unitOfWork,
    IErrorNotificationService errorNotificationService,
    ILoanApplicationSettingsService loanApplicationSettingsService
    ) : Controller
{
    [HttpGet]
    public async Task<IActionResult> EditAsync([FromQuery] string? msg, [FromQuery] ToastMsgTypeEnum? msgType)
    {
        var dto = await unsecuredLoanLimitService.GetSettingsAsync(HttpContext.GetTenant().Id, HttpContext.GetTenant().Slug);
        var model = UnsecuredLoanLimitSettingsViewModel.FromDto(dto, HttpContext.GetTenant().Slug);
        PrepareEditPage(model, msg, msgType);
        return View("Edit", model);
    }

    private void PrepareEditPage(UnsecuredLoanLimitSettingsViewModel model, string? msg, ToastMsgTypeEnum? msgType)
    {
        model.NavHeader =
            LoanApplicationSettingsNavHeaderViewModel.FromDto(
                loanApplicationSettingsService.GetNavHeader(HttpContext.GetTenant().Slug),
                Constants.LoanApplicationHeaderTabEnum.UnsecuredLoanLimit);

        if (!string.IsNullOrWhiteSpace(msg))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Error,
            };
        }
    }

    [HttpPost]
    public async Task<IActionResult> EditAsync([FromForm] UnsecuredLoanLimitSettingsViewModel model, [FromRoute] string fiSlug)
    {
        model.CheckRoute(fiSlug);
        var dto = model.ToDto();
        if (!ModelState.IsValid)
        {
            return GetEditPageForPostback(model);
        }
        try
        {
            var result = await unsecuredLoanLimitService.SaveSettingsAsync(dto, HttpContext.GetTenant().Id);
            if (result.IsSuccessful)
            {
                await unitOfWork.CommitAsync();
                var msg = errorNotificationService.GetSuccessfulDatabaseActionMessage(unsecuredLoanLimitService.RecordName,
                    EntityState.Modified);
                return this.RedirectToLocalAction(a => a.EditAsync,
                    new { area = Constants.FiConfigurationArea, fiSlug, msg, msgType = ToastMsgTypeEnum.Success, });
            }
            return GetEditPageForPostback(model, result.ErrorMessage);
        }
        catch (Exception ex)
        {
            var errMsg = errorNotificationService.GetDatabaseErrorMessage(ex, model);
            return GetEditPageForPostback(model, errMsg);
        }
    }

    private ViewResult GetEditPageForPostback(UnsecuredLoanLimitSettingsViewModel userSubmittedModel, string? errorMessage = null)
    {
        var dto = userSubmittedModel.ToDto();
        var model = UnsecuredLoanLimitSettingsViewModel.FromDto(dto, HttpContext.GetTenant().Slug);
        PrepareEditPage(model, errorMessage, ToastMsgTypeEnum.Error);
        return View("Edit", model);
    }
}
