using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Extensions;
using QCash.Web.Extensions;
using QCash.Service.Core;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Web.Areas.FIConfiguration.Models;
using QCash.Web.Models;
using QCash.Web.Models.General;
using Enums = QCash.Service.Models.Core.Enums;

namespace QCash.Web.Areas.FIConfiguration.Controllers;

[Area(Constants.FiConfigurationArea)]
[Route($"{{fiSlug}}/{Constants.FiConfigurationArea}/{{controller}}/{{whiteListIpType:WhiteListIpTypeEnum}}")]
public class WhiteListIpController(
    IWhiteListIpService whiteListIpService,
    IUnitOfWork unitOfWork,
    ILogger<WhiteListIpController> logger,
    IErrorNotificationService errorNotificationService
) : Controller
{
    [HttpGet, Route("List")]
    public IActionResult List([FromRoute] Enums.WhiteListIpTypeEnum whiteListIpType, [FromQuery] string? msg) =>
        View("List", new WhiteListIpListPageViewModel()
        {
            FiSlug = HttpContext.GetTenant().Slug,
            WhiteListIpType = whiteListIpType,
            SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = ToastMsgTypeEnum.Success,
            },
        });

    [HttpGet, Route("GetGridData")]
    public async Task<IActionResult> GetGridDataAsync([DataSourceRequest] DataSourceRequest request, [FromRoute] Enums.WhiteListIpTypeEnum whiteListIpType)
    {
        var data = await whiteListIpService.GetAllAsync(whiteListIpType);
        var result = await data.ToDataSourceResultAsync(request);
        result.Data = result.Data
            .Cast<WhiteListIpDto>()
            .Select(WhiteListIpListItemViewModel.FromDto);
        return Json(result);
    }

    [HttpGet, Route("Create")]
    public IActionResult Create([FromRoute] Enums.WhiteListIpTypeEnum whiteListIpType, string? msg) =>
        View("Edit", WhiteListIpCreatePageViewModel.FromDto(whiteListIpService.CreateBlankItem(whiteListIpType), HttpContext.GetTenant().Slug)
        );

    [HttpPost, Route("Create")]
    public virtual async Task<IActionResult> CreateAsync(WhiteListIpCreatePageViewModel model)
    {
        var dto = model.ToDto();
        whiteListIpService.ValidateModel(dto, ModelState);
        if (!ModelState.IsValid)
        {
            return GetEditPageForPostback(model);
        }
        try
        {
            var result = await whiteListIpService.SaveAsync(dto, HttpContext.GetTenant().Id);
            if (result.IsSuccessful)
            {
                await unitOfWork.CommitAsync();
                return this.RedirectToAction<WhiteListIpController>(a => a.List,
                    new
                    {
                        area = Constants.FiConfigurationArea,
                        FiSlug = HttpContext.GetTenant().Slug,
                        whiteListIpType = model.WhiteListIpType,
                        msg = errorNotificationService.GetSuccessfulDatabaseActionMessage("IP Address", EntityState.Modified),
                    });
            }
            return GetEditPageForPostback(model, result.ErrorMessage);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, $"Error attempting to save a financial institution record");
            return GetEditPageForPostback(model, "Error while saving financial institution information.");
        }
    }

    private ViewResult GetEditPageForPostback(WhiteListIpCreatePageViewModel userSubmittedModel, string? errorMessage = null)
    {
        var dto = userSubmittedModel.ToDto();
        var model = WhiteListIpCreatePageViewModel.FromDto(dto, HttpContext.GetTenant().Slug);
        if (!string.IsNullOrWhiteSpace(errorMessage))
        {
            model.SubmissionNotifications = errorNotificationService.GetNotyErrorNotification(errorMessage);
        }
        return View("Edit", model);
    }

    /// <summary>
    /// Delete action comes from the list page.  If it fails, user will return to the list page.
    /// </summary>
    /// <returns></returns>
    [HttpPost, Route("Delete")]
    public async Task<IActionResult> DeleteAsync([FromRoute] string fiSlug,
        Enums.WhiteListIpTypeEnum whiteListIpType,
        [FromForm] DeleteItemViewModel userSubmittedModel)
    {
        userSubmittedModel.CheckRoute(fiSlug);
        var dto = userSubmittedModel.ToDto();
        var result = await whiteListIpService.DeleteItemAsync(whiteListIpType, dto);
        var msg = result.ErrorMessage;
        if (result.IsSuccessful)
        {
            await unitOfWork.CommitAsync();
            msg = "Item has been deleted.";
        }
        return this.RedirectToAction<WhiteListIpController>(a => a.List, new
        {
            FiSlug = fiSlug,
            area = Constants.FiConfigurationArea,
            msg,
            whiteListIpType,
        });
    }
}
