using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Extensions;
using QCash.Service.Core;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Web.Areas.FIConfiguration.Models;
using QCash.Web.Models;
using QCash.Web.Extensions;

namespace QCash.Web.Areas.FIConfiguration.Controllers;

[Area(Constants.FiConfigurationArea)]
[Route($"{{fiSlug}}/{Constants.FiConfigurationArea}/Invoice/[controller]/[action]")]
public class InvoiceGeneralSettingsController(
    IInvoiceSettingsService invoiceSettingsService,
    IErrorNotificationService errorNotificationService,
    IUnitOfWork unitOfWork
    ) : Controller
{
    public async Task<IActionResult> EditAsync([FromQuery] string? msg, [FromQuery] ToastMsgTypeEnum? msgType)
    {
        var dto = await invoiceSettingsService.GetGeneralInfoAsync(HttpContext.GetTenant().Id);
        var model = InvoiceGeneralSettingsViewModel.FromDto(dto, HttpContext.GetTenant().Slug);
        PrepareEditPage(model, msg, msgType);
        return View("Edit", model);
    }

    private void PrepareEditPage(InvoiceGeneralSettingsViewModel model, string? msg, ToastMsgTypeEnum? msgType)
    {
        model.NavHeader =
            InvoiceSettingsNavHeaderViewModel.FromDto(
                invoiceSettingsService.GetNavHeader(HttpContext.GetTenant().Slug),
                Constants.InvoiceHeaderTabEnum.GeneralTab);
        if (!string.IsNullOrWhiteSpace(msg))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Error,
            };
        }
    }

    [HttpPost]
    public async Task<IActionResult> EditAsync([FromForm] InvoiceGeneralSettingsViewModel model, [FromRoute] string fiSlug)
    {
        var dto = model.ToDto();
        if (!ModelState.IsValid)
        {
            return GetEditPageForPostback(model);
        }
        try
        {
            var result = await invoiceSettingsService.SaveSettingsAsync(dto, HttpContext.GetTenant().Id);
            if (result.IsSuccessful)
            {
                await unitOfWork.CommitAsync();
                return this.RedirectToLocalAction(a => a.EditAsync, new
                {
                    area = Constants.FiConfigurationArea,
                    fiSlug = model.FiSlug,
                    msg = errorNotificationService.GetSuccessfulDatabaseActionMessage(invoiceSettingsService.RecordName, EntityState.Modified),
                    msgType = ToastMsgTypeEnum.Success,
                });
            }
            return GetEditPageForPostback(model, result.ErrorMessage);
        }
        catch (Exception ex)
        {
            var errMsg = errorNotificationService.GetDatabaseErrorMessage(ex, model);
            return GetEditPageForPostback(model, errMsg);
        }
    }

    private ViewResult GetEditPageForPostback(InvoiceGeneralSettingsViewModel userSubmittedModel, string? errorMessage = null, ToastMsgTypeEnum? msgType = ToastMsgTypeEnum.Error)
    {
        var dto = userSubmittedModel.ToDto();
        var model = InvoiceGeneralSettingsViewModel.FromDto(dto, HttpContext.GetTenant().Slug);
        PrepareEditPage(model, errorMessage, msgType);
        return View("Edit", model);
    }
}
