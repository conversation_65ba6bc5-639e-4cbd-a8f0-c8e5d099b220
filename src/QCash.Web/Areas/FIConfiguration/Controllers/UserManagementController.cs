using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Extensions;
using QCash.Service.Core;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.User.Interfaces;
using QCash.Service.Utilities;
using QCash.Web.Areas.FIConfiguration.Models;
using QCash.Web.Models;
using static QCash.Web.Extensions.RedirectToActionExtensions;

namespace QCash.Web.Areas.FIConfiguration.Controllers;

[Area(Constants.FiConfigurationArea)]
[Route($"{{fiSlug}}/{Constants.FiConfigurationArea}/[controller]")]
public class UserManagementController(
    ILogger<UserManagementController> logger,
    IUserService userService,
    IUnitOfWork unitOfWork,
    IErrorNotificationService errorNotificationService,
    IAuthUserService authUserService) : Controller
{
    private object GetRedirectParams(string userName, string msg, ToastMsgTypeEnum msgType) =>
        new
        {
            userName,
            area = Constants.FiConfigurationArea,
            fiSlug = HttpContext.GetTenant().Slug,
            msg,
            msgType,
        };

    [Route("[action]")]
    public IActionResult List([FromQuery] string? msg, [FromQuery] ToastMsgTypeEnum? msgType)
    {
        var model = new BaseViewModel()
        {
            FiSlug = HttpContext.GetTenant().Slug,
            SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Success,
            },
        };
        return View(model);
    }

    [HttpGet, Route("[action]")]
    public async Task<IActionResult> CreateAsync([FromQuery] string? msg, [FromQuery] ToastMsgTypeEnum? msgType = null)
    {
        var dto = await userService.GetBlankDtoToCreateUserAsync();
        return await PrepCreatePageAsync(dto, msg, msgType);
    }

    [HttpGet, Route("[action]/{userName}")]
    public async Task<IActionResult> EditAsync([FromRoute] string userName, [FromQuery] string? msg, [FromQuery] ToastMsgTypeEnum? msgType = null)
    {
        var dto = await GetDtoFromDbAsync(userName);
        if (dto == null)
        {
            return RedirectToAction("List", "UserManagement",
                GetRedirectParams(userName, $"No such user {userName} could be found ", ToastMsgTypeEnum.Error));
        }
        return await PrepEditPageAsync(dto, msg, msgType);
    }

    private async Task<UserDetailsPageDTO?> GetDtoFromDbAsync(string userName)
    {
        if (userName == ItemConstants.CreateItemTag)
        {
            return await userService.GetBlankDtoToCreateUserAsync();
        }
        var dto = await userService.GetUserPageDataAsync(userName);
        return dto;
    }

    private void SetPermissions(UserEditPageViewModel model)
    {
        model.IsSuperUser = authUserService.IsSuperUser();
        model.IsFiManager = authUserService.IsFiManager();
        model.FormIsDisabled = FormShouldBeDisabled(model);
    }

    private void SetPermissions(UserCreatePageViewModel model)
    {
        model.IsSuperUser = authUserService.IsSuperUser();
        model.IsFiManager = authUserService.IsFiManager();
        model.FormIsDisabled = FormShouldBeDisabled(model);
    }

    private bool FormShouldBeDisabled(UserCreatePageViewModel model)
    {
        if (model.EditingSuperUser && !model.IsSuperUser)
        {
            return true;
        }
        if (model.IsFiManager && !model.CanFIMngrEditInputs)
        {
            return true;
        }
        return false;
    }

    private bool FormShouldBeDisabled(UserEditPageViewModel model)
    {
        if (model.EditingSuperUser && !model.IsSuperUser)
        {
            return true;
        }
        if (model.IsFiManager && !model.CanFIMngrEditInputs)
        {
            return true;
        }
        return false;
    }

    [HttpPost, Route("[action]/{userName}")]
    public async Task<ActionResult> EditAsync([FromForm] UserEditPagePostingModel userSubmittedModel,
        [FromRoute] string fiSlug, [FromRoute] string userName)
    {
        userSubmittedModel.CheckRoute(fiSlug, userName);
        var roleChoices = await userService.GetRoleChoicesAsync();
        var dto = userSubmittedModel.ToDTO(roleChoices);
        if (!ModelState.IsValid)
        {
            return await PrepEditPageAsync(dto);
        }
        try
        {
            var result = await userService.SaveAsync(HttpContext.GetTenant().Id, userSubmittedModel.UserName, dto);
            if (!result.IsSuccessful)
            {
                return await PrepEditPageAsync(dto, result.ErrorMessage, ToastMsgTypeEnum.Error);
            }

            await unitOfWork.CommitAsync();
            return SendToPageAfterSave(userSubmittedModel.CreatingUser());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error attempting to save user {UserName}", userName);
            return await PrepEditPageAsync(dto, "Error while saving user");
        }
    }

    [HttpPost, Route("[action]")]
    public async Task<ActionResult> CreateAsync([FromForm] UserCreatePagePostingModel userSubmittedModel, [FromRoute] string fiSlug)
    {
        userSubmittedModel.CheckRoute(fiSlug);
        var roleChoices = await userService.GetRoleChoicesAsync();
        var dto = userSubmittedModel.ToDTO(roleChoices);
        if (!ModelState.IsValid)
        {
            return await PrepCreatePageAsync(dto);
        }
        try
        {
            var result = await userService.SaveAsync(HttpContext.GetTenant().Id, userSubmittedModel.UserName, dto);
            if (!result.IsSuccessful)
            {
                return await PrepCreatePageAsync(dto, result.ErrorMessage, ToastMsgTypeEnum.Error);
            }

            await unitOfWork.CommitAsync();
            return SendToPageAfterSave(userSubmittedModel.CreatingUser());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error attempting to create user");
            return await PrepCreatePageAsync(dto, "Error while creating user");
        }
    }

    private RedirectToActionResult SendToPageAfterSave(bool creatingUser) =>
        this.RedirectToAction<UserManagementController>(a => a.List,
            new
            {
                msg = errorNotificationService.GetSuccessfulDatabaseActionMessage(
                        userService.RecordName,
                        creatingUser ? EntityState.Added : EntityState.Modified),
                msgType = ToastMsgTypeEnum.Success,
            });

    private async Task<ActionResult> PrepEditPageAsync(UserDetailsPageDTO dto, string? message = null, ToastMsgTypeEnum? msgType = null)
    {
        var roles = await PrepPagePopulateDataAsync(dto);
        var model = UserEditPageViewModel.FromDTO(dto, roles, HttpContext.GetTenant().Slug);
        return PrepPageGetResult(message, msgType, model);
    }

    private async Task<ActionResult> PrepCreatePageAsync(UserDetailsPageDTO dto, string? message = null, ToastMsgTypeEnum? msgType = null)
    {
        var roles = await PrepPagePopulateDataAsync(dto);
        var model = UserCreatePageViewModel.FromDTO(dto, roles, HttpContext.GetTenant().Slug);
        if (!string.IsNullOrWhiteSpace(message))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(message),
                MsgType = msgType ?? ToastMsgTypeEnum.Success,
            };
        }
        SetPermissions(model);
        return View(model);
    }

    private ActionResult PrepPageGetResult(string? message, ToastMsgTypeEnum? msgType, UserEditPageViewModel model)
    {
        if (!string.IsNullOrWhiteSpace(message))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(message),
                MsgType = msgType ?? ToastMsgTypeEnum.Success,
            };
        }
        SetPermissions(model);
        return View(model);
    }

    private async Task<List<QListItem<Guid>>> PrepPagePopulateDataAsync(UserDetailsPageDTO dto)
    {
        var roles = await userService.GetRoleChoicesAsync();
        if (!roles.Any())
        {
            ModelState.AddModelError("", "No roles have been defined.");   // Run seed process to create AspNetRoles in db.
        }
        userService.PopulateMetadata(dto, roles);
        return roles;
    }
}
