using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Extensions;
using QCash.Service.Core;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Web.Areas.FIConfiguration.Models;
using QCash.Web.Models;
using QCash.Service.Utilities.Extensions;
using QCash.Web.Extensions;

namespace QCash.Web.Areas.FIConfiguration.Controllers;

[Area(Constants.FiConfigurationArea)]
[Route($"{{fiSlug}}/{Constants.FiConfigurationArea}/LoanApplication/QCashConnect/[action]")]
public class LoanApplicationQCashConnectController(
    IQCashConnectService qCashConnectSettingService,
    ILoanApplicationSettingsService loanApplicationSettingsService,
    IErrorNotificationService errorNotificationService,
    ILookupService lookupService,
    IUnitOfWork unitOfWork
    ) : Controller
{
    [HttpGet]
    public async Task<IActionResult> EditAsync([FromQuery] string? msg, [FromQuery] ToastMsgTypeEnum? msgType)
    {
        var dto = await qCashConnectSettingService.GetSettingsAsync();
        dto.ThrowIfNull();
        var model = QCashConnectSettingsViewModel.FromDto(dto, HttpContext.GetTenant().Slug);
        PrepareEditPage(model, msg, msgType);
        if (!model.NavHeader.ShowNSFSettings)
        {
            return Forbid();
        }
        return View("Edit", model);
    }

    private void PrepareEditPage(QCashConnectSettingsViewModel model, string? msg, ToastMsgTypeEnum? msgType)
    {
        model.NavHeader =
            LoanApplicationSettingsNavHeaderViewModel.FromDto(
                loanApplicationSettingsService.GetNavHeader(HttpContext.GetTenant().Slug),
                Constants.LoanApplicationHeaderTabEnum.QCashConnect);

        model.PreferredDeliveryChoices = lookupService.GetItemsFromEnum<Enums.FraudControlPreferredDeliveryMethod>()
            .ToList();

        if (!string.IsNullOrWhiteSpace(msg))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Error,
            };
        }
    }

    [HttpPost]
    public async Task<IActionResult> EditAsync([FromForm] QCashConnectSettingsViewModel model, [FromRoute] string fiSlug)
    {
        model.CheckRoute(HttpContext.GetTenant().Slug);
        var dto = model.ToDto();
        if (!ModelState.IsValid)
        {
            return GetEditPageForPostback(model);
        }
        try
        {
            var result = await qCashConnectSettingService.SaveSettingsAsync(dto, HttpContext.GetTenant().Id);
            if (result.IsSuccessful)
            {
                await unitOfWork.CommitAsync();
                return this.RedirectToLocalAction(a => a.EditAsync, new
                {
                    area = Constants.FiConfigurationArea,
                    fiSlug,
                    msg = errorNotificationService.GetSuccessfulDatabaseActionMessage(qCashConnectSettingService.RecordName, EntityState.Modified),
                    msgType = ToastMsgTypeEnum.Success,
                });
            }
            return GetEditPageForPostback(model, result.ErrorMessage);
        }
        catch (Exception ex)
        {
            var errMsg = errorNotificationService.GetDatabaseErrorMessage(ex, model);
            return GetEditPageForPostback(model, errMsg);
        }
    }

    private ViewResult GetEditPageForPostback(QCashConnectSettingsViewModel userSubmittedModel, string? errorMessage = null)
    {
        var dto = userSubmittedModel.ToDto();
        var model = QCashConnectSettingsViewModel.FromDto(dto, HttpContext.GetTenant().Slug);
        PrepareEditPage(model, errorMessage, ToastMsgTypeEnum.Error);
        return View("Edit", model);
    }
}
