using System.ComponentModel.DataAnnotations;
using static QCash.Service.Utilities.Extensions.ExceptionExtensions;
using FoolProof.Core;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Utils.Extensions;

namespace QCash.Web.Areas.FIConfiguration.Models;

// This is a viewmodel, so the properties we want to receive from a post cannot be private.
// Suppressing incorrect warnings with the line below.
// ReSharper disable PropertyCanBeMadeInitOnly.Global
// ReSharper disable MemberCanBeProtected.Global
// ReSharper disable MemberCanBePrivate.Global
public class UserCreatePagePostingModel : UserEditPagePostingModel, IValidatableObject
{
    [Required]
    [Display(Name = "Confirm Email")]
    [DataType(DataType.EmailAddress, ErrorMessage = "Invalid Email Address")]
    [EmailAddress(ErrorMessage = "Invalid Email Address")]
    [EqualTo(nameof(Email))]
    public string? EmailConfirm { get; set; } = "";

    [Required]
    [Display(Name = "Password")]
    public required string Password { get; set; }

    [Required]
    [Display(Name = "Confirm Password")]
    [EqualTo(nameof(Password))]
    public required string PasswordConfirm { get; set; }

    public void CheckRoute(string fiSlug) =>
        ThrowIfUnequal(FiSlug, fiSlug, "Potential XSS problem.");

    public override UserDetailsPageDTO ToDTO(List<QListItem<Guid>> roleChoices)
    {
        var model = new UserDetailsPageDTO()
        {
            Id = UserId,
            FirstName = FirstName,
            LastName = LastName,
            Email = Email,
            UserName = UserName,
            PhoneNumber = PhoneNumber,
            IsApproved = IsActive,
            RoleTypeId = RoleTypeId,
            SelectedRoles = GetRolesForDto(roleChoices),
            Password = Password,
            PasswordConfirm = PasswordConfirm,
            TimeStamp = Convert.FromBase64String(TimeStamp ?? ""),
        };
        return model;
    }

    public new IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var baseValidations = base.Validate(validationContext);
        foreach (var valItem in baseValidations)
        {
            yield return valItem;
        }

        var passwordValidator = (PasswordValidator)validationContext.GetService(typeof(PasswordValidator))!;
        var passwordValidationResult = passwordValidator.ValidatePassword(Password);

        if (!passwordValidationResult.Succeeded)
        {
            foreach (var error in passwordValidationResult.Errors)
            {
                yield return new ValidationResult(error);
            }

            // Setting the error message to blank space to prevent the default error message from showing.
            // Password rules will appear in the validation summary
            yield return new ValidationResult(" ", [nameof(Password)]);
        }
    }
}
