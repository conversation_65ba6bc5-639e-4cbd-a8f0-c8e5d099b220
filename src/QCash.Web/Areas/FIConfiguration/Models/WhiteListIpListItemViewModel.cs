using QCash.Service.Models.FIConfiguration;

namespace QCash.Web.Areas.FIConfiguration.Models;

public class WhiteListIpListItemViewModel
{
    public static WhiteListIpListItemViewModel FromDto(WhiteListIpDto dto) =>
        new()
        {
            Id = dto.Id,
            IpAddress = dto.IpAddress,
            Description = dto.Description,
            Port = dto.Port,
            TimeStamp = Convert.ToBase64String(dto.TimeStamp),
        };

    public required Guid Id { get; set; }

    public required int? Port { get; set; }

    public required string Description { get; set; }

    public required string IpAddress { get; set; }

    public required string TimeStamp { get; set; }
}
