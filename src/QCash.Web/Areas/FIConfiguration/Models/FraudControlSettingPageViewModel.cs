using System.ComponentModel.DataAnnotations;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Web.Models;
using static QCash.Service.Utilities.Extensions.ExceptionExtensions;

namespace QCash.Web.Areas.FIConfiguration.Models;

public class FraudControlSettingPageViewModel
{
    public LoanApplicationSettingsNavHeaderViewModel NavHeader { get; set; } =
        LoanApplicationSettingsNavHeaderViewModel.CreateBlank();
    public required Guid FraudControlSettingId { get; init; }
    public required string? FraudControlSettingTimeStamp { get; init; }
    public required Guid FinancialInstitutionId { get; init; }

    [Display(Name = "Fraud Control")]
    public required bool IsEnabled { get; init; }

    [Display(Name = "Preferred Delivery Method")]
    public required string PreferredDelivery { get; init; }

    [Display(Name = "Resend Threshold")]
    public required int ResendThreshold { get; init; }

    [Display(Name = "Submit Threshold")]
    public required int SubmitThreshold { get; init; }

    [Display(Name = "Code Validity Period (minutes)")]
    public required int CodeValidityMinutesThreshold { get; init; }
    public ToastModel? SubmissionNotifications { get; set; }
    public required string FiSlug { get; init; }
    public List<QListItem<Enums.FraudControlPreferredDeliveryMethod>> PreferredDeliveryChoices { get; set; } = [];

    public static FraudControlSettingPageViewModel FromDto(FraudControlSettingsDto dto) => new()
    {
        FiSlug = dto.FiSlug,
        FraudControlSettingId = dto.Id,
        FraudControlSettingTimeStamp = Convert.ToBase64String(dto.TimeStamp),
        FinancialInstitutionId = dto.FinancialInstitutionId,
        IsEnabled = dto.IsEnabled,
        PreferredDelivery = dto.PreferredDelivery,
        ResendThreshold = dto.ResendThreshold,
        SubmitThreshold = dto.SubmitThreshold,
        CodeValidityMinutesThreshold = dto.CodeValidityMinutesThreshold,
    };

    public FraudControlSettingsDto ToDto() => new()
    {
        FiSlug = FiSlug,
        Id = FraudControlSettingId,
        TimeStamp = Convert.FromBase64String(FraudControlSettingTimeStamp ?? ""),
        FinancialInstitutionId = FinancialInstitutionId,
        IsEnabled = IsEnabled,
        PreferredDelivery = PreferredDelivery,
        ResendThreshold = ResendThreshold,
        SubmitThreshold = SubmitThreshold,
        CodeValidityMinutesThreshold = CodeValidityMinutesThreshold,
    };

    public void CheckRoute(string fiSlug) => ThrowIfUnequal(FiSlug, fiSlug, "Potential XSS problem.");
}
