using System.ComponentModel.DataAnnotations;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Utilities;
using static QCash.Service.Models.FIConfiguration.UserManagement.Constants;
using static QCash.Service.Utilities.Extensions.ExceptionExtensions;

// View Models must be able to set the data during a post, so the following recommendations are incorrect:
// ReSharper disable MemberCanBeProtected.Global
// ReSharper disable PropertyCanBeMadeInitOnly.Global
namespace QCash.Web.Areas.FIConfiguration.Models;

public class UserEditPagePostingModel : IValidatableObject
{
    public string FiSlug { get; set; } = null!;

    [Required]
    public Guid UserId { get; set; }

    [Required]
    [Display(Name = "First Name")]
    [RegularExpression(RegexExpressions.LettersAndNumbers, MatchTimeoutInMilliseconds = 1000, ErrorMessage = "First Name should contain only letters and digits")]
    public string FirstName { get; set; } = "";

    [Required]
    [Display(Name = "Last Name")]
    [RegularExpression(RegexExpressions.LettersAndNumbers, MatchTimeoutInMilliseconds = 1000, ErrorMessage = "Last Name should contain only letters and digits")]
    public string LastName { get; set; } = "";

    [Required]
    [Display(Name = "Email Address")]
    [DataType(DataType.EmailAddress, ErrorMessage = "Invalid Email Address")]
    [EmailAddress(ErrorMessage = "Invalid Email Address")]
    public string Email { get; set; } = "";

    [Required]
    [Display(Name = "User name (first initial and last name)")]
    public string UserName { get; set; } = "";

    [Display(Name = "Phone Number")]
    public string? PhoneNumber { get; set; } = "";

    [Display(Name = "Active Status")]
    public bool IsActive { get; set; }

    [Display(Name = "Lockout Status")]
    public bool IsLockedOut { get; set; }

    /// Metadata
    [Display(Name = "Role Type")]
    public Guid RoleTypeId { get; set; }

    [Display(Name = "Date Created")]
    public DateTime? DateCreatedLocal { get; set; }

    [Display(Name = "Date Updated")]
    public DateTime? DateUpdatedLocal { get; set; }

    [Display(Name = "Role Manager Options")]
    public List<QListItemWithSelection<Guid>>? RoleSubTypeManagerOptions { get; set; } = [];

    [Display(Name = "Roles")]
    public List<QListItemWithSelection<Guid>>? RoleSubTypeOtherOptions { get; set; } = [];

    public string? TimeStamp { get; set; } = "";

    public bool CreatingUser() => UserId == Guid.Empty;


    protected static List<QListItemWithSelection<Guid>> GetSubTypeOptions(UserDetailsPageDTO dto, List<QListItem<Guid>> roleChoices, List<string> textOptions)
    {
        var optionsWithSelection = from roleOption in roleChoices
                .Where(r => textOptions.Contains(r.Text, StringComparer.Ordinal))
                                   from sel in dto.SelectedRoles.Where(a => a.Text == roleOption.Text).DefaultIfEmpty()
                                   select new { roleOption, sel };
        var result = optionsWithSelection.Select(r => new QListItemWithSelection<Guid>
        {
            Text = r.roleOption.Text,
            Value = r.roleOption.Value,
            IsChecked = r.sel != null,
        }).ToList();
        return result;
    }

    public virtual UserDetailsPageDTO ToDTO(List<QListItem<Guid>> roleChoices)
    {
        var model = new UserDetailsPageDTO()
        {
            Id = UserId,
            FirstName = FirstName,
            LastName = LastName,
            Email = Email,
            UserName = UserName,
            PhoneNumber = PhoneNumber,
            IsApproved = IsActive,
            RoleTypeId = RoleTypeId,
            SelectedRoles = GetRolesForDto(roleChoices),
            Password = null,
            PasswordConfirm = null,
            EmailConfirm = null,
            TimeStamp = Convert.FromBase64String(TimeStamp ?? ""),
        };
        return model;
    }

    internal List<QListItem<Guid>> GetRolesForDto(List<QListItem<Guid>> roleChoices)
    {
        var updatedRoles = new List<QListItem<Guid>>();
        var roleTypeItem = roleChoices.FirstOrDefault(a => a.Value == RoleTypeId);
        if (roleTypeItem?.Text == SuperUserAbrv || roleTypeItem?.Text == SystemAdminAbrv)
        {
            // These two items only have a top-level role type, and no sub-items
            updatedRoles.AddRange(
                roleChoices.Where(a => a.Value == RoleTypeId)
            );
        }
        else
        {
            // These two role types are more of a category.  We don't save the role type.  We save the child-level roles.
            if (RoleTypeId == ManagerRoleTypeGuid)
            {
                updatedRoles.AddRange(
                    RoleSubTypeManagerOptions?
                        .Where(a => a.IsChecked) ?? []
                );
            }
            else if (RoleTypeId == OtherRoleTypeGuid)
            {
                updatedRoles.AddRange(
                    RoleSubTypeOtherOptions?
                        .Where(a => a.IsChecked) ?? []
                );
            }
        }

        return updatedRoles;
    }

    public void CheckRoute(string fiSlug, string userName)
    {
        ThrowIfUnequal(FiSlug, fiSlug, "Potential XSS problem.");
        ThrowIfUnequal(UserName, userName, "Potential XSS problem.");
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        var managerRoleSelected = RoleSubTypeManagerOptions?.Any(a => a.IsChecked) ?? false;
        var otherRoleSelected = RoleSubTypeOtherOptions?.Any(a => a.IsChecked) ?? false;
        if ((RoleTypeId == ManagerRoleTypeGuid && !managerRoleSelected) || (RoleTypeId == OtherRoleTypeGuid && !otherRoleSelected))
        {
            yield return new ValidationResult("User needs to have at least one role selected.", [nameof(RoleTypeId)]);
        }
    }
}
