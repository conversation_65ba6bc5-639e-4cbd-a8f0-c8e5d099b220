using Microsoft.AspNetCore.Mvc;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Web.Models;
using static QCash.Service.Models.FIConfiguration.UserManagement.Constants;

namespace QCash.Web.Areas.FIConfiguration.Models;

[Bind]
public class UserEditPageViewModel : UserEditPagePostingModel
{
    public bool FormIsDisabled { get; set; } = true;
    public bool IsSuperUser { get; set; }
    public bool IsFiManager { get; set; }
    public ToastModel? SubmissionNotifications { get; set; }
    public bool CanFIMngrEditInputs { get; internal init; }

    public bool EditingSuperUser { get; internal init; }
    public bool CanUpdateRoles { get; init; } = true;

    public string CanUpdateRolesExplanation { get; init; } = "";

    public List<QListItem<Guid>> RoleTypeOptions { get; init; } = [];
    public static UserEditPageViewModel FromDTO(UserDetailsPageDTO dto, List<QListItem<Guid>> roles, string fiSlug)
    {
        var result = new UserEditPageViewModel()
        {
            UserId = dto.Id,
            FirstName = dto.FirstName,
            LastName = dto.LastName,
            Email = dto.Email,
            UserName = dto.UserName,
            PhoneNumber = dto.PhoneNumber,
            DateUpdatedLocal = dto.DateUpdatedLocal,
            DateCreatedLocal = dto.DateCreatedLocal,
            IsActive = dto.IsApproved,
            IsLockedOut = dto.IsLockedOut,
            // Metadata:
            EditingSuperUser = dto.EditingSuperUser,
            CanFIMngrEditInputs = dto.CanFIMngrEditInputs,
            RoleTypeId = dto.RoleTypeId,
            CanUpdateRoles = dto.CanUpdateRoles?.IsSuccessful ?? false,
            CanUpdateRolesExplanation = dto.CanUpdateRoles?.ErrorMessage ?? "",
            RoleSubTypeManagerOptions = GetSubTypeOptions(dto, roles, [DeManagerAbrv, FiManagerAbrv]),
            RoleSubTypeOtherOptions = GetSubTypeOptions(dto, roles, [SupportUserAbrv, ReportRoleAbrv, RoleAdminAbrv, QcfFinanceAbrv]),
            RoleTypeOptions = dto.RoleTypeOptions,
            FiSlug = fiSlug,
            TimeStamp = Convert.ToBase64String(dto.TimeStamp),
        };
        return result;
    }

}
