using System.ComponentModel.DataAnnotations;
using QCash.Service.Models.FIConfiguration;
using QCash.Web.Models;

namespace QCash.Web.Areas.FIConfiguration.Models;

public class FiGeneralInfoPostingViewModel : BaseViewModel
{
    public Guid SettingId { get; init; }
    public Guid FinancialInstitutionId { get; init; }
    public required string SettingTimeStamp { get; init; }

    [Display(Name = "Monitor Account")]
    public required string MonitorAccount { get; init; }

    [Display(Name = "Two factor authentication")]
    public required bool UseTwoFactorAuthentication { get; init; }

    [Display(Name = "Cache when core down")]
    public required bool CacheFailedCoreResponses { get; init; }

    [Display(Name = "Core check interval (seconds):")]
    public required int? CoreCheckInterval { get; init; }


    public FiGeneralInfoDto ToDto() =>
        new()
        {
            FiSlug = FiSlug,
            SettingId = SettingId,
            SettingTimeStamp = Convert.FromBase64String(SettingTimeStamp),
            FinancialInstitutionId = FinancialInstitutionId,
            MonitorAccount = MonitorAccount,
            CoreCheckInterval = CoreCheckInterval,
            CacheFailedCoreResponses = CacheFailedCoreResponses,
            UseTwoFactorAuthentication = UseTwoFactorAuthentication,
        };

}
