using System.ComponentModel.DataAnnotations;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Web.Models;
using Enums = QCash.Service.Models.DecisionEngine.Enums;
using static QCash.Service.Utilities.Extensions.ExceptionExtensions;

namespace QCash.Web.Areas.FIConfiguration.Models;


// This is a viewmodel, so the properties we want to receive from a post cannot be private.
// Suppressing incorrect warnings with the line below.
// ReSharper disable PropertyCanBeMadeInitOnly.Global
// ReSharper disable MemberCanBeProtected.Global
// ReSharper disable MemberCanBePrivate.Global
public class CalcEngineViewModel : BaseViewModel
{
    public required Guid CalcEngineSettingId { get; init; }
    public required string? CalcEngineSettingTimeStamp { get; init; }

    [Display(Name = "Round to Factor")]
    public required string RoundToFactor { get; init; }

    [Display(Name = "Amort Method")]
    public required string AmortMethod { get; init; }
    public List<QListItem<Enums.CalcEngineAmortMethodEnum>> AmortMethodChoices { get; set; } = [];
    public List<QListItem<Enums.CalcEngineRoundToIndicatorEnum>> RoundToFactorChoices { get; set; } = [];
    public required Guid FinancialInstitutionId { get; init; }

    public LoanApplicationSettingsNavHeaderViewModel NavHeader { get; set; } =
        LoanApplicationSettingsNavHeaderViewModel.CreateBlank();

    public static CalcEngineViewModel FromDto(CalcEngineSettingsDto dto, string slug) =>
        new()
        {
            AmortMethod = dto.AmortMethod,
            RoundToFactor = dto.RoundToFactor,
            FiSlug = slug,
            FinancialInstitutionId = dto.FinancialInstitutionId,
            CalcEngineSettingId = dto.Id,
            CalcEngineSettingTimeStamp = Convert.ToBase64String(dto.TimeStamp),
        };

    public CalcEngineSettingsDto ToDto() =>
        new()
        {
            Id = CalcEngineSettingId,
            FinancialInstitutionId = FinancialInstitutionId,
            AmortMethod = AmortMethod,
            TimeStamp = Convert.FromBase64String(CalcEngineSettingTimeStamp ?? ""),
            RoundToFactor = RoundToFactor,
        };

    public void CheckRoute(string fiSlug) =>
        ThrowIfUnequal(FiSlug, fiSlug, "Potential XSS problem.");
}
