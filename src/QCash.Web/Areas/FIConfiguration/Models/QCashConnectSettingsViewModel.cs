using System.ComponentModel.DataAnnotations;
using QCash.Service.Models;
using QCash.Service.Models.LoanApplication;
using QCash.Web.Models;
using Enums = QCash.Service.Models.FIConfiguration.Enums;
using static QCash.Service.Utilities.Extensions.ExceptionExtensions;

namespace QCash.Web.Areas.FIConfiguration.Models;

public class QCashConnectSettingsViewModel
{
    public required string FiSlug { get; init; }
    public required Guid FraudControlSettingId { get; init; }
    public required string? FraudControlSettingTimeStamp { get; init; }

    [Display(Name = "QCash Connect")]
    public required bool IsFraudControlEnabled { get; init; }

    [Display(Name = "Preferred Delivery Method")]
    public required string PreferredDelivery { get; init; }
    public required string? Location { get; init; }

    [Display(Name = "Teller Id")]
    public required string? TellerId { get; init; }

    [Display(Name = "Resend Threshold")]
    public required int ResendThreshold { get; init; }

    [Display(Name = "Submit Threshold")]
    public required int SubmitThreshold { get; init; }

    [Display(Name = "Code Validity Period (minutes)")]
    public required int CodeValidityMinutesThreshold { get; init; }

    [Display(Name = "Threshold Exceeded Block Period (hours)")]
    public required int ThresholdExceededBlockPeriod { get; init; }
    // Metadata
    public LoanApplicationSettingsNavHeaderViewModel NavHeader { get; set; } =
        LoanApplicationSettingsNavHeaderViewModel.CreateBlank();
    public List<QListItem<Enums.FraudControlPreferredDeliveryMethod>> PreferredDeliveryChoices { get; set; } = [];
    public ToastModel? SubmissionNotifications { get; set; }

    public static QCashConnectSettingsViewModel FromDto(QCashConnectSettingsDto dto, string fiSlug) =>
        new()
        {
            FraudControlSettingId = dto.FraudControlSettingId,
            FraudControlSettingTimeStamp = Convert.ToBase64String(dto.FraudControlSettingTimeStamp),
            PreferredDelivery = dto.PreferredDelivery,
            IsFraudControlEnabled = dto.IsFraudControlEnabled,
            FiSlug = fiSlug,
            Location = dto.Location,
            TellerId = dto.TellerId,
            ResendThreshold = dto.ResendThreshold,
            SubmitThreshold = dto.SubmitThreshold,
            CodeValidityMinutesThreshold = dto.CodeValidityMinutesThreshold,
            ThresholdExceededBlockPeriod = dto.ThresholdExceededBlockPeriod,
        };

    public QCashConnectSettingsDto ToDto() =>
        new()
        {
            FraudControlSettingId = FraudControlSettingId,
            FraudControlSettingTimeStamp = Convert.FromBase64String(FraudControlSettingTimeStamp ?? ""),
            PreferredDelivery = PreferredDelivery,
            IsFraudControlEnabled = IsFraudControlEnabled,
            Location = Location,
            TellerId = TellerId,
            ResendThreshold = ResendThreshold,
            SubmitThreshold = SubmitThreshold,
            CodeValidityMinutesThreshold = CodeValidityMinutesThreshold,
            ThresholdExceededBlockPeriod = ThresholdExceededBlockPeriod,
        };

    public void CheckRoute(string fiSlug) =>
        ThrowIfUnequal(FiSlug, fiSlug, "Potential XSS problem.");
}
