@using QCash.Web.Areas.MemberInterface.Controllers
@using QCash.Web.Areas.MemberInterface.Models
@using QCash.Web.Models
@using QCash.Web.Utils
@using Constants = QCash.Web.Areas.MemberInterface.Models.Constants
@model QCash.Web.Areas.MemberInterface.Models.TextLandingPageViewModel

@{ Layout = "_StandardLayout"; }

<div class="container">
    @await Html.PartialAsync("_TitleRow", new TitleViewModel
    {
        Title = "Member Interface Text",
    })

    <div style="margin-top: 30px; padding-top:5px; padding-bottom: 20px">
        @(Html.Kendo().Grid<TextLandingPageListItemViewModel>()
            .Name("mitext-list-grid")
            .Columns(columns =>
            {
                columns.Template("<div hidden='hidden' data-fieldname='Id'>#= Id #</div>");
                columns.Bound(t => t.FieldName).Title("Field")
                    .ClientTemplate("<div class='text-truncate-container'>#= FieldName #</div>");
                columns.Bound(t => t.Value).Title("Value")
                    .ClientTemplate("<div class='text-truncate-container'>#= Value #</div>");
            })
            .Sortable()
            .Pageable(pageable => pageable
                .ButtonCount(TableSettings.DefaultButtonCount)
                .Refresh(TableSettings.ShowRefreshButton)
                .PageSizes(TableSettings.DefaultPageSizes))
            .DataSource(ds => ds
                .WebApi()
                .Model(m => { m.Id(p => p.Id); })
                .Read(read => read.Url(
                    Url.Action<TextListController>(u => u.GetAsync, new { area = Constants.MemberInterfaceArea, })))
                .Sort(sort => { sort.Add(nameof(TextLandingPageListItemViewModel.FieldName)); })
            )
            )
        )
    </div>
</div>

 @await Html.PartialAsync("_ToastOnPageLoad", Model.SubmissionNotifications)