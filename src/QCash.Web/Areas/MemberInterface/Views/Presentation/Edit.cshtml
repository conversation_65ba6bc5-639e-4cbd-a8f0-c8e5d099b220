@using Humanizer
@using QCash.Service.Utilities.Extensions
@using QCash.Web.Models
@model QCash.Web.Areas.MemberInterface.Models.PresentationPageViewModel

@{ Layout = "_StandardLayout"; }

<div class="container">
    <form id="presentationForm" method="post" class="custom needs-validation"
          asp-controller="Presentation" asp-action="Edit" novalidate >
        @Html.AntiForgeryToken()
        @Html.HiddenFor(a => Model.MemberInterfacePresentationId)
        @Html.HiddenFor(a => Model.MemberInterfacePresentationTimeStamp)
        @Html.HiddenFor(a => Model.ShouldClearLogo)

        @await Html.PartialAsync("_TitleRow", new TitleViewModel
        {
            Title = "Presentation",
            EnableActionButton = false,
        })
        @await Html.PartialAsync("_ValidationSummary", Model)

        <div class="rounded-box">
            <div class="row">
                <div class="col-6">
                    <span class="form-label">Logo Upload</span>
                    @(Html.Kendo().Upload()
                        .Name("LogoFiles")
                        .Multiple(false)
                        .Enable(true)
                        .Validation(validation =>
                        {
                            validation.AllowedExtensions(QCash.Web.Areas.MemberInterface.Models.Constants.LogoFileTypesAllowed);
                            validation.MaxFileSize(QCash.Service.Constants.MaxFileSize);
                        })
                        .HtmlAttributes(new
                        {
                            @accept = string.Join(',', QCash.Web.Areas.MemberInterface.Models.Constants.LogoFileTypesAllowed),
                            @class = "form-control",
                        })
                        .Events(events => events
                            .Select(@<text>function(e) { uploadFileSelect(e); }</text>)
                            )
                    )
                    <button id="clearLogoButton" type="submit" class="btn btn-primary button-large" onclick="return removeImage();" style="width:150px; margin-top:3px">
                        Clear Logo
                    </button>
                </div>
                <div class="col-6">
                    @Html.HiddenFor(m => m.LogoLink, new { id = "hfLogoLink" })
                    @Html.Image(Url.Content(Model.LogoLink), "Logo", new { id = "imgLogoLink", style = "max-width:100%; width: auto;" })
                </div>
            </div>
            <hr/>
            <div class="row">
                <div class="col-6">
                    @Html.QDropDownListFor(a => a.BodyFont, Model.BodyFontChoices)
                </div>
            </div>
            <hr/>
            <div class="row">
                <div class="col-4">
                    @Html.QColorPickerFor(a => a.FirebirdPrimaryColor)
                </div>
                <div class="col-4">
                    @Html.QColorPickerFor(a => a.FirebirdSecondaryColor)
                </div>
            </div>
        </div>

        @await Html.PartialAsync("_SubmitButton", new SubmitButtonViewModel
        {
            FormIsEnabled = !Model.MemberInterfacePresentationId.Equals(Guid.Empty),
        })
    </form>
</div>
@await Html.PartialAsync("_ToastOnPageLoad", Model.SubmissionNotifications)

<script>
    function uploadFileSelect(e) {
        $.each(e.files, function (index, value) {
            if(!!value.validationErrors && value.validationErrors.length > 0) {
                let msg = null;
                if (value.validationErrors[0] === "invalidFileExtension") {
                    msg = 'Only @(QCash.Web.Areas.MemberInterface.Models.Constants.LogoFileTypesAllowed.Humanize("or")) files can be uploaded.';
                } else if (value.validationErrors[0] === "invalidMaxFileSize") {
                    msg = 'File size is too large.  Max file size is @(QCash.Service.Constants.MaxFileSize).';
                }
                if (msg !== null) {
                    noty({text: msg, layout: "topCenter", type: 'error', timeout: 3000});
                } else {
                    console.log("File: " + value.name + " Error: " + value.validationErrors[0]);
                }
            }
        });
    }

    function removeImage() {
        $("#imgLogoLink").attr("src", "@(Url.Content(Model.DefaultLogoUrl))");
        $("#ShouldClearLogo").val("True");

        const uploadControl =  $("#LogoFiles").data("kendoUpload");
        uploadControl.clearAllFiles();

        return false;
    }
</script>
