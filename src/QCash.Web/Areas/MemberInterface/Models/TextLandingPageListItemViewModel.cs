using System.Web;
using QCash.Service.Models.MemberInterface;

namespace QCash.Web.Areas.MemberInterface.Models;

public class TextLandingPageListItemViewModel
{
    public required Guid Id { get; init; }
    public required string Value { get; init; }
    public required string FieldName { get; init; }

    public static TextLandingPageListItemViewModel FromDto(TextLandingPageListItemDto input) => new()
    {
        Id = input.Id,
        FieldName = HttpUtility.HtmlEncode(input.FieldName),
        Value = HttpUtility.HtmlEncode(input.Value),
    };
}
