using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc;
using QCash.Service.Models.MemberInterface;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.MemberInterface.Interfaces;
using QCash.Web.Areas.MemberInterface.Models;
using QCash.Web.Models;

namespace QCash.Web.Areas.MemberInterface.Controllers;

[Area(Constants.MemberInterfaceArea)]
public class TextListController(
    IMemberInterfaceTextService memberInterfaceTextService,
    IErrorNotificationService errorNotificationService
    ) : Controller
{
    public IActionResult List([FromQuery] string? msg, [FromQuery] ToastMsgTypeEnum? msgType = null)
    {
        var model = new TextLandingPageViewModel();
        if (!string.IsNullOrWhiteSpace(msg))
        {
            model.SubmissionNotifications = new ToastModel
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Error,
            };
        }
        return View(model);
    }

    [HttpGet]
    public async Task<IActionResult> GetAsync([DataSourceRequest] DataSourceRequest request, string lookupTypeName)
    {
        // TODO: sanitization needed here!  Raw HTML is coming from the database and it may not be safe.
        var result = await memberInterfaceTextService.GetAllAsync(request);
        result.Data = result.Data
            .Cast<TextLandingPageListItemDto>()
            .Select(TextLandingPageListItemViewModel.FromDto);
        return Json(result);
    }
}
