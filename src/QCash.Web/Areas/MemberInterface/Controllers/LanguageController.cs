using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Extensions;
using QCash.Service.Core;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.MemberInterface.Interfaces;
using QCash.Web.Areas.MemberInterface.Models;
using QCash.Web.Areas.MemberInterface.Models.Language;
using QCash.Web.Models;
using static QCash.Service.Utilities.Extensions.ExceptionExtensions;

namespace QCash.Web.Areas.MemberInterface.Controllers;

[Area(Constants.MemberInterfaceArea)]
public class LanguageController(
    ILanguageService languageService,
    IMemberInterfaceLanguageService memberInterfaceLanguageService,
    IUnitOfWork unitOfWork,
    ILogger<LanguageController> logger,
    IErrorNotificationService errorNotificationService) : Controller
{
    public async Task<IActionResult> IndexAsync([FromQuery] string? msg, [FromQuery] ToastMsgTypeEnum? msgType = null)
    {
        var model = new LanguagePageViewModel()
        {
            FiSlug = HttpContext.GetTenant().Slug,
            Languages = [.. (await languageService.GetLanguagesAsync()).Select(LanguageItemViewModel.FromDto)],
        };
        if (!string.IsNullOrWhiteSpace(msg))
        {
            model.SubmissionNotifications = new ToastModel()
            {
                Message = errorNotificationService.GetNotyErrorNotification(msg),
                MsgType = msgType ?? ToastMsgTypeEnum.Error,
            };
        }
        return View(model);
    }

    [HttpPost]
    [Route("{fiSlug}/[controller]/UpdateSupportedLanguage")]
    public async Task<IActionResult> UpdateSupportedLanguageAsync([FromForm] LanguageItemViewModel model, [FromRoute] string fiSlug)
    {
        ThrowIfUnequal(fiSlug, HttpContext.GetTenant().Slug, "Potential XSS problem.");

        if (!ModelState.IsValid)
        {
            return RedirectToAction("Index", routeValues: new
            {
                msg = errorNotificationService.DefaultErrorMessage,
                msgType = ToastMsgTypeEnum.Error,
            });
        }

        try
        {
            var dto = model.LanguageSupportStatus.ToDto();
            var result = await memberInterfaceLanguageService.UpdateLanguageSupportStatusAsync(HttpContext.GetTenant().Id, dto);
            if (result.IsSuccessful)
            {
                await unitOfWork.CommitAsync();
                return RedirectToAction("Index", routeValues: new
                {
                    msg = errorNotificationService.GetSuccessfulDatabaseActionMessage(memberInterfaceLanguageService.RecordName, EntityState.Modified),
                    msgType = ToastMsgTypeEnum.Success,
                });
            }
            return RedirectToAction("Index", routeValues: new
            {
                msg = result.ErrorMessage ?? errorNotificationService.DefaultErrorMessage,
                msgType = ToastMsgTypeEnum.Error,
            });
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Error attempting to update supported language");
            return RedirectToAction("Index", routeValues: new
            {
                msg = errorNotificationService.GetDatabaseErrorMessage(ex, model),
                msgType = ToastMsgTypeEnum.Error,
            });
        }
    }
}
