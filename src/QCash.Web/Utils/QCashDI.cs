using System.Net.Http.Headers;
using Azure.Identity;
using Azure.Messaging.ServiceBus;
using QCash.LoanApplication.Interfaces;
using QCash.Service;
using QCash.Service.BusinessLogic.EligibilityRules;
using QCash.Service.BusinessLogic.InvoiceGenerator;
using QCash.Service.BusinessLogic.PricingScenarioCalculator;
using QCash.Service.BusinessLogic.Validators.InvoicePlan;
using QCash.Service.BusinessLogic.Validators.MemberExclusions;
using QCash.Service.BusinessLogic.Validators.PricingScenario;
using QCash.Service.BusinessLogic.Validators.ProductsExclusions;
using QCash.Service.CoreProvider;
using QCash.Service.FileSystem;
using QCash.Service.Infrastructure;
using QCash.Service.MicroBuilt;
using QCash.Service.Reports;
using QCash.Service.Reports.DataSources;
using QCash.Service.Reports.Interfaces;
using QCash.Service.Services;
using QCash.Service.Services.DecisionEngine;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.FIConfiguration;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Helpers;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.LoanApplication;
using QCash.Service.Services.LoanApplication.Interfaces;
using QCash.Service.Services.MemberInterface;
using QCash.Service.Services.MemberInterface.Interfaces;
using QCash.Service.Services.Product;
using QCash.Service.Services.Product.Interfaces;
using QCash.Service.Services.Reports;
using QCash.Service.Services.Reports.Interfaces;
using QCash.Service.Services.User;
using QCash.Service.Services.User.Interfaces;
using QCash.Utils.Core;
using QCash.Utils.Settings;

namespace QCash.Web.Utils;

// ReSharper disable once InconsistentNaming
public static class QCashDI
{
    public static IHostApplicationBuilder ConfigureQCashServices(this IHostApplicationBuilder builder)
    {
        builder.Services.AddScoped<IFiQueries, FiQueries>();
        builder.Services.AddScoped<ITenantService, TenantService>();
        builder.Services.AddScoped<IFiService, FiService>();
        builder.Services.AddScoped<IUserService, UserService>();
        builder.Services.AddScoped<IProductService, ProductService>();
        builder.Services.AddScoped<ILookupService, LookupService>();
        builder.Services.AddScoped<ILookupQueries, LookupQueries>();
        builder.Services.AddScoped<IErrorNotificationService, ErrorNotificationService>();
        builder.Services.AddScoped<IGuidExtensionService, GuidExtensionService>();
        builder.Services.AddScoped<IPermissionDebuggingService, PermissionDebuggingService>();
        builder.Services.AddScoped<ISystemClockServiceBase, SystemClockServiceBase>();
        builder.Services.AddScoped<ISystemClockService, SystemClockService>();
        builder.Services.AddScoped<IProductQueries, ProductQueries>();
        builder.Services.AddScoped<IRoleService, RoleService>();
        builder.Services.AddScoped<IAuthUserService, AuthUserService>();
        builder.Services.AddScoped<ILookupListService, LookupListService>();
        builder.Services.AddScoped<ILookupItemService, LookupItemService>();
        builder.Services.AddScoped<IDecisionEngineSettingsService, DecisionEngineSettingsService>();
        builder.Services.AddScoped<IEfPocoService, EfPocoService>();
        builder.Services.AddScoped<ILanguageService, LanguageService>();
        builder.Services.AddScoped<IDETrackingRecordDataMappingService, DETrackingRecordDataMappingService>();
        builder.Services.AddScoped<IManageRestrictionsPageService, ManageRestrictionsPageService>();
        builder.Services.AddScoped<ICheckingAccountService, CheckingAccountService>();
        builder.Services.AddScoped<ISavingAccountService, SavingAccountService>();
        builder.Services.AddScoped<IDecisionEngineParametersProvider, CoreProviderService>();
        builder.Services.AddScoped<IScoreTypeService, ScoreTypeService>();
        builder.Services.AddScoped<IDvSettingGenericService, DvSettingGenericService>();
        builder.Services.AddScoped<IIncludedNumberOfElectronicTransactionsTellerIdService, IncludedNumberOfElectronicTransactionsTellerIdService>();
        builder.Services.AddScoped<IFiSettingService, FiSettingService>();
        builder.Services.AddScoped<IReportQueries, ReportQueries>();
        builder.Services.AddScoped<IApplicationLogService, ApplicationLogService>();
        builder.Services.AddScoped<IAuditLogService, AuditLogService>();
        builder.Services.AddScoped<ISsoTestSetupService, SsoTestSetupService>();
        builder.Services.AddScoped<IMemberIdentifiersSettingsService, MemberIdentifiersSettingsService>();
        builder.Services.AddScoped<IFinancialInstitutionConfigurationService, FinancialInstitutionConfigurationService>();
        builder.Services.AddScoped<ILoanApplicationService, LoanApplicationService>();
        builder.Services.AddScoped<ILoanApplicationHelper, LoanApplicationHelper>();
        builder.Services.AddScoped<ILoanApplicationStepsService, LoanApplicationStepsService>();
        builder.Services.AddScoped<IDecisionHelperService, DecisionHelperService>();
        builder.Services.AddScoped<IDecisionManagerService, DecisionManagerService>();
        builder.Services.AddScoped<IDecisionEngineService, DecisionEngineService>();
        builder.Services.AddScoped<IEligibilityRulesRunner, EligibilityRulesRunner>();
        builder.Services.AddScoped<IMemberExclusionsValidatorRunner, MemberExclusionsValidatorRunner>();
        builder.Services.AddScoped<IProductsExclusionsValidatorRunner, ProductsExclusionsValidatorRunner>();
        builder.Services.AddScoped<IMemberProcessInterfaceService, MemberProcessInterfaceService>();
        builder.Services.AddScoped<IMemberInterfaceHelper, MemberInterfaceHelper>();
        builder.Services.AddScoped<INotificationService, NotificationService>();
        builder.Services.AddScoped<IPaymentOptionService, PaymentOptionService>();
        builder.Services.AddScoped<IGlobalContainer, FileSystemGlobalContainer>();  //note - once AzureGlobalContainer is migrated, need some branching logic here
        builder.Services.AddTransient<ISmsClient, SmsClient>();
        builder.Services.AddTransient<IMailSender, MailSender>();
        builder.Services.AddScoped<IFinancialInstitutionService, FinancialInstitutionService>();
        builder.Services.AddScoped<IMicroBuiltService, MicroBuiltService>();
        builder.Services.AddScoped<ITaxIdService, TaxIdService>();
        builder.Services.AddScoped<ITokenService, TokenService>();
        builder.Services.AddSingleton<ServiceBusClient>(_ =>
        {
            if (builder.Environment.IsDevelopment())
            {
                var localConnectionString = builder.Configuration.GetValue<string>("QCashApplication:RetryServiceServiceConnectionString", "");
                return new ServiceBusClient(localConnectionString);
            }

            var retryServiceServiceBusNamespace = builder.Configuration.GetValue<string>("QCashApplication:RetryServiceServiceBusNamespace", "");
            var azureTenantId = builder.Configuration.GetValue<string>("QCashApplication:AzureTenantId", "defaultTenantId");
            var azureClientId = builder.Configuration.GetValue<string>("QCashApplication:AzureClientId", "");
            var azureClientSecret = builder.Configuration.GetValue<string>("QCashApplication:AzureClientSecret", "");
            var tokenCredential = new ClientSecretCredential(azureTenantId, azureClientId, azureClientSecret);
            return new ServiceBusClient(retryServiceServiceBusNamespace, tokenCredential);
        });
        builder.Services.AddSingleton<CoreProviderRetryService>();
        builder.Services.AddSingleton<ICoreProviderRetryService>(provider => provider.GetRequiredService<CoreProviderRetryService>());
        builder.Services.AddHostedService<CoreProviderRetryService>(provider => provider.GetRequiredService<CoreProviderRetryService>());
        builder.Services.AddScoped<ICoreProvider, CoreProviderService>();
        builder.Services.AddScoped<ICoreProvidersService, CoreProvidersService>();
        builder.Services.AddScoped<ICoreConnectionService, CoreConnectionService>();
        builder.Services.AddScoped<ErrorHelper>();
        builder.Services.AddScoped<IWhiteListIpService, WhiteListIpService>();
        builder.Services.AddScoped<IIPAddressService, IPAddressService>();
        builder.Services.AddScoped<ICalcEngineService, CalcEngineService>();
        builder.Services.AddScoped<ILoanApplicationSettingsService, LoanApplicationSettingsService>();
        builder.Services.AddScoped<INSFService, NSFService>();
        builder.Services.AddScoped<IFinancialCoachingService, FinancialCoachingService>();
        builder.Services.AddScoped<ILoanExclusionService, LoanExclusionService>();
        builder.Services.AddScoped<IFraudControlService, FraudControlService>();
        builder.Services.AddScoped<IInsuranceProductsService, InsuranceProductsService>();
        builder.Services.AddScoped<IUnsecuredLoanLimitService, UnsecuredLoanLimitService>();

        builder.Services.AddScoped<IReportDownloadService, ReportDownloadService>();
        builder.Services.AddScoped<IReportDownloadQueries, ReportDownloadQueries>();
        builder.Services.AddScoped<IReportHelper, ReportHelper>();
        builder.Services.AddScoped<LoanApplicationDetailedReportDataSource>();
        builder.Services.AddScoped<IInvoiceSettingsService, InvoiceSettingsService>();
        builder.Services.AddScoped<ITemplateService, TemplateService>();
        var qcashAppSettings = builder.Configuration.GetSection(ApplicationOptions.SectionName)
            .Get<ApplicationOptions>();
        if (qcashAppSettings?.UseFileProvider ?? false)
            builder.Services.AddScoped<IFileSystemProvider, FileSystemProvider>();
        else
            builder.Services.AddScoped<IFileSystemProvider, AzureFileSystemProvider>();
        builder.Services.AddScoped<IFilenameTemplateService, FilenameTemplateService>();
        builder.Services.AddScoped<IProductDecisionEngineSettingsService, ProductDecisionEngineSettingsService>();
        builder.Services.AddScoped<IModelManagerService, ModelManagerService>();
        builder.Services.AddScoped<IQCashConnectService, QCashConnectService>();
        builder.Services.AddScoped<IMemberInterfacePresentationService, MemberInterfacePresentationService>();
        builder.Services.AddScoped<IApplicationKeyService, ApplicationKeyService>();
        builder.Services.AddScoped<IMemberInterfaceLanguageService, MemberInterfaceLanguageService>();

        builder.Services.AddScoped<IInvoiceService, InvoiceService>();
        builder.Services.AddScoped<IInvoiceGeneratorRunner, InvoiceGeneratorRunner>();
        builder.Services.AddScoped<IInvoicePlanItemValidator, InvoicePlanItemValidator>();
        builder.Services.AddScoped<IPricingScenarioCalculatorRunner, PricingScenarioCalculatorRunner>();
        builder.Services.AddScoped<IPricingScenarioValidator, PricingScenarioValidator>();
        builder.Services.AddScoped<IAuditService, AuditService>();

        builder.Services.AddHttpClient("FunctionAppHttpClient", config =>
        {
            config.BaseAddress = new Uri(builder.Configuration.GetValue<string>("QCashApplication:ConfiguredReportsEndpoint", "") ?? string.Empty);
            config.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("text/plain"));
            config.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            config.Timeout = TimeSpan.FromMinutes(5);
        });
        builder.Services.AddSingleton<IFunctionAppClient, FunctionAppClient>();
        builder.Services.AddScoped<IReportService, ReportService>();
        builder.Services.AddScoped<ISettingService, SettingService>();
        builder.Services.AddScoped<ICampaignService, CampaignService>();
        builder.Services.AddScoped<ISitemapService, SitemapService>();
        builder.Services.AddScoped<IMemberInterfaceTextService, MemberInterfaceTextService>();

        return builder;
    }
}
