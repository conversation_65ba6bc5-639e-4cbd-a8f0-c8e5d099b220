using System;

namespace QCash.Middleware.Messaging.Helpers
{
    //This was brought over from QCash.Common.Helpers
    //All we really needed was .Age()

    public static class DateTimeExtension
    {
        public static bool IsValidBirthDate(this System.DateTime birthDate)
        {
            if (birthDate.Year < 2050)
            {
                return birthDate.Year > 1900;
            }

            return false;
        }

        public static int Age(this System.DateTime birthDate)
        {
            System.DateTime today = System.DateTime.Today;
            int num = today.Year - birthDate.Year;
            if (birthDate > today.AddYears(-num))
            {
                num--;
            }

            return num;
        }

        public static int GetMonthDifference(this System.DateTime lhs, System.DateTime rhs)
        {
            System.DateTime dateTime = ((lhs < rhs) ? lhs : rhs);
            System.DateTime dateTime2 = ((lhs < rhs) ? rhs : lhs);
            int num = (dateTime2.Year - dateTime.Year) * 12 + dateTime2.Month - dateTime.Month;
            if (dateTime.AddMonths(num) > dateTime2)
            {
                num--;
            }

            if (!(lhs < rhs))
            {
                return -num;
            }

            return num;
        }

        public static string GetDateTimeGmtFormatted(this System.DateTime gmtDateTime)
        {
            return gmtDateTime.ToString("yyyy-MM-ddTHH:mm:sszzz");
        }

        public static string GetDateTimeFormatted(this System.DateTime dateTime)
        {
            return dateTime.ToString("yyyy-MM-ddTHH:mm:ss");
        }

        public static string GetDateFormatted(this System.DateTime date)
        {
            return date.ToString("yyyy-MM-dd");
        }
    }
}
