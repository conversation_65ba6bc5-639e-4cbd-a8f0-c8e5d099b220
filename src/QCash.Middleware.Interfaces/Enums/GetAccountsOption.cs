using System;
using System.Runtime.Serialization;

namespace QCash.LoanApplication;

/// <summary>
/// This is a bit options for filtering the accounts returned
/// </summary>
[Flags]
[DataContract]
public enum GetAccountsOption
{
    /// <summary>
    /// Includes everything
    /// </summary>
    [EnumMember]
    NothingToReturn = 0,

    /// <summary>
    /// Only return deposit accounts
    /// </summary>
    [EnumMember]
    DepositOnly = 1,

    /// <summary>
    /// Only return loan accounts
    /// </summary>
    [EnumMember]
    LoanOnly = 2,
}