using System;
using System.Runtime.Serialization;

namespace QCash.LoanApplication;

/// <summary>
/// Address types
/// </summary>
[Flags]
[DataContract]
public enum AddressType
{
    /// <summary>
    /// Unknown address
    /// </summary>
    [EnumMember]
    Unknown = 0x00,

    /// <summary>
    /// Home address
    /// </summary>
    [EnumMember]
    Home = 0x01,

    /// <summary>
    /// Work address
    /// </summary>
    [EnumMember]
    Work = 0x02,

    /// <summary>
    /// Domestic address
    /// </summary>
    [EnumMember]
    Domestic = 0x04,

    /// <summary>
    /// Foreign address
    /// </summary>
    [EnumMember]
    Foreign = 0x08,

    /// <summary>
    /// Special foreign address
    /// </summary>
    [EnumMember]
    SpecialForeign = 0x10,
}