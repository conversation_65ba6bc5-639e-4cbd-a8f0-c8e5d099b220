using System;
using System.Runtime.Serialization;

namespace QCash.LoanApplication;

/// <summary>
/// This is a bit options, if you need information include share and loan then do the following
/// HistoryQueryOption.ShareOnly | HistoryQueryOption.DepositOnly
/// Basically or them to get the option you need
/// </summary>
[Flags]
[DataContract]
public enum HistoryQueryOption
{
    /// <summary>
    /// Includes everything
    /// </summary>
    [EnumMember]
    Everything = 0,

    /// <summary>
    /// Translate the result to general format or use core specific format
    /// </summary>
    [EnumMember]
    TranslateResult = 1,

    /// <summary>
    /// Only return deposit records
    /// </summary>
    [EnumMember]
    DepositOnly = 2,

    /// <summary>
    /// Only return withdrawal records
    /// </summary>
    [EnumMember]
    WithdrawalOnly = 4,

    /// <summary>
    /// Only return share account records
    /// </summary>
    [EnumMember]
    ShareOnly = 8,

    /// <summary>
    /// Only return loan account records
    /// </summary>
    [EnumMember]
    LoanOnly = 16,

    /// <summary>
    /// Return all account records
    /// </summary>
    [EnumMember]
    AllAccountTypes = 32,

    /// <summary>
    /// Only return fee records
    /// </summary>
    [EnumMember]
    FeeOnly = 64
}