using System.Runtime.Serialization;

namespace QCash.LoanApplication;

/// <summary>
/// A data contract that denotes a type of transfer in the core system.
/// </summary>
[DataContract]
public enum TransferType
{
    /// <summary>
    /// Fund loan account when the loan account is created
    /// </summary>
    [EnumMember]
    FundLoan = 0,

    /// <summary>
    /// A standard in-house transfer between two accounts that are not loan accounts. 
    /// </summary>
    [EnumMember]
    Standard = 1,

    /// <summary>
    /// Pay for fee
    /// </summary>
    [EnumMember]
    Fee = 2,

    /// <summary>
    /// Loan funding, could be LoanAccount to ShareAccount, LoanAccount to GLAccount, GLAccount to ShareAccount
    /// </summary>
    [EnumMember]
    LoanFunding = 3,

    /// <summary>
    /// Transfer between GL accounts
    /// </summary>
    [EnumMember]
    GLTransfer = 4,

    /// <summary>
    /// Debit GL
    /// </summary>
    [EnumMember]
    DebitGL = 5,

    /// <summary>
    /// Debit a loan account
    /// </summary>
    [EnumMember]
    DebitLoan = 6,

    /// <summary>
    /// Fund a DDA account
    /// </summary>
    [EnumMember]
    FundDDA = 7,

    /// <summary>
    /// Credit a GL account
    /// </summary>
    [EnumMember]
    CreditGL = 8,

    /// <summary>
    /// Post a loan fee
    /// </summary>
    [EnumMember]
    LoanFee = 9,

    /// <summary>
    /// Payoff a loan
    /// </summary>
    [EnumMember]
    Payoff = 10,
}