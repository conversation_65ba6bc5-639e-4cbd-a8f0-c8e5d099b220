using QCash.LoanApplication.ApiRequestModels;
using QCash.LoanApplication.ResponseMessages;
using System;
using System.Collections.Generic;
using System.Reflection;
using System.ServiceModel;
using System.Threading;
using System.Threading.Tasks;
using QCash.LoanApplication.Models;

namespace QCash.LoanApplication.Interfaces;

/// <summary>
/// The interface between loan engine and the core provider
/// </summary>
[ServiceContract]
public interface ICoreProvider
{
    /// <summary>
    /// Query function for frontend to pull back a list of cores supported
    /// </summary>
    /// <returns>A list of cores supported</returns>
    [OperationContract]
    [FaultContract(typeof(CoreProviderFault))]
    Task<CoreInfo[]> GetSupportedCoresAsync(string? correlationId = null, CancellationToken cancellationToken = default);

    [OperationContract]
    [FaultContract(typeof(CoreProviderFault))]
    Task<GetContextResponse> GetContextAsync(Guid coreId, string password, string username);

    /// <summary>
    /// Ping to see whether the core is alive
    /// </summary>
    /// <param name="coreId">The core on which you want to check the aliveness</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>True if the core is alive, false otherwise</returns>
    [OperationContract]
    [FaultContract(typeof(CoreProviderFault))]
    Task<CoreConnectionResponse> GetCoreConnectionStatusAsync(Guid coreId, string? correlationId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// A giant call to pull back all information, which will be stored in the incoming member object (BaseAccounts/WarningCode etc)
    /// </summary>
    /// <param name="coreId">The core id</param>
    /// <param name="member">Inside this member object, the caller will have to provide either the memberId or the taxId</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="applicationId">The application id</param>
    [OperationContract]
    [FaultContract(typeof(CoreProviderFault))]
    Task<Member> GetMemberAsync(Guid coreId, GetMemberParameter member, string correlationId, int? applicationId = null, bool takeFromCache = false);

    /// <summary>
    /// Pull back all the history transaction for one account
    /// NOTES: we may want to expand this to something more powerful so that we can query in anyway we want
    /// For now we only support HistoryQueryOption.ShareOnly | HistoryQueryOption.DepositOnly
    /// </summary>
    /// <param name="coreId">The core id</param>
    /// <param name="account">
    /// The account on which we want to pull the history from
    /// </param>
    /// <param name="option">
    /// The query options
    /// </param>
    /// <param name="transactionTypeFilter">
    /// The filter on transactions to pull back, for SYMITAR, it could be 'D' for deposit, 'P' for payment etc
    /// </param>
    /// <param name="startDate">
    /// The start Date.
    /// </param>
    /// <param name="endDate">
    /// The end Date.
    /// </param>
    /// <param name="extraFilters">
    /// Extra filters (in name/value pair) for future cores
    /// </param>
    [OperationContract]
    [FaultContract(typeof(CoreProviderFault))]
    Task<GetTransactionHistoryResponse> GetTransactionHistoryAsync(GetTransactionHistoryApiRequest request);



    /// <summary>
    /// Transfer money from one account to another, cases could be: (comment need be configurable)
    /// 1. Application Fee
    ///     a.Debit Share – Credit Application Fee GL
    ///         Transfer(coreId, fromShareAccount, toGLAccount, TransferType.Fee, "APPLICATION FEE", null)
    /// 2. Loan Funding (No loan fee)
    ///     a.Debit Loan – Credit Share
    ///         Transfer(coreId, fromLoanAccount, toShareAccount, TransferType.LoanFunding, "NEW QCASH LOAN", null) -- Here the fromLoanAccount is typically the one you just created through CreateNewLoan
    /// 3. Loan Funding (Loan Fee)
    ///     a.Debit Loan – Credit Clearing GL (Loan Proceeds plus Fee) -- Here the fromLoanAccount is typically the one you just created through CreateNewLoan
    ///         Transfer(coreId, fromLoanAccount, toGLAccount, TransferType.LoanFunding, "NEW QCASH LOAN", null) 
    ///     b.Credit Share – Debit Clearing GL (Loan Proceeds)
    ///         Transfer(coreId, fromGLAccount, toShareAccount, TransferType.LoanFunding, "NEW QCASH LOAN", null) 
    ///     c.Credit Loan Fee GL – Debit Clearing GL (Fee)
    ///         Transfer(coreId, fromGLAccount, toGLAccount, TransferType.GLTransfer, "NEW QCASH LOAN FEE", null) -- The GLAccountNumber used here will be different from what used above, more like a full number
    /// 2. Member's checking account to loan account for payment
    /// </summary>
    /// <param name="coreId">
    /// The core Id.
    /// </param>
    /// <param name="fromAccount">
    /// Debit account withdrawing money
    /// </param>
    /// <param name="toAccount">
    /// Credit account receiving money
    /// </param>
    /// <param name="amount">
    /// transaction amounts
    /// </param>
    /// <param name="transferType">
    /// The transfer Type.
    /// </param>
    /// <param name="comment">
    /// The comment.
    /// </param>
    /// <param name="extraTransferInfo">
    /// The extra Transfer Info.
    /// </param>
    [OperationContract]
    [FaultContract(typeof(CoreProviderFault))]
    Task<TransferResponse> TransferAsync(TransferApiRequest req);

    /// <summary>
    /// Create a new loan account and then fund a share account 
    /// </summary>
    /// <param name="coreId">Core to call</param>
    /// <param name="newLoanAppId">New loan application id for post back</param>
    /// <param name="baseAccount">The base account under which the new loan account will be created</param>
    /// <param name="newLoan">The new loan account information</param>
    /// <param name="toAccount">The share account to be funded</param>
    /// <param name="amount">The amount to fund</param>
    /// <param name="transferType">The transfer type</param>
    /// <param name="fromAcctForApplicationFee">From Account for the application fee payment</param>
    /// <param name="autoPaymentAcct"> Auto payment account</param>
    /// <param name="comment">The comment</param>
    /// <param name="extraTransferInfo">Extra information for the loan funding</param>
    /// <returns>The result of the create and fund loan</returns>
    [OperationContract]
    [FaultContract(typeof(CoreProviderFault))]
    Task<CreateAndFundLoanResult> CreateAndFundLoanAsync(CreateAndFundLoanApiRequest request);

    /// <summary>
    /// Get the average monthly checking deposit balance for a member
    /// </summary>
    /// <param name="coreId">Core to call</param>
    /// <param name="parameters">Parameters required for calculating the average monthly checking deposit balance</param>
    /// <param name="correlationId">Value used for reference</param>
    /// <returns></returns>
    [OperationContract]
    [FaultContract(typeof(CoreProviderFault))]
    Task<decimal> GetAverageMonthlyCheckingDepositBalanceAsync(Guid coreId, AverageCheckingDepositBalanceParameter parameters, string correlationId, int applicationId, string additionalHeaders, string clientTimeZone);

    /// <summary>
    /// Inserts tracking record for account, loan, share or external loan level.
    /// </summary>
    /// <param name="coreId"></param>
    /// <param name="request"></param>
    /// <param name="correlationId"></param>
    [OperationContract]
    [FaultContract(typeof(CoreProviderFault))]
    Task<InsertTrackingRecordResponse> InsertTrackingRecordAsync(InsertTrackingRecordApiRequest request);


    /// <summary>
    /// Create a new share account on the Core system. Returns the newly created account.
    /// </summary>
    /// <param name="coreId">Core to call.</param>
    /// <param name="parameters">Parameters required for creating a share account.</param>
    /// <param name="correlationId">Value used for reference number.</param>
    /// <returns></returns>
    [Obsolete]
    [OperationContract]
    [FaultContract(typeof(CoreProviderFault))]
    Task<Account> CreateShareAsync(Guid coreId, CreateShareAccountParameters parameters, string correlationId, int applicationId, string clientTimeZone);

    ///// <summary>
    ///// Orders a credit card to be made
    ///// </summary>
    ///// <param name="coreId">Core to call</param>
    ///// <param name="req"></param>
    ///// <param name="correlationId"></param>
    ///// <returns></returns>
    //[Obsolete]
    //[OperationContract]
    //[FaultContract(typeof(CoreProviderFault))]
    //Task<OrderCreditCardResponse> OrderCreditCardAsync(OrderCreditCardRequest req);

    /// <summary>
    /// Preforms gateway check if its connected.
    /// </summary>
    /// <param name="coreId"></param>
    /// <returns></returns>
    [Obsolete]
    [OperationContract]
    [FaultContract(typeof(CoreProviderFault))]
    Task<GatewayHealthCheckResponse> GatewayHealthCheckAsync(Guid coreId);
}
