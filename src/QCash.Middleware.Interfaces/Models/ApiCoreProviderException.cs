using QCash.LoanApplication;
using QCash.LoanApplication.ResponseMessages;
using System;

namespace QCash.LoanApplication;

public class ApiCoreProviderException : CoreProviderException
{
    public ApiCoreProviderException(QCashResponseBase response, string message, Exception? inner = null)
        : base(response.Fault, message, inner)
    {
        Response = response;
    }

    public ApiCoreProviderException(CoreProviderFault fault, string message, Exception? inner = null)
        : base(fault, message, inner)
    {
    }

    public QCashResponseBase? Response { get; set; }
}
