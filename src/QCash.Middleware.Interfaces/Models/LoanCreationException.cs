using System;

namespace QCash.LoanApplication;

/// <summary>
/// Exception during creation of the loan
/// </summary>
[Serializable]
public class LoanCreationException : Exception
{
    /// <summary>
    /// Initializes a new instance of the <see cref="LoanCreationException"/> class.
    /// </summary>
    public LoanCreationException() { }

    /// <summary>
    /// Initializes a new instance of the <see cref="LoanCreationException"/> class.
    /// </summary>
    /// <param name="message">
    /// The message.
    /// </param>
    public LoanCreationException(string message) : base(message)
    {
    }
}