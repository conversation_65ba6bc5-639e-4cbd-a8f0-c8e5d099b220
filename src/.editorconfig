# Schema: http://EditorConfig.org
# Docs: https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference

# top-most EditorConfig file
root = true

# Don't use tabs for indentation.
[*]
indent_style = space
trim_trailing_whitespace = true
guidelines = 140
max_line_length = 140

# Code files
[*.{cs,csx,vb,vbx}]
indent_size = 4
insert_final_newline = true
#charset = utf-8-bom

# Xml project files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj}]
indent_size = 2

# Xml config files
[*.{props,targets,ruleset,config,nuspec,resx,vsixmanifest,vsct,xml,stylecop}]
indent_size = 2

# JSON files
[*.json]
indent_size = 2

# Powershell files
[*.ps1]
indent_size = 2

# Shell scripts
[*.sh]
end_of_line = lf
indent_size = 2

[*.{cmd,bat}]
end_of_line = crlf
indent_size = 2

## Language conventions
# Dotnet code style settings:
[*.{cs,vb}]

# Organize usings
dotnet_sort_system_directives_first = true
dotnet_separate_import_directive_groups = false
# "This." and "Me." qualifiers
dotnet_style_qualification_for_field = false:suggestion
dotnet_style_qualification_for_property = false:suggestion
dotnet_style_qualification_for_method = false:suggestion
dotnet_style_qualification_for_event = false:suggestion

# Language keywords instead of framework type names for type references
dotnet_style_predefined_type_for_locals_parameters_members = true:suggestion
dotnet_style_predefined_type_for_member_access = true:suggestion

# Modifier preferences
dotnet_style_require_accessibility_modifiers = always:suggestion
dotnet_style_readonly_field = true:warning

# Parentheses preferences
dotnet_style_parentheses_in_arithmetic_binary_operators = always_for_clarity:silent
dotnet_style_parentheses_in_other_binary_operators = always_for_clarity:silent
dotnet_style_parentheses_in_other_operators = never_if_unnecessary:silent
dotnet_style_parentheses_in_relational_binary_operators = always_for_clarity:silent

# Expression-level preferences
dotnet_style_object_initializer = true:suggestion
dotnet_style_collection_initializer = true:suggestion
dotnet_style_explicit_tuple_names = true:suggestion
dotnet_style_prefer_inferred_tuple_names = true:suggestion
dotnet_style_prefer_inferred_anonymous_type_member_names = true:suggestion
dotnet_style_prefer_auto_properties = true:silent
dotnet_style_prefer_conditional_expression_over_assignment = true:suggestion
dotnet_style_prefer_conditional_expression_over_return = true:suggestion

# Null-checking preferences
dotnet_style_coalesce_expression = true:suggestion
dotnet_style_null_propagation = true:suggestion





# CSharp formatting settings:
[*.cs]
# Newline options
csharp_new_line_before_open_brace = all
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types = true
csharp_new_line_between_query_expression_clauses = true

# Identation options
csharp_indent_block_contents = true
csharp_indent_braces = false
csharp_indent_case_contents = true
csharp_indent_case_contents_when_block = false
csharp_indent_switch_labels = true

csharp_indent_labels = no_change

# CSharp code style settings:
[*.cs]
# Modifier preferences
csharp_preferred_modifier_order = public,private,protected,internal,const,static,extern,new,virtual,abstract,sealed,override,readonly,unsafe,volatile,async:suggestion

# Implicit and explicit types
csharp_style_var_for_built_in_types = true:suggestion
csharp_style_var_when_type_is_apparent = true:suggestion
csharp_style_var_elsewhere = true:none

# Expression-bodied members
# Explicitly disabled due to difference in coding style between source and tests
#csharp_style_expression_bodied_methods = false:silent
#csharp_style_expression_bodied_constructors = false:silent
csharp_style_expression_bodied_operators = false:silent
csharp_style_expression_bodied_properties = true:suggestion
csharp_style_expression_bodied_indexers = true:suggestion
csharp_style_expression_bodied_accessors = true:suggestion

# Pattern matching
csharp_style_pattern_matching_over_is_with_cast_check = true:suggestion
csharp_style_pattern_matching_over_as_with_null_check = true:suggestion

# Inlined variable declarations
csharp_style_inlined_variable_declaration = true:suggestion

# Expression-level preferences
csharp_prefer_simple_default_expression = true:suggestion
csharp_style_deconstructed_variable_declaration = true:suggestion
csharp_style_pattern_local_over_anonymous_function = true:suggestion

# Null-checking preference
csharp_style_throw_expression = true:suggestion
csharp_style_conditional_delegate_call = true:suggestion

# Code block preferences
csharp_prefer_braces = true:suggestion

# Primary constructors
csharp_style_prefer_primary_constructors = true:suggestion
# Spacing options
csharp_space_after_cast = false
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_after_comma = true
csharp_space_after_dot = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_after_semicolon_in_for_statement = true
csharp_space_around_binary_operators = before_and_after
csharp_space_around_declaration_statements = do_not_ignore
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_before_comma = false
csharp_space_before_dot = false
csharp_space_before_open_square_brackets = false
csharp_space_before_semicolon_in_for_statement = false
csharp_space_between_empty_square_brackets = false
csharp_space_between_method_call_empty_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis = false
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_declaration_name_and_open_parenthesis = false
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_parentheses = false
csharp_space_between_square_brackets = false


# Wrap options
csharp_preserve_single_line_blocks = true
csharp_preserve_single_line_statements = true

## Naming conventions
[*.{cs,vb}]

## Naming styles

dotnet_naming_style.pascal_case_style.capitalization = pascal_case
dotnet_naming_style.camel_case_style.capitalization = camel_case

# PascalCase with I prefix
dotnet_naming_style.interface_style.capitalization = pascal_case
dotnet_naming_style.interface_style.required_prefix = I

# PascalCase with T prefix
dotnet_naming_style.type_parameter_style.capitalization = pascal_case
dotnet_naming_style.type_parameter_style.required_prefix = T

# camelCase with _ prefix
dotnet_naming_style._camelCase.capitalization = camel_case
dotnet_naming_style._camelCase.required_prefix = _

## Rules
# Interfaces
dotnet_naming_symbols.interface_symbol.applicable_kinds = interface
dotnet_naming_symbols.interface_symbol.applicable_accessibilities = *
dotnet_naming_rule.interface_naming.symbols = interface_symbol
dotnet_naming_rule.interface_naming.style = interface_style
dotnet_naming_rule.interface_naming.severity = suggestion

# Classes, Structs, Enums, Properties, Methods, Local Functions, Events, Namespaces
dotnet_naming_symbols.class_symbol.applicable_kinds = class, struct, enum, property, method, local_function, event, namespace, delegate
dotnet_naming_symbols.class_symbol.applicable_accessibilities = *

dotnet_naming_rule.class_naming.symbols = class_symbol
dotnet_naming_rule.class_naming.style = pascal_case_style
dotnet_naming_rule.class_naming.severity = suggestion

# Type Parameters
dotnet_naming_symbols.type_parameter_symbol.applicable_kinds = type_parameter
dotnet_naming_symbols.type_parameter_symbol.applicable_accessibilities = *

dotnet_naming_rule.type_parameter_naming.symbols = type_parameter_symbol
dotnet_naming_rule.type_parameter_naming.style = type_parameter_style
dotnet_naming_rule.type_parameter_naming.severity = suggestion

# Visible Fields
dotnet_naming_symbols.public_field_symbol.applicable_kinds = field
dotnet_naming_symbols.public_field_symbol.applicable_accessibilities = public, internal, protected, protected_internal, private_protected

dotnet_naming_rule.public_field_naming.symbols = public_field_symbol
dotnet_naming_rule.public_field_naming.style = pascal_case_style
dotnet_naming_rule.public_field_naming.severity = suggestion

# Private constant Fields
dotnet_naming_symbols.const_field_symbol.applicable_kinds = field
dotnet_naming_symbols.const_field_symbol.applicable_accessibilities = private
dotnet_naming_symbols.const_field_symbol.required_modifiers = const

dotnet_naming_rule.const_field_naming.symbols = const_field_symbol
dotnet_naming_rule.const_field_naming.style = pascal_case_style
dotnet_naming_rule.const_field_naming.severity = suggestion

# Parameters
dotnet_naming_symbols.parameter_symbol.applicable_kinds = parameter
dotnet_naming_symbols.parameter_symbol.applicable_accessibilities = *

dotnet_naming_rule.parameter_naming.symbols = parameter_symbol
dotnet_naming_rule.parameter_naming.style = camel_case_style
dotnet_naming_rule.parameter_naming.severity = suggestion

# Everything Local
dotnet_naming_symbols.everything_else.applicable_kinds = local
dotnet_naming_symbols.everything_else.applicable_accessibilities = *

dotnet_naming_rule.everything_else_naming.symbols = everything_else
dotnet_naming_rule.everything_else_naming.style = camel_case_style
dotnet_naming_rule.everything_else_naming.severity = suggestion

# Microsoft .NET properties
csharp_style_expression_bodied_methods = true:suggestion

# ReSharper properties
resharper_local_function_body = expression_body

## Alloya Custom overrides

[*]
dotnet_diagnostic.VSTHRD200.severity=warning

# Code files
[*.{cs,csx,vb,vbx}]
charset = utf-8-bom

[*.cs]
csharp_style_expression_bodied_methods = true:suggestion
csharp_style_expression_bodied_constructors = true:suggestion
csharp_indent_labels = flush_left

[*.{cs,vb}]
# IDE0055: Fix formatting
dotnet_diagnostic.IDE0055.severity = warning

# error RS2008: Enable analyzer release tracking for the analyzer project containing rule '{0}'
dotnet_diagnostic.RS2008.severity = none

# IDE0073: File header
#dotnet_diagnostic.IDE0073.severity = warning
#file_header_template = Licensed to the .NET Foundation under one or more agreements.\nThe .NET Foundation licenses this file to you under the MIT license.\nSee the LICENSE file in the project root for more information.

# IDE0035: Remove unreachable code
dotnet_diagnostic.IDE0035.severity = warning

# IDE0036: Order modifiers
dotnet_diagnostic.IDE0036.severity = warning

# IDE0043: Format string contains invalid placeholder
dotnet_diagnostic.IDE0043.severity = warning

# IDE0044: Make field readonly
dotnet_diagnostic.IDE0044.severity = warning

# RS0016: Only enable if API files are present
dotnet_public_api_analyzer.require_api_files = true

# Enable CA1872: Prefer Convert.ToHexString over BitConverter.ToString
dotnet_diagnostic.CA1872.severity = suggestion

#Enable CA2016: Forward the CancellationToken parameter to methods that take one
dotnet_diagnostic.CA2016.severity = error

[*.cs]
dotnet_diagnostic.MA0001.severity = none;   # Not appropriate for IQueryable 
dotnet_diagnostic.MA0004.severity = none;   # We should respect these rules, but it's too much in this PBI.  Come back later.
MA0004.report = DetectContext # (default) Try to detect the current context and report only if it considers ConfigureAwait is needed
dotnet_diagnostic.MA0005.severity = none;   # Duplicate of CA1825
dotnet_diagnostic.MA0006.severity = none;   # Not appropriate for IQueryable
dotnet_diagnostic.MA0011.severity = none;   # We should respect these rules, but it's too much in this PBI.  Come back later.
dotnet_diagnostic.MA0016.severity = none;   # ignore: TODO's
dotnet_diagnostic.MA0020.severity = none;   # ignore: Use List<T>.Find over FirstOrDefault (is actually slower in .NET 9+)
dotnet_diagnostic.MA0026.severity = none;   # ignore: TODO's
dotnet_diagnostic.MA0025.severity = none;   # ignore: NotImplementedExceptions
dotnet_diagnostic.MA0029.severity = none;   # ignore: Optimize LINQ (Self-optimizing in .NET 9+)
dotnet_diagnostic.MA0031.severity = none;   # ignore: Duplicate of CA1829
dotnet_diagnostic.MA0032.severity = none;   # ignore: Duplicate of CA2016
dotnet_diagnostic.MA0039.severity = none;   # ignore: Do not write your own certificate validation method
dotnet_diagnostic.MA0040.severity = none;   # ignore: Duplicate of CA2016
dotnet_diagnostic.MA0051.severity = none;   # ignore: Method is too long warnings
dotnet_diagnostic.MA0071.severity = none;   # Avoid using redundant else - Analyzer is buggy and flags non-redundant else's 
dotnet_diagnostic.MA0073.severity = warning;# Avoid comparison with bool constant - Readability issue due to uncertainty if bool or bool?
dotnet_diagnostic.MA0078.severity = none;   # ignore: Suggestion is bad and causes a significant performance hit
dotnet_diagnostic.MA0090.severity = error;  # Remove empty else/finally block - will likely trigger SAST dead code finding
dotnet_diagnostic.MA0112.severity = none;   # ignore: Use 'Count > 0' instead of 'Any()'
dotnet_diagnostic.MA0137.severity = error;  # Use 'Async' suffix when a method returns an awaitable type
dotnet_diagnostic.MA0138.severity = error;  # Do not use 'Async' suffix when a method does not return an awaitable type
dotnet_diagnostic.MA0156.severity = error;  # Use 'Async' suffix when a method returns IAsyncEnumerable<T>
dotnet_diagnostic.MA0157.severity = error;  # Do not use 'Async' suffix when a method does not return IAsyncEnumerable<T>
dotnet_diagnostic.IDE0007.severity = none;  # Do not force use of 'var' when an explicit type is used.
dotnet_diagnostic.MA0002.severity = none;
dotnet_diagnostic.IDE0160.severity = none;  # ignore: Use block-scoped namespaces
dotnet_diagnostic.IDE0161.severity = warning; # Use file-scoped namespaces
dotnet_diagnostic.IDE0022.severity = suggestion;  # Use expression bodied methods
dotnet_diagnostic.CS8524.severity = none;   # ignore non-named enum values.  Basically numbers in-between actual enum entries
