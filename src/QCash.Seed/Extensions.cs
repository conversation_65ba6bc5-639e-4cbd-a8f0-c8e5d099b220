using System.Diagnostics.CodeAnalysis;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Humanizer;
using Microsoft.Extensions.Logging;
using QCash.Data.Models;
using QCash.Seed.Models;
using QCash.Seed.Util;

namespace QCash.Seed;

[ExcludeFromCodeCoverage]
public static class Extensions
{
    public static string ToAccountNumber(this string accountNumber)
    {
        if (accountNumber.Length == 6) return $"{accountNumber[..3]}.{accountNumber.Substring(3, 3)}";

        if (accountNumber.Length != 7) throw new ArgumentException($"Invalid account number: {accountNumber}");

        return accountNumber;
    }

    public static string AsAccount(this string accountNumber)
    {
        return accountNumber.PadLeft(6, '0');
    }

    public static string AsRtn(this string rtn)
    {
        return rtn.PadLeft(9, '0');
    }

    public static IEnumerable<T> RemoveNulls<T>(this IEnumerable<T?> ts)
    {
        return ts.
            Where(t => t != null)
            .Cast<T>();
    }

    public static bool IsNullOrWhitespace(this string s)
    {
        return string.IsNullOrWhiteSpace(s);
    }

    [return: NotNullIfNotNull("s")]
    public static string? Left(this string? s, int length)
    {
        return s?.Length > length
            ? s.Substring(0, length)
            : s;
    }

    public static List<string> Values(this DocumentFormat.OpenXml.Spreadsheet.Row r, WorkbookPart wb, bool includeEmptyCells)
    {
        if (includeEmptyCells) return SpreadsheetHelper.GetRowCells(r).Select(c => GetCellValue(c, wb, false)).ToList();
        return r.Cast<DocumentFormat.OpenXml.Spreadsheet.Cell>().Select(c => GetCellValue(c, wb, false)).ToList();
    }

    public static Dictionary<string, string> SheetNames(this SpreadsheetDocument doc)
    {
        var workbookPart = doc.WorkbookPart;
        var workbook = workbookPart!.Workbook;
        var sheets = workbook.Sheets?.Cast<DocumentFormat.OpenXml.Spreadsheet.Sheet>() ?? new List<DocumentFormat.OpenXml.Spreadsheet.Sheet>();
        return sheets!.ToDictionary(k => k.Id!.Value!, v => v.Name!.Value!);
    }

    public static SheetData SheetData(this SpreadsheetDocument doc, string sheetId)
    {
        var workbookPart = doc.WorkbookPart!;

        //// Find sheet id by name
        //var workbook = workbookPart!.Workbook;
        //var sheets = workbook.Sheets?.Cast<Sheet>() ?? new List<Sheet>();
        //var sheet = sheets.First(s => s.Name == name);

        // Get worksheet part by id
        var worksheet = ((WorksheetPart)workbookPart.GetPartById(sheetId)).Worksheet;
        return worksheet.GetFirstChild<SheetData>()!;

    }

    static string GetCellValue(DocumentFormat.OpenXml.Spreadsheet.Cell cell, WorkbookPart workbookPart, bool isDate = false)
    {
        var cellValue = string.Empty;
        if (cell.DataType != null)
        {
            if (cell.DataType == CellValues.SharedString)
            {
                if (Int32.TryParse(cell.InnerText, out var id))
                {
                    var item = workbookPart.SharedStringTablePart!.SharedStringTable.Elements<SharedStringItem>().ElementAt(id);
                    if (item.Text != null)
                    {
                        cellValue = item.Text.Text;
                    }
                    else if (item.InnerText != null)
                    {
                        cellValue = item.InnerText;
                    }
                    else if (item.InnerXml != null)
                    {
                        cellValue = item.InnerXml;
                    }
                }
            }
        }
        else
        {
            if (isDate)
            {
                cellValue = DateTime.FromOADate(int.Parse(cell.InnerText)).ToString("MM/dd/yyyy");
            }
            else
            {
                cellValue = cell.InnerText;
            }
        }

        return cellValue;
    }

    public static bool? AsBool(this QCash.Seed.Models.Cell cell)
    {
        return cell.Value.Trim().ToUpper() switch
        {
            "Y" => true,
            "1" => true,
            "T" => true,
            "TRUE" => true,
            "N" => false,
            "0" => false,
            "F" => false,
            "FALSE" => false,
            _ => null
        };
    }

    public static int? AsInt(this QCash.Seed.Models.Cell cell)
    {
        return int.TryParse(cell.Value.Trim(), out var i)
            ? i
            : null;
    }

    public static string AsString(this QCash.Seed.Models.Cell cell, ILogger logger, int maxLength)
    {
        if (!cell.HasValue)
            return "";

        var value = cell.Value.Trim();
        if (value.Length > maxLength)
        {
            logger.LogError("Value '{value}' exceeds maximum length of {maxLength} and has been truncated", value, maxLength);
        }

        return value.Truncate(maxLength, "");
    }

    public static int? AsId<T>(this QCash.Seed.Models.Cell cell, IReadOnlyDictionary<string, T> dict)
    {
        var value = cell.Value.Trim();
        if (string.IsNullOrWhiteSpace(value))
            return default;

        var i = dict[value];
        return (int?)i?.GetType()
            .GetProperty("Id")?
            .GetValue(i, null);
    }

    public static T? FromDict<T>(this QCash.Seed.Models.Cell cell, IReadOnlyDictionary<string, T> dict)
    {
        var value = cell.Value.Trim();
        return string.IsNullOrWhiteSpace(value)
            ? default
            : dict[value];
    }
}
