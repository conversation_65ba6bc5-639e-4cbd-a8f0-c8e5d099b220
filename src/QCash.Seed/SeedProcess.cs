using System.Security.Cryptography;
using System.Text;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Core;
using Microsoft.AspNetCore.Identity;
using QCash.Data.Models.Enums;

namespace QCash.Seed;

public class SeedProcess(QCashContext context, IUnitOfWork unitOfWork, ILogger<SeedProcess> logger)
{
    private enum IdIndexes
    {
        FinancialInstitution = 1,
        Setting = 2,
        States = 3,
        LoanTypes = 5,
        AspNetRole = 4,
        LoanCategories = 6,
        DocumentTypes = 7,
        Language = 8,
        ScoreTypes = 9,
        MaintenanceSetting = 10,
        DecisionModelTypes = 11,
        DecisionModelStatuses = 12,
        MemberInterfacePresentation = 13,
        MemberInterfacePresentationTemplate = 14,
        LanguageSupportStatus = 15,
        PaymentOptionTypes = 16,
        LoanStatusTypes = 17,
        CalcEngineSetting = 18,
        FinancialInstitutionConfiguration = 19,
        AanReasonDefaultText = 20,
        InterfaceDefaultText = 21,
    }
    private FinancialInstitution[] GetFinancialInstitutionsSeedData() =>
    [
        new FinancialInstitution
        {
            Id = GetID(IdIndexes.FinancialInstitution, 0, 1),
            Name = "Test Financial Institution",
            Address = "123 Main Street",
            City = "Anytown",
            State = "NY",
            Zip = "12345",
            ContactName = "Test Contact 1",
            ContactPhone = "************",
            MailingAddress = "123 Main Street",
            GlLoanAccount = "100.000",
            Slug = "testslug",
        },
        new FinancialInstitution
        {
            Id = GetID(IdIndexes.FinancialInstitution, 0, 2),
            Name = "Symitar Sandbox FI",
            Address = "123 Symitar Ave",
            City = "Symitar Town",
            State = "NJ",
            Zip = "34567",
            ContactName = "Test Contact 1",
            ContactPhone = "************",
            MailingAddress = "123 Symitar Ave",
            GlLoanAccount = "100.000",
            Slug = "symitarsandboxfi",
        },
    ];

    private Guid GetID(IdIndexes idIndexEnum, int idMultiplier, int val)
    {
        var idIndex = (int)idIndexEnum;
        var adjustedVal = 1000 * idMultiplier + val;
        return Guid.Parse($"********-0000-0000-00{idIndex:00}-{adjustedVal:************}");
    }

    public async Task<bool> CanSeedInitialTestDataAsync()
    {
        var slowQueryLogging = context.SlowQueryLogging;
        try
        {
            context.SlowQueryLogging = false;
            var fiSeedData = GetFinancialInstitutionsSeedData();

            foreach (var fiSeed in fiSeedData)
            {
                var financialInstitution = await GetFinancialInstitutionAsync(fiSeed.Id);
                if (financialInstitution is null) return true;

                var setting = await GetSettingAsync(financialInstitution);
                if (setting is null) return true;

                var maintenanceSetting = await GetMaintenanceSettingAsync(financialInstitution);
                if (maintenanceSetting is null) return true;

                var states = await GetStatesAsync(financialInstitution);
                if (states is []) return true;

                var loanTypes = await GetLoanTypesAsync(financialInstitution);
                if (loanTypes is []) return true;

                var loanCategories = await GetLoanCategoriesAsync(financialInstitution);
                if (loanCategories is []) return true;

                var roles = await GetRolesAsync();
                if (roles is []) return true;

                var documentTypes = await GetDocumentTypesAsync(financialInstitution);
                if (documentTypes is []) return true;

                var languages = await GetLanguagesAsync();
                if (languages is []) return true;

                var languageSupportStatuses = await GetLanguageSupportStatusesAsync(financialInstitution);
                if (languageSupportStatuses is []) return true;

                var scoreTypes = await GetScoreTypesAsync(financialInstitution);
                if (scoreTypes is []) return true;

                var decisionModelTypes = await GetDecisionModelTypesAsync(financialInstitution);
                if (decisionModelTypes is []) return true;

                var decisionModelStatuses = await GetDecisionModelStatusesAsync(financialInstitution);
                if (decisionModelStatuses is []) return true;

                var users = await GetUsersAsync(financialInstitution);
                if (users is [] || users.Count == 0) return true;

                var mips = await GetMemberInterfacePresentationsAsync(financialInstitution);
                if (mips is [] || mips.Count == 0) return true;

                var mipts = await GetMemberInterfacePresentationTemplatesAsync(financialInstitution);
                if (mipts is [] || mipts.Count == 0) return true;

                var paymentOptionTypes = await GetPaymentOptionTypesAsync(financialInstitution);
                if (paymentOptionTypes is []) return true;

                var loanStatusTypes = await GetLoanStatusTypesAsync(financialInstitution);
                if (loanStatusTypes is []) return true;

                var calcEngineSetting = await GetCalcEngineSettingAsync(financialInstitution);
                if (calcEngineSetting is null) return true;

                var financialInstitutionConfiguration = await GetFinancialInstitutionConfigurationAsync(financialInstitution);
                if (financialInstitutionConfiguration is null) return true;

                var aanReasonDefaultTexts = await GetAanReasonDefaultTextAsync();
                if (aanReasonDefaultTexts is []) return true;

                var interfaceDefaultTexts = await GetInterfaceDefaultTextAsync();
                if (interfaceDefaultTexts is []) return true;
            }
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error while seeding");
            throw;
        }
        finally
        {
            context.SlowQueryLogging = slowQueryLogging;
        }
        return false;
    }

    public async Task SeedAllAsync()
    {
        var slowQueryLogging = context.SlowQueryLogging;
        try
        {
            context.SlowQueryLogging = false;
            var financialInstitutionSeedData = GetFinancialInstitutionsSeedData();

            var roles = await SeedRoleDataAsync();
            var languages = await SeedLanguagesAsync();
            await SeedAanReasonDefaultTextAsync(languages);
            await SeedInterfaceDefaultTextAsync(languages);

            // Build a role dictionary once
            var allRoles = await GetRolesAsync();
            var rolesByName = allRoles.ToDictionary(r => r.Name ?? string.Empty, r => r);

            for (var i = 0; i < financialInstitutionSeedData.Length; i++)
            {
                var fiSeed = financialInstitutionSeedData[i];

                var financialInstitution = await SeedFinancialInstitutionAsync(fiSeed);
                var setting = await SeedSettingAsync(financialInstitution, i);
                var maintenanceSetting = await SeedMaintenanceSettingAsync(financialInstitution, i);
                var states = await SeedStatesAsync(financialInstitution, i);
                var loanTypes = await SeedLoanTypesAsync(financialInstitution, i);
                var loanCategories = await SeedLoanCategoriesAsync(financialInstitution, i);
                var documentTypes = await SeedDocumentTypesAsync(financialInstitution, i);
                var scoreTypes = await SeedScoreTypesAsync(financialInstitution, i);
                var decisionModelTypes = await SeedDecisionModelTypesAsync(financialInstitution, i);
                var decisionModelStatuses = await SeedDecisionModelStatusesAsync(financialInstitution, i);
                var users = await SeedUsersAsync(financialInstitution, rolesByName);
                var mips = await SeedMemberInterfacePresentationsAsync(financialInstitution, i);
                var mipts = await SeedMemberInterfacePresentationTemplatesAsync(financialInstitution, i);
                var languageSupportStatuses = await SeedLanguageSupportStatusesAsync(financialInstitution, languages, i);
                var paymentOptionTypes = await SeedPaymentOptionTypesAsync(financialInstitution, i);
                var loanStatusTypes = await SeedLoanStatusTypesAsync(financialInstitution, i);
                var calcEngineSetting = await SeedCalcEngineSettingAsync(financialInstitution, i);
                var financialInstitutionConfiguration = await SeedFinancialInstitutionConfigurationAsync(financialInstitution, i);
            }
        }
        catch (Exception e)
        {
            logger.LogError(e, "Error while seeding");
            throw;
        }
        finally
        {
            context.SlowQueryLogging = slowQueryLogging;
        }
    }

    public async Task<FinancialInstitution?> GetFinancialInstitutionAsync(Guid financialInstitutionId)
    {
        return await context.FinancialInstitutions
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(fi => fi.Id == financialInstitutionId);
    }
    public async Task<FinancialInstitution> SeedFinancialInstitutionAsync(FinancialInstitution fiSeed)
    {
        var financialInstitution = await GetFinancialInstitutionAsync(fiSeed.Id);

        if (financialInstitution != null)
        {
            logger.LogInformation("Financial Institution '{@FinancialInstitution}' already exists", financialInstitution);
            return financialInstitution;
        }

        await context.FinancialInstitutions.AddAsync(fiSeed);
        await context.SaveChangesAsync();

        logger.LogInformation("Created Financial Institution '{@FinancialInstitution}'", fiSeed);
        return fiSeed;
    }

    public async Task<Setting?> GetSettingAsync(FinancialInstitution financialInstitution)
    {
        return await context.Settings
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(s => s.FinancialInstitutionId == financialInstitution.Id);
    }
    public async Task<Setting> SeedSettingAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var setting = await GetSettingAsync(financialInstitution);

        if (setting != null)
        {
            logger.LogInformation("Settings '{@Setting}' already exists", setting);
            return setting;
        }

        setting = new Setting
        {
            FinancialInstitutionId = financialInstitution.Id,
            FinancialInstitution = financialInstitution,
            Id = GetID(IdIndexes.Setting, idMultiplier, 1),
            IsActive = true,
            FilenameAan = "",
            FilenameTila = "",
            LoanIdSource = "",
            Mla = "",
            TimeZone = "Central Standard Time",
            MonitorAccount = "",
            TilaCalculatorSource = "",
            AppLogsFileNameTemplate = "",
            Bankruptcy = "",
            FilenameEConsent = "",
        };

        await context.Settings.AddAsync(setting);
        await context.SaveChangesAsync();

        logger.LogInformation("Created Settings '{@Setting}' already exists", setting);
        return setting;
    }


    private async Task<CalcEngineSetting?> GetCalcEngineSettingAsync(FinancialInstitution financialInstitution)
    {
        return await context.CalcEngineSettings
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(ms => ms.FinancialInstitutionId == financialInstitution.Id);
    }
    private async Task<CalcEngineSetting> SeedCalcEngineSettingAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var calcEngineSetting = await GetCalcEngineSettingAsync(financialInstitution);
        if (calcEngineSetting != null)
        {
            logger.LogInformation("CalcEngineSettings '{@CalcEngineSetting}' already exists", calcEngineSetting);
            return calcEngineSetting;
        }
        calcEngineSetting = new CalcEngineSetting
        {
            Id = GetID(IdIndexes.CalcEngineSetting, idMultiplier, 1),
            FinancialInstitutionId = financialInstitution.Id,
            AmortMethod = "ACTUALDAY",
            RoundToFactor = "Up",
            DateCreatedUtc = DateTime.UtcNow,
            DateUpdatedUtc = DateTime.UtcNow,
            TimeStamp = [],
        };
        context.CalcEngineSettings.Add(calcEngineSetting);

        await context.SaveChangesAsync();
        logger.LogInformation("Created CalcEngineSettings '{@CalcEngineSetting}'", calcEngineSetting);
        return calcEngineSetting;
    }

    private async Task<FinancialInstitutionConfiguration?> GetFinancialInstitutionConfigurationAsync(FinancialInstitution financialInstitution) =>
        await context.FinancialInstitutionConfigurations
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(ms => ms.FinancialInstitutionId == financialInstitution.Id);

    private async Task<List<AanReasonDefaultText>> GetAanReasonDefaultTextAsync() =>
        await context.AanReasonDefaultTexts.ToListAsync();

    private async Task<List<InterfaceDefaultText>> GetInterfaceDefaultTextAsync() =>
        await context.InterfaceDefaultTexts.ToListAsync();

    private async Task<FinancialInstitutionConfiguration> SeedFinancialInstitutionConfigurationAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var financialInstitutionConfiguration = await GetFinancialInstitutionConfigurationAsync(financialInstitution);
        if (financialInstitutionConfiguration != null)
        {
            logger.LogInformation("FinancialInstitutionConfiguration '{@financialInstitutionConfiguration}' already exists", financialInstitutionConfiguration);
            return financialInstitutionConfiguration;
        }
        financialInstitutionConfiguration = new FinancialInstitutionConfiguration
        {
            Id = GetID(IdIndexes.FinancialInstitutionConfiguration, idMultiplier, 1),
            FinancialInstitutionId = financialInstitution.Id,
            Description = financialInstitution.Slug,
            DefaultProviderId = Guid.Empty,
            LogAuthorization = true,
            Active = true,
            DateCreatedUtc = DateTime.UtcNow,
            DateUpdatedUtc = DateTime.UtcNow,
            TimeStamp = [],
            EnableInitiateErrorRedirectUrl = false,
        };
        context.FinancialInstitutionConfigurations.Add(financialInstitutionConfiguration);

        await context.SaveChangesAsync();
        logger.LogInformation("Created FinancialInstitutionConfiguration '{@financialInstitutionConfiguration}'", financialInstitutionConfiguration);
        return financialInstitutionConfiguration;
    }

    private async Task SeedAanReasonDefaultTextAsync(List<Language> languages)
    {
        var aanReasons = await GetAanReasonDefaultTextAsync();
        var englishLanguageId = languages.Single(a => a.LanguageCode == "").Id;
        var spanishLanguageId = languages.Single(a => a.LanguageCode == "es-MX").Id;

        var newRecords = AanReasonDefaultTextEnum.Instance.GetAll();
        foreach (var item in newRecords.Select((record, index) => new { index, record }))
        {
            var realLanguageId = item.record.LanguageId == AanReasonDefaultTextEnum.SpanishDummyId ? spanishLanguageId : englishLanguageId;
            var existingRecord = aanReasons.SingleOrDefault(a => a.Code == item.record.Code && a.LanguageId == realLanguageId);
            if (existingRecord != null)
            {
                continue;       // record already exists.  do not duplicate.
            }
            item.record.Id = GetID(IdIndexes.AanReasonDefaultText, 0, item.index);
            item.record.DateCreatedUtc = DateTime.UtcNow;
            item.record.DateUpdatedUtc = DateTime.UtcNow;
            item.record.LanguageId = (item.record.LanguageId == AanReasonDefaultTextEnum.SpanishDummyId) ? spanishLanguageId : englishLanguageId;
            context.AanReasonDefaultTexts.Add(item.record);
        }
        await context.SaveChangesAsync();
        logger.LogInformation("Created AanReasonDefaultTexts");
    }

    private async Task SeedInterfaceDefaultTextAsync(List<Language> languages)
    {
        var aanReasons = await GetInterfaceDefaultTextAsync();
        var englishLanguageId = languages.Single(a => a.LanguageCode == "").Id;
        var spanishLanguageId = languages.Single(a => a.LanguageCode == "es-MX").Id;
        var newRecords = InterfaceDefaultTextsEnum.Instance.GetAll();

        foreach (var item in newRecords.Select((record, index) => new { index, record }))
        {
            var existingRecord = aanReasons
                .SingleOrDefault(a => a.Abrv == item.record.Abrv && a.LanguageId == item.record.LanguageId && a.IsStaticText && item.record.IsStaticText);
            if (existingRecord != null)
            {
                continue;       // record already exists.  do not create a duplicate.
            }
            item.record.Id = GetID(IdIndexes.InterfaceDefaultText, 0, item.index);
            item.record.DateCreatedUtc = DateTime.UtcNow;
            item.record.DateUpdatedUtc = DateTime.UtcNow;
            item.record.LanguageId = item.record.LanguageId == LanguageEnum.Spanish.Id ? spanishLanguageId : englishLanguageId;
            context.InterfaceDefaultTexts.Add(item.record);
        }
        await context.SaveChangesAsync();
        logger.LogInformation("Created InterfaceDefaultTexts");
    }

    private async Task<MaintenanceSetting?> GetMaintenanceSettingAsync(FinancialInstitution financialInstitution) =>
        await context.MaintenanceSettings
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(ms => ms.FinancialInstitutionId == financialInstitution.Id);

    private async Task<MaintenanceSetting> SeedMaintenanceSettingAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var maintenanceSetting = await GetMaintenanceSettingAsync(financialInstitution);
        if (maintenanceSetting != null)
        {
            logger.LogInformation("MaintenanceSettings '{@MaintenanceSettings}' already exists", maintenanceSetting);
            return maintenanceSetting;
        }
        maintenanceSetting = new MaintenanceSetting
        {
            Id = GetID(IdIndexes.MaintenanceSetting, idMultiplier, 1),
            FinancialInstitutionId = financialInstitution.Id,
        };
        context.MaintenanceSettings.Add(maintenanceSetting);

        await context.SaveChangesAsync();
        logger.LogInformation("Created MaintenanceSettings '{@MaintenanceSettings}'", maintenanceSetting);
        return maintenanceSetting;
    }

    private async Task<List<State>> GetStatesAsync(FinancialInstitution financialInstitution) =>
        await context.States
            .IgnoreQueryFilters()
            .Where(s => s.FinancialInstitutionId == financialInstitution.Id)
            .ToListAsync();

    private async Task<List<State>> SeedStatesAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var states = await GetStatesAsync(financialInstitution);
        if (states.Count > 0)
        {
            logger.LogInformation("States already exist");
            return states;
        }

        states = new List<State>
        {
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 1),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Alabama",
                Abrv = "AL",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 2),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Alaska",
                Abrv = "AK",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 3),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Arizona",
                Abrv = "AZ",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 4),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Arkansas",
                Abrv = "AR",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 5),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "California",
                Abrv = "CA",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 6),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Colorado",
                Abrv = "CO",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 7),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Connecticut",
                Abrv = "CT",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 8),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Delaware",
                Abrv = "DE",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 9),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Florida",
                Abrv = "FL",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 10),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Georgia",
                Abrv = "GA",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 11),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Hawaii",
                Abrv = "HI",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 12),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Idaho",
                Abrv = "ID",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 13),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Illinois",
                Abrv = "IL",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 14),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Indiana",
                Abrv = "IN",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 15),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Iowa",
                Abrv = "IA",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 16),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Kansas",
                Abrv = "KS",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 17),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Kentucky",
                Abrv = "KY",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 18),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Louisiana",
                Abrv = "LA",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 19),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Maine",
                Abrv = "ME",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 20),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Maryland",
                Abrv = "MD",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 21),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Massachusetts",
                Abrv = "MA",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 22),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Michigan",
                Abrv = "MI",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 23),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Minnesota",
                Abrv = "MN",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 24),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Mississippi",
                Abrv = "MS",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 25),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Missouri",
                Abrv = "MO",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 26),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Montana",
                Abrv = "MT",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 27),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Nebraska",
                Abrv = "NE",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 28),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Nevada",
                Abrv = "NV",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 29),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "New Hampshire",
                Abrv = "NH",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 30),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "New Jersey",
                Abrv = "NJ",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 31),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "New Mexico",
                Abrv = "NM",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 32),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "New York",
                Abrv = "NY",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 33),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "North Carolina",
                Abrv = "NC",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 34),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "North Dakota",
                Abrv = "ND",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 35),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Ohio",
                Abrv = "OH",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 36),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Oklahoma",
                Abrv = "OK",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 37),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Oregon",
                Abrv = "OR",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 38),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Pennsylvania",
                Abrv = "PA",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 39),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Rhode Island",
                Abrv = "RI",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 40),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "South Carolina",
                Abrv = "SC",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 41),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "South Dakota",
                Abrv = "SD",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 42),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Tennessee",
                Abrv = "TN",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 43),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Texas",
                Abrv = "TX",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 44),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Utah",
                Abrv = "UT",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 45),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Vermont",
                Abrv = "VT",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 46),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Virginia",
                Abrv = "VA",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 47),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Washington",
                Abrv = "WA",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 48),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "West Virginia",
                Abrv = "WV",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 49),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Wisconsin",
                Abrv = "WI",
            },
            new()
            {
                Id = GetID(IdIndexes.States, idMultiplier, 50),
                FinancialInstitutionId = financialInstitution.Id,
                Name = "Wyoming",
                Abrv = "WY",
            },
        };
        context.States.AddRange(states);
        await unitOfWork.CommitAsync();

        logger.LogInformation("Created States");
        return states;
    }


    private async Task<List<LoanStatusType>> GetLoanStatusTypesAsync(FinancialInstitution financialInstitution) =>
        await context.LoanStatusTypes
            .IgnoreQueryFilters()
            .Where(s => s.FinancialInstitutionId == financialInstitution.Id)
            .ToListAsync();

    private async Task<List<LoanStatusType>> SeedLoanStatusTypesAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var loanStatusTypes = await GetLoanStatusTypesAsync(financialInstitution);
        if (loanStatusTypes.Count > 0)
        {
            logger.LogInformation("Loan Status Types already exist");
            return loanStatusTypes;
        }

        //While many of these (payoff, etc) are not still actively being used, existing data could have loan applications in any status.
        loanStatusTypes = new List<LoanStatusType> {
            new() { Name = "Eligible Account Exclusion", Abrv = "LSTEAE", AppAbrv = "LSTEAE", Slug = "eligible_account_exclusion", Description = "Eligible Account Exclusion", ProductDependency = "PreProduct" },
            new() { Name = "Contact Info Changed Exclusion", Abrv = "LSTCICE", AppAbrv = "LSTCICE", Slug = "contact_info_changed_exclusion", Description = "Contact Info Changed Exclusion", ProductDependency = "PreProduct" },
            new() { Name = "Funding Pending", Abrv = "LSTFP", AppAbrv = "LSTFP", Slug = "funding_pending", Description = "Funding Pending", ProductDependency = "Product" },
            new() { Name = "Excluded by Bad Address", Abrv = "LSTBAE", AppAbrv = "LSTBAE", Slug = "bad_address_exclusion", Description = "Excluded by Bad Address", ProductDependency = "PreProduct" },
            new() { Name = "Processing Error", Abrv = "LSTPE", AppAbrv = "LSTPE", Slug = "processing_error", Description = "Processing Error", ProductDependency = "Product" },
            new() { Name = "Excluded by Bad Email", Abrv = "LSTBEE", AppAbrv = "LSTBEE", Slug = "bad_email_exclusion", Description = "Excluded by Bad Email", ProductDependency = "PreProduct" },
            new() { Name = "Approved", Abrv = "LSTA", AppAbrv = "LSTA", Slug = "approved", Description = "Approved", ProductDependency = "PreProduct" },
            new() { Name = "Denied", Abrv = "LSTD", AppAbrv = "LSTD", Slug = "denied", Description = "Denied", ProductDependency = "PreProduct" },
            new() { Name = "Canceled", Abrv = "LSTC", AppAbrv = "LSTC", Slug = "canceled", Description = "Canceled", ProductDependency = "None" },
            new() { Name = "Declined TILA", Abrv = "LSTDET", AppAbrv = "LSTDET", Slug = "declined_tila", Description = "Declined TILA", ProductDependency = "Product" },
            new() { Name = "Declined eConsent", Abrv = "LSTDEE", AppAbrv = "LSTDEE", Slug = "declined_econsent", Description = "Declined eConsent", ProductDependency = "PreProduct" },
            new() { Name = "Excluded Account", Abrv = "LSTEA", AppAbrv = "LSTEA", Slug = "excluded_account", Description = "Excluded Account", ProductDependency = "PreProduct" },
            new() { Name = "Excluded State", Abrv = "LSTES", AppAbrv = "LSTES", Slug = "excluded_state", Description = "Excluded State", ProductDependency = "PreProduct" },
            new() { Name = "Initiate", Abrv = "LSTI", AppAbrv = "LSTI", Slug = "initiate", Description = "Initiate", ProductDependency = "PreProduct" },
            new() { Name = "Military No Product", Abrv = "LSTMNP", AppAbrv = "LSTMNP", Slug = "military_no_product", Description = "Military No Product", ProductDependency = "PreProduct" },
            new() { Name = "Cancelled Before DE", Abrv = "LSTCBD", AppAbrv = "LSTCBD", Slug = "cancelled_before_de", Description = "Cancelled Before DE", ProductDependency = "PreProduct" },
            new() { Name = "Cancelled After DE", Abrv = "LSTCAD", AppAbrv = "LSTCAD", Slug = "cancelled_after_de", Description = "Cancelled After DE", ProductDependency = "PreProduct" },
            new() { Name = "Restricted due to Loan in Process", Abrv = "LSTLIP", AppAbrv = "LSTLIP", Slug = "loan_in_process", Description = "Restricted due to Loan in Process", ProductDependency = "PreProduct" },
            new() { Name = "Is eConsent", Abrv = "LSTIE", AppAbrv = "LSTDIE", Slug = "is_econsent", Description = "Is eConsent", ProductDependency = "PreProduct" },
            new() { Name = "Accept eConsent", Abrv = "LSTAE", AppAbrv = "LSTDAE", Slug = "accept_econsent", Description = "Accept eConsent", ProductDependency = "PreProduct" },
            new() { Name = "Loan Application", Abrv = "LSTLA", AppAbrv = "LSTLA", Slug = "loan_application", Description = "Loan Application", ProductDependency = "PreProduct" },
            new() { Name = "Accept TILA", Abrv = "LSTAT", AppAbrv = "LSTAT", Slug = "accept_tila", Description = "Accept TILA", ProductDependency = "Product" },
            new() { Name = "Display TILA", Abrv = "LSTDT", AppAbrv = "LSTDT", Slug = "display_tila", Description = "Display TILA", ProductDependency = "Product" },
            new() { Name = "Loan Application Fee", Abrv = "LSTLAF", AppAbrv = "LSTLAF", Slug = "loan_fee", Description = "Loan Application Fee", ProductDependency = "PreProduct" },
            new() { Name = "Payoff Initiate", Abrv = "LSTPOINI", AppAbrv = "LSTPOINI", Slug = "payoff_initiate", Description = "Payoff Initiate", ProductDependency = "None" },
            new() { Name = "Payoff Completed", Abrv = "LSTPOCMP", AppAbrv = "LSTPOCMP", Slug = "payoff_complete", Description = "Payoff Completed", ProductDependency = "None" },
            new() { Name = "Payoff Cancelled", Abrv = "LSTPOCNC", AppAbrv = "LSTPOCNC", Slug = "payoff_cancel", Description = "Payoff Cancelled", ProductDependency = "None" },
            new() { Name = "Payoff Error", Abrv = "LSTPOERR", AppAbrv = "LSTPOERR", Slug = "payoff_error", Description = "Payoff Error", ProductDependency = "None" },
            new() { Name = "Payoff Complete", Abrv = "LSTPOPOC", AppAbrv = "LSTPOPOC", Slug = "payoff_po_complete", Description = "Payoff Complete", ProductDependency = "Product" },
            new() { Name = "Payoff Failed", Abrv = "LSTPOPOF", AppAbrv = "LSTPOPOF", Slug = "payoff_po_failed", Description = "Payoff Failed", ProductDependency = "Product" },
            new() { Name = "Local", Abrv = "mla_local", AppAbrv = "mla_local", Slug = "mla_local", Description = "MLA Local", ProductDependency = "None" },
            new() { Name = "Remote", Abrv = "mla_remote", AppAbrv = "mla_remote", Slug = "mla_remote", Description = "MLA Remote", ProductDependency = "None" },
            new() { Name = "Error", Abrv = "mla_error", AppAbrv = "mla_error", Slug = "mla_error", Description = "MLA Error", ProductDependency = "None" },
            new() { Name = "Display Loan Landing", Abrv = "LSTDL", AppAbrv = "LSTDL", Slug = "display_loan_landing", Description = "Display Loan Landing", ProductDependency = "PreProduct" },
            new() { Name = "Display Awareness", Abrv = "LSTDA", AppAbrv = "LSTDA", Slug = "display_awareness", Description = "Display Awareness", ProductDependency = "PreProduct" },
            new() { Name = "Cancelled PreApp", Abrv = "LSTCPA", AppAbrv = "LSTCPA", Slug = "cancelled_pre_app", Description = "Cancelled PreApp", ProductDependency = "PreProduct" },
            new() { Name = "Loan Hub", Abrv = "LSTLH", AppAbrv = "LSTLH", Slug = "loan_hub", Description = "Loan Hub", ProductDependency = "PreProduct" },
            new() { Name = "Cancelled Awareness", Abrv = "LSTCA", AppAbrv = "LSTCA", Slug = "cancelled_awareness", Description = "Cancelled Awareness", ProductDependency = "PreProduct" },
            new() { Name = "Restricted Due To Denied Loans", Abrv = "LSTEDL", AppAbrv = "LSTEDL", Slug = "Denied Loan", Description = "Restricted Due To Denied Loans", ProductDependency = "PreProduct" },
            new() { Name = "ESignature Pending", Abrv = "LSTESP", AppAbrv = "LSTESP", Slug = "esignature_pending", Description = "ESignature Pending", ProductDependency = "Product" },
            new() { Name = "Excluded Missing Email", Abrv = "LSTEME", AppAbrv = "LSTEME", Slug = "excluded_missing_email", Description = "Excluded Missing Email", ProductDependency = "PreProduct" },
            new() { Name = "ESignature Error", Abrv = "LSTESE", AppAbrv = "LSTESE", Slug = "esignature_error", Description = "ESignature Error", ProductDependency = "Product" },
            new() { Name = "Restricted Due To Bankruptcy", Abrv = "LSTBEX", AppAbrv = "LSTBEX", Slug = "bankruptcy_exclusion", Description = "Restricted Due To Bankruptcy", ProductDependency = "PreProduct" },
            new() { Name = "Excluded By Age", Abrv = "LSTAGE", AppAbrv = "LSTAGE", Slug = "age_exclusion", Description = "Excluded By Age", ProductDependency = "PreProduct" },
            new() { Name = "Troubled Debt Exclusion", Abrv = "LSTTDE", AppAbrv = "LSTTDE", Slug = "troubled_debt_exclusion", Description = "Troubled Debt Exclusion", ProductDependency = "PreProduct" },
            new() { Name = "Restricted Due To Signature Pending", Abrv = "LSTRESP", AppAbrv = "LSTRESP", Slug = "restricted_esignature_pending", Description = "Restricted Due To Signature Pending", ProductDependency = "PreProduct" },
            new() { Name = "Not Funded", Abrv = "LSTNF", AppAbrv = "LSTNF", Slug = "not_funded", Description = "Not Funded", ProductDependency = "Product" },
            new() { Name = "Excluded Personal Loan", Abrv = "LSTEPL", AppAbrv = "LSTEPL", Slug = "excluded_personal_loan", Description = "Excluded Personal Loan", ProductDependency = "PreProduct" },
            new() { Name = "Maintenance", Abrv = "LSTM", AppAbrv = "LSTM", Slug = "maintenance", Description = "Maintenance", ProductDependency = "PreProduct" },
            new() { Name = "Excluded Joint Account", Abrv = "LSTJAEX", AppAbrv = "LSTJAEX", Slug = "joint_account_exclusion", Description = "Excluded Joint Account", ProductDependency = "PreProduct" },
            new() { Name = "Fraud Control", Abrv = "LSTFC", AppAbrv = "LSTFC", Slug = "fraud_control", Description = "Fraud Control", ProductDependency = "PreProduct" },
            new() { Name = "Fraud Control Code Expired", Abrv = "LSTFCE", AppAbrv = "LSTFCE", Slug = "fraud_control_expired", Description = "Fraud Control Code Expired", ProductDependency = "PreProduct" },
            new() { Name = "Excluded Fraud Control", Abrv = "LSEFC", AppAbrv = "LSEFC", Slug = "excluded_fraud_control", Description = "Excluded Fraud Control", ProductDependency = "PreProduct" },
            new() { Name = "Excluded Missing Email and Phone", Abrv = "LSEMEP", AppAbrv = "LSEMEP", Slug = "excluded_missing_email_and_phone", Description = "Excluded Missing Email and Phone", ProductDependency = "PreProduct" },
            new() { Name = "Cancelled Fraud Control", Abrv = "LSTCFC", AppAbrv = "LSTCFC", Slug = "cancelled_fraud_control", Description = "Cancelled Fraud Control", ProductDependency = "PreProduct" },
            new() { Name = "Excluded Loan Funding Wait Period", Abrv = "LSELFWP", AppAbrv = "LSELFWP", Slug = "excluded_loan_funding_wait_period", Description = "Excluded Loan Funding Wait Period", ProductDependency = "PreProduct" },
            new() { Name = "QCF Audit", Abrv = "LSTQCFA", AppAbrv = "LSTQCFA", Slug = "qcf_audit", Description = "QCF Audit", ProductDependency = "None" },
            new() { Name = "Blocklist Exclusion", Abrv = "LSTBLE", AppAbrv = "LSTBLE", Slug = "blocklist_exclusion", Description = "Blocklist Exclusion", ProductDependency = "PreProduct" },
            new() { Name = "Funding Complete", Abrv = "LSTCMP", AppAbrv = "LSTCMP", Slug = "completed", Description = "Funding Complete", ProductDependency = "Product" },
            new() { Name = "Declined Data Collection", Abrv = "LSTDEDC", AppAbrv = "LSTDEDC", Slug = "declinde_data_collection", Description = "Declined Data Collection", ProductDependency = "None" },
            new() { Name = "Data Collection", Abrv = "LSTDC", AppAbrv = "LSTDC", Slug = "data_collection", Description = "Data Collection", ProductDependency = "None" },
            new() { Name = "In Process", Abrv = "LSTIP", AppAbrv = "LSTIP", Slug = "in_progress", Description = "In Progress", ProductDependency = "None" },
        };
        foreach (var item in loanStatusTypes.Select((lsType, index) => new { index, lsType }))
        {
            item.lsType.Id = GetID(IdIndexes.LoanStatusTypes, idMultiplier, item.index);
            item.lsType.FinancialInstitutionId = financialInstitution.Id;
            item.lsType.DateCreatedUtc = DateTime.UtcNow;
            item.lsType.DateUpdatedUtc = DateTime.UtcNow;
            item.lsType.TimeStamp = [];
            item.lsType.IsDeleted = false;
        }
        context.LoanStatusTypes.AddRange(loanStatusTypes);
        await unitOfWork.CommitAsync();

        logger.LogInformation("Created LoanStatusTypes");
        return loanStatusTypes;
    }

    private async Task<List<PaymentOptionType>> GetPaymentOptionTypesAsync(FinancialInstitution financialInstitution) =>
        await context.PaymentOptionTypes
            .IgnoreQueryFilters()
            .Where(s => s.FinancialInstitutionId == financialInstitution.Id)
            .ToListAsync();

    private async Task<List<PaymentOptionType>> SeedPaymentOptionTypesAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var paymentOptionTypes = await GetPaymentOptionTypesAsync(financialInstitution);
        if (paymentOptionTypes.Count > 0)
        {
            logger.LogInformation("Payment Option Types already exist");
            return paymentOptionTypes;
        }

        paymentOptionTypes = new List<PaymentOptionType> {
            new() { Name = "One-Time", Abrv = "OT", Slug = "one_time", ProductDependency = "FeeBased" },
            new() { Name = "Bi-Weekly", Abrv = "BW", Slug = "bi_weekly", ProductDependency = "InterestBased" },
            new() { Name = "Monthly - Calculated First Payment Date", Abrv = "MCFPD", Slug = "monthly_calculated_first_payment_date", ProductDependency = "InterestBased" },
            new() { Name = "Monthly - Pick a Day", Abrv = "MPAD", Slug = "monthly_pick_a_day", ProductDependency = "InterestBased" },
            new() { Name = "None", Abrv = "NONE", Slug = "none", ProductDependency = "None" },
        };
        foreach (var item in paymentOptionTypes.Select((poType, index) => new { index, poType }))
        {
            item.poType.Id = GetID(IdIndexes.PaymentOptionTypes, idMultiplier, item.index);
            item.poType.FinancialInstitutionId = financialInstitution.Id;
            item.poType.AppAbrv = item.poType.Abrv;
            item.poType.Description = item.poType.Name;
            item.poType.DateCreatedUtc = DateTime.UtcNow;
            item.poType.DateUpdatedUtc = DateTime.UtcNow;
            item.poType.TimeStamp = [];
            item.poType.IsDeleted = false;
        }
        context.PaymentOptionTypes.AddRange(paymentOptionTypes);
        await unitOfWork.CommitAsync();

        logger.LogInformation("Created PaymentOptionTypes");
        return paymentOptionTypes;
    }

    private async Task<List<LoanType>> GetLoanTypesAsync(FinancialInstitution financialInstitution) =>
        await context.LoanTypes
            .IgnoreQueryFilters()
            .Where(s => s.FinancialInstitutionId == financialInstitution.Id)
            .ToListAsync();

    private async Task<List<LoanType>> SeedLoanTypesAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var loanTypes = await GetLoanTypesAsync(financialInstitution);
        if (loanTypes.Count > 0)
        {
            logger.LogInformation("Loan Types already exist");
            return loanTypes;
        }

        loanTypes = new List<LoanType> {
            new() { Name = "Interest Based", Abrv = "IB", Slug = "interest_based", },
            new() { Name = "No Product", Abrv = "NP", Slug = "no_product", },
            new() { Name = "Fee Based", Abrv = "FB", Slug = "fee_based", },
        };
        foreach (var item in loanTypes.Select((loanType, index) => new { index, loanType }))
        {
            item.loanType.Id = GetID(IdIndexes.LoanTypes, idMultiplier, item.index);
            item.loanType.FinancialInstitutionId = financialInstitution.Id;
            item.loanType.AppAbrv = item.loanType.Abrv;
            item.loanType.Description = item.loanType.Name;
            item.loanType.DateCreatedUtc = DateTime.UtcNow;
            item.loanType.DateUpdatedUtc = DateTime.UtcNow;
        }
        context.LoanTypes.AddRange(loanTypes);
        await unitOfWork.CommitAsync();

        logger.LogInformation("Created LoanTypes");
        return loanTypes;
    }

    private async Task<List<LoanCategory>> GetLoanCategoriesAsync(FinancialInstitution financialInstitution) =>
        await context.LoanCategories
            .IgnoreQueryFilters()
            .Where(s => s.FinancialInstitutionId == financialInstitution.Id)
            .ToListAsync();

    private async Task<List<LoanCategory>> SeedLoanCategoriesAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var loanCategories = await GetLoanCategoriesAsync(financialInstitution);
        if (loanCategories.Count > 0)
        {
            logger.LogInformation("Loan Types already exist");
            return loanCategories;
        }

        loanCategories = new List<LoanCategory> {
            new() { Name = "Unsecured Closed-End", Abrv = "UC", Slug = "unsecured_closed_end", IsOpen = false, },
            new() { Name = "Unsecured Open-End", Abrv = "UO", Slug = "line_of_credit", IsOpen = true, },
        };
        foreach (var item in loanCategories.Select((loanCategory, index) => new { index, loanCategory }))
        {
            item.loanCategory.Id = GetID(IdIndexes.LoanCategories, idMultiplier, item.index);
            item.loanCategory.FinancialInstitutionId = financialInstitution.Id;
            item.loanCategory.AppAbrv = item.loanCategory.Abrv;
            item.loanCategory.Description = item.loanCategory.Name;
            item.loanCategory.DateCreatedUtc = DateTime.UtcNow;
            item.loanCategory.DateUpdatedUtc = DateTime.UtcNow;
        }
        context.LoanCategories.AddRange(loanCategories);
        await unitOfWork.CommitAsync();

        logger.LogInformation("Created LoanCategories");
        return loanCategories;
    }

    public async Task SeedSecondaryTestDataAsync()
    {
        await SeedRoleDataAsync();

        await unitOfWork.CommitAsync();
    }

    private async Task<List<AspNetRole>> GetRolesAsync() =>
        await context.AspNetRoles
            .IgnoreQueryFilters()
            .ToListAsync();

    private async Task<List<DocumentType>> GetDocumentTypesAsync(FinancialInstitution financialInstitution) =>
        await context.DocumentTypes
            .IgnoreQueryFilters()
            .Where(dt => dt.FinancialInstitutionId == financialInstitution.Id)
            .ToListAsync();

    private async Task<List<ScoreType>> GetScoreTypesAsync(FinancialInstitution financialInstitution) =>
        await context.ScoreTypes
            .Where(sc => sc.FinancialInstitutionId == financialInstitution.Id)
            .IgnoreQueryFilters()
            .ToListAsync();

    private async Task<List<Language>> GetLanguagesAsync() =>
        await context.Languages
            .IgnoreQueryFilters()
            .ToListAsync();
    private async Task<List<LanguageSupportStatus>> GetLanguageSupportStatusesAsync(FinancialInstitution financialInstitution) =>
        await context.LanguageSupportStatuses
            .Where(sl => sl.FinancialInstitutionId == financialInstitution.Id)
            .IgnoreQueryFilters()
            .ToListAsync();

    private async Task<List<DecisionModelType>> GetDecisionModelTypesAsync(FinancialInstitution financialInstitution) =>
        await context.DecisionModelTypes
            .Where(dmt => dmt.FinancialInstitutionId == financialInstitution.Id)
            .IgnoreQueryFilters()
            .ToListAsync();

    private async Task<List<DecisionModelStatus>> GetDecisionModelStatusesAsync(FinancialInstitution financialInstitution) =>
        await context.DecisionModelStatuses
            .Where(dmt => dmt.FinancialInstitutionId == financialInstitution.Id)
            .IgnoreQueryFilters()
            .ToListAsync();

    private async Task<List<MemberInterfacePresentation>> GetMemberInterfacePresentationsAsync(FinancialInstitution financialInstitution) =>
        await context.MemberInterfacePresentations
            .Where(dmt => dmt.FinancialInstitutionId == financialInstitution.Id)
            .IgnoreQueryFilters()
            .ToListAsync();

    private async Task<List<MemberInterfacePresentationTemplate>> GetMemberInterfacePresentationTemplatesAsync(FinancialInstitution financialInstitution) =>
        await context.MemberInterfacePresentationTemplates
            .Where(dmt => dmt.FinancialInstitutionId == financialInstitution.Id)
            .IgnoreQueryFilters()
            .ToListAsync();

    public async Task<List<AspNetRole>> SeedRoleDataAsync()
    {
        var roleNames = Service.Models.FIConfiguration.UserManagement.Constants.GetAllRoles();
        var roles = await GetRolesAsync();
        var missingRoles = roleNames
            .Except(roles.Select(r => r.Name))
            .ToList();

        if (!missingRoles.Any())
        {
            logger.LogInformation("Roles already exist");
            return roles;
        }

        foreach (var roleName in missingRoles)
        {
            var newRecord = new AspNetRole
            {
                Id = GenerateStableGuid($"********-0000-0000-000{(int)IdIndexes.AspNetRole}-", roleName ?? ""),
                Name = roleName,
            };
            await context.AspNetRoles.AddAsync(newRecord);
        }

        await unitOfWork.CommitAsync();
        logger.LogInformation("Created Roles");

        return await GetRolesAsync();
    }

    public async Task<List<ScoreType>> SeedScoreTypesAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var newScoreTypes = new List<ScoreType> {
            new() { AppAbrv = "IntrestBaseLoanPaybackPeriod", Abrv = "IntrestBaseLoanPaybackPeriod", Slug = "intrest_base_loan_payback_period", Name = "Q-Cash Plus Loan Payback Period", Description = "Q-Cash Plus Loan Payback Period", IsDeleted = false },
            new() { AppAbrv = "DepositstobeincludedforAggregateDepositTotal", Abrv = "DepositstobeincludedforAggregateDepositTotal", Slug = "deposits_to_be_included_for_aggregate_deposit_total", Name = "Deposits to be included for Aggregate Deposit Total", Description = "Deposits to be included for Aggregate Deposit Total", IsDeleted = false },
            new() { AppAbrv = "DepositstobeexcludedforAggregateDepositTotal", Abrv = "DepositstobeexcludedforAggregateDepositTotal", Slug = "deposits_to_be_excluded_for_aggregate_deposit_total", Name = "Deposits to be excluded for Aggregate Deposit Total", Description = "Deposits to be excluded for Aggregate Deposit Total", IsDeleted = false },
            new() { AppAbrv = "VoidedTransaction", Abrv = "VoidedTransaction", Slug = "voided_transaction", Name = "Voided Transaction", Description = "Voided Transaction", IsDeleted = false },
            new() { AppAbrv = "NSFTransactionCode", Abrv = "NSFTransactionCode", Slug = "nsf_transaction_code", Name = "NSF Transaction Code", Description = "NSF Transaction Code", IsDeleted = false },
            new() { AppAbrv = "TransformationScore", Abrv = "TransformationScore", Slug = "transformation_score", Name = "Transformation Score", Description = "Transformation Score Type", IsDeleted = false },
            new() { AppAbrv = "LoanOriginationFee", Abrv = "LoanOriginationFee", Slug = "loan_origination_fee", Name = "Loan Origination Fee", Description = "Loan Origination Fee Score Type", IsDeleted = false },
        };

        var existingScoreTypes = await GetScoreTypesAsync(financialInstitution);
        var existingCount = existingScoreTypes.Count;
        var missingRecords = newScoreTypes.Select(st => st.Name)
            .Except(existingScoreTypes.Select(r => r.Name))
            .ToList();

        if (!missingRecords.Any())
        {
            logger.LogInformation("Records already exist");
            return existingScoreTypes;
        }

        var recordIndex = existingCount;
        foreach (var name in missingRecords)
        {
            var record = newScoreTypes.Single(st => st.Name == name);
            record.Id = GetID(IdIndexes.ScoreTypes, idMultiplier, recordIndex);
            record.FinancialInstitutionId = financialInstitution.Id;
            record.DateCreatedUtc = DateTime.UtcNow;
            record.DateUpdatedUtc = DateTime.UtcNow;
            await context.ScoreTypes.AddAsync(record);
            recordIndex++;
        }

        await unitOfWork.CommitAsync();
        logger.LogInformation("Created Score Types");

        return await GetScoreTypesAsync(financialInstitution);
    }

    public async Task<List<DecisionModelType>> SeedDecisionModelTypesAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var newRecords = new List<DecisionModelType> {
            new() { AppAbrv = "TR", Abrv = "T", Slug = "traditional", Name = "Traditional", Description = "Traditional model type", },
            new() { AppAbrv = "ST", Abrv = "S", Slug = "statistical", Name = "Statistical", Description = "Statistical model type", },
        };

        var existingRecords = await GetDecisionModelTypesAsync(financialInstitution);
        var existingCount = existingRecords.Count;
        var missingRecords = newRecords.Select(st => st.Name)
            .Except(existingRecords.Select(r => r.Name))
            .ToList();

        if (!missingRecords.Any())
        {
            logger.LogInformation("Records already exist");
            return existingRecords;
        }

        var recordIndex = existingCount;
        foreach (var name in missingRecords)
        {
            var record = newRecords.Single(st => st.Name == name);
            record.Id = GetID(IdIndexes.DecisionModelTypes, idMultiplier, recordIndex);
            record.FinancialInstitutionId = financialInstitution.Id;
            record.DateCreatedUtc = DateTime.UtcNow;
            record.DateUpdatedUtc = DateTime.UtcNow;
            record.IsDeleted = false;
            await context.DecisionModelTypes.AddAsync(record);
            recordIndex++;
        }

        await unitOfWork.CommitAsync();
        logger.LogInformation("Created Decision Model Types");
        return await GetDecisionModelTypesAsync(financialInstitution);
    }

    public async Task<List<DecisionModelStatus>> SeedDecisionModelStatusesAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var newRecords = new List<DecisionModelStatus> {
            new() { AppAbrv = "ALRT", Abrv = "ALRT", Slug = "alerts", Name = "Alerts", },
            new() { AppAbrv = "INCT", Abrv = "INCT", Slug = "inactive", Name = "Inactive", },
            new() { AppAbrv = "ACTV", Abrv = "ACTV", Slug = "active", Name = "Active", },
            new() { AppAbrv = "ARHV", Abrv = "ARHV", Slug = "archived", Name = "Archived", },
        };

        var existingRecords = await GetDecisionModelStatusesAsync(financialInstitution);
        var existingCount = existingRecords.Count;
        var missingRecords = newRecords.Select(st => st.Name)
            .Except(existingRecords.Select(r => r.Name))
            .ToList();

        if (!missingRecords.Any())
        {
            logger.LogInformation("Records already exist");
            return existingRecords;
        }

        var recordIndex = existingCount;
        foreach (var name in missingRecords)
        {
            var record = newRecords.Single(st => st.Name == name);
            record.Id = GetID(IdIndexes.DecisionModelStatuses, idMultiplier, recordIndex);
            record.FinancialInstitutionId = financialInstitution.Id;
            record.DateCreatedUtc = DateTime.UtcNow;
            record.DateUpdatedUtc = DateTime.UtcNow;
            record.IsDeleted = false;
            await context.DecisionModelStatuses.AddAsync(record);
            recordIndex++;
        }

        await unitOfWork.CommitAsync();
        logger.LogInformation("Created Decision Model Statuses");
        return await GetDecisionModelStatusesAsync(financialInstitution);
    }

    public async Task<List<MemberInterfacePresentation>> SeedMemberInterfacePresentationsAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var newRecords = new List<MemberInterfacePresentation> {
            new() { Abrv = "QCash", Name = "QCash", IsDefault = true, HeaderFooterColor = "#3282b8", HeaderFooterTextColor = "#1d3e53", FormFooterColor = "#3282b8", BodyBackgroundColor = "#ffffff", BodyTextColor = "#1d3e53", TitleTextColor = "#0f4c75", BorderColor = "#1d3e53", BodyFont = "Arial, Helvetica, Geneva, sans-serif", HyperlinkColor = "#3282b8", HyperlinkHoverColor = "#bbe1fa", LogoLink = "~/Content/themes/qcash/images/Blank.png", Gray1 = "#e6e5e6", Gray2 = "#f4f4f4", Gray0 = "#f0f0f0", CssPath = "~/Content/themes/qcash/stylesheets/variables.css", HyperlinkDisabledColor = "#0f4c75", HyperlinkDisabledHoverColor = "#bbe1fa", HyperlinkDownloadColor = "#0f4c75", HyperlinkDownloadHoverColor = "#bbe1fa", PopUpHeaderBackgroundColor = "#3282b8", PopUpHeaderTextColor = "#ffffff", CarouselButtonColor = "#0f4c75", CarouselButtonHoverColor = "#0f4c75", HyperlinkCancelRequestBackgroundColor = "#bbe1fa", HyperlinkCancelRequestHoverColor = "#3282b8", HyperlinkCancelRequestTextColor = "", PreapprovedGroupBackgroundColor = "#3282b8", PreapprovedGroupHoverColor = "#bbe1fa", LoanHubBackgroundColor = "#ffffff", LoanHubPayoffBackgroundColor = "#3282b8", LoanHubPayoffHoverColor = "#bbe1fa", QCashConnectBoxBackgroundColor = "#243B5F", FirebirdPrimaryColor = "#0a48f5", FirebirdSecondaryColor = "#9747ff", },
            new() { Abrv = "QCashTemplate", Name = "QCashTemplate", IsDefault = false, HeaderFooterColor = "#3282b8", HeaderFooterTextColor = "#1d3e53", FormFooterColor = "#3282b8", BodyBackgroundColor = "#ffffff", BodyTextColor = "#1d3e53", TitleTextColor = "#0f4c75", BorderColor = "#1d3e53", BodyFont = "Arial, Helvetica, Geneva, sans-serif", HyperlinkColor = "#3282b8", HyperlinkHoverColor = "#bbe1fa", LogoLink = "mainlogo.png", Gray1 = "#e6e5e6", Gray2 = "#f4f4f4", Gray0 = "#f0f0f0", CssPath = "variables.css", HyperlinkDisabledColor = "#0f4c75", HyperlinkDisabledHoverColor = "#bbe1fa", HyperlinkDownloadColor = "#0f4c75", HyperlinkDownloadHoverColor = "#bbe1fa", PopUpHeaderBackgroundColor = "#3282b8", PopUpHeaderTextColor = "#ffffff", CarouselButtonColor = "#0f4c75", CarouselButtonHoverColor = "#0f4c75", HyperlinkCancelRequestBackgroundColor = "#bbe1fa", HyperlinkCancelRequestHoverColor = "#3282b8", HyperlinkCancelRequestTextColor = "", PreapprovedGroupBackgroundColor = "#3282b8", PreapprovedGroupHoverColor = "#bbe1fa", LoanHubBackgroundColor = "#ffffff", LoanHubPayoffBackgroundColor = "#3282b8", LoanHubPayoffHoverColor = "#bbe1fa", QCashConnectBoxBackgroundColor = "#243B5F",FirebirdPrimaryColor = "#0a48f5", FirebirdSecondaryColor = "#9747ff", },
        };

        var existingRecords = await GetMemberInterfacePresentationsAsync(financialInstitution);
        var existingCount = existingRecords.Count;
        var missingRecords = newRecords.Select(st => st.Name)
            .Except(existingRecords.Select(r => r.Name))
            .ToList();

        if (!missingRecords.Any())
        {
            logger.LogInformation("Records already exist");
            return existingRecords;
        }

        var recordIndex = existingCount;
        foreach (var name in missingRecords)
        {
            var record = newRecords.Single(st => st.Name == name);
            record.Id = GetID(IdIndexes.MemberInterfacePresentation, idMultiplier, recordIndex);
            record.FinancialInstitutionId = financialInstitution.Id;
            record.DateCreatedUtc = DateTime.UtcNow;
            record.DateUpdatedUtc = DateTime.UtcNow;
            await context.MemberInterfacePresentations.AddAsync(record);
            recordIndex++;
        }

        await unitOfWork.CommitAsync();
        logger.LogInformation("Created Member Interface Presentations");
        return await GetMemberInterfacePresentationsAsync(financialInstitution);
    }

    public async Task<List<MemberInterfacePresentationTemplate>> SeedMemberInterfacePresentationTemplatesAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var newRecords = new List<MemberInterfacePresentationTemplate> {
            new() { Abrv = "QCash", IsDefault = true,
                Template = "body{color:{BodyTextColor};font-family:{BodyFont}}h1,h2,h3,h4,h5,h6{font-family:{BodyFont};color:{TitleTextColor}}body,.header-end span,.header-end>span{background-color:{BodyBackgroundColor}}a{color:{HyperlinkColor}}a:hover,a:focus{color:{HyperlinkHoverColor}}a.button,a.button-large,input.button,input.button-large{background-color:{HyperlinkColor}}a:hover.button,a:hover.button-large,input:hover.button,input:hover.button-large,a:focus.button,a:focus.button-large,input:focus.button,input:focus.button-large{background-color:{HyperlinkHoverColor}}a[disabled]{color:{HyperlinkDisabledColor}}a[disabled]:hover{color:{HyperlinkDisabledHoverColor}}a[disabled].button,a[disabled].button-large,input[disabled].button,input[disabled].button-large{background-color:{HyperlinkDisabledColor}}a[disabled]:hover.button,a[disabled]:hover.button-large,input[disabled]:hover.button,input[disabled]:hover.button-large{background-color:{HyperlinkDisabledHoverColor}}a.print-button,a.print-button,input.print-button{background-color:{HyperlinkDownloadColor}}a:hover.print-button,a:hover.print-button,input:hover.print-button,input:hover.print-button{background-color:{HyperlinkDownloadHoverColor}}.member-header,.member-footer,.member-header h1{background-color:{HeaderFooterColor};color:{HeaderFooterTextColor}}.member-form-footer{border-top:1px solid{BorderColor};color:{FormFooterColor}}.title{border-bottom:1px solid{BorderColor};border-top:1px solid{BorderColor};color:{TitleTextColor}}hr{border-color:{BorderColor}}.border{border:1px solid{BorderColor}}.header-end{background-image:-webkit-gradient(linear,50% 0%,50% 100%,color-stop(40%,{HeaderFooterColor}),color-stop(100%,{BodyBackgroundColor}));background-image:-webkit-linear-gradient({HeaderFooterColor}, 40%,{BodyBackgroundColor});background-image:-moz-linear-gradient({HeaderFooterColor}, 40%,{BodyBackgroundColor});background-image:-o-linear-gradient({HeaderFooterColor}, 40%,{BodyBackgroundColor});background-image:linear-gradient({HeaderFooterColor}, 40%,{BodyBackgroundColor})}.reveal-modal h4{background-color:{HeaderFooterColor};-moz-border-radius-topleft:6px;-webkit-border-top-left-radius:6px;border-top-left-radius:6px;-moz-border-radius-topright:6px;-webkit-border-top-right-radius:6px;border-top-right-radius:6px}.reveal-modal h4.title{background-color: {PopUpHeaderBackgroundColor}; color: {PopUpHeaderTextColor}} .btn--carousel{background-color:{CarouselButtonColor};}.btn--carousel:hover,.btn--carousel:focus,.btn--carousel:active {background-color: {CarouselButtonHoverColor};} input.btn-cancel, button.btn-cancel { background-color: {HyperlinkCancelRequestBackgroundColor}; color: {HyperlinkCancelRequestTextColor}} input.btn-cancel:hover, button.btn-cancel:hover {background-color: {HyperlinkCancelRequestHoverColor}; color: {HyperlinkCancelRequestTextColor}} .button-pre-approved{background-color:{PreapprovedGroupBackgroundColor};color: white}.button-pre-approved:hover{background-color:{PreapprovedGroupHoverColor};color: white}.button-standalone{background-color:{LoanHubSDLBackgroundColor};color:white}.button-standalone:hover{background-color:{LoanHubSDLHoverColor};color: white}.button-payoff-enabled{background-color:{LoanHubPayoffBackgroundColor};color: white}.button-payoff-enabled:hover{background-color:{LoanHubPayoffHoverColor};color: white}.loan-hub{background-color:{LoanHubBackgroundColor}}#qcash-connect-box.login-form-content{background-color: {QCashConnectBoxBackgroundColor}}",
                TemplateIe8 = @".gradient {
  -pie-background: linear-gradient({BodyBackgroundColor}, {Gray0});
  behavior: url(/Scripts/PIE.htc);
}

/* line 10, ../sass/ie8.scss */
.member-header, .admin-header {
  border-radius: 6px 6px 0px 0px;
  behavior: url(/Scripts/PIE.htc);
}

/* line 15, ../sass/ie8.scss */
.header-end {
  -pie-background: linear-gradient({HeaderFooterColor}, {BodyBackgroundColor});
  behavior: url(/Scripts/PIE.htc);
}

/* line 20, ../sass/ie8.scss */
.f-dropdown {
  width: 200px !important;
}

/* line 24, ../sass/ie8.scss */
.admin-user-dropdown {
  -pie-background: linear-gradient(#a2a2a2, #8e8e8e);
  behavior: url(/Scripts/PIE.htc);
}

/* line 30, ../sass/ie8.scss */
table tr {
  border-bottom: 1px solid #ddd;
}

/* line 36, ../sass/ie8.scss */
.admin-menu-list li, .admin-menu-list-second-level li {
  -pie-background: linear-gradient({BodyBackgroundColor}, {Gray2});
  behavior: url(/Scripts/PIE.htc);
}
/* line 40, ../sass/ie8.scss */
.admin-menu-list li:hover, .admin-menu-list-second-level li:hover {
  -pie-background: linear-gradient({BodyBackgroundColor}, {Gray1});
  behavior: url(/Scripts/PIE.htc);
}",

            },
        };

        var existingRecords = await GetMemberInterfacePresentationTemplatesAsync(financialInstitution);
        var existingCount = existingRecords.Count;
        var missingRecords = newRecords.Select(st => st.Abrv)
            .Except(existingRecords.Select(r => r.Abrv))
            .ToList();

        if (!missingRecords.Any())
        {
            logger.LogInformation("Records already exist");
            return existingRecords;
        }

        var recordIndex = existingCount;
        foreach (var name in missingRecords)
        {
            var record = newRecords.Single(st => st.Abrv == name);
            record.Id = GetID(IdIndexes.MemberInterfacePresentationTemplate, idMultiplier, recordIndex);
            record.FinancialInstitutionId = financialInstitution.Id;
            record.DateCreatedUtc = DateTime.UtcNow;
            record.DateUpdatedUtc = DateTime.UtcNow;
            await context.MemberInterfacePresentationTemplates.AddAsync(record);
            recordIndex++;
        }

        await unitOfWork.CommitAsync();
        logger.LogInformation("Created Member Interface Presentation Templates");
        return await GetMemberInterfacePresentationTemplatesAsync(financialInstitution);
    }

    public async Task<List<AspNetUser>> GetUsersAsync(FinancialInstitution financialInstitution)
    {
        return await context.AspNetUsers
            .IgnoreQueryFilters()
            .Where(u => u.FinancialInstitution.Id == financialInstitution.Id)
            .ToListAsync();
    }

    public async Task<List<AspNetUser>> SeedUsersAsync(FinancialInstitution financialInstitution, IReadOnlyDictionary<string, AspNetRole> rolesByName)
    {
        var usersToSeed = new[]
        {
            new { UserName = "testuser", FirstName = "Test", LastName = "User", Email = "<EMAIL>", Password = "TestUser@123", Role = QCash.Service.Models.FIConfiguration.UserManagement.Constants.FiManagerAbrv },
            new { UserName = "testadmin", FirstName = "Test", LastName = "Admin", Email = "<EMAIL>", Password = "TestAdmin@123", Role = QCash.Service.Models.FIConfiguration.UserManagement.Constants.SystemAdminAbrv },
            new { UserName = "testsuperuser", FirstName = "Test", LastName = "SuperUser", Email = "<EMAIL>", Password = "TestSuperUser@123", Role = QCash.Service.Models.FIConfiguration.UserManagement.Constants.SuperUserAbrv }
        };
        var now = DateTime.UtcNow;
        var passwordHasher = new PasswordHasher<AspNetUser>();
        var users = new List<AspNetUser>();

        foreach (var userSeed in usersToSeed)
        {
            var normalizedEmail = Normalize(userSeed.Email);
            var existingUser = await context.AspNetUsers
                .IgnoreQueryFilters()
                .Where(u => u.FinancialInstitution.Id == financialInstitution.Id)
                .FirstOrDefaultAsync(u => u.NormalizedEmail == normalizedEmail);

            if (existingUser == null)
            {
                if (!rolesByName.TryGetValue(userSeed.Role, out var role))
                {
                    throw new Exception($"Role '{userSeed.Role}' not found in provided roles. Ensure roles are seeded before users.");
                }
                var newUser = new AspNetUser
                {
                    Id = Guid.NewGuid(),
                    FirstName = userSeed.FirstName,
                    LastName = userSeed.LastName,
                    UserName = userSeed.UserName,
                    NormalizedUserName = Normalize(userSeed.UserName),
                    Email = userSeed.Email,
                    NormalizedEmail = Normalize(userSeed.Email),
                    EmailConfirmed = true,
                    SecurityStamp = Guid.NewGuid().ToString(),
                    IsApproved = true,
                    IsDeleted = false,
                    PasswordChangeDate = now,
                    LastLoginDate = now,
                    FinancialInstitution = financialInstitution,
                    PasswordHash = passwordHasher.HashPassword(null!, userSeed.Password),
                };
                newUser.Roles.Add(role);
                users.Add(newUser);

                await context.AspNetUsers.AddAsync(newUser);
                await context.SaveChangesAsync();
                logger.LogInformation("Seeded user '{UserName}' with role '{Role}' for FI {FinancialInstitutionName}", userSeed.UserName, userSeed.Role, financialInstitution.Name);
            }
            else
            {
                users.Add(existingUser);
                logger.LogInformation("User '{UserName}' already exists for FI {FinancialInstitutionName}", userSeed.UserName, financialInstitution.Name);
            }
        }

        return users;
    }

    // This is not a perfect implementation. It uses MD5 which can have collisions, and only uses 6 bytes of it
    private static Guid GenerateStableGuid(string prefix, string roleName)
    {
        var hash = MD5.HashData(Encoding.UTF8.GetBytes(roleName));

        // Combine the prefix with the first 12 bytes of the hash
        var guidBytes = new Guid(prefix + "************").ToByteArray();
        Array.Copy(hash, 0, guidBytes, 10, 6);

        return new Guid(guidBytes);
    }

    private async Task<List<DocumentType>> SeedDocumentTypesAsync(FinancialInstitution financialInstitution, int idMultiplier)
    {
        var existingDocumentTypes = await GetDocumentTypesAsync(financialInstitution);

        var allDocumentTypes = new List<DocumentType> {
            new() { Name = "AAN", Abrv = "DTAAN", Slug  = "aan", },
            new() { Name = "Loan Disclosure", Abrv = "DTLD", Slug  = "load_disclosure", },
            new() { Name = "eConsent Disclosure", Abrv = "DTEDD", Slug  = "econsent_disclosure", },
            new() { Name = "TILA", Abrv = "DTTILA", Slug  = "tila", },
        };

        var docTypesToAdd = allDocumentTypes.Where(a => !existingDocumentTypes.Any(dt => dt.Abrv == a.Abrv))
            .ToList();

        var index = 1;
        foreach (var docType in docTypesToAdd)
        {
            docType.Id = GetID(IdIndexes.DocumentTypes, idMultiplier, index);
            docType.FinancialInstitutionId = financialInstitution.Id;
            docType.AppAbrv = docType.Abrv;
            docType.Description = docType.Name;
            docType.IsDeleted = false;
            docType.DateCreatedUtc = DateTime.UtcNow;
            docType.DateUpdatedUtc = DateTime.UtcNow;
            index++;
        }

        context.DocumentTypes.AddRange(docTypesToAdd);
        await unitOfWork.CommitAsync();

        logger.LogInformation("Created Document Types");
        return docTypesToAdd;
    }

    private async Task<List<Language>> SeedLanguagesAsync()
    {
        var existingLanguages = await GetLanguagesAsync();
        if (existingLanguages.Count > 0)
        {
            logger.LogInformation("languages already exist");
            return existingLanguages;
        }

        var allLanguages = LanguageEnum.Instance.GetAll();
        var languagesToAdd = allLanguages
            .Where(l => !existingLanguages.Any(e => e.LanguageCode == l.LanguageCode))
            .ToList();
        if (!languagesToAdd.Any())
        {
            return [];
        }

        var index = 1;
        foreach (var docType in languagesToAdd)
        {
            docType.Id = GetID(IdIndexes.Language, 0, index);
            docType.DateCreatedUtc = DateTime.UtcNow;
            docType.DateUpdatedUtc = DateTime.UtcNow;
            index++;
        }

        context.Languages.AddRange(languagesToAdd);
        await unitOfWork.CommitAsync();

        logger.LogInformation("Created Languages");
        return languagesToAdd;
    }

    private async Task<List<LanguageSupportStatus>> SeedLanguageSupportStatusesAsync(FinancialInstitution financialInstitution, List<Language> languages, int idMultiplier)
    {
        // For seed data, only English support needs to be seeded since that should always be active by default
        // When more languages are available at a later date; we do not need to seed the language support table
        //      A new relationship record will be created when the client activates language support for the specified language

        var existingLanguageSupportStatuses = await GetLanguageSupportStatusesAsync(financialInstitution);

        if (languages.Count == 0)
        {
            logger.LogInformation("No languages to add to existing supported languages");
            return existingLanguageSupportStatuses;
        }
        if (languages.All(l => l.LanguageCode != ""))
        {
            logger.LogInformation("English language not found - skipping language support status seeding since only English is needed");
            return existingLanguageSupportStatuses;
        }

        var index = existingLanguageSupportStatuses.Count;
        foreach (var languageSupportStatus in from language in languages
                                              where language.LanguageCode == "" && existingLanguageSupportStatuses.All(lss => language.Id != lss.LanguageId)
                                              select new LanguageSupportStatus()
                                              {
                                                  Id = GetID(IdIndexes.LanguageSupportStatus, idMultiplier, index++),
                                                  FinancialInstitutionId = financialInstitution.Id,
                                                  LanguageId = language.Id,
                                                  IsActive = language.Name == "English",
                                                  DateCreatedUtc = DateTime.UtcNow,
                                                  DateUpdatedUtc = DateTime.UtcNow
                                              })
        {
            await context.LanguageSupportStatuses.AddAsync(languageSupportStatus);
            existingLanguageSupportStatuses.Add(languageSupportStatus);
            break;
        }

        await unitOfWork.CommitAsync();
        logger.LogInformation("Created Supported Languages");
        return existingLanguageSupportStatuses;
    }

    private static string Normalize(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
            return string.Empty;
        var normalized = input.Trim().Normalize(System.Text.NormalizationForm.FormD);
        var sb = new System.Text.StringBuilder();
        foreach (var c in normalized)
        {
            var unicodeCategory = System.Globalization.CharUnicodeInfo.GetUnicodeCategory(c);
            if (unicodeCategory != System.Globalization.UnicodeCategory.NonSpacingMark)
                sb.Append(c);
        }
        return sb.ToString().Normalize(System.Text.NormalizationForm.FormC);
    }
}
