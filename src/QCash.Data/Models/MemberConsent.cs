using System;
using System.Collections.Generic;

namespace QCash.Data.Models;

public partial class MemberConsent
{
    public Guid Id { get; set; }

    public Guid LoanApplicationId { get; set; }

    public Guid? DocumentId { get; set; }

    public string AcceptanceType { get; set; } = null!;

    public bool Accepted { get; set; }

    public bool? PreviouslyAccepted { get; set; }

    public DateTime DateCreatedUtc { get; set; }

    public DateTime DateUpdatedUtc { get; set; }

    public byte[] TimeStamp { get; set; } = null!;

    public virtual Document? Document { get; set; }

    public virtual LoanApplication LoanApplication { get; set; } = null!;
}
