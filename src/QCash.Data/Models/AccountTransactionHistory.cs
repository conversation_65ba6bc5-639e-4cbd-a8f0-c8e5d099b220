using System;
using System.Collections.Generic;

namespace QCash.Data.Models;

public partial class AccountTransactionHistory
{
    public Guid Id { get; set; }

    public Guid MemberAccountId { get; set; }

    public string TransactionSequence { get; set; } = null!;

    public decimal Amount { get; set; }

    public string? Description { get; set; }

    public string? TranType { get; set; }

    public DateTime DateCreatedUtc { get; set; }

    public DateTime DateUpdatedUtc { get; set; }

    public byte[] TimeStamp { get; set; } = null!;

    public DateTime? PaymentDueDateUtc { get; set; }

    public DateTime? PostedDateUtc { get; set; }

    public DateTime? TransactionDateUtc { get; set; }

    public virtual MemberAccount MemberAccount { get; set; } = null!;
}
