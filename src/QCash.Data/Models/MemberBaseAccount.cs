using System;
using System.Collections.Generic;

namespace QCash.Data.Models;

public partial class MemberBaseAccount
{
    public Guid Id { get; set; }

    public string AccountId { get; set; } = null!;

    public Guid FinancialInstitutionMemberId { get; set; }

    public string OwnerShipType { get; set; } = null!;

    public DateTime DateCreatedUtc { get; set; }

    public DateTime DateUpdatedUtc { get; set; }

    public byte[] TimeStamp { get; set; } = null!;

    public string Type { get; set; } = null!;

    public DateTime? CloseDateUtc { get; set; }

    public DateTime? OpenDateUtc { get; set; }

    public string? BranchNumber { get; set; }

    public bool? IsExcluded { get; set; }

    public virtual FinancialInstitutionMember FinancialInstitutionMember { get; set; } = null!;

    public virtual ICollection<LoanApplication> LoanApplications { get; set; } = new List<LoanApplication>();

    public virtual ICollection<MemberAccount> MemberAccounts { get; set; } = new List<MemberAccount>();

    public virtual ICollection<MemberBaseAccountWarningCode> MemberBaseAccountWarningCodes { get; set; } = new List<MemberBaseAccountWarningCode>();
}
