namespace QCash.Data.Models.Enums;

/// <summary>
/// The interface to be implemented by all enum types.
/// </summary>
/// <typeparam name="T">The type of the id the object takes.</typeparam>
/// <typeparam name="R">The return type of the methods.</typeparam>
public interface IEnum<T, R>
{
    /// <summary>
    /// Retrieve all instances of this enum type.
    /// </summary>
    /// <returns>An array of all enum values.</returns>
    R[] GetAll();

    /// <summary>
    /// Retrieve the enum for an id.
    /// </summary>
    /// <param name="id">The id for which to find an enum instance.</param>
    /// <returns>The object if found, otherwise throws <see cref="ObjectNotFoundException"/></returns>
    /// <exception cref="ObjectNotFoundException"/>
    R GetById(T id);
}
