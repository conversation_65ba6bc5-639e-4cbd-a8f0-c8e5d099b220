using System;
using System.Collections.Generic;

namespace QCash.Data.Models;

public partial class PaymentOption
{
    public Guid Id { get; set; }

    public Guid PaymentOptionTypeId { get; set; }

    public Guid ProductId { get; set; }

    public bool IsEnabled { get; set; }

    public DateTime DateCreatedUtc { get; set; }

    public DateTime DateUpdatedUtc { get; set; }

    public byte[] TimeStamp { get; set; } = null!;

    public virtual BiWeekly? BiWeekly { get; set; }

    public virtual MonthlyCalculatedFirstPaymentDate? MonthlyCalculatedFirstPaymentDate { get; set; }

    public virtual MonthlyPickADay? MonthlyPickADay { get; set; }

    public virtual OneTime? OneTime { get; set; }

    public virtual ICollection<PaymentOptionScore> PaymentOptionScores { get; set; } = new List<PaymentOptionScore>();

    public virtual ICollection<PaymentOptionTitle> PaymentOptionTitles { get; set; } = new List<PaymentOptionTitle>();

    public virtual PaymentOptionType PaymentOptionType { get; set; } = null!;

    public virtual Product Product { get; set; } = null!;
}
