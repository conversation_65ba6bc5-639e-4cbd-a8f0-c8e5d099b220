using System;
using System.Collections.Generic;

namespace QCash.Data.Models;

public partial class AccountWarningCode
{
    public Guid Id { get; set; }

    public Guid FinancialInstitutionId { get; set; }

    public string AppAbrv { get; set; } = null!;

    public string Name { get; set; } = null!;

    public bool Active { get; set; }

    public DateTime DateCreatedUtc { get; set; }

    public DateTime DateUpdatedUtc { get; set; }

    public byte[] TimeStamp { get; set; } = null!;

    public string Abrv { get; set; } = null!;

    public string Slug { get; set; } = null!;

    public string Description { get; set; } = null!;

    public bool IsDeleted { get; set; }

    public virtual FinancialInstitution FinancialInstitution { get; set; } = null!;
}
