using System;
using System.Collections.Generic;

namespace QCash.Data.Models;

public partial class LoanApplicationNotification
{
    public Guid Id { get; set; }

    public Guid LoanApplicationId { get; set; }

    public string NotificationType { get; set; } = null!;

    public bool Sent { get; set; }

    public DateTime DateCreatedUtc { get; set; }

    public DateTime DateUpdatedUtc { get; set; }

    public byte[] TimeStamp { get; set; } = null!;

    public string NotificationChanel { get; set; } = null!;

    public bool IsSentAutomatically { get; set; }

    public virtual LoanApplication LoanApplication { get; set; } = null!;
}
