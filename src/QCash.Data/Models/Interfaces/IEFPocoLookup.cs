namespace QCash.Data.Models.Interfaces;

public interface IEFPocoLookup : IEFPoco, IFinancialInstitutionId
{
    /// <summary>
    /// Gets or sets IsDeleted.
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// Gets or sets the name.
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// Gets or sets the slug.
    /// </summary>
    public string Slug { get; set; }

    public string Abrv { get; set; }
}
