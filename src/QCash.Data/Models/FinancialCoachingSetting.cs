using System;
using System.Collections.Generic;

namespace QCash.Data.Models;

public partial class FinancialCoachingSetting
{
    public Guid Id { get; set; }

    public Guid FinancialInstitutionId { get; set; }

    public int FeeBasedLoansThreshold { get; set; }

    public int InterestBasedLoansThreshold { get; set; }

    public bool AwarenessPage { get; set; }

    public bool DebtCounselorConfirmation { get; set; }

    public string? DebtManagementAgencyName { get; set; }

    public string? DebtManagementEmail { get; set; }

    public DateTime DateCreatedUtc { get; set; }

    public DateTime DateUpdatedUtc { get; set; }

    public byte[] TimeStamp { get; set; } = null!;

    public bool LandingPageDebtManagement { get; set; }

    public int AwarenessPeriod { get; set; }

    public virtual FinancialInstitution FinancialInstitution { get; set; } = null!;
}
