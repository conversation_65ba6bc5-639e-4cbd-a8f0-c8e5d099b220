using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace QCash.Data.Migrations
{
    /// <inheritdoc />
    public partial class _8908AspNetIdentityservicespart2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameIndex(
                name: "PK_AspNetUsers",
                newName: "PK_AspNetUser",
                table: "AspNetUsers");

            migrationBuilder.RenameIndex(
                name: "UX_AspNetUsers_UserName",
                newName: "UX_AspNetUser_UserName",
                table: "AspNetUsers");

            migrationBuilder.RenameIndex(
                name: "PK_AspNetUserLogins",
                newName: "PK_AspNetUserLogin",
                table: "AspNetUserLogins");

            migrationBuilder.RenameIndex(
                name: "PK_AspNetUserClaims",
                newName: "PK_AspNetUserClaim",
                table: "AspNetUserClaims");

            migrationBuilder.RenameIndex(
                name: "PK_AspNetRoles",
                newName: "PK_AspNetRole",
                table: "AspNetRoles");

            migrationBuilder.DropColumn(
                name: "LockoutEndDateUtc",
                table: "AspNetUsers");

            migrationBuilder.RenameTable(
                name: "AspNetUsers",
                newName: "AspNetUser");

            migrationBuilder.RenameTable(
                name: "AspNetUserLogins",
                newName: "AspNetUserLogin");

            migrationBuilder.RenameTable(
                name: "AspNetUserClaims",
                newName: "AspNetUserClaim");

            migrationBuilder.RenameTable(
                name: "AspNetRoles",
                newName: "AspNetRole");

            migrationBuilder.AlterColumn<string>(
                name: "Slug",
                table: "Product",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(100)",
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "UserName",
                table: "AspNetUser",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(256)",
                oldMaxLength: 256);

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "AspNetUser",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTimeOffset>(
                name: "LockoutEnd",
                table: "AspNetUser",
                type: "datetimeoffset",
                nullable: true,
                defaultValueSql: "(getutcdate())");

            migrationBuilder.AddColumn<string>(
                name: "NormalizedEmail",
                table: "AspNetUser",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NormalizedUserName",
                table: "AspNetUser",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProviderDisplayName",
                table: "AspNetUserLogin",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "AspNetRole",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(256)",
                oldMaxLength: 256);

            migrationBuilder.AddColumn<string>(
                name: "ConcurrencyStamp",
                table: "AspNetRole",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NormalizedName",
                table: "AspNetRole",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: true,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "AspNetRoleClaim",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RoleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ClaimType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ClaimValue = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoleClaim", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetRoleClaim_AspNetRole",
                        column: x => x.RoleId,
                        principalTable: "AspNetRole",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserToken",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    LoginProvider = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    Value = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserToken", x => new { x.UserId, x.LoginProvider, x.Name });
                });

            migrationBuilder.CreateIndex(
                name: "UX_AspNetUser_NormalizedEmail",
                table: "AspNetUser",
                column: "NormalizedEmail",
                unique: true,
                filter: "[NormalizedEmail] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "UX_AspNetUser_NormalizedUserName",
                table: "AspNetUser",
                column: "NormalizedUserName",
                unique: true,
                filter: "[NormalizedUserName] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetRoleClaim_RoleId",
                table: "AspNetRoleClaim",
                column: "RoleId");

            migrationBuilder.RenameTable(
                name: "FK_AspNetUserClaims_AspNetUsers",
                newName: "FK_AspNetUserClaim_AspNetUser"
                );

            migrationBuilder.RenameIndex(
                name: "IX_AspNetUserClaims_UserId",
                newName: "IX_AspNetUserClaim_UserId",
                table: "AspNetUserClaim");

            migrationBuilder.RenameTable(
                name: "FK_AspNetUserLogins_AspNetUsers",
                newName: "FK_AspNetUserLogin_AspNetUser");

            migrationBuilder.RenameIndex(
                name: "IX_AspNetUserLogins_UserId",
                newName: "IX_AspNetUserLogin_UserId",
                table: "AspNetUserLogin");

            migrationBuilder.RenameTable(
                name: "AspNetUserRoles",
                newName: "AspNetUserRole");

            migrationBuilder.RenameIndex(
                name: "PK_AspNetUserRoles",
                newName: "PK_AspNetUserRole",
                table: "AspNetUserRole");

            migrationBuilder.RenameTable(
                name: "FK_AspNetUserRoles_AspNetRoles",
                newName: "FK_AspNetUserRole_AspNetRole");

            migrationBuilder.RenameTable(
                name: "FK_AspNetUserRoles_AspNetUsers",
                newName: "FK_AspNetUserRole_AspNetUser");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            throw new NotImplementedException();
        }
    }
}
