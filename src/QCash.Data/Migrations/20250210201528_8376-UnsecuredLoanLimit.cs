using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace QCash.Data.Migrations
{
    /// <inheritdoc />
    public partial class _8376UnsecuredLoanLimit : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ApplicationId",
                table: "UnsecuredLoanLimit",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_UnsecuredLoanLimit_ApplicationId",
                table: "UnsecuredLoanLimit",
                column: "ApplicationId");

            migrationBuilder.AddForeignKey(
                name: "FK_UnsecuredLoanLimit_Applications",
                table: "UnsecuredLoanLimit",
                column: "ApplicationId",
                principalTable: "Application",
                principalColumn: "Id");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "UnsecuredLoanLimit",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500,
                oldNullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UnsecuredLoanLimit_Applications",
                table: "UnsecuredLoanLimit");

            migrationBuilder.DropIndex(
                name: "IX_UnsecuredLoanLimit_ApplicationId",
                table: "UnsecuredLoanLimit");

            migrationBuilder.DropColumn(
                name: "ApplicationId",
                table: "UnsecuredLoanLimit");

            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "UnsecuredLoanLimit",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(500)",
                oldMaxLength: 500);
        }
    }
}
