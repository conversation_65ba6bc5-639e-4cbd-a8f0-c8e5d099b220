using EntityFramework.Exceptions.SqlServer;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Options;
using QCash.Data.Extensions;
using QCash.Data.Models;
using Z.EntityFramework.Plus;
using AuditEntry = Z.EntityFramework.Plus.AuditEntry;

namespace QCash.Data.Context;

public partial class QCashContext
{
    private readonly IHttpContextAccessor? _httpContextAccessor;
    private readonly QCashContextOptions _qCashContextOptions = new();

    private MultiTenantInfo Tenant => _httpContextAccessor?.HttpContext?.GetTenant() ?? MultiTenantInfo.UnresolvedTenant;
    static QCashContext()
    {
        var ignoredProperties = new List<string> { "TimeStamp", "DateCreatedUtc", "DateUpdatedUtc" };
        AuditManager.DefaultConfiguration.AutoSavePreAction = (context, audit) =>
        {
            foreach (var entry in audit.Entries)
            {
                entry.Properties.RemoveAll(p => ignoredProperties.Contains(p.PropertyName));
            }
            var ctx = (QCashContext)context;
            // ADD "Where(x => x.AuditEntryID == 0)" to allow multiple SaveChanges with same Audit
            var auditEntries = audit.ToAuditEntries(ctx, ctx.GetUserId());
            ctx.AuditEntries.AddRange(auditEntries);
        };

        // Audit options:

        AuditManager.DefaultConfiguration.UseUtcDateTime = true; // UTC time or "local" time?

        // Tables to include or exclude in the audit
        AuditManager.DefaultConfiguration.Exclude(_ => true); // exclude everything we don't explicitly include
        AuditManager.DefaultConfiguration.IncludeTables(); // include the specific tables we care about

        // Properties to include or exclude in the audit
        AuditManager.DefaultConfiguration.IncludeProperty();

        // Whether to audit only the properties that have changed or always all properties
        AuditManager.DefaultConfiguration.IgnorePropertyUnchanged = true; //always save all properties?

        AuditManager.DefaultConfiguration.AddCustomFormatters();

        AuditManager.DefaultConfiguration.AuditEntryFactory = AuditEntryFactory;
    }

    public QCashContext(DbContextOptions<QCashContext> options, IHttpContextAccessor httpContextAccessor, IOptionsSnapshot<QCashContextOptions> qCashContextOptions)
       : base(options)
    {
        _httpContextAccessor = httpContextAccessor;
        _qCashContextOptions = qCashContextOptions.Value;
    }

    public override int SaveChanges(bool acceptAllChangesOnSuccess)
    {
        var audit = new Audit { CreatedBy = GetUser() };
        SetModificationDates();
        audit.PreSaveChanges(this);
        var rowsAffected = base.SaveChanges(acceptAllChangesOnSuccess);
        audit.PostSaveChanges();

        if (audit.Configuration.AutoSavePreAction != null)
        {
            audit.Configuration.AutoSavePreAction(this, audit);
            base.SaveChanges(acceptAllChangesOnSuccess);
        }

        return rowsAffected;
    }

    public bool SlowQueryLogging
    {
        get => _qCashContextOptions.SlowQueryLogging;
        set => _qCashContextOptions.SlowQueryLogging = value;
    }

    private static AuditEntry AuditEntryFactory(AuditEntryFactoryArgs arg)
    {
        var auditEntry = new AuditEntry { CreatedDate = DateTime.UtcNow };

        return auditEntry;
    }

    public override async Task<int> SaveChangesAsync(bool acceptAllChangesOnSuccess, CancellationToken cancellationToken = default)
    {
        var audit = new Audit { CreatedBy = GetUser() };
        SetModificationDates();
        audit.PreSaveChanges(this);
        var rowsAffected = await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        audit.PostSaveChanges();

        if (audit.Configuration.AutoSavePreAction != null)
        {
            audit.Configuration.AutoSavePreAction(this, audit);
            await base.SaveChangesAsync(acceptAllChangesOnSuccess, cancellationToken);
        }

        return rowsAffected;
    }

    private void SetModificationDates()
    {
        ChangeTracker.DetectChanges();
        var entityEntries = ChangeTracker.Entries()
            .Where(x => x.State is EntityState.Added or EntityState.Modified);
        foreach (EntityEntry entityEntry in entityEntries)
        {
            if (entityEntry.Metadata.FindProperty("DateUpdatedUtc") != null)
            {
                entityEntry.Property("DateUpdatedUtc").CurrentValue = DateTime.UtcNow;
            }

            if (entityEntry.State != EntityState.Added)
            {
                continue;
            }

            if (entityEntry.Metadata.FindProperty("DateCreatedUtc") != null)
            {
                entityEntry.Property("DateCreatedUtc").CurrentValue = DateTime.UtcNow;
            }
        }
    }

    private string GetUser()
    {
        //Rest api will have a HttpContext but not a user associated.
        //When this occurs, the user making the changes is the "System User"
        //if (isApiCall) return $"{ApplicationUserEnum.PVSystemUser.FirstName} {ApplicationUserEnum.PVSystemUser.LastName}";

        // Scheduled jobs have no http context. When this occurs, the user making the changes is the "System User"
        return _httpContextAccessor?.HttpContext?.User.GetUsername()!;
    }

    public Guid? GetUserId()
    {
        var userId = _httpContextAccessor?.HttpContext?.User.GetUserId();
        return userId;
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder.UseExceptionProcessor();
        optionsBuilder.AddInterceptors(new SlowQueryInterceptor((DbContextOptions<QCashContext>)optionsBuilder.Options, _qCashContextOptions));
    }
}
