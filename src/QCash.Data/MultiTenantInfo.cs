namespace QCash.Data;

public class MultiTenantInfo
{
    public Guid Id { get; init; }
    public required string Slug { get; init; }
    public required string Name { get; init; }
    public bool IsResolved { get; init; } = false;

    public static readonly MultiTenantInfo UnresolvedTenant = new()
    {
        Id = Guid.Empty,
        Slug = "",
        Name = "",
        IsResolved = false,
    };
}
