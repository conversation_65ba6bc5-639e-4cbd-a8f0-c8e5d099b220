using QCash.Middleware.Concert.Client.Messaging;
using QCash.LoanApplication;
using QCash.LoanApplication.ResponseMessages;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using QCash.Middleware.Concert.Client.Constants;

namespace QCash.Middleware.Concert.Client;

/// <summary>
/// The Q-Cash Concert Client
/// </summary>
/// <remarks>
/// This client should be created once and re-used.
/// </remarks>
public class QCashConcertClient : IQCashConcertClient, IDisposable
{
    private static readonly string _sessionId = Guid.NewGuid().ToString("N");
    private readonly ClientConfiguration _configuration;
    private readonly ConcurrentDictionary<Guid, CoreClient> _coreClients = new();

    private readonly Guid _agentId;
    private bool _disposed;
    private readonly Action<Action> _backgroundTask;

    #region Constructors

    public QCashConcertClient(ClientConfiguration configuration, Action<Action> backgroundTask)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        if (configuration.NamespaceConfigurations?.Count > 0)
        {
            DefaultNamespaceName = _configuration.NamespaceConfigurations.Keys.First();
        }

        _agentId = configuration.AgentId;
        _backgroundTask = backgroundTask;
    }

    #endregion

    #region Properties

    /// <summary>
    /// The default namespace to use if one not provided.
    /// </summary>
    public string DefaultNamespaceName { get; }

    #endregion

    #region Concert Methods

    /// <summary>
    /// Request to pull back a list of cores supported by Concert.
    /// </summary>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A list of cores supported.</returns>
    public Task<Core[]> GetCoreProvidersAsync(CancellationToken cancellationToken)
    {
        return GetCoreProvidersAsync(DefaultNamespaceName, cancellationToken);
    }

    /// <summary>
    /// Request to pull back a list of cores supported by Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A list of cores supported.</returns>
    public async Task<Core[]> GetCoreProvidersAsync(string namespaceName, CancellationToken cancellationToken)
    {
        var response = await SendAndReceiveAsync<QCashRequestBase, GetCoreProvidersResponse>(null, namespaceName,
            ConcertInfo.GetCoreProvidersTransaction, null, cancellationToken).ConfigureAwait(false);

        return response.Cores;
    }

    /// <summary>
    /// Request to get core connection status from Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core on which you want to check the status.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The concert connection status.</returns>
    public Task<CoreConnectionResponse> GetCoreConnectionStatusAsync(string namespaceName, Guid coreId, CancellationToken cancellationToken)
    {
        return SendAndReceiveAsync<QCashRequestBase, CoreConnectionResponse>(null, namespaceName,
            ConcertInfo.GetCoreConnectionsTransaction, coreId, cancellationToken);
    }

    /// <summary>
    /// Request to get core connection status from Concert.
    /// </summary>
    /// <param name="coreId">The core on which you want to check the status.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The concert connection status.</returns>
    public Task<CoreConnectionResponse> GetCoreConnectionStatusAsync(Guid coreId, CancellationToken cancellationToken)
    {
        return GetCoreConnectionStatusAsync(DefaultNamespaceName, coreId, cancellationToken);
    }

    /// <summary>
    /// Request to get member information from specified core via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters used to find the member on the core.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The member information.</returns>
    public async Task<Member> GetMemberAsync(string namespaceName, Guid coreId, GetMemberParameter parameters, string correlationId, CancellationToken cancellationToken)
    {
        var getMemberRequest = new GetMemberRequest
        {
            CoreId = coreId,
            Parameter = parameters,
            CorrelationId = correlationId,
        };

        var response = await SendAndReceiveAsync<GetMemberRequest, GetMemberResponse>(getMemberRequest, namespaceName,
            ConcertInfo.GetMemberTransaction, coreId, cancellationToken).ConfigureAwait(false);

        return response.Member;
    }

    /// <summary>
    /// Request to get member information from specified core via Concert.
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters used to find the member on the core.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The member information.</returns>
    public Task<Member> GetMemberAsync(Guid coreId, GetMemberParameter parameters, string correlationId, CancellationToken cancellationToken)
    {
        return GetMemberAsync(DefaultNamespaceName, coreId, parameters, correlationId, cancellationToken);
    }

    /// <summary>
    /// Pull back all the history transaction for one account
    /// NOTES: we may want to expand this to something more powerful so that we can query in anyway we want
    /// For now we only support HistoryQueryOption.ShareOnly | HistoryQueryOption.DepositOnly
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="account">
    /// The account on which we want to pull the history from
    /// </param>
    /// <param name="queryOptions">
    /// The query options
    /// </param>
    /// <param name="transactionTypeFilter">
    /// The filter on transactions to pull back, for SYMITAR, it could be 'D' for deposit, 'P' for payment etc
    /// </param>
    /// <param name="startDate">
    /// The start Date.
    /// </param>
    /// <param name="endDate">
    /// The end Date.
    /// </param>
    /// <param name="extraFilters">
    /// Extra filters (in name/value pair) for future cores
    /// </param>
    /// <param name="applicationId"></param>
    /// <param name="clientTimeZone"></param>
    /// <param name="cancellationToken"></param>
    [Obsolete("This call was not being used in Web Role so can be removed.")]
    public Task GetTransactionHistoryAsync(Guid coreId, Account account, HistoryQueryOption queryOptions,
        string transactionTypeFilter, DateTime startDate, DateTime endDate, ExtraInfo extraFilters, int? applicationId, string clientTimeZone, CancellationToken cancellationToken)
    {
        return GetTransactionHistoryAsync(DefaultNamespaceName, coreId, account, queryOptions, transactionTypeFilter, startDate, endDate, extraFilters, applicationId, clientTimeZone, cancellationToken);
    }

    /// <summary>
    /// Pull back all the history transaction for one account
    /// NOTES: we may want to expand this to something more powerful so that we can query in anyway we want
    /// For now we only support HistoryQueryOption.ShareOnly | HistoryQueryOption.DepositOnly
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="account">
    /// The account on which we want to pull the history from
    /// </param>
    /// <param name="option"></param>
    /// <param name="transactionTypeFilter">
    /// The filter on transactions to pull back, for SYMITAR, it could be 'D' for deposit, 'P' for payment etc
    /// </param>
    /// <param name="startDate">
    /// The start Date.
    /// </param>
    /// <param name="endDate">
    /// The end Date.
    /// </param>
    /// <param name="extraFilters">
    /// Extra filters (in name/value pair) for future cores
    /// </param>
    /// <param name="applicationId"></param>
    /// <param name="clientTimeZone"></param>
    /// <param name="cancellationToken"></param>
    [Obsolete("This call was not being used in Web Role so can be removed.")]
    public async Task GetTransactionHistoryAsync(string namespaceName, Guid coreId, Account account, HistoryQueryOption option,
        string transactionTypeFilter, DateTime startDate, DateTime endDate, ExtraInfo extraFilters, int? applicationId, string clientTimeZone, CancellationToken cancellationToken)
    {
        var request = new GetTransactionHistoryRequest
        {
            CoreId = coreId,
            Account = account,
            Option = option,
            TransactionTypeFilter = transactionTypeFilter,
            StartDate = startDate,
            EndDate = endDate,
            ApplicationId = applicationId,
            ClientTimeZone = clientTimeZone,
        };

        var response = await SendAndReceiveAsync<GetTransactionHistoryRequest, GetTransactionHistoryResponse>(request, namespaceName,
            ConcertInfo.GetTransactionHistoryTransaction, coreId, cancellationToken).ConfigureAwait(false);

        account.TransactionHistory = response.History;

    }

    /// <summary>
    /// Request to transfer funds from one account to another account for a member via Concert.
    /// </summary>
    /// <param name="request">The transfer request containing the information.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The transfer result.</returns>
    public Task<TransferResponse> TransferAsync(TransferRequest request, CancellationToken cancellationToken)
    {
        return TransferAsync(DefaultNamespaceName, request, cancellationToken);
    }

    /// <summary>
    /// Request to transfer funds from one account to another account for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="request">The transfer request containing the information.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The transfer result.</returns>
    public Task<TransferResponse> TransferAsync(string namespaceName, TransferRequest request, CancellationToken cancellationToken)
    {
        return SendAndReceiveAsync<TransferRequest, TransferResponse>(request, namespaceName,
            ConcertInfo.TransferTransaction, request.CoreId, cancellationToken);
    }

    /// <summary>
    /// Request to create and fund a loan on for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="request">The create and fund loan request containing the information.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The loan creation and funding result.</returns>
    public async Task<CreateAndFundLoanResult> CreateAndFundLoanAsync(string namespaceName, CreateAndFundLoanRequest request, CancellationToken cancellationToken)
    {
        var response = await SendAndReceiveAsync<CreateAndFundLoanRequest, CreateAndFundLoanResponse>(request, namespaceName,
            ConcertInfo.CreateAndFundLoanTransaction, request.CoreId, cancellationToken).ConfigureAwait(false);

        return response.Result;
    }

    /// <summary>
    /// Request to create and fund a loan on for a member via Concert.
    /// </summary>
    /// <param name="request">The create and fund loan request containing the information.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The loan creation and funding result.</returns>
    public Task<CreateAndFundLoanResult> CreateAndFundLoanAsync(CreateAndFundLoanRequest request, CancellationToken cancellationToken)
    {
        return CreateAndFundLoanAsync(DefaultNamespaceName, request, cancellationToken);
    }

    /// <summary>
    /// Request to create a share account for a member via Concert.
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters containing request information.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="clientTimeZone"></param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <param name="applicationId"></param>
    /// <returns>The account that was created.</returns>
    public Task<Account> CreateShareAsync(Guid coreId, CreateShareAccountParameters parameters, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken)
    {
        return CreateShareAsync(DefaultNamespaceName, coreId, parameters, correlationId, applicationId, clientTimeZone, cancellationToken);
    }

    /// <summary>
    /// Request to create a share account for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters containing request information.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="clientTimeZone"></param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <param name="applicationId"></param>
    /// <returns>The account that was created.</returns>
    public async Task<Account> CreateShareAsync(string namespaceName, Guid coreId,
        CreateShareAccountParameters parameters, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken)
    {
        var request = new CreateShareRequest
        {
            CoreId = coreId,
            ShareParameters = parameters,
            CorrelationId = correlationId,
            ApplicationId = applicationId,
            ClientTimeZone = clientTimeZone,
        };
        var response = await SendAndReceiveAsync<CreateShareRequest, CreateShareResponse>(request, namespaceName,
            ConcertInfo.CreateShareTransaction, request.CoreId, cancellationToken).ConfigureAwait(false);

        return response.Account;
    }

    /// <summary>
    /// Request to get the average monthly checking deposit balance for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters containing the request information.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="clientTimeZone"></param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <param name="applicationId" />
    /// <returns>The average amount.</returns>
    public async Task<decimal> GetAverageMonthlyCheckingDepositBalanceAsync(string namespaceName, Guid coreId,
        AverageCheckingDepositBalanceParameter parameters, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken)
    {
        var request = new GetAverageMonthlyCheckingDepositBalanceRequest
        {
            CoreId = coreId,
            CorrelationId = correlationId,
            Parameters = parameters,
            ApplicationId = applicationId,
            ClientTimeZone = clientTimeZone,
        };

        var response = await SendAndReceiveAsync<GetAverageMonthlyCheckingDepositBalanceRequest, GetAverageMonthlyCheckingDepositBalanceResponse>(request, namespaceName,
            ConcertInfo.GetAverageMonthlyCheckingDepositBalanceTransaction, request.CoreId, cancellationToken).ConfigureAwait(false);

        return response.Result;
    }

    /// <summary>
    /// Request to get the average monthly checking deposit balance for a member via Concert.
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters containing the request information.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="clientTimeZone"></param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <param name="applicationId"></param>
    /// <returns>The average amount.</returns>
    public Task<decimal> GetAverageMonthlyCheckingDepositBalanceAsync(Guid coreId, AverageCheckingDepositBalanceParameter parameters, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken)
    {
        return GetAverageMonthlyCheckingDepositBalanceAsync(DefaultNamespaceName, coreId, parameters, correlationId, applicationId, clientTimeZone, cancellationToken);
    }

    /// <summary>
    /// Request to get the decision engine parameters for a member via Concert.
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters containing the request information.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="clientTimeZone"></param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <param name="applicationId"></param>
    /// <returns>The decision engine parameters.</returns>
    public Task<DecisionEngineParameters> GetDecisionEngineParametersAsync(Guid coreId, DecisionEngineSearchParameters parameters,
        string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken)
    {
        return GetDecisionEngineParametersAsync(DefaultNamespaceName, coreId, parameters, correlationId, applicationId, clientTimeZone, cancellationToken);
    }

    /// <summary>
    /// Request to get the decision engine parameters for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters containing the request information.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="clientTimeZone"></param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <param name="applicationId"></param>
    /// <returns>The decision engine parameters.</returns>
    public async Task<DecisionEngineParameters> GetDecisionEngineParametersAsync(string namespaceName, Guid coreId,
        DecisionEngineSearchParameters parameters, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken)
    {
        var request = new GetDecisionEngineParametersRequest
        {
            CoreId = coreId,
            SearchParams = parameters,
            CorrelationId = correlationId,
            ApplicationId = applicationId,
            ClientTimeZone = clientTimeZone,
        };

        var response = await SendAndReceiveAsync<GetDecisionEngineParametersRequest, GetDecisionEngineParametersResponse>(request, namespaceName,
            ConcertInfo.GetDecisionEngineParametersTransaction, request.CoreId, cancellationToken).ConfigureAwait(false);

        return response.Result;
    }

    /// <summary>
    /// Request to verify a product type filter expression for a core.
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="expression">The product type expression.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The product type filter expression result.</returns>
    public Task<ProductTypeFilterExpressionParsingResult> ParseProductTypeFilterExpressionAsync(Guid coreId,
        string expression, CancellationToken cancellationToken)
    {
        return ParseProductTypeFilterExpressionAsync(DefaultNamespaceName, coreId, expression, cancellationToken);
    }

    /// <summary>
    /// Request to verify a product type filter expression for a core.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="expression">The product type expression.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The product type filter expression result.</returns>
    public async Task<ProductTypeFilterExpressionParsingResult> ParseProductTypeFilterExpressionAsync(string namespaceName,
        Guid coreId, string expression, CancellationToken cancellationToken)
    {
        var request = new ParseProductTypeFilterExpressionRequest
        {
            CoreId = coreId,
            Expression = expression,
        };

        var response = await SendAndReceiveAsync<ParseProductTypeFilterExpressionRequest, ParseProductTypeFilterExpressionResponse>(request, namespaceName,
            ConcertInfo.ParseProductTypeFilterExpressionTransaction, request.CoreId, cancellationToken).ConfigureAwait(false);

        return response.Result;
    }

    /// <summary>
    /// Request to get MLA status for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="member">The member to get the MLA status of.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="clientTimeZone"></param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <param name="applicationId"></param>
    /// <returns>The members MLA status.</returns>
    public async Task<MLAResponse> GetMlaStatusAsync(string namespaceName, Guid coreId, Member member, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken)
    {
        var request = new GetMLAStatusRequest
        {
            CoreId = coreId,
            Person = member,
            ReferenceNumber = correlationId,
            CorrelationId = correlationId,
            ApplicationId = applicationId,
            ClientTimeZone = clientTimeZone,
        };
        try
        {
            var response = await SendAndReceiveAsync<GetMLAStatusRequest, GetMLAStatusResponse>(request, namespaceName,
                ConcertInfo.GetMlaStatusTransaction, null, cancellationToken).ConfigureAwait(false);

            return new MLAResponse
            {
                Error = response.Error,
                MLAStatus = response.MLAStatus,
            };
        }
        catch (OperationCanceledException)
        {
            return new MLAResponse
            {
                Error = new MLAError
                {
                    Message = "Timeout error",
                    Severity = "Error",
                    Type = "Timeout error"
                }
            };
        }
    }

    /// <summary>
    /// Request to get MLA status for a member via Concert.
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="member">The member to get the MLA status of.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="clientTimeZone"></param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <param name="applicationId"></param>
    /// <returns>The members MLA status.</returns>
    public Task<MLAResponse> GetMlaStatusAsync(Guid coreId, Member member, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken)
    {
        return GetMlaStatusAsync(DefaultNamespaceName, coreId, member, correlationId, applicationId, clientTimeZone, cancellationToken);
    }

    /// <summary>
    /// Orders a credit card to be ordered for a member via Concert.
    /// </summary>
    /// <param name="request">Credit card order request.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The order credit card response.</returns>

    public Task<OrderCreditCardResponse> OrderCreditCardAsync(OrderCreditCardRequest request, CancellationToken cancellationToken)
    {
        return OrderCreditCardAsync(DefaultNamespaceName, request, cancellationToken);
    }

    /// <summary>
    /// Orders a credit card to be ordered for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="request">Credit card order request.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The order credit card response.</returns>

    public Task<OrderCreditCardResponse> OrderCreditCardAsync(string namespaceName, OrderCreditCardRequest request, CancellationToken cancellationToken)
    {
        return SendAndReceiveAsync<OrderCreditCardRequest, OrderCreditCardResponse>(request, namespaceName,
            ConcertInfo.OrderCreditCardTransaction, null, cancellationToken);
    }

    /// <summary>
    /// Request to insert a SymXchange tracking record via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="request">The tracking record request</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The insert tracking record result.</returns>
    public Task<InsertTrackingRecordResponse> InsertTrackingRecordAsync(string namespaceName, InsertTrackingRecordRequest request, CancellationToken cancellationToken)
    {
        return SendAndReceiveAsync<InsertTrackingRecordRequest, InsertTrackingRecordResponse>(request, namespaceName,
            ConcertInfo.InsertTrackingRecordTransaction, request.CoreId, cancellationToken);
    }

    /// <summary>
    /// Request to insert a SymXchange tracking record via Concert.
    /// </summary>
    /// <param name="request">The tracking record request</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The insert tracking record result.</returns>
    public Task<InsertTrackingRecordResponse> InsertTrackingRecordAsync(InsertTrackingRecordRequest request, CancellationToken cancellationToken)
    {
        return InsertTrackingRecordAsync(DefaultNamespaceName, request, cancellationToken);
    }

    #endregion

    #region Helper Methods

    private async Task<TResponse> SendAndReceiveAsync<TRequest, TResponse>(TRequest request, string namespaceName,
        string transactionType, Guid? coreId, CancellationToken cancellationToken)
        where TResponse : QCashResponseBase
        where TRequest : QCashRequestBase
    {
        if (!cancellationToken.IsCancellationRequested)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException(GetType().Name);
            }

            if (!_configuration.NamespaceConfigurations.TryGetValue(namespaceName, out ServiceBusConfiguration serviceBusConfiguration))
            {
                throw new CoreProviderException(new CoreProviderFault { ErrorMessage = "Missing configuration for provided namespace name." });
            }

            var coreClient = _coreClients.GetOrAdd(coreId.GetValueOrDefault(), (key) =>
            {
                return new CoreClient(_sessionId, key, serviceBusConfiguration, _backgroundTask);
            });

            //We will use same id for message id and message set id.
            var id = Guid.NewGuid();

            var message = new ConcertMessage(id, id)
            {
                TransactionTypeName = transactionType,
                AgentId = _agentId,
            };

            if (request is not null)
            {
                message.Data = Serializer<TRequest>.Instance.SerializeXml(request);
            }

            ConcertMessage responseMessage = await coreClient.SendAsync(message, cancellationToken).ConfigureAwait(false);

            var response = Serializer<TResponse>.Instance.DeserializeXml(responseMessage.Data);

            return response.Fault is null
                ? response
                : throw new CoreProviderException(response.Fault);
        }

        throw new OperationCanceledException(cancellationToken);
    }

    #endregion

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                try
                {
                    var clients = _coreClients;

                    foreach (var coreId in clients.Keys)
                    {
                        if (clients.TryRemove(coreId, out var coreClient))
                        {
                            coreClient.Dispose();
                        }
                    }
                }
                catch
                {
                    // ignored
                }
            }

            _disposed = true;
        }
    }

    public void Dispose()
    {
        // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }
}
