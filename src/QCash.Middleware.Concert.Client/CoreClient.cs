using Azure.Storage.Blobs;
using QCash.Middleware.Concert.Client.Constants;
using QCash.Middleware.Concert.Client.Messaging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;

namespace QCash.Middleware.Concert.Client;

/// <summary>
/// This is the core client used for sending/receiving requests from/to Concert.
/// </summary>
internal sealed class CoreClient : IAsyncDisposable, IDisposable
{
    #region Fields
    private readonly string _whoAmI;
    private readonly string _coreId;
    private readonly ServiceBusConfiguration _serviceBusConfiguration;
    private readonly string _requestQueueName;
    private readonly string _responseQueue;
    private readonly TimeSpan _requestTimeout;
    private readonly Dictionary<string, string> _messagingHelperInfo;

    private volatile ReceiverClient? _receiverClient;
    private SenderClient? _senderClient;
    private BlobContainerClient? _blobContainerClient;
    private Aes? _symmetricAlgorithm;
    private readonly Action<Action> _backgroundTask;

    private ConcurrentDictionary<Guid, MessageTaskCompletionSource> _pending = new(Environment.ProcessorCount * 3, 13);

    private SemaphoreSlim _initializeSync = new(1, 1);
    private long _initialized;
    private bool _disposed;

    #endregion

    #region Constructors

    public CoreClient(string sessionId, Guid coreId, ServiceBusConfiguration configuration, Action<Action> backgroundTask)
    {
        _whoAmI = Guid.NewGuid().ToString();
        _coreId = coreId.ToString("N");
        if (Guid.Empty.Equals(coreId))
        {
            _requestQueueName = QueueNames.RequestQueueName;
            _responseQueue = $"{QueueNames.ResponseQueueName}.{sessionId}";
        }
        else
        {
            _requestQueueName = $"{QueueNames.RequestQueueName}.{_coreId}";
            _responseQueue = $"{QueueNames.ResponseQueueName}.{_coreId}.{sessionId}";
        }

        _messagingHelperInfo = new Dictionary<string, string>()
        {
            { $"{QueueNames.ResponseQueueName}.{_coreId}", _responseQueue }
        };

        _requestTimeout = configuration.RequestTimeout;
        _serviceBusConfiguration = configuration;
        _backgroundTask = backgroundTask;
    }

    #endregion

    #region Methods

    /// <summary>
    /// Send request to Concert via queue and wait for response async.
    /// </summary>
    /// <param name="message">The Concert message to send.</param>
    /// <param name="cancellationToken">The cancellation token to cancel out before completed.</param>
    /// <returns>The response Concert message.</returns>
    public async Task<ConcertMessage> SendAsync(ConcertMessage message, CancellationToken cancellationToken)
    {
        var tcs = new MessageTaskCompletionSource(message.MessageSetId, _pending,
            _requestTimeout, cancellationToken);

        if (_pending.TryAdd(message.MessageSetId, tcs))
        {
            try
            {
                if (_initialized == 0)
                {
                    await InitializeAsync(tcs.Token).ConfigureAwait(false);
                }

                //We need this on Concert side, so we know which queue to respond to.
                message.MessagingHelperInfo = _messagingHelperInfo;

                if (!tcs.Token.IsCancellationRequested)
                {
                    _ = _senderClient.SendAsync(message, _requestTimeout, cancellationToken).ContinueWith((result) =>
                    {
                        if (_pending.TryRemove(message.MessageSetId, out var taskCompletionSource)
                            && result.Exception is not null)
                        {
                            taskCompletionSource.TrySetException(result.Exception);
                        }

                    }, TaskContinuationOptions.OnlyOnFaulted);
                }
            }
            catch (Exception exception)
            {
                _pending.TryRemove(message.MessageSetId, out _);

                tcs.TrySetException(exception);
            }
        }

        return await tcs.Task.ConfigureAwait(false);
    }

    private async Task InitializeAsync(CancellationToken cancellationToken)
    {
        if (_initialized == 0)
        {
            try
            {
                await _initializeSync.WaitAsync(cancellationToken).ConfigureAwait(false);

                try
                {
                    if (_initialized == 0)
                    {
                        if (!string.IsNullOrEmpty(_serviceBusConfiguration.StorageAccountName))
                        {
                            _blobContainerClient = ServiceBusFactory.GetOrCreateBlobContainerClient(_serviceBusConfiguration.StorageAccountName, _coreId, _serviceBusConfiguration.MaxRetries);
                        }

                        if (!string.IsNullOrEmpty(_serviceBusConfiguration.KeyVaultAccountName))
                        {
                            _symmetricAlgorithm = await ServiceBusFactory.GetSymmetricKeyAsync(_serviceBusConfiguration.KeyVaultAccountName, _coreId, cancellationToken).ConfigureAwait(false);
                        }

                        await InitializeReceiverAsync(cancellationToken).ConfigureAwait(false);

                        _senderClient = await ServiceBusFactory.CreateSenderAsync(_serviceBusConfiguration.FullyQualifiedNamespace,
                            _symmetricAlgorithm, _blobContainerClient,
                            _requestQueueName, _serviceBusConfiguration.MaxRetries,
                            cancellationToken).ConfigureAwait(false);

                        Interlocked.CompareExchange(ref _initialized, 1, 0);
                    }
                }
                finally
                {
                    _initializeSync.Release();
                }
            }
            //Handle if the cancellationToken is cancelled.
            catch (ObjectDisposedException) { }
            catch (OperationCanceledException) { }
        }
    }

    private async Task InitializeReceiverAsync(CancellationToken cancellationToken)
    {
        if (_receiverClient == null)
        {
            var receiver = _receiverClient = await ServiceBusFactory.CreateReceiverAsync(_serviceBusConfiguration.FullyQualifiedNamespace,
                _symmetricAlgorithm, _blobContainerClient, _responseQueue,
                _serviceBusConfiguration.QueueTimeToLive.GetValueOrDefault(TimeSpan.FromMinutes(5)),
                _serviceBusConfiguration.MaxRetries,
                _backgroundTask,
                cancellationToken).ConfigureAwait(false);

            await receiver.OnMessageAsync((ConcertMessage message) =>
            {
                if (_pending.TryGetValue(message.MessageSetId, out MessageTaskCompletionSource taskCompletionSource))
                {
                    taskCompletionSource.TrySetResult(message);
                }

                return Task.CompletedTask;
            }).ConfigureAwait(false);
        }
    }

    public async ValueTask DisposeAsync()
    {
        //Dispose of _initializeSync
        try
        {
            var sync = _initializeSync;
            _initializeSync = null;
            await sync.WaitAsync().ConfigureAwait(false);

            try
            {
                _symmetricAlgorithm?.Dispose();
            }
            catch { }
            finally
            {
                sync.Release();
            }

            sync.Dispose();

        }
        catch { }

        //Dispose of _receiverClient, so connection closes
        try
        {
            var receiver = _receiverClient;

            _receiverClient = null;

            if (receiver != null)
            {
                await receiver.DisposeAsync().ConfigureAwait(false);
            }
        }
        catch { }

        //Fail all pending..
        try
        {
            var pending = _pending;

            _pending = null;

            foreach (var pair in pending)
            {
                try
                {
                    pair.Value.TrySetException(new ObjectDisposedException(nameof(CoreClient)));
                }
                catch { }
            }

            pending.Clear();
        }
        catch { }
    }

    private void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                //Dispose of _initializeSync
                try
                {
                    var sync = _initializeSync;
                    _initializeSync = null;
                    sync.Wait();

                    try
                    {
                        _symmetricAlgorithm?.Dispose();
                    }
                    catch { }
                    finally
                    {
                        sync.Release();
                    }

                    sync.Dispose();

                }
                catch { }

                //Dispose of _receiverClient, so connection closes
                try
                {
                    var receiver = _receiverClient;

                    _receiverClient = null;

                    receiver?.DisposeAsync().AsTask().GetAwaiter().GetResult();
                }
                catch { }

                //Fail all pending..
                try
                {
                    var pending = _pending;

                    _pending = null;

                    foreach (var pair in pending)
                    {
                        try
                        {
                            pair.Value.TrySetException(new ObjectDisposedException(nameof(CoreClient)));
                        }
                        catch { }
                    }

                    pending.Clear();
                }
                catch { }
            }

            _disposed = true;
        }
    }

    public void Dispose()
    {
        // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
        Dispose(disposing: true);
    }

    #endregion
}
