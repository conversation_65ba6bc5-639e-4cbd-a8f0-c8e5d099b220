using QCash.LoanApplication;
using QCash.LoanApplication.ResponseMessages;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace QCash.Middleware.Concert.Client;

/// <summary>
/// The Q-Cash Concert Client interface.
/// </summary>
public interface IQCashConcertClient
{

    /// <summary>
    /// Request to pull back a list of cores supported by Concert.
    /// </summary>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A list of cores supported.</returns>
    Task<Core[]> GetCoreProvidersAsync(CancellationToken cancellationToken);
    /// <summary>
    /// Request to pull back a list of cores supported by Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>A list of cores supported.</returns>
    Task<Core[]> GetCoreProvidersAsync(string namespaceName, CancellationToken cancellationToken);


    /// <summary>
    /// Request to get core connection status from Concert.
    /// </summary>
    /// <param name="coreId">The core on which you want to check the status.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The concert connection status.</returns>
    Task<CoreConnectionResponse> GetCoreConnectionStatusAsync(Guid coreId, CancellationToken cancellationToken);
    /// <summary>
    /// Request to get core connection status from Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core on which you want to check the status.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The concert connection status.</returns>
    Task<CoreConnectionResponse> GetCoreConnectionStatusAsync(string namespaceName, Guid coreId, CancellationToken cancellationToken);

    /// <summary>
    /// Request to get member information from specified core via Concert.
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters used to find the member on the core.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The member information.</returns>
    Task<Member> GetMemberAsync(Guid coreId, GetMemberParameter parameters, string correlationId, CancellationToken cancellationToken);

    /// <summary>
    /// Request to get member information from specified core via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters used to find the member on the core.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The member information.</returns>
    Task<Member> GetMemberAsync(string namespaceName, Guid coreId, GetMemberParameter parameters, string correlationId, CancellationToken cancellationToken);

    /// <summary>
    /// Request to transfer funds from one account to another account for a member via Concert.
    /// </summary>
    /// <param name="request">The transfer request containing the information.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The transfer result.</returns>
    Task<TransferResponse> TransferAsync(TransferRequest request, CancellationToken cancellationToken);
    /// <summary>
    /// Request to transfer funds from one account to another account for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="request">The transfer request containing the information.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The transfer result.</returns>
    Task<TransferResponse> TransferAsync(string namespaceName, TransferRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// Request to create and fund a loan on for a member via Concert.
    /// </summary>
    /// <param name="request">The create and fund loan request containing the information.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The loan creation and funding result.</returns>
    Task<CreateAndFundLoanResult> CreateAndFundLoanAsync(CreateAndFundLoanRequest request, CancellationToken cancellationToken);
    /// <summary>
    /// Request to create and fund a loan on for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="request">The create and fund loan request containing the information.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The loan creation and funding result.</returns>
    Task<CreateAndFundLoanResult> CreateAndFundLoanAsync(string namespaceName, CreateAndFundLoanRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// Request to get MLA status for a member via Concert.
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="member">The member to get the MLA status of.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The members MLA status.</returns>
    Task<MLAResponse> GetMlaStatusAsync(Guid coreId, Member member, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken);
    /// <summary>
    /// Request to get MLA status for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="member">The member to get the MLA status of.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The order credit card response.</returns>
    Task<MLAResponse> GetMlaStatusAsync(string namespaceName, Guid coreId, Member member, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken);

    /// <summary>
    /// Orders a credit card to be ordered for a member via Concert.
    /// </summary>
    /// <param name="request">Credit card order request.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The order credit card response.</returns>
    Task<OrderCreditCardResponse> OrderCreditCardAsync(OrderCreditCardRequest request, CancellationToken cancellationToken);
    /// <summary>
    /// Orders a credit card to be ordered for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="request">Credit card order request.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The order credit card response.</returns>
    Task<OrderCreditCardResponse> OrderCreditCardAsync(string namespaceName, OrderCreditCardRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// Request to create a share account for a member via Concert.
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters containing request information.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The account that was created.</returns>
    Task<Account> CreateShareAsync(Guid coreId, CreateShareAccountParameters parameters, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken);
    /// <summary>
    /// Request to create a share account for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters containing request information.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The account that was created.</returns>
    Task<Account> CreateShareAsync(string namespaceName, Guid coreId, CreateShareAccountParameters parameters, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken);

    /// <summary>
    /// Request to get the average monthly checking deposit balance for a member via Concert.
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters containing the request information.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The average amount.</returns>
    Task<decimal> GetAverageMonthlyCheckingDepositBalanceAsync(Guid coreId, AverageCheckingDepositBalanceParameter parameters, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken);
    /// <summary>
    /// Request to get the average monthly checking deposit balance for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters containing the request information.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The average amount.</returns>
    Task<decimal> GetAverageMonthlyCheckingDepositBalanceAsync(string namespaceName, Guid coreId, AverageCheckingDepositBalanceParameter parameters, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken);

    /// <summary>
    /// Request to get the decision engine parameters for a member via Concert.
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters containing the request information.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The decision engine parameters.</returns>
    Task<DecisionEngineParameters> GetDecisionEngineParametersAsync(Guid coreId, DecisionEngineSearchParameters parameters, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken);
    /// <summary>
    /// Request to get the decision engine parameters for a member via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="parameters">The parameters containing the request information.</param>
    /// <param name="correlationId">The correlation id</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The decision engine parameters.</returns>
    Task<DecisionEngineParameters> GetDecisionEngineParametersAsync(string namespaceName, Guid coreId, DecisionEngineSearchParameters parameters, string correlationId, int? applicationId, string clientTimeZone, CancellationToken cancellationToken);

    /// <summary>
    /// Request to insert a SymXchange tracking record via Concert.
    /// </summary>
    /// <param name="request">The tracking record request</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The insert tracking record result.</returns>
    Task<InsertTrackingRecordResponse> InsertTrackingRecordAsync(InsertTrackingRecordRequest request, CancellationToken cancellationToken);
    /// <summary>
    /// Request to insert a SymXchange tracking record via Concert.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="request">The tracking record request</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The insert tracking record result.</returns>
    Task<InsertTrackingRecordResponse> InsertTrackingRecordAsync(string namespaceName, InsertTrackingRecordRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// Request to verify a product type filter expression for a core.
    /// </summary>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="expression">The product type expression.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The product type filter expression result.</returns>
    Task<ProductTypeFilterExpressionParsingResult> ParseProductTypeFilterExpressionAsync(Guid coreId, string expression, CancellationToken cancellationToken);
    /// <summary>
    /// Request to verify a product type filter expression for a core.
    /// </summary>
    /// <param name="namespaceName">The name of the namespace to use.</param>
    /// <param name="coreId">The core id the request should be sent to.</param>
    /// <param name="expression">The product type expression.</param>
    /// <param name="cancellationToken">The cancellation token.</param>
    /// <returns>The product type filter expression result.</returns>
    Task<ProductTypeFilterExpressionParsingResult> ParseProductTypeFilterExpressionAsync(string namespaceName, Guid coreId, string expression, CancellationToken cancellationToken);
}
