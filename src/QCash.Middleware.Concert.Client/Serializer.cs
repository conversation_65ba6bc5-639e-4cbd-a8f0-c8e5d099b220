using Microsoft.IO;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Xml;

namespace QCash.Middleware.Concert.Client;

public class Serializer<TType> : Serializer
    where TType : class
{
    private readonly DataContractSerializer _serializer = new(typeof(TType));
    public static readonly Serializer<TType> Instance = new();

    private Serializer() { }

    public byte[] SerializeXml(TType request)
    {
        using MemoryStream memoryStream = StreamPool.GetStream();
        using XmlWriter xmlWriter = XmlWriter.Create(memoryStream, DefaultXmlWriterSettings);
        using XmlDictionaryWriter xmlDictionaryWriter = XmlDictionaryWriter.CreateDictionaryWriter(xmlWriter);
        _serializer.WriteObject(xmlDictionaryWriter, request);
        xmlDictionaryWriter.Flush();
        return memoryStream.ToArray();
    }

    public TType DeserializeXml(byte[] data)
    {
        using StreamReader textReader = new StreamReader(new MemoryStream(data), Encoding.UTF8, true);
        using XmlReader xmlReader = XmlReader.Create(textReader, DefaultXmlReaderSettings);
        xmlReader.MoveToContent();

        return (TType)_serializer.ReadObject(xmlReader);
    }

    /*

    Just putting these here for future reference.  The DataContractSerializer puts the namespaces in the XML going out,
    and Concert requires the QCash.LoanApplication namespace. On the way in, DataContractSerializer constructs objects
    based on the specified namespace as well.  I tried changing everything to reasonable namespaces and then overwriting
    here, but it's a bit of a hack.

    No attempt to override the namespaces properly worked; a better solution might be found by using XmlSerializer instead
    of DataContractSerializer, but there are important differences between the two. Switching would certainly introduce side
    effects.

    public byte[] SerializeXmlWithNamespaceFix(TType request)
       {
           using MemoryStream memoryStream = StreamPool.GetStream();
           using XmlWriter xmlWriter = XmlWriter.Create(memoryStream, DefaultXmlWriterSettings);
           using XmlDictionaryWriter xmlDictionaryWriter = XmlDictionaryWriter.CreateDictionaryWriter(xmlWriter);
           _serializer.WriteObject(xmlDictionaryWriter, request);
           xmlDictionaryWriter.Flush();
           memoryStream.Seek(0, SeekOrigin.Begin);
           using StreamReader reader = new StreamReader(memoryStream);
           var xmlstring = reader.ReadToEnd();
           xmlstring = xmlstring.Replace(@"http://schemas.datacontract.org/2004/07/" + typeof(TType).Assembly.GetName().Name + ".RequestMessages", @"http://schemas.datacontract.org/2004/07/QCash.LoanApplication");
           xmlstring = xmlstring.Replace(@"http://schemas.datacontract.org/2004/07/" + typeof(TType).Assembly.GetName().Name, @"http://schemas.datacontract.org/2004/07/QCash.LoanApplication");
           xmlstring = xmlstring.Replace(@"http://schemas.datacontract.org/2004/07/QCash.Middleware.Models",
               "http://schemas.datacontract.org/2004/07/QCash.LoanApplication");

           return Encoding.UTF8.GetBytes(xmlstring);
       }

       public TType DeserializeXml(byte[] data)
       {
           //This is incomplete because there are contained classes that would also need their namespaces changed.
           //Ultimately need something like a dictionary cross-reference for every possible namespaced element.

           using StreamReader textReader = new StreamReader(new MemoryStream(data), Encoding.UTF8, true);
           //Response messages are unfortunately always of QCash.LoanApplication.ResponseMessages namespace and DataContractSerializer really cares about that.
           var xml = textReader.ReadToEnd().Replace(@"http://schemas.datacontract.org/2004/07/QCash.LoanApplication", @"http://schemas.datacontract.org/2004/07/" + typeof(TType).Assembly.GetName().Name);
           //it's possible we may need to revert QCash.Middleware.Models.Enums back to just the common Models namespace as well
           using var textReaderModified = GenerateStreamFromString(xml);
           using XmlReader xmlReader = XmlReader.Create(textReaderModified, DefaultXmlReaderSettings);
           xmlReader.MoveToContent();

           return (TType)_serializer.ReadObject(xmlReader);
       }
     */

}

public class Serializer
{
    public static readonly RecyclableMemoryStreamManager StreamPool = new();

    public static readonly XmlWriterSettings DefaultXmlWriterSettings = new()
    {
        Encoding = new UTF8Encoding(false, false),
        OmitXmlDeclaration = true,
        NewLineHandling = NewLineHandling.None,
    };
    public static readonly XmlReaderSettings DefaultXmlReaderSettings = new()
    {
        Async = false,
        CheckCharacters = false,
        ConformanceLevel = ConformanceLevel.Auto,
        IgnoreComments = true,
        IgnoreProcessingInstructions = true,
        ValidationFlags = System.Xml.Schema.XmlSchemaValidationFlags.None,
    };

    public static object ReflectionDataContractResolver { get; private set; }
}
