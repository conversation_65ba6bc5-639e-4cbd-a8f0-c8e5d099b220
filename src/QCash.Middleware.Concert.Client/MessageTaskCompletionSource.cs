using QCash.Middleware.Concert.Client.Messaging;
using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;

namespace QCash.Middleware.Concert.Client;

public class MessageTaskCompletionSource : TaskCompletionSource<ConcertMessage>, IDisposable
{
    private bool _disposed;
    private CancellationTokenSource _cancellationTokenSource;
    private readonly Guid _messageSetId;
    private readonly ConcurrentDictionary<Guid, MessageTaskCompletionSource> _pending;

    public MessageTaskCompletionSource(Guid messageSetId,
        ConcurrentDictionary<Guid, MessageTaskCompletionSource> pending,
        TimeSpan timeout, CancellationToken cancellationToken)
        : base(TaskCreationOptions.RunContinuationsAsynchronously)
    {
        //Need to store incase it's cancelled or timed out.
        _messageSetId = messageSetId;
        _pending = pending;

        if (cancellationToken.CanBeCanceled)
        {
            _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, CancellationToken.None);
            _cancellationTokenSource.CancelAfter(timeout);
        }
        else
        {
            _cancellationTokenSource = new CancellationTokenSource(timeout);
        }

        _cancellationTokenSource.Token.Register(() => CancelToken(_cancellationTokenSource.Token));
    }

    public CancellationToken Token => _cancellationTokenSource.Token;

    public void CancelToken(CancellationToken cancellationToken)
    {
        //If in pending queue we want to remove.
        if (_pending.TryRemove(_messageSetId, out var result))
        {
            result.TrySetCanceled(cancellationToken);
        }
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                _cancellationTokenSource?.Dispose();
            }

            _cancellationTokenSource = null;

            _disposed = true;
        }
    }

    ~MessageTaskCompletionSource()
    {
        Dispose(false);
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
