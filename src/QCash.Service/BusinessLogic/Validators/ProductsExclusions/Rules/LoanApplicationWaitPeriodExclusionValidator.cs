using static QCash.Service.Models.Core.Enums;
using QCash.Service.Services.Helpers;
using static QCash.Service.Services.LoanApplication.Enums;
using QCash.Service.Utilities.Extensions;
using QCash.Data.Context;
using Microsoft.EntityFrameworkCore;

namespace QCash.Service.BusinessLogic.Validators.ProductsExclusions.Rules;


/// <summary>
/// LoanApplicationWaitPeriodExclusionValidator implementation
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="LoanApplicationWaitPeriodExclusionValidator"/> class
/// </remarks>
public class LoanApplicationWaitPeriodExclusionValidator(QCashContext qCashContext) : ProductsExclusionValidatorBase(qCashContext), IProductsExclusionValidator
{

    /// <summary>
    /// Check and Exclude Products.
    /// </summary>
    ///<param name="dto">The dto. </param>
    public async Task<bool> ValidateAsync(ProductsExclusionsValidatorDTO dto)
    {
        ArgumentNullException.ThrowIfNull(dto);

        Timer.Start();

        // Gets the product with the longest delay time period.
        var productWithLongestDelayPeriod = dto.AvailableProducts.OrderBy(x => x.LoanDelayTimePeriodDays).LastOrDefault();
        if (productWithLongestDelayPeriod != null && productWithLongestDelayPeriod.LoanDelayTimePeriodDays != 0 && productWithLongestDelayPeriod.LoanDelayTimePeriodDays.HasValue)
        {
            var clientTimeZone = (await DbContext.Settings.AsNoTracking().FirstOrDefaultAsync())?.TimeZone ?? string.Empty;
            var clientNow = DateTime.UtcNow.FromUTC(TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone));
            var fromDateBase = clientNow.AddDays(-(productWithLongestDelayPeriod.LoanDelayTimePeriodDays.Value + 1));

            // Get all Member Accounts in the given time period.

            var memberAccounts = await DbContext.MemberBaseAccounts
                .Where(mba => mba.FinancialInstitutionMemberId == dto.LoanApplication.FinancialInstitutionMemberId && mba.OwnerShipType == LoanApplication.OwnerShipType.Primary.ToString())
                .SelectMany(s => s.MemberAccounts)
                .Where(ma => ma.Category == AccountCategory.LOAN.ToString() && ma.DateOpened >= fromDateBase)
                .ToListAsync();

            var excludedProducts = new List<Data.Models.Product>();

            foreach (var prod in dto.AvailableProducts)
            {
                if (!prod.LoanDelayTimePeriodDays.HasValue)
                {
                    continue;
                }

                var type = prod.Abrv;
                string? subType = null;

                if (prod.Abrv.Contains('.'))
                {
                    var types = prod.Abrv.Split('.');
                    type = types[0];
                    subType = types[1];
                }

                var fromDate = clientNow.AddDays(-(prod.LoanDelayTimePeriodDays.Value + 1));
                var fromDateClient = new DateTime(fromDate.Year, fromDate.Month, fromDate.Day, 0, 0, 0, 1);
                var excludedAccounts = memberAccounts.Any(x => x.Type == type && x.SubType == subType && x.DateOpened > fromDateClient);

                if (excludedAccounts)
                {
                    excludedProducts.Add(prod);
                }
            }

            dto.AvailableProducts.RemoveAll(x => excludedProducts.Contains(x));
        }

        Timer.Stop();
        var isExcluded = !dto.AvailableProducts.Any();

        dto.Logs.AddLogDetail(
            LogType.Clean,
            LogSortOrder.ExclusionByLoanApplicationWaitPeriod,
            $"ProductExclusionByLoanApplicationWaitPeriod: {(isExcluded ? "Yes" : "No")}");
        dto.Logs.AddLogDetail($"Loan Application Wait Period exclusion finished: {Timer.Elapsed.TotalSeconds}");
        if (isExcluded)
        {
            var loanStatusLSELFWP = await DbContext.LoanStatusTypes.AsNoTracking().SingleAsync(o => o.Abrv == "LSELFWP");
            dto.CreateExclusionValidatorResult(LoanApplicationStep.ExclusionByLoanApplicationWaitPeriod, loanStatusLSELFWP, LogSortOrder.ExclusionByLoanApplicationWaitPeriod);
        }

        return !isExcluded;
    }
}
