using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace QCash.Service.Utilities.Extensions
{
    internal static class DateTimeExtensions
    {
        /// <summary>
        /// Adds one month and subtracts a millisecond.
        /// </summary>
        /// <param name="value">The value.</param>
        /// <returns></returns>
        public static DateTime AddMonthMinusMillisecond(this DateTime value)
        {
            return value.AddMonths(1).AddMilliseconds(-1);
        }

        /// <summary>
        /// Adds the business days.
        /// </summary>
        /// <param name="date">The date.</param>
        /// <param name="days">The days.</param>
        /// <returns></returns>
        /// <exception cref="System.ArgumentException">Days cannot be negative - days</exception>
        public static DateTime AddBusinessDays(this DateTime date, int days)
        {
            if (days < 0)
            {
                throw new ArgumentException("Days cannot be negative", "days");
            }

            if (days == 0)
            {
                return date;
            }

            if (date.DayOfWeek == DayOfWeek.Saturday)
            {
                date = date.AddDays(2);
                days -= 1;
            }
            else if (date.DayOfWeek == DayOfWeek.Sunday)
            {
                date = date.AddDays(1);
                days -= 1;
            }

            date = date.AddDays(days / 5 * 7);
            int extraDays = days % 5;

            if ((int)date.DayOfWeek + extraDays > 5)
            {
                extraDays += 2;
            }

            return date.AddDays(extraDays);
        }

        /// <summary>
        /// Converts the DateTime UTC value in the specific zone. 
        /// </summary>
        /// <param name="value">The value.</param>
        /// <param name="destinationZone">The destination zone.</param>
        /// <returns>DateTime value in the specifice zone from provided UTC value.</returns>
        public static DateTime FromUTC(this DateTime value, TimeZoneInfo destinationZone)
        {
            var time = new DateTime(value.Ticks, DateTimeKind.Utc);
            return TimeZoneInfo.ConvertTimeFromUtc(time, destinationZone);
        }

        /// <summary>
        /// Converts the DateTime UTC value in the specific zone. 
        /// </summary>
        /// <param name="value">The value.</param>
        /// <param name="destinationZone">The destination zone.</param>
        /// <returns>DateTime value in the specifice zone from provided UTC value.</returns>
        public static DateTime? FromUTC(this DateTime? value, TimeZoneInfo destinationZone)
        {
            if (value.HasValue)
            {
                return TimeZoneInfo.ConvertTimeFromUtc(value.GetValueOrDefault(), destinationZone);
            }

            return null;
        }

        /// <summary>
        /// Converts provided date to the UTC DateTime.
        /// </summary>
        /// <param name="value">The value.</param>
        /// <param name="sourceZone">The source zone.</param>
        /// <returns>DateTime converted to the UTC.</returns>
        public static DateTime ToUTC(this DateTime value, TimeZoneInfo sourceZone)
        {
            var time = new DateTime(value.Ticks, DateTimeKind.Unspecified);
            return TimeZoneInfo.ConvertTimeToUtc(time, sourceZone);
        }

        /// <summary>
        /// Converts provided date to the UTC DateTime.
        /// </summary>
        /// <param name="value">The value.</param>
        /// <param name="sourceZone">The source zone.</param>
        /// <returns>DateTime converted to the UTC.</returns>
        public static DateTime? ToUTC(this DateTime? value, TimeZoneInfo sourceZone)
        {
            if (value.HasValue)
            {
                var time = new DateTime(value.GetValueOrDefault().Ticks, DateTimeKind.Unspecified);
                return TimeZoneInfo.ConvertTimeToUtc(time, sourceZone);
            }
            return null;
        }

        /// <summary>
        /// To nullable date time object.
        /// </summary>
        /// <param name="value">The value.</param>
        /// <returns>Null if value is DateTime.MinValue.</returns>
        public static DateTime? ToNullableDateTime(this DateTime value)
        {
            if (value == DateTime.MinValue)
            {
                return (DateTime?)null;
            }

            return value;
        }

        /// <summary>
        /// First following day of week (excluding weekends). Return current date if it is not weekend day. 
        /// </summary>
        /// <param name="value">The value.</param>
        /// <returns>First following day of week (excluding weekends).</returns>
        public static DateTime FirstWeekday(this DateTime value)
        {
            if (value.DayOfWeek == DayOfWeek.Saturday)
            {
                value = value.AddDays(2);
            }
            else if (value.DayOfWeek == DayOfWeek.Sunday)
            {
                value = value.AddDays(1);
            }

            return value;
        }

        public static TimeZoneInfo GetTimeZoneInfo(this string? value)
        {
            return TimeZoneInfo.FindSystemTimeZoneById(value ?? string.Empty);
        }

    }
}
