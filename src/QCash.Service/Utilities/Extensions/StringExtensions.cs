using System.Text;

namespace QCash.Service.Utilities.Extensions;
using System;
using System.Text.RegularExpressions;

/// <summary>
/// String extensions.
/// </summary>
public static class StringExtensions
{
    public static DateTime? ToDateTimeOrNull(this string? input)
    {
        if (DateTime.TryParse(input, out var convertedDate))
        {
            return convertedDate;
        }

        return null;
        //Not immediately bringing everything over from the QC6 version of this; migrating as needed.
    }

#pragma warning disable MA0023
    public static string StripHtmlTags(this string source) =>
        Regex.Replace(source, "<[^>]*(>|$)", string.Empty, RegexOptions.None, TimeSpan.FromSeconds(1));
#pragma warning restore MA0023

    public static string JoinWithConjunction<T>(
        this IEnumerable<T?> values,
        in string? separator = ", ",
        in string? lastSeparator = ", and ") => JoinWithConjunction(values, new StringBuilder(), separator, lastSeparator).ToString();

    private static StringBuilder JoinWithConjunction<T>(
        this IEnumerable<T?> values,
        StringBuilder sb,
        in string? separator = ", ",
        in string? lastSeparator = ", and ")
    {
        ArgumentNullException.ThrowIfNull(values);
        ArgumentNullException.ThrowIfNull(separator);

        using var enumerator = values.GetEnumerator();

        // add first item without a separator
        if (enumerator.MoveNext())
        {
            sb.Append(enumerator.Current);
        }

        var nextItem = (hasValue: false, item: default(T?));
        // see if there is a next item
        if (enumerator.MoveNext())
        {
            nextItem = (true, enumerator.Current);
        }

        // while there is a next item, add separator and current item
        while (enumerator.MoveNext())
        {
            sb.Append(separator);
            sb.Append(nextItem.item);
            nextItem = (true, enumerator.Current);
        }

        // add last separator and last item
        if (nextItem.hasValue)
        {
            sb.Append(lastSeparator ?? separator);
            sb.Append(nextItem.item);
        }

        return sb;
    }
}
