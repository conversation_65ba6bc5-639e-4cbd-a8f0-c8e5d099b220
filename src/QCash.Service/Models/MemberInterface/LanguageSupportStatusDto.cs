namespace QCash.Service.Models.MemberInterface;

public class LanguageSupportStatusDto
{
    public Guid Id { get; init; }
    public Guid LanguageId { get; init; }

    public Guid FinancialInstitutionId { get; init; }
    public bool IsActive { get; init; }
    public DateTime DateCreatedUtc { get; init; }
    public DateTime DateUpdatedUtc { get; init; }

    public required byte[] TimeStamp { get; init; }
}
