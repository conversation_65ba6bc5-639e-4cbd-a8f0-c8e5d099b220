using QCash.Service.Utilities;

namespace QCash.Service.Models.FIConfiguration;

public class UserDetailsPageDTO
{
    public string UserName { get; set; } = "";
    public Guid Id { get; init; }
    public string FirstName { get; init; } = "";
    public string LastName { get; init; } = "";
    public string Email { get; init; } = "";
    public string? PhoneNumber { get; init; } = "";
    public List<QListItem<Guid>> SelectedRoles { get; init; } = [];
    public Guid RoleTypeId { get; set; }

    public bool IsApproved { get; init; }
    public bool IsLockedOut { get; init; }

    public DateTime? DateUpdatedUtc { get; init; }
    public DateTime? DateCreatedUtc { get; init; }

    /// <summary>
    /// Metadata
    /// </summary>
    public bool EditingSuperUser { get; internal set; }
    public bool CanFIMngrEditInputs { get; internal set; }
    public GenericActionResult? CanUpdateRoles { get; internal set; }
    public List<QListItem<Guid>> RoleTypeOptions { get; set; } = [];
    public DateTime? DateCreatedLocal { get; set; }
    public DateTime? DateUpdatedLocal { get; set; }
    public string? EmailConfirm { get; set; }      // required when creating, not required or populated when editing
    public string? Password { get; set; }       // required when creating, not required or populated when editing
    public string? PasswordConfirm { get; set; }      // required when creating, not required or populated when editing
    public required byte[] TimeStamp { get; init; } = [];

    public bool CreatingUserRecord() => UserName == ItemConstants.CreateItemTag;
}
