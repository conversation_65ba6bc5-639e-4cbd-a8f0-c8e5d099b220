namespace QCash.Service.Models.FIConfiguration;

public class FiInfoDto
{
    public required Guid FinancialInstitutionId { get; init; }
    public required string Name { get; init; }
    public required string Address { get; init; }
    public required string City { get; init; }
    public required string? NoReplySubdomain { get; init; }
    public required string? NoReplyDisplayName { get; init; }
    public required byte[] FinancialInstitutionTimeStamp { get; init; }
    public required string Zip { get; init; }
    public required string State { get; init; }
    public required string ContactName { get; init; }
    public required string ContactPhone { get; init; }
    public required string ContactEmail { get; init; }
    public required string? SupportEmail { get; init; }
    public required string TimeZone { get; init; }
    public required Guid? SettingId { get; init; }
    public required byte[] SettingTimeStamp { get; init; }
}

