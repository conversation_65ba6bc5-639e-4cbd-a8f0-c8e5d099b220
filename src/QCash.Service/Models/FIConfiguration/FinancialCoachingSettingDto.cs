namespace QCash.Service.Models.FIConfiguration;

public class FinancialCoachingSettingDto
{
    public required string FiSlug { get; init; }
    public required Guid Id { get; init; }
    public required Guid FinancialInstitutionId { get; set; }
    public required byte[] TimeStamp { get; init; }
    public required bool AwarenessPage { get; init; }
    public required bool LandingPageDebtManagement { get; init; }
    public required int FeeBasedLoansThreshold { get; init; }
    public required int InterestBasedLoansThreshold { get; init; }
    public required int AwarenessPeriod { get; init; }
    public required bool DebtCounselorConfirmation { get; set; }
    public required string? DebtManagementAgencyName { get; set; }
    public required string? DebtManagementEmail { get; set; }
}
