namespace QCash.Service.Models.LoanApplication;

public class ManageRestrictionsPageDto
{
    public required string FiSlug { get; init; }

    /// <summary>
    /// In the UI, known as [Max Interest Rate for MLA Covered Borrowers]
    /// </summary>
    public decimal MilitaryAnnualPercentageRate { get; init; }

    public required Guid SettingId { get; init; }
    public required byte[] SettingTimeStamp { get; init; } = [];
    public List<ManageRestrictionStateDto> ExcludedStates { get; init; } = [];
    public List<ManageRestrictionProductDto> Products { get; init; } = [];
    public List<ManageRestrictionRateDto> RestrictedRates { get; init; } = [];
    public bool IncludeInactiveProducts { get; init; }
}
