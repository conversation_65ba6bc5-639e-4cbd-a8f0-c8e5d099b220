namespace QCash.Service.Models.LoanApplication;

public class ManageRestrictionRateDto
{
    public Guid Id { get; init; }
    public Guid ProductId { get; init; }
    public required string State { get; init; }
    public decimal Rate { get; init; }
    public required byte[] TimeStamp { get; init; } = [];
    public bool CreateRequested { get; init; }
    public bool DeleteRequested { get; init; }
    public bool UpdateRequested { get; init; }
}
