using System.ComponentModel.DataAnnotations;

namespace QCash.Service.Models.Reports;

public static class Enums
{
    public enum ReportTypeEnum
    {
        Unknown = 0,

        [Display(Name = "Location Detail Report")]
        LoanApplicationDetailed = 1,

        [Display(Name = "Location Summary Report")]
        LoanApplicationSummary = 2,

        [Display(Name = "Denied Loan Report")]
        DeniedLoan = 3,

        [Display(Name = "No Email Report")]
        NoEmail = 4,

        [Display(Name = "Application and Origination Failed Fee Report")]
        FailedFees = 5,

        [Display(Name = "Unique Members Report")]
        UniqueMembers = 6,

        [Display(Name = "Credit Card Report")]
        CreditCard = 7,

        [Display(Name = "Invoice Report")]
        Invoice = 8,

        [Display(Name = "Data Extract")]
        DataExtract = 9,
    }
}
