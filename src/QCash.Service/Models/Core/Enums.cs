using System.ComponentModel;

namespace QCash.Service.Models.Core;

public static class Enums
{
    public enum LoanApplicationStep
    {
        None = 0,
        EConsentDisclosure = 1,
        Application = 2,
        ApplicationQualified = 3,
        TILA = 4,
        FundingComplete = 5,
        FundingPending = 6,
        ProcessingError = 8,
        ProductExclusionByState = 12,
        AdverseAction = 13,
        TILACancellationFinal = 14,
        ApplicationCancellationFinal = 15,
        AccountExclusionByType = 16,
        HaveLoanApplicationInProgress = 17,
        MilitaryNoProduct = 18,
        LoanHub = 19,
        PayoffList = 20,
        Payoff = 21,
        PayoffFinal = 22,
        PayoffExit = 23,
        PayoffError = 24,
        PayoffConfirmation = 25,
        TILAEmail = 26,
        AANEmail = 27,
        Awareness = 28,
        LoanLanding = 30,
        LoanLandingInitial = 31,
        PayoffInitial = 33,
        HaveMaximumDeniedLoans = 34,
        FundingCompleteCreditCard = 35,
        FundingCompleteCreditCardError = 36,
        ExclusionByBankruptcyStatus = 37,
        ProductExclusionByEmail = 39,
        HaveLoanApplicationSignaturePending = 41,
        ProductExclusionByPersonalLoan = 42,
        Maintenance = 43,
        ExclusionByJointAccount = 44,
        FraudControl = 45,
        FraudControlCodeExpired = 47,
        ExclusionByFraudControlStatus = 48,
        ExclusionByMissingEmailAndPhone = 49,
        ExclusionByLoanApplicationWaitPeriod = 50,
        QCFAudit = 51,
        ExclusionByTroubledDebt = 52,
        ExclusionByNoEligibleAccounts = 53,
        ExclusionByContactInfoChanged = 54,
        ExclusionByBlocklist = 55,
        DataCollection = 56,
        DataCollectionDeclined = 57,
        ExclusionByAge = 58,
        ExclusionByBadEmail = 59,
        ExclusionByBadAddress = 60,
    }

    public enum LoanApplicationDocumentType
    {
        None = 0,

        [Description("DTAAN")]
        AdverseActionNotice = 1,

        [Description("DTEDD")]
        EConsentDisclosure = 2,

        [Description("DTTILA")]
        TILA = 3,

        Invoice = 4,
        AppLogs = 5,
        PaymentGuard = 6,
    }

    public enum WhiteListIpTypeEnum
    {
        Unknown = 0,
        AdminUI = 1,
        MemberUI = 2,
    }

    public enum AccountCategory
    {
        SHARE,
        LOAN
    }

    public enum DecisionModelStatusEnum
    {
        Unknown = 0,

        [Description("Alerts")]
        ALRT = 1,

        [Description("Inactive")]
        INCT = 2,

        [Description("Active")]
        ACTV = 3,

        [Description("Archived")]
        ARHV = 4,
    }

    public enum ImportExportInterfaceTextType
    {
        InterfaceText,
        PurposeOfLoanType,
        AanReason,
        PaymentDate,
        PaymentOptionTitle,
        Weekday
    }
}
