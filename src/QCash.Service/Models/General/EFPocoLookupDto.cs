using QCash.Data.Models.Interfaces;
using QCash.Service.Models.DecisionEngine;
using QCash.Utils.Extensions;

namespace QCash.Service.Models.General;

// ReSharper disable once InconsistentNaming
public class EFPocoLookupDto : IEFPocoLookup
{
    public int GetObjectID() => throw new NotImplementedException();

    public DateTime DateCreatedUtc { get; set; }
    public DateTime DateUpdatedUtc { get; set; }
    public Guid Id { get; set; }
    public byte[] TimeStamp { get; set; } = [];
    public bool IsDeleted { get; set; }
    public required string Name { get; set; } = null!;
    public required string Description { get; init; } = null!;
    public string Abrv { get; set; } = string.Empty;
    public required string Code { get; init; } = null!;
    public string AppAbrv { get; init; } = string.Empty;
    public required string Slug { get; set; } = null!;
    public Guid FinancialInstitutionId { get; set; }
    public string? TranslationName { get; set; }
    public string? TranslationDescription { get; set; }
    public bool SupportsForeignLanguage { get; set; }
    public string NameHeader { get; set; } = string.Empty;
    public LanguageDto? ForeignLanguage { get; set; }
    public Enums.DecisionEngineLookupTypeEnum LookupTypeEnum { get; set; }
    public required string FiSlug { get; set; }
    public required string LookupTypeName { get; init; }

    public static EFPocoLookupDto FromDbRecord(IEFPocoLookup dbRecord, string lookupTypeName)
    {
        var properties = dbRecord.GetType().GetProperties();
        var result = new EFPocoLookupDto()
        {
            Id = dbRecord.Id,
            Name = dbRecord.Name,
            Slug = dbRecord.Slug,
            IsDeleted = dbRecord.IsDeleted,
            FinancialInstitutionId = dbRecord.FinancialInstitutionId,
            TimeStamp = dbRecord.TimeStamp,
            DateCreatedUtc = dbRecord.DateCreatedUtc,
            DateUpdatedUtc = dbRecord.DateUpdatedUtc,
            Description = properties.GetPropertyValue<string>(dbRecord, nameof(Description)) ?? "",
            Abrv = properties.GetPropertyValue<string>(dbRecord, nameof(Abrv)) ?? "",
            AppAbrv = properties.GetPropertyValue<string>(dbRecord, nameof(AppAbrv)) ?? "",
            Code = properties.GetPropertyValue<string>(dbRecord, nameof(Code)) ?? "",
            FiSlug = "",
            LookupTypeName = lookupTypeName,
        };

        return result;
    }

}
