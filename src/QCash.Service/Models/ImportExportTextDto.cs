using System.Net;
using QCash.Service.Models.Core;

namespace QCash.Service.Models;

public class ImportExportTextDto(Enums.ImportExportInterfaceTextType type, string abrv, string name, string fieldValue)
{
    public Enums.ImportExportInterfaceTextType Type { get; set; } = type;
    public string Abrv { get; set; } = abrv;
    public string Name { get; set; } = WebUtility.HtmlDecode(name);
    public string FieldValue { get; set; } = WebUtility.HtmlDecode(fieldValue);
}
