using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using QCash.Data.Models;

namespace QCash.Service.Classes.DecisionManager
{
    public class ProductAvailableResult
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ProductAvailableResult"/> class.
        /// </summary>
        public ProductAvailableResult()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ProductAvailableResult" /> class.
        /// </summary>
        /// <param name="product">The product.</param>
        /// <param name="modelSelectorId">The model selector identifier.</param>
        /// <param name="decisionModelsSelection">The decision models selection.</param>
        /// <param name="campaignCodeAccount">The campaign code account.</param>
        public ProductAvailableResult(Product product, Guid? modelSelectorId, IList<DecisionModelSelection> decisionModelsSelection, CampaignCodeAccount? campaignCodeAccount)
        {
            Product = product;
            ModelSelectorId = modelSelectorId;
            DecisionModelsSelection = decisionModelsSelection;
            CampaignCodeAccount = campaignCodeAccount;
        }

        /// <summary>
        /// Gets or sets the product.
        /// </summary>
        public Product? Product { get; set; }

        /// <summary>
        /// Gets or sets the model selector identifier.
        /// </summary>
        public Guid? ModelSelectorId { get; set; }

        /// <summary>
        /// Gets or sets the decision models selection.
        /// </summary>
        public IList<DecisionModelSelection>? DecisionModelsSelection { get; set; }

        /// <summary>
        /// Gets or sets the personal campaign.
        /// </summary>
        public CampaignCodeAccount? CampaignCodeAccount { get; set; }
    }
}
