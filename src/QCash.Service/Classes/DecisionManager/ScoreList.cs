using System.Globalization;

namespace QCash.Service.Classes.DecisionManager;

public class ScoreList : List<Score>
{
    public Guid TransformationId { get; set; }
    public Guid ModelId { get; set; }

    public void Add(double from, double to, double value)
    {
        Add(new Score
        {
            From = from.ToString(),
            To = to,
            Value = value
        });
    }

    public void Add(string from, double to, double value)
    {
        Add(new Score
        {
            From = from,
            To = to,
            Value = value
        });
    }

    public Score? GetScore(double value)
    {
        foreach (var item in this)
        {
            var isNumeric = double.TryParse(item.From, NumberStyles.Number, new CultureInfo("en-US"), out var from);

            if (isNumeric)
            {
                if (value >= from && value <= Convert.ToDouble(item.To))
                {
                    return item;
                }
            }
            else
            {
                try
                {
                    if (FormulaCalculator.CalculateExpression(FormulaCalculator.InfixToPostFix(value.ToString() + item.From + item.To.ToString())) == 1)
                    {
                        return item;
                    }
                }
                catch (Exception)
                {
                }
            }
        }
        return null;

    }

    public double? GetScoreValue(double value)
    {
        try
        {
            return GetScore(value)?.Value;
        }
        catch (Exception)
        {
            return 0;
        }
    }
}
