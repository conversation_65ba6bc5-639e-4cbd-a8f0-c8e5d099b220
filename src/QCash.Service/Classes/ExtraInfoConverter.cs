using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using QCash.LoanApplication;

namespace QCash.Service.Classes
{

    public class ExtraInfoConverter : JsonConverter
    {
        private static readonly string[] ExtraInfoExcludeList = ["MemberToken"];

        public override bool CanConvert(Type objectType) => objectType == typeof(ExtraInfo);

        public override object? ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
        {
            if (reader.TokenType == JsonToken.Null)
            {
                return null;
            }

            JObject item = JObject.Load(reader);
            return item.ToObject<ExtraInfo>();
        }

        public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
        {
            ExtraInfo? extraInfo = (ExtraInfo?)value;
            var copied = JsonConvert.DeserializeObject<ExtraInfo>(JsonConvert.SerializeObject(extraInfo));

            // Need to preserve source object.
            if (extraInfo?.Infoes != null && extraInfo.Infoes.Count > 0)
            {
                foreach (var exclude in ExtraInfoExcludeList)
                {
                    copied?.Infoes.Remove(exclude);
                }
            }

            if (copied != null)
            {
                var obj = JObject.FromObject(copied);
                obj.WriteTo(writer);
            }
        }
    }
}
