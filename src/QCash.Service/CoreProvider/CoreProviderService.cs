using System.Text;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using QCash.LoanApplication;
using QCash.LoanApplication.ApiRequestModels;
using QCash.LoanApplication.Interfaces;
using QCash.LoanApplication.Models;
using QCash.LoanApplication.ResponseMessages;
using QCash.Utils.Settings;

namespace QCash.Service.CoreProvider
{
    public class CoreProviderService : ICoreProvider, IDecisionEngineParametersProvider
    {
        public HttpClient HttpClient { get; set; }
        public string DateFormat { get; set; } = "yyyy'-'MM'-'dd'T'HH':'mm':'ss";

        public string? MiddlewareRouterBaseAddress { get; set; }

        private readonly ILogger<CoreProviderService> _logger;
        private readonly IDistributedCache _cache;

        private readonly ICoreProviderRetryService _retryService;

        //Leaving the old token stuff in here and commented out for now - not sure whether/what path we'll take with this, see #8090 Address security
        //private Token? _token;
        //public string ClientId { get; set; }
        //public string ClientSecret { get; set; }
        //public string AccessTokenUrl { get; set; }

        public CoreProviderService(
            ICoreProviderRetryService retryService,
            IDistributedCache cache,
            ILogger<CoreProviderService> logger,
            IHttpClientFactory httpclientfactory,
            IOptions<ApplicationOptions> applicationOptions
            )
        {
            _retryService = retryService;
            _cache = cache;
            _logger = logger;
            MiddlewareRouterBaseAddress = applicationOptions.Value.MiddlewareRouterBaseUrl;

            HttpClient = httpclientfactory.CreateClient("MiddlewareRouter");
            HttpClient.BaseAddress = new Uri(MiddlewareRouterBaseAddress ?? string.Empty);
        }

        public async Task<CreateAndFundLoanResult> CreateAndFundLoanAsync(CreateAndFundLoanApiRequest request)
        {
            //Returns the Result property of the Response
            try
            {
                var createAndFundLoanResponse = await PostRequestAsync<CreateAndFundLoanApiRequest, CreateAndFundLoanResponse>(request,
                    "Loan/CreateAndFundLoan");
                return createAndFundLoanResponse.Result;
            }
            catch (ApiCoreProviderException ex)
            {
                var response = ex.Response as CreateAndFundLoanResponse;
                if (response?.Result == null)
                {
                    throw;
                }

                if (response.Result.Result == CreateAndFundLoanResultEnum.FailedMemoMode)
                {
                    if (request.UseQCashRetryQueue)
                    {
                        await _retryService.SendMessageToRetryQueueAsync(request);
                    }
                    //otherwise, the Core/MW supports its own automatic retry
                    response.Result.Result = CreateAndFundLoanResultEnum.Pending;
                }

                throw;
            }
        }

        public async Task<decimal> GetAverageMonthlyCheckingDepositBalanceAsync(
            Guid coreId,
            AverageCheckingDepositBalanceParameter parameters,
            string correlationId,
            int applicationId,
            string additionalHeaders,
            string clientTimeZone)
        {
            try
            {
                GetAverageMonthlyCheckingDepositBalanceApiRequest request = new GetAverageMonthlyCheckingDepositBalanceApiRequest
                {
                    CoreId = coreId,
                    AverageCheckingDepositBalanceParameter = parameters,
                    CorrelationId = correlationId,
                    ApplicationId = applicationId,
                    AdditionalHeaders = additionalHeaders,
                    TimeZone = clientTimeZone
                };

                var getAverageMonthlyCheckingDepositResponse = await PostRequestAsync<GetAverageMonthlyCheckingDepositBalanceApiRequest, GetAverageMonthlyCheckingDepositBalanceResponse>(request,
                    "Member/GetAverageMonthlyCheckingDepositBalance");
                return getAverageMonthlyCheckingDepositResponse.Result;
            }
            catch (ApiCoreProviderException ex)
            {
                var response = ex.Response as GetAverageMonthlyCheckingDepositBalanceResponse;
                if (response?.Result == null)
                {
                    throw;
                }

                throw;
            }
        }

        public async Task<GetContextResponse> GetContextAsync(Guid coreId, string password, string username)
        {
            var getContextRequest = new GetContextApiRequest
            {
                CoreId = coreId,
                Account = username,
                Password = password
            };

            return await PostRequestAsync<GetContextApiRequest, GetContextResponse>(getContextRequest, "Core/GetContext");
        }

        public async Task<CoreConnectionResponse> GetCoreConnectionStatusAsync(Guid coreId, string? correlationId = null, CancellationToken cancellationToken = default)
        {
            var request = new GetCoreConnectionStatusApiRequest
            {
                CoreId = coreId,
                CorrelationId = correlationId,
            };

            var jsonQueryString = JsonConvert.SerializeObject(request);

            var path = $"Core/GetCoreConnectionStatus?CoreId={coreId}";
            if (!string.IsNullOrWhiteSpace(correlationId))
            {
                path += $"?CorrelationId={correlationId}";
            }

            return await GetRequestAsync<CoreConnectionResponse>(path, cancellationToken);
        }

        public async Task<CoreInfo[]> GetSupportedCoresAsync(string? correlationId = null, CancellationToken cancellationToken = default)
        {
            var path = "Core/GetCoreProviders";
            if (!string.IsNullOrWhiteSpace(correlationId))
            {
                path += $"?CorrelationId={correlationId}";
            }
            var response = await GetRequestAsync<GetAvailableCoresResponse>(path, cancellationToken);
            return response.Cores ?? [];
        }

        public async Task<Member> GetMemberAsync(
            Guid coreId,
            GetMemberParameter member,
            string correlationId,
            int? applicationId = null,
            bool takeFromCache = false)
        {
            var request = new GetMemberApiRequest
            {
                CoreId = coreId,
                ApplicationId = applicationId,
                CorrelationId = correlationId,
                Parameters = member,
                TakeFromCache = takeFromCache
            };

            var response = await PostRequestAsync<GetMemberApiRequest, GetMemberResponse>(request, "Member/GetMember");

            return response.Member;
        }

        public async Task<TransferResponse> TransferAsync(TransferApiRequest request) => await PostRequestAsync<TransferApiRequest, TransferResponse>(request, "Loan/Transfer");

        public async Task<InsertTrackingRecordResponse> InsertTrackingRecordAsync(InsertTrackingRecordApiRequest request) => await PostRequestAsync<InsertTrackingRecordApiRequest, InsertTrackingRecordResponse>(request, "Loan/InsertTrackingRecord");

        public async Task<GetTransactionHistoryResponse> GetTransactionHistoryAsync(GetTransactionHistoryApiRequest getTransactionHistoryRequest) => await GetTransactionHistoryAsync(getTransactionHistoryRequest, string.Empty);

        public async Task<GetTransactionHistoryResponse> GetTransactionHistoryAsync(GetTransactionHistoryApiRequest getTransactionHistoryRequest, string additionalHeaders)
        {
            var headers = JsonConvert.DeserializeObject<(string, string)[]>(additionalHeaders);

            var response = await PostRequestAsync<GetTransactionHistoryApiRequest, GetTransactionHistoryResponse>(
                getTransactionHistoryRequest, "Member/GetTransactionHistory", headers ?? []);

            return response;
        }

        public async Task<GetDecisionEngineParametersResponse> GetDecisionEngineParametersAsync(Guid coreId,
            DecisionEngineSearchParameters searchParams, string correlationId, int applicationId, string additionalHeaders, string timeZone)
        {
            GetDecisionEngineParametersApiRequest request = new GetDecisionEngineParametersApiRequest
            {
                CoreId = coreId,
                ApplicationId = applicationId,
                AdditionalHeaders = additionalHeaders,
                TimeZone = timeZone,
                CorrelationId = correlationId,
                Parameters = searchParams
            };

            var response = await PostRequestAsync<GetDecisionEngineParametersApiRequest, GetDecisionEngineParametersResponse>(
                request, "DecisionEngine/GetDecisionEngineParameters");

            return response;

        }

        public async Task<ProductTypeFilterExpressionParsingResult> ParseProductTypeFilterExpressionAsync(Guid coreId, string expression, string businessRuleSlug)
        {
            ProductTypeFilterExpressionApiRequest request = new ProductTypeFilterExpressionApiRequest
            {
                CoreId = coreId,
                Expression = expression,
                BusinessRuleSlug = businessRuleSlug
            };
            var response = await PostRequestAsync<ProductTypeFilterExpressionApiRequest, ParseProductTypeFilterExpressionResponse>(
                request, "DecisionEngine/GetParseProductTypeFilterExpression");

            return response.Result ?? new ProductTypeFilterExpressionParsingResult();
        }

        #region Not Implemented

        public Task<Account> CreateShareAsync(
            Guid coreId,
            CreateShareAccountParameters parameters,
            string correlationId,
            int applicationId,
            string clientTimeZone) => throw new NotImplementedException();

        public Task<GatewayHealthCheckResponse> GatewayHealthCheckAsync(Guid coreId) => throw new NotImplementedException();

        //public async Task<OrderCreditCardResponse> OrderCreditCardAsync(OrderCreditCardRequest req) =>
        //    throw new NotImplementedException();

        #endregion

        //------Base

        private async Task<TResponse> GetRequestAsync<TResponse>(string path, CancellationToken cancellationToken = default) where TResponse : QCashResponseBase
        {
            var message = new HttpRequestMessage(HttpMethod.Get, path);
            return await SendMessageAsync<TResponse>(message, cancellationToken);
        }

        private async Task<TResponse> PostRequestAsync<TRequest, TResponse>(TRequest? request, string path, params (string key, string value)[] headers)
            where TResponse : QCashResponseBase
        {
            var message = new HttpRequestMessage(HttpMethod.Post, path)
            {
                Content = new StringContent(JsonConvert.SerializeObject(request, new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore,
                    DateFormatString = DateFormat,
                }), Encoding.UTF8, "application/json")
            };
            return await SendMessageAsync<TResponse>(message, headers: headers);
        }

        private async Task<TResponse> SendMessageAsync<TResponse>(HttpRequestMessage message, CancellationToken cancellationToken = default, params (string key, string value)[] headers) where TResponse : QCashResponseBase
        {
            //if (!string.IsNullOrWhiteSpace(AccessTokenUrl))
            //{
            //    await RefreshTokenAsync();
            //    message.Headers.Add("Authorization", $"Bearer {_token!.AccessToken}");
            //}

            foreach (var header in headers)
            {
                message.Headers.Add(header.key, header.value);
            }

            //should we also put this in a trycatch, to not allow an exception to break out here?
            var response = await HttpClient.SendAsync(message, cancellationToken);

            TResponse rsp;

            //two options: either some httpexception, or http success
            //but if response.Fault exists then that's also an Exception case

            try
            {
                rsp = await GetResponseAsync<TResponse>(response);
                if (response.IsSuccessStatusCode && rsp.Fault == null)
                {
                    return rsp;
                }
                _logger.LogDebug("Failure response from Middleware, received response: {Response}", await response.Content.ReadAsStringAsync(cancellationToken));
            }
            catch (Exception e)
            {
                throw new ApiCoreProviderException(
                    new CoreProviderFault { ErrorMessage = e.Message },
                    $"Error receiving response. (Http status code: {response.StatusCode})",
                    e);
            }

            throw new ApiCoreProviderException(rsp, $"Error receiving response. (Http status code: {response.StatusCode})");
        }

        private static async Task<TResponse> GetResponseAsync<TResponse>(HttpResponseMessage response)
            where TResponse : QCashResponseBase
        {
            var stringResponse = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            try
            {
                TResponse? tresponse = JsonConvert.DeserializeObject<TResponse>(stringResponse);
                if (tresponse != null)
                {
                    return tresponse;
                }
                else
                {
                    throw new Exception($"Deserialized response was null: {stringResponse}");
                }
            }
            catch (Exception e)
            {
                throw new Exception($"Failed to deserialize: {stringResponse}", e);
            }
        }

        //private async Task RefreshTokenAsync()
        //{
        //    if (_token is { IsExpired: false })
        //        return;

        //    var form = new Dictionary<string, string>
        //    {
        //        { "grant_type", "client_credentials" },
        //        { "client_id", ClientId ?? string.Empty },
        //        { "client_secret", ClientSecret ?? string.Empty }
        //    };

        //    var tokenResponse = await HttpClient!.PostAsync(AccessTokenUrl, new FormUrlEncodedContent(form));
        //    var jsonContent = await tokenResponse.Content.ReadAsStringAsync();
        //    _token = JsonConvert.DeserializeObject<Token>(jsonContent);
        //    _token!.DateCreated = DateTime.UtcNow;
        //}

        //internal class Token
        //{
        //    [JsonProperty("access_token")]
        //    public string? AccessToken { get; set; }

        //    [JsonProperty("token_type")]
        //    public string? TokenType { get; set; }

        //    [JsonProperty("expires_in")]
        //    public int ExpiresIn { get; set; }

        //    [JsonProperty("refresh_token")]
        //    public string? RefreshToken { get; set; }

        //    public DateTime DateCreated { get; set; }

        //    public bool IsExpired => DateTime.UtcNow.Subtract(DateCreated).TotalSeconds >= ExpiresIn + 60;
        //}

    }
}
