using Newtonsoft.Json;

namespace QCash.Service.MicroBuilt.Response;

internal class AccessTokenResponse
{
    [JsonProperty("issued_at")]
    public string? SssuedAt { get; set; }

    [JsonProperty("expires_in")]
    public string? ExpiresIn { get; set; }

    [JsonProperty("token_type")]
    public string? TokenType { get; set; }

    [JsonProperty("access_token")]
    public string? AccessToken { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }
}