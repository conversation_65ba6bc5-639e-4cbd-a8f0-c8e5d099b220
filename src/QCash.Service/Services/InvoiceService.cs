using System.Text;
using Microsoft.EntityFrameworkCore;
using QCash.Common.Enums;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.BusinessLogic.InvoiceGenerator;
using QCash.Service.BusinessLogic.Validators.InvoicePlan;
using QCash.Service.Models;
using QCash.Service.Services.Interfaces;
using LoanApplicationSettingType = QCash.Service.Services.LoanApplication.Enums.LoanApplicationSettingType;

namespace QCash.Service.Services;

public class InvoiceService(
    QCashContext qCashContext,
    IInvoicePlanItemValidator invoicePlanItemValidator,
    IInvoiceGeneratorRunner invoiceGeneratorRunner)
    : IInvoiceService
{
    /// <summary>
    /// Gets the InvoicePlan for specific Financial Institution.
    /// </summary>
    /// <param name="fiSlug">The FI slug.</param>
    public async Task<InvoicePlan?> GetInvoicePlanForFiAsync(string fiSlug)
    {
        var fiId = await qCashContext.FinancialInstitutions.IgnoreQueryFilters().FirstOrDefaultAsync(o => o.Slug == fiSlug);
        if (fiId == null)
        {
            return null;
        }

        //called from GenerateInvoiceCsvForAllFisAsync
        var invoicePlan = await qCashContext.InvoicePlans.IgnoreQueryFilters()
            .FirstOrDefaultAsync(o => o.FinancialInstitutionId == fiId.Id);

        return invoicePlan;
    }

    /// <summary>
    /// Gets the Invoice Plan Item.
    /// </summary>
    /// <param name="invoicePlanId">Invoice Plan identifier.</param>
    /// <param name="invoicePlanItemTypeId">Invoice Plan Item Type identifier.</param>
    public async Task<IEnumerable<InvoicePlanItem>> GetInvoicePlanItemsAsync(Guid invoicePlanId, Guid invoicePlanItemTypeId)
    {
        var ipItems = await qCashContext.InvoicePlanItems
            .Include(o => o.GroupByLoanStatus).ThenInclude(o => o!.LoanStatusType)
            .Include(o => o.GroupByProduct).ThenInclude(o => o!.Product)
            .Where(p => p.InvoicePlanId == invoicePlanId && p.InvoicePlanItemTypeId == invoicePlanItemTypeId).ToListAsync();
        return ipItems;
    }

    /// <summary>
    /// Gets the paged list of <see cref="InvoicePlanExcludedAccount"/>'s.
    /// </summary>
    /// <param name="invoicePlanId">The invoice plan identifier.</param>
    /// <param name="pageIndex">Page index.</param>
    /// <param name="pageSize">Page size.</param>
    /// <param name="orderBy">The order by.</param>
    /// <param name="search">The search.</param>
    /// <param name="isDescendingSortOrder">The is descending sort order flag.</param>
    public async Task<List<InvoicePlanExcludedAccount>> GetInvoicePlanExcludedAccountsPagedAsync(Guid invoicePlanId, int pageIndex, int pageSize, string orderBy, string search, bool isDescendingSortOrder)
    {
        var query = qCashContext.InvoicePlanExcludedAccounts.Where(p => p.InvoicePlanId == invoicePlanId);

        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(s => s.AccountId.ToLower().Contains(search.ToLower()) || (s.Description != null && s.Description.ToLower().Contains(search.ToLower())));
        }

        switch (orderBy)
        {
            case nameof(InvoicePlanExcludedAccount.AccountId):
                query = isDescendingSortOrder
                    ? query.OrderByDescending(l => l.AccountId)
                    : query.OrderBy(l => l.AccountId);
                break;

            case nameof(InvoicePlanExcludedAccount.Description):
                query = isDescendingSortOrder
                    ? query.OrderByDescending(l => l.Description)
                    : query.OrderBy(l => l.Description);
                break;

            default:
                query = query.OrderByDescending(l => l.DateUpdatedUtc);
                break;
        }

        var result = query.Skip(pageSize * (pageIndex - 1)).Take(pageSize);

        return await result.ToListAsync();
    }

    /// <summary>
    /// Gets the individual invoice plan item.
    /// </summary>
    /// <param name="invoicePlanId">The invoice plan identifier.</param>
    /// <param name="invoicePlanItemTypeId">The invoice plan item type identifier.</param>
    public async Task<InvoicePlanItem?> GetIndividualInvoicePlanItemAsync(Guid invoicePlanId, Guid invoicePlanItemTypeId) =>
        await qCashContext.InvoicePlanItems.FirstOrDefaultAsync(p => p.InvoicePlanId == invoicePlanId && p.InvoicePlanItemTypeId == invoicePlanItemTypeId);

    /// <summary>
    /// Gets the <see cref="InvoicePlanItem"/> model.
    /// </summary>
    /// <param name="id">The identifier.</param>
    public async Task<InvoicePlanItem?> GetInvoicePlanItemAsync(Guid id)
    {
        var ipItem = await qCashContext.InvoicePlanItems.Include(o => o.InvoicePlanItemType).FirstOrDefaultAsync(p => p.Id.Equals(id));
        return ipItem;
    }

    /// <summary>
    /// Gets the list of <see cref="InvoicePlanItem"/> individual items.
    /// </summary>
    /// <param name="invoicePlanId">The invoice plan identifier.</param>
    public async Task<IEnumerable<InvoicePlanItem>> GetIndividualInvoicePlanItemsAsync(Guid invoicePlanId)
    {
        //include invoicePlanItemType
        var invoicePlanItemTypeIds = await qCashContext.InvoicePlanItemTypes
            .Where(p => p.RelationshipType != nameof(InvoicePlanItemRelationshipType.ItemGroup) && !p.IsDeleted)
            .Select(p => p.Id).ToArrayAsync();

        return await qCashContext.InvoicePlanItems
            .Include(o => o.InvoicePlanItemType)
            .Where(p => invoicePlanItemTypeIds.Contains(p.InvoicePlanItemTypeId) && p.InvoicePlanId == invoicePlanId).ToListAsync();
    }

    /// <summary>
    /// Gets unused Products for GroupByProduct.
    /// </summary>
    /// <param name="invoicePlanId">The invoice plan item identifier.</param>
    /// <param name="includeInactive">The include inactive flag.</param>
    public async Task<IEnumerable<Data.Models.Product>> GetUnusedProductsForGroupByProductAsync(Guid invoicePlanId, bool includeInactive = false)
    {
        var allProducts = qCashContext.Products
            .Include(o => o.LoanCategory)
            .Include(o => o.InterfaceDefaultTexts);

        var products = includeInactive ? await allProducts.ToListAsync() : await allProducts.Where(o => o.IsActive).ToListAsync();
        var groupByProducts = await qCashContext.GroupByProducts
            .Where(p => p.IdNavigation.InvoicePlanId == invoicePlanId).ToListAsync();

        return products.Where(p => groupByProducts.All(d => d.ProductId != p.Id));
    }

    /// <summary>
    /// Gets the GroupByProduct.
    /// </summary>
    /// <param name="id">The identifier.</param>
    public async Task<GroupByProduct?> GetGroupByProductAsync(Guid id) =>
        await qCashContext.GroupByProducts.Include(o => o.Product).Include(o => o.ProductLoanStatusGroups).FirstOrDefaultAsync(p => p.Id == id);

    /// <summary>
    /// Gets Group by Product for View Model.
    /// </summary>
    /// <param name="id">The identifier.</param>
    public async Task<GroupByProduct?> GetGroupByProductForVmAsync(Guid id)
    {
        var groupByProduct = await qCashContext.GroupByProducts
            .Include(o => o.Product)
            .Include(o => o.ProductLoanStatusGroups).ThenInclude(o => o.LoanStatusType)
            .Include(o => o.IdNavigation).ThenInclude(o => o.PricingScenarios).ThenInclude(o => o.PricingScenarioType)
            .Include(o => o.IdNavigation).ThenInclude(o => o.PricingScenarios).ThenInclude(o => o.PricingScenarioTierdItems)
            .Include(o => o.IdNavigation).ThenInclude(o => o.InvoicePlanItemType)
            .Include(o => o.IdNavigation).ThenInclude(o => o.PricingPlanType)
            .AsNoTracking()
            .FirstOrDefaultAsync(p => p.Id == id);

        //This is OK because we are not tracking this entity.
        if (groupByProduct != null && groupByProduct.IdNavigation.PricingScenarios.Any())
        {
            groupByProduct.IdNavigation.PricingScenarios = groupByProduct.IdNavigation.PricingScenarios.Where(p => p.PricingPlanTypeId == groupByProduct.IdNavigation.PricingPlanTypeId).ToList();
        }

        return groupByProduct;
    }

    /// <summary>
    /// Gets Group by Product.
    /// </summary>
    /// <param name="invoicePlanId">The invoice plan identifier.</param>
    /// <param name="productId">The product identifier.</param>
    public async Task<GroupByProduct?> GetGroupByProductAsync(Guid invoicePlanId, Guid productId) =>
        await qCashContext.GroupByProducts.Include(o => o.Product).FirstOrDefaultAsync(p => p.IdNavigation.InvoicePlanId == invoicePlanId && p.ProductId == productId);

    /// <summary>
    /// Gets the <see cref="ProductLoanStatusGroup"/> model.
    /// </summary>
    /// <param name="groupByProductId">The <see cref="GroupByProduct"/> identifier.</param>
    /// <param name="loanStatusTypeId">The <see cref="LoanStatusType"/> identifier.</param>
    public async Task<ProductLoanStatusGroup?> GetProductLoanStatusGroupAsync(Guid groupByProductId, Guid loanStatusTypeId) =>
        await qCashContext.ProductLoanStatusGroups.FirstOrDefaultAsync(p =>
            p.GroupByProductId == groupByProductId && p.LoanStatusTypeId == loanStatusTypeId);

    /// <summary>
    /// Gets all unused Loan Status Types for Product Loan Status Group.
    /// </summary>
    /// <param name="groupByProductId">The group by product identifier.</param>
    public async Task<IEnumerable<LoanStatusType>> GetUnusedLoanStatusTypesForProductLoanStatusGroupAsync(Guid groupByProductId)
    {
        var groupByProduct = await GetGroupByProductAsync(groupByProductId);

        var productDependency = groupByProduct?.ProductId == Guid.Empty ? LoanStatusTypeProductDependency.PreProduct : LoanStatusTypeProductDependency.Product;

        var productStatuses = await qCashContext.LoanStatusTypes
            .Where(p => p.ProductDependency == productDependency.ToString() && !p.IsDeleted)
            .ToListAsync();

        if (groupByProduct == null)
        {
            return productStatuses;
        }

        return productStatuses.Where(p => groupByProduct.ProductLoanStatusGroups.All(d => d.LoanStatusTypeId != p.Id));
    }

    /// <summary>
    /// Gets the <see cref="PricingScenario"/> model.
    /// </summary>
    /// <param name="id">The identifier.</param>
    public async Task<PricingScenario?> GetPricingScenarioAsync(Guid id) =>
        await qCashContext.PricingScenarios
            .Include(o => o.PricingScenarioType)
            .Include(o => o.PricingScenarioTierdItems)
            .Include(o => o.PricingPlanType)
            .FirstOrDefaultAsync(p => p.Id == id);

    /// <summary>
    /// Gets the list of <see cref="PricingScenario"/> models.
    /// </summary>
    /// <param name="invoicePlanItemId">The invoice plan item identifer.</param>
    public async Task<IEnumerable<PricingScenario>> GetPricingScenariosAsync(Guid invoicePlanItemId) =>
        await qCashContext.PricingScenarios
            .Include(o => o.PricingScenarioType)
            .Include(o => o.PricingScenarioTierdItems)
            .Include(o => o.PricingPlanType)
            .Where(p => p.InvoicePlanItemId == invoicePlanItemId).ToListAsync();

    /// <summary>
    /// Inserts <see cref="PricingScenarioTierdItem"/>.
    /// </summary>
    /// <param name="entity">The entity.</param>
    public async Task<PricingScenarioTierdItem> InsertPricingScenarioTierdItemAsync(PricingScenarioTierdItem entity)
    {
        await ValidateLoanFeeAsync(entity);
        qCashContext.PricingScenarioTierdItems.Add(entity);
        await qCashContext.SaveChangesAsync();
        return entity;
    }

    /// <summary>
    /// Gets the <see cref="PricingScenarioTierdItem"/> model.
    /// </summary>
    /// <param name="id">The identifier.</param>
    public async Task<PricingScenarioTierdItem?> GetPricingScenarioTierdItemAsync(Guid id) =>
        await qCashContext.PricingScenarioTierdItems
            .Include(o => o.PricingScenario).ThenInclude(o => o.InvoicePlanItem)
            .FirstOrDefaultAsync(p => p.Id == id);

    /// <summary>
    /// Gets the <see cref="GroupByLoanStatus"/> model.
    /// </summary>
    /// <param name="id">The identifier.</param>
    public async Task<GroupByLoanStatus?> GetGroupByLoanStatusAsync(Guid id) =>
        await qCashContext.GroupByLoanStatuses
            .Include(o => o.LoanStatusType)
            .Include(o => o.LoanStatusProductGroups)
            .FirstOrDefaultAsync(p => p.Id == id);

    /// <summary>
    /// Gets <see cref="GroupByLoanStatus"/> for View Model.
    /// </summary>
    /// <param name="id">The identifier.</param>
    public async Task<GroupByLoanStatus?> GetGroupByLoanStatusForVmAsync(Guid id)
    {
        var groupByLoanStatus = await qCashContext.GroupByLoanStatuses
            .Include(o => o.LoanStatusProductGroups).ThenInclude(o => o.Product)
            .Include(o => o.IdNavigation).ThenInclude(o => o.PricingScenarios).ThenInclude(o => o.PricingScenarioType)
            .Include(o => o.IdNavigation).ThenInclude(o => o.PricingScenarios).ThenInclude(o => o.PricingScenarioTierdItems)
            .Include(o => o.IdNavigation).ThenInclude(o => o.InvoicePlanItemType)
            .Include(o => o.IdNavigation).ThenInclude(o => o.PricingPlanType)
            .Include(o => o.LoanStatusType)
            .AsNoTracking()
            .FirstOrDefaultAsync(p => p.Id == id);

        //This is OK because we are not tracking this entity.
        if (groupByLoanStatus != null && groupByLoanStatus.IdNavigation.PricingScenarios.Any())
        {
            groupByLoanStatus.IdNavigation.PricingScenarios = groupByLoanStatus.IdNavigation.PricingScenarios.Where(p => p.PricingPlanTypeId == groupByLoanStatus.IdNavigation.PricingPlanTypeId).ToList();
        }

        return groupByLoanStatus;
    }

    /// <summary>
    /// Gets unused Loan Status Types for GroupByLoanStatus.
    /// </summary>
    /// <param name="invoicePlanId">The invoice plan identifier.</param>
    public async Task<IEnumerable<LoanStatusType>> GetUnusedLoanStatusForGroupByLoanStatusTypeAsync(Guid invoicePlanId)
    {
        var loanStatusTypes = await qCashContext.LoanStatusTypes
            .Where(p => p.ProductDependency != nameof(LoanStatusTypeProductDependency.None) && !p.IsDeleted)
            .ToListAsync();

        var groupByLoanStatuses = await qCashContext.GroupByLoanStatuses
            .Where(p => p.IdNavigation.InvoicePlanId == invoicePlanId)
            .ToListAsync();

        return loanStatusTypes.Where(p => groupByLoanStatuses.All(d => d.LoanStatusTypeId != p.Id));
    }

    /// <summary>
    /// Gets the unused product for LoanStatusProduct group.
    /// </summary>
    /// <param name="groupByLoanStatusId">The group by loan status identifier.</param>
    public async Task<IEnumerable<Data.Models.Product>> GetUnusedProductsForLoanStatusProductGroupAsync(Guid groupByLoanStatusId)
    {
        var groupByLoanStatus = await GetGroupByLoanStatusAsync(groupByLoanStatusId);

        var groupProductDependency = groupByLoanStatus?.LoanStatusType.ProductDependency ?? "None";
        var productDependency = (LoanStatusTypeProductDependency)Enum.Parse(typeof(LoanStatusTypeProductDependency), groupProductDependency, false);

        var products = await qCashContext.Products
            .Include(o => o.LoanCategory)
            .Include(o => o.InterfaceDefaultTexts)
            .ThenInclude(o => o.InterfaceOverrideTexts)
            .ToListAsync();

        if (productDependency == LoanStatusTypeProductDependency.PreProduct)
        {
            products = products.Where(p => p.Id == Guid.Empty).ToList();
        }
        else if (productDependency == LoanStatusTypeProductDependency.Product)
        {
            products = products.Where(p => p.Id != Guid.Empty).ToList();
        }
        else
        {
            products = new List<Data.Models.Product>();
        }

        if (groupByLoanStatus == null)
        {
            return products;
        }

        return products.Where(p => groupByLoanStatus.LoanStatusProductGroups.All(d => d.ProductId != p.Id));
    }

    /// <summary>
    /// Gets <see cref="GroupByLoanStatus"/> model.
    /// </summary>
    /// <param name="invoicePlanId">The invoice plan identifier.</param>
    /// <param name="loanStatusTypeId">The loan status type identifier.</param>
    public async Task<GroupByLoanStatus?> GetGroupByLoanStatusAsync(Guid invoicePlanId, Guid loanStatusTypeId) =>
        await qCashContext.GroupByLoanStatuses.FirstOrDefaultAsync(p =>
            p.IdNavigation.InvoicePlanId == invoicePlanId && p.LoanStatusTypeId == loanStatusTypeId);

    /// <summary>
    /// Gets the <see cref="LoanStatusProductGroup"/> model.
    /// </summary>
    /// <param name="groupByLoanStatusId">The group by loan status identifier.</param>
    /// <param name="productId">The product identifier.</param>
    public async Task<LoanStatusProductGroup?> GetLoanStatusProductGroupAsync(Guid groupByLoanStatusId, Guid productId) =>
        await qCashContext.LoanStatusProductGroups.FirstOrDefaultAsync(p => p.GroupByLoanStatusId == groupByLoanStatusId && p.ProductId == productId);

    /// <summary>
    /// Gets unused <see cref="LoanStatusType"/> list for MLA group.
    /// </summary>
    /// <param name="invoicePlanItemId">The invoice plan item identifier.</param>
    public async Task<IEnumerable<LoanStatusType>> GetUnusedMlaForGroupByMLAStatusAsync(Guid invoicePlanItemId)
    {
        var groupByMlaStatuses = await qCashContext.GroupByMlaStatuses.Where(p => p.InvoicePlanItemId.Equals(invoicePlanItemId))
            .ToListAsync();

        var mlaStatuses = await qCashContext.LoanStatusTypes
            .Where(p => p.Abrv.StartsWith("mla_") && !p.IsDeleted)
            .ToListAsync();

        return mlaStatuses.Where(p => groupByMlaStatuses.All(d => d.LoanStatusTypeId != p.Id));
    }

    /// <summary>
    /// Gets the <see cref="GroupByMlaStatus"/> model.
    /// </summary>
    /// <param name="invoicePlanItemId">The invoice plan item identifier.</param>
    /// <param name="loanStatusTypeId">The loan status type identifier.</param>
    public async Task<GroupByMlaStatus?> GetGroupByMlaStatusAsync(Guid invoicePlanItemId, Guid loanStatusTypeId) =>
        await qCashContext.GroupByMlaStatuses.FirstOrDefaultAsync(p =>
            p.InvoicePlanItemId.Equals(invoicePlanItemId) && p.LoanStatusTypeId == loanStatusTypeId);

    /// <summary>
    /// Gets the unused individual invoice item types.
    /// </summary>
    /// <param name="invoicePlanId">The invoice plan identifier.</param>
    public async Task<IEnumerable<InvoicePlanItemType>> GetUnusedIndividualInvoicePlanItemTypesAsync(Guid invoicePlanId)
    {
        var invoicePlanItemTypes = await qCashContext.InvoicePlanItemTypes
            .Where(p => p.RelationshipType != nameof(InvoicePlanItemRelationshipType.ItemGroup)
                        && !p.IsDeleted).ToListAsync();
        var individualInvoiceItems = await GetIndividualInvoicePlanItemsAsync(invoicePlanId);
        return invoicePlanItemTypes.Where(p => individualInvoiceItems.All(d => d.InvoicePlanItemTypeId != p.Id));
    }

    /// <summary>
    /// Gets the <see cref="InvoicePlanItem"/> for View Model.
    /// </summary>
    /// <param name="id">The identifier.</param>
    public async Task<InvoicePlanItem?> GetIndividualInvoicePlanItemForVmAsync(Guid id)
    {
        var individualInvoicePlanItem = await qCashContext.InvoicePlanItems
            .AsNoTracking()
            .Include(p => p.PricingScenarios).ThenInclude(p => p.PricingScenarioType)
            .Include(p => p.PricingScenarios).ThenInclude(p => p.PricingScenarioTierdItems)
            .Include(p => p.GroupByMlaStatuses).ThenInclude(p => p.LoanStatusType)
            .Include(p => p.InvoicePlanItemType)
            .Include(p => p.PricingPlanType)
            .FirstOrDefaultAsync(p => p.Id == id);

        //This is OK because we are not tracking this entity.
        if (individualInvoicePlanItem != null && individualInvoicePlanItem.PricingScenarios.Any())
        {
            individualInvoicePlanItem.PricingScenarios = individualInvoicePlanItem.PricingScenarios.Where(p => p.PricingPlanTypeId == individualInvoicePlanItem.PricingPlanTypeId).ToList();
        }

        return individualInvoicePlanItem;
    }

    /// <summary>
    /// Generates the Invoice.
    /// </summary>
    /// <param name="dateFromUtc">The UTC date from.</param>
    /// <param name="dateToUtc">The UTC date to.</param>
    public async Task<Invoice> GenerateInvoiceAsync(DateTime dateFromUtc, DateTime dateToUtc)
    {
        var invoicePlan = await qCashContext.InvoicePlans
            .AsNoTracking()
            .Include(o => o.InvoicePlanItems).ThenInclude(o => o.PricingScenarios).ThenInclude(o => o.PricingScenarioTierdItems)
            .Include(o => o.InvoicePlanItems).ThenInclude(o => o.GroupByProduct).ThenInclude(o => o!.ProductLoanStatusGroups)
            .Include(o => o.InvoicePlanItems).ThenInclude(o => o.GroupByLoanStatus).ThenInclude(o => o!.LoanStatusProductGroups)
            .Include(o => o.InvoicePlanItems).ThenInclude(o => o.GroupByMlaStatuses).FirstOrDefaultAsync();

        if (invoicePlan == null)
        {
            throw new Exception("No Invoice Plan to generate Invoice from.");
        }

        List<InvoicePlanItem> validItems = new List<InvoicePlanItem>();
        foreach (var ipItem in invoicePlan.InvoicePlanItems)
        {
            var valid = await invoicePlanItemValidator.ValidateAsync(ipItem);
            if (valid.IsValid)
            {
                validItems.Add(ipItem);
            }
        }

        invoicePlan.InvoicePlanItems = validItems;

        //var invoicePlanItemTypeLookup = await qCashContext.InvoicePlanItemTypes.AsNoTracking().Where(o => !o.IsDeleted).ToListAsync();
        //var ipitItbk = invoicePlanItemTypeLookup.Single(o => o.Abrv == "ITBK");
        var ipitItmls = await qCashContext.InvoicePlanItemTypes.AsNoTracking().SingleAsync(o => o.Abrv == "ITMLS");

        //bankruptcy - removed
        //var bankruptcyStatusTypeIds = new List<Guid>();
        //if (invoicePlan.InvoicePlanItems.Any(p => p.InvoicePlanItemTypeId == InvoicePlanItemTypeLookup.ITBK.Id))
        //{
        //    bankruptcyStatusTypeIds.AddRange(invoicePlan.InvoicePlanItems
        //        .FirstOrDefault(p => p.InvoicePlanItemTypeId == InvoicePlanItemTypeLookup.ITBK.Id)
        //        .GroupByBankruptcyStatus.Select(p => p.BankruptcyStatusTypeId));
        //}

        var mlaCheckStatusTypeIds = new List<Guid>();
        if (invoicePlan.InvoicePlanItems.Any(p => p.InvoicePlanItemTypeId == ipitItmls.Id))
        {
            var addMls = invoicePlan.InvoicePlanItems
                .FirstOrDefault(p => p.InvoicePlanItemTypeId == ipitItmls.Id)
                ?.GroupByMlaStatuses.Select(p => p.LoanStatusTypeId);
            if (addMls != null)
            {
                mlaCheckStatusTypeIds.AddRange(addMls);
            }
        }

        var invoiceDataSet = await GetInvoiceDataSetAsync(dateFromUtc, dateToUtc, mlaCheckStatusTypeIds);
        var invoice = await invoiceGeneratorRunner.GenerateInvoiceAsync(invoiceDataSet.Item1, invoiceDataSet.Item2, invoicePlan);
        invoice.MonthlyActivityForUtc = dateFromUtc;

        return invoice;
    }

    /// <summary>
    /// Gets the Invoice Data Set.
    /// </summary>
    /// <param name="dateFromUtc">The date from UTC.</param>
    /// <param name="dateToUtc">The date to UTC.</param>
    /// <param name="mlaCheckStatusTypeIds">The list of MLA Check Status Type ids to include.</param>
    private async Task<(List<LoanApplicationInvoiceItem>, List<LoanApplicationSettingType>)> GetInvoiceDataSetAsync(DateTime dateFromUtc, DateTime dateToUtc, IEnumerable<Guid> mlaCheckStatusTypeIds)
    {
        var laInvoiceItems = await PopulateLaInvoiceItemsAsync(dateFromUtc, dateToUtc, mlaCheckStatusTypeIds);

        var invoiceItemsSettingsEnabled = await PopulateInvoiceItemsSettingsAsync(dateFromUtc, dateToUtc);

        return (laInvoiceItems, invoiceItemsSettingsEnabled);
    }

    private async Task<List<LoanApplicationInvoiceItem>> PopulateLaInvoiceItemsAsync(DateTime dateFromUtc, DateTime dateToUtc, IEnumerable<Guid> mlaCheckStatusTypeIds)
    {
        //write query and project to new LoanApplicationInvoiceItems rather than use the dataset method

#pragma warning disable CS8073 // The result of the expression is always the same since a value of this type is never equal to 'null'
        var qq = from la in qCashContext.LoanApplications
                 join lasso in qCashContext.LoanApplicationSsos on new { id = la.Id, istest = false } equals new { id = lasso.Id, istest = lasso.IsTest } into la_lasso_grouping
                 from lasso in la_lasso_grouping.DefaultIfEmpty()
                 join lst in qCashContext.LoanStatusTypes on la.LoanStatusId equals lst.Id into la_lst_grouping
                 from lst in la_lst_grouping.DefaultIfEmpty()
                 join laem in qCashContext.LoanApplicationNotifications on new { id = la.Id, sent = true, channel = nameof(LoanApplicationNotificationChanel.Email), automatic = false } equals new { id = laem.LoanApplicationId, sent = laem.Sent, channel = laem.NotificationChanel, automatic = laem.IsSentAutomatically } into la_laem_grouping
                 from laem in la_laem_grouping.DefaultIfEmpty()
                 join laemaut in qCashContext.LoanApplicationNotifications on new { id = la.Id, sent = true, channel = nameof(LoanApplicationNotificationChanel.Email), automatic = true } equals new { id = laemaut.LoanApplicationId, sent = laemaut.Sent, channel = laemaut.NotificationChanel, automatic = laemaut.IsSentAutomatically } into la_laemaut_grouping
                 from laemaut in la_laemaut_grouping.DefaultIfEmpty()
                 join lasms in qCashContext.LoanApplicationNotifications on new { id = la.Id, sent = true, channel = nameof(LoanApplicationNotificationChanel.SMS) } equals new { id = lasms.LoanApplicationId, sent = lasms.Sent, channel = lasms.NotificationChanel } into la_lasms_grouping
                 from lasms in la_lasms_grouping.DefaultIfEmpty()
                 join mlaCSL in qCashContext.MlaCheckStatusLogs.Where(o => mlaCheckStatusTypeIds.Contains(o.MlaCheckStatusTypeId)) on la.FinancialInstitutionMemberId equals mlaCSL.FinancialInstitutionMemberId into la_mlaCSL_grouping
                 from mlaCSL in la_mlaCSL_grouping.DefaultIfEmpty()
                 join ipea in qCashContext.InvoicePlanExcludedAccounts on la.AccountId equals ipea.AccountId into la_ipea_grouping
                 from ipea in la_ipea_grouping.DefaultIfEmpty()
                 where
                     la.DateCreatedUtc > dateFromUtc
                     && la.DateCreatedUtc < dateToUtc
                     && (lst.ProductDependency == "PreProduct" || lst.ProductDependency == "Product")
                     && ipea.Id == null
                 group new { la.Id, la.LoanStatusId, la.SelectedProductId, laemID = laem.Id, laemautID = laemaut.Id, laSMSID = lasms.Id, mlaCSLID = mlaCSL.Id } by new { la.Id, la.LoanStatusId, la.SelectedProductId } into grouped
                 select new LoanApplicationInvoiceItem
                 {
                     LoanApplicationId = grouped.Key.Id,
                     LoanStatusId = grouped.Key.LoanStatusId,
                     SelectedProductId = grouped.Key.SelectedProductId,
                     LoanApplicationEmailCount = grouped.Select(x => x.laemID).Distinct().Count(),
                     AutomaticDisclosureEmailCount = grouped.Select(x => x.laemautID).Distinct().Count(),
                     LoanApplicationSmsCount = grouped.Select(x => x.laSMSID).Distinct().Count(),
                     MLACheckCount = grouped.Select(x => x.mlaCSLID).Distinct().Count(),
                 };
#pragma warning restore CS8073 // The result of the expression is always the same since a value of this type is never equal to 'null'

        //produce set of LoanApplicationInvoiceItem
        return await qq.ToListAsync();

        //Was SQL:
        //var bankruptcyINClause = cmd.Parameters.AddINClauseParameter(SqlDbType.UniqueIdentifier, "@BKType", bankruptcyStatusTypeIds.ToArray());
        //var mlaINClause = cmd.Parameters.AddINClauseParameter(SqlDbType.UniqueIdentifier, "@MLAType", mlaCheckStatusTypeIds.ToArray());
        //var query = $@"SELECT DISTINCT
        //             la.Id AS LoanApplicationId,
        //             la.LoanStatusId AS LoanStatusId,
        //             la.SelectedProductId AS SelectedProductId,
        //             COUNT(DISTINCT laem.Id) AS LoanApplicationEmailCount,
        //             COUNT(DISTINCT laemaut.Id) AS AutomaticDisclosureEmail,
        //             COUNT(DISTINCT lasms.Id) AS LoanApplicationSMSCount,
        //             COUNT(DISTINCT bkc.Id) AS BankruptcyCheckCount,
        //             COUNT(DISTINCT mlaCSL.Id) AS MLACheckCount
        //            FROM LoanApplication la
        //            LEFT JOIN LoanApplicationSSO lasso ON lasso.Id = la.Id
        //            LEFT JOIN LoanStatusType lst ON lst.Id = la.LoanStatusId
        //            LEFT JOIN LoanApplicationNotification laem ON laem.LoanApplicationId = la.Id AND laem.Sent = 1 AND laem.NotificationChanel = 'Email' AND laem.IsSentAutomatically = 0
        //            LEFT JOIN LoanApplicationNotification laemaut ON laemaut.LoanApplicationId = la.Id AND laemaut.Sent = 1 AND laemaut.NotificationChanel = 'Email' AND Laemaut.IsSentAutomatically = 1
        //            LEFT JOIN LoanApplicationNotification lasms ON lasms.LoanApplicationId = la.Id AND lasms.Sent = 1 AND lasms.NotificationChanel = 'SMS'
        //            LEFT JOIN BankruptcyCheck bkc ON bkc.Id = la.FinancialInstitutionMemberId AND bkc.BankruptcyStatusTypeId IN ({bankruptcyINClause})
        //            LEFT JOIN MLACheckStatusLog mlaCSL ON mlaCSL.FinancialInstitutionMemberId = la.FinancialInstitutionMemberId AND mlaCSL.MLACheckStatusTypeId IN ({mlaINClause})
        //            LEFT JOIN invoice.InvoicePlanExcludedAccount ipea ON la.AccountId = ipea.AccountId
        //            WHERE la.DateCreated BETWEEN @DateFrom AND @DateTo
        //                AND lst.ProductDependency IN ('PreProduct', 'Product')
        //                AND ipea.Id IS NULL
        //                AND lasso.IsTest = 0
        //            GROUP BY la.Id, la.LoanStatusId, la.SelectedProductId";
    }

    private async Task<List<LoanApplicationSettingType>> PopulateInvoiceItemsSettingsAsync(DateTime dateFromUtc, DateTime dateToUtc)
    {
        //Was SQL:
        //var query = $@"SELECT
        //             (CASE WHEN SUM(CASE WHEN SettingKey = 'FeeRefundEnabled' AND SettingValue = 1 THEN 1 END) > 0 THEN 1 ELSE 0 END) AS FeeRefundEnabled,
        //             (CASE WHEN SUM(CASE WHEN SettingKey = 'AwarenessPage' AND SettingValue = 1 THEN 1 END) > 0 THEN 1 ELSE 0 END) AS AwarenessPage,
        //             (CASE WHEN SUM(CASE WHEN SettingKey = 'DebtCounselorConfirmationDialog' AND SettingValue = 1 THEN 1 END) > 0 THEN 1 ELSE 0 END) AS DebtCounselorConfirmationDialog,
        //             (CASE WHEN SUM(CASE WHEN SettingKey = 'FraudControl' AND SettingValue = 1 THEN 1 END) > 0 THEN 1 ELSE 0 END) AS FraudControl
        //            FROM LoanApplicationSettingsLog
        //            WHERE DateCreated BETWEEN @DateFrom AND @DateTo";

        //Get all LoanApplicationSettingsLogs in date range
        //that have settingvalue = 1 and settingkey in ('FeeRefundEnabled', 'AwarenessPage','DebtCounselorConfirmationDialog','FraudControl')
        //if count for any of those > 0, consider it enabled

        var checkFors = new Dictionary<string, LoanApplicationSettingType>
        {
            { nameof(LoanApplicationSettingType.FeeRefundEnabled), LoanApplicationSettingType.FeeRefundEnabled },
            { nameof(LoanApplicationSettingType.AwarenessPage), LoanApplicationSettingType.AwarenessPage },
            { nameof(LoanApplicationSettingType.DebtCounselorConfirmationDialog), LoanApplicationSettingType.DebtCounselorConfirmationDialog },
            { nameof(LoanApplicationSettingType.FraudControl), LoanApplicationSettingType.FraudControl },
        };

        var lasLogs = await qCashContext.LoanApplicationSettingsLogs
            .Where(o => o.SettingValue && checkFors.Keys.Contains(o.SettingKey) && o.DateCreatedUtc >= dateFromUtc && o.DateCreatedUtc <= dateToUtc)
            .ToListAsync();

        var foundLass = new List<LoanApplicationSettingType>();

        foreach (var check in checkFors)
        {
            if (lasLogs.Any(o => o.SettingKey == check.Key))
            {
                foundLass.Add(check.Value);
            }
        }

        return foundLass;
    }

    /// <summary>
    /// Creates the Invoice CSV file.
    /// </summary>
    /// <param name="invoices"></param>
    public byte[] CreateInvoiceCsv(IEnumerable<InvoiceCsvDto> invoices)
    {
        //As designed in QC6
        var zone = TimeZoneInfo.FindSystemTimeZoneById("Pacific Standard Time");
        var pacificNow = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, zone);

        var csv = new StringBuilder();
        AppendInvoiceHeaders(csv);

        foreach (var dto in invoices)
        {
            // Last day of invoiced period.
            var glPostingDate = new DateTime(
                dto.Invoice.MonthlyActivityForUtc.Year,
                dto.Invoice.MonthlyActivityForUtc.Month,
                DateTime.DaysInMonth(dto.Invoice.MonthlyActivityForUtc.Year, dto.Invoice.MonthlyActivityForUtc.Month));

            //Add 30 days to PST for DateDue in csv
            var dateDue = pacificNow.AddDays(30);

            var firstItem = dto.Invoice.InvoiceItems.OrderBy(x => x.SeqId).First();
            var customerId = string.IsNullOrEmpty(dto.Invoice.Reseller) ? dto.Invoice.CustomerId : dto.Invoice.Reseller;
            var transactionType = dto.Invoice.TransactionType;
            var shipTo = string.IsNullOrEmpty(dto.Invoice.Reseller) ? "" : dto.Invoice.CustomerId;

            var isFirstItemProduct = firstItem.InvoicePlanItemTypeId == dto.InvoicePlanItemTypeId;

            var firstQuantity = dto.OverrideLoanValues && isFirstItemProduct ? "1" : $"{firstItem.Quantity:0}";
            var firstUnitPrice = dto.OverrideLoanValues && isFirstItemProduct
                ? (dto.MinimumFee ?? 0).ToString("0.##")
                : firstItem.UnitPrice.ToString("0.##");

            AppendInvoiceItem(
                csv,
                InvoiceHardcodedValues.DoNotImport,
                transactionType,
                pacificNow.ToString("MM/dd/yy"),
                glPostingDate.ToString("MM/dd/yy"),
                $"{glPostingDate:MM/dd/yy}-{dto.FIIdentifier:D3}",
                customerId,
                InvoiceHardcodedValues.TermName,
                dateDue.ToString("MM/dd/yy"),
                InvoiceHardcodedValues.State,
                firstItem.SeqId.ToString(),
                firstItem.ItemId,
                firstItem.Description,
                firstQuantity,
                InvoiceHardcodedValues.Unit,
                firstUnitPrice,
                InvoiceHardcodedValues.DepartmentId,
                dto.Invoice.CustomerId,
                shipTo);

            //Remove first item from list
            dto.Invoice.InvoiceItems.Remove(firstItem);

            //Append for the rest of the items in list
            foreach (var iDetail in dto.Invoice.InvoiceItems.OrderBy(x => x.SeqId))
            {
                var isProduct = iDetail.InvoicePlanItemTypeId == dto.InvoicePlanItemTypeId;

                AppendInvoiceItem(csv,
                    "", "", "", "", "", "", "", "", "",
                    iDetail.SeqId.ToString(),
                    iDetail.ItemId,
                    iDetail.Description,
                    dto.OverrideLoanValues && isProduct ? "0" : $"{iDetail.Quantity:0}",
                    "Each",
                    dto.OverrideLoanValues && isProduct ? "0" : iDetail.UnitPrice.ToString("0.##"),
                    "100",
                    dto.Invoice.CustomerId);
            }
        }

        return Encoding.ASCII.GetBytes(csv.ToString());
    }

    /// <summary>
    /// Gets the <see cref="Invoice"/> for specific Financial Institution.
    /// </summary>
    /// <param name="year">The invoice year.</param>
    /// <param name="month">The invoice month.</param>
    /// <param name="fiSlug">The FI slug.</param>
    public async Task<Invoice?> GetInvoiceForFiAsync(int year, int month, string fiSlug)
    {
        var fiId = await qCashContext.FinancialInstitutions.IgnoreQueryFilters().FirstOrDefaultAsync(o => o.Slug == fiSlug);
        if (fiId == null)
        {
            return null;
        }
        //TODO - resolve Invoice/FI relationship (and uncomment below)
        //var invoice = await qCashContext.Invoices.IgnoreQueryFilters()
        //    .Include(o => o.InvoiceItems)
        //    .FirstOrDefaultAsync(p => p.MonthlyActivityForUtc.Year == year && p.MonthlyActivityForUtc.Month == month && p.FinancialInstitutionId == fiId.Id);
        //return invoice;
        return null;
    }

    public async Task<Guid?> GetInvoicePlanItemTypeForFiAsync(string abrv, string fiSlug)
    {
        var fiId = await qCashContext.FinancialInstitutions.IgnoreQueryFilters().FirstOrDefaultAsync(o => o.Slug == fiSlug);
        if (fiId == null)
        {
            return null;
        }

        var invoicePlanItemType = await qCashContext.InvoicePlanItemTypes.IgnoreQueryFilters()
            .FirstOrDefaultAsync(t => t.AppAbrv == abrv && t.FinancialInstitutionId == fiId.Id);

        return invoicePlanItemType?.Id;
    }

    private static void AppendInvoiceHeaders(StringBuilder sb)
    {
        sb.Append("\"DoNotImport\"");
        sb.Append(",\"TransactionType\"");
        sb.Append(",\"Date\"");
        sb.Append(",\"GLPostingDate\"");
        sb.Append(",\"DocumentNo\"");
        sb.Append(",\"Customer_Id\"");
        sb.Append(",\"TermName\"");
        sb.Append(",\"DateDue\"");
        sb.Append(",\"State\"");
        sb.Append(",\"Line\"");
        sb.Append(",\"ItemId\"");
        sb.Append(",\"ItemDescription\"");
        sb.Append(",\"Quantity\"");
        sb.Append(",\"Unit\"");
        sb.Append(",\"Price\"");
        sb.Append(",\"DepartmentId\"");
        sb.Append(",\"SoDocumentEntry_CustomerId\"");
        sb.Append(",\"SODOCUMENTENTRY_SHIPTO\"");
    }

    private void AppendInvoiceItem(StringBuilder sb, params string?[] rowItems)
    {
        sb.AppendLine();
        for (var i = 0; i < rowItems.Length; i++)
        {
            if (i > 0)
            {
                sb.Append(",");
            }

            var entry = rowItems[i] ?? string.Empty;
            sb.Append($"\"{entry}\"");
        }
    }

    private async Task ValidateLoanFeeAsync(PricingScenarioTierdItem entity)
    {
        if (entity.FromAmount >= entity.ToAmount)
        {
            throw new Exception("'From' value must be lower than 'To' value.");
        }

        var tieredItems = await qCashContext.PricingScenarioTierdItems
            .Where(p => p.PricingScenarioId.Equals(entity.PricingScenarioId))
            .OrderBy(p => p.Id).ToListAsync();

        var lowerScore = tieredItems.LastOrDefault(s => s.FromAmount <= entity.FromAmount);
        var higherScore = tieredItems.FirstOrDefault(s => s.FromAmount >= entity.FromAmount);

        if (lowerScore != null && entity.FromAmount <= lowerScore.ToAmount)
        {
            throw new Exception($"'From' value must be higher than {lowerScore.ToAmount} because it overlaps with an existing item.");
        }

        if (higherScore != null && entity.ToAmount >= higherScore.FromAmount)
        {
            throw new Exception($"'To' value must be lower than {higherScore.FromAmount} because it overlaps with an existing item.");
        }
    }
}
