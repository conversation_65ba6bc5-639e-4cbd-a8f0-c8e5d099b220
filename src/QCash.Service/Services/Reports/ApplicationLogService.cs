using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.EntityFrameworkCore;
using QCash.Service.Models.Reports;
using QCash.Service.Services.Reports.Interfaces;

namespace QCash.Service.Services.Reports;
public class ApplicationLogService(IReportQueries reportQueries) : IApplicationLogService
{
    public async Task<DataSourceResult> GetAppLogsQueryableAsync(DataSourceRequest request)
    {
        var data = reportQueries.GetApplicationInformation();
        var result = await data.ToDataSourceResultAsync(request);
        return result;
    }

    public async Task<ApplicationLogsPageDto> GetAppLogsByAppIdQueryableAsync(int appId)
    {
        var data = reportQueries.GetApplicationInformationByAppId(appId);
        var result = await data.SingleAsync();
        return result;
    }

    public async Task<List<ApplicationLogDetailDto>> GetDetailedAppLogsQueryableAsync(int appId)
    {
        var data = reportQueries.GetDetailedApplicationInformationByAppId(appId);
        var detailedLogsList = await data.ToListAsync();
        return detailedLogsList;
    }

    public IQueryable<string> GetDistinctModels(DataSourceRequest request)
    {
        var data = reportQueries.GetDistinctModels();
        return data;
    }

    public IQueryable<string> GetDistinctLoanStatusTypes(DataSourceRequest request)
    {
        var data = reportQueries.GetDistinctLoanStatusTypes();
        return data;
    }
}
