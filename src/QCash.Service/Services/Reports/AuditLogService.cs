using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using QCash.Service.Services.Reports.Interfaces;

namespace QCash.Service.Services.Reports;

public class AuditLogService(IReportQueries reportQueries) : IAuditLogService
{
    public async Task<DataSourceResult> GetAuditLogsQueryableAsync(DataSourceRequest request)
    {
        var data = reportQueries.GetAuditLogs();
        var result = await data.ToDataSourceResultAsync(request);
        return result;
    }
}
