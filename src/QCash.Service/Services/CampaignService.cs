using System.Text;
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Classes;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Helpers;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.LoanApplication;
using QCash.Service.Utilities.Extensions;
using Z.EntityFramework.Plus;
using ApplicationOptions = QCash.Utils.Settings.ApplicationOptions;

namespace QCash.Service.Services;

public partial class CampaignService(QCashContext qCashContext, IOptions<ApplicationOptions> applicationOptions, IGuidExtensionService guidExtensionService) : ICampaignService
{
    //This class contains methods moved from QC6 SettingService, all relating to Campaigns.

    /// <summary>
    /// Gets the campaign code accounts by campaign code identifier.
    /// </summary>
    /// <param name="campaignCodeId">The campaign code identifier.</param>
    /// <param name="personalCampaign">Personal Campaign Flag</param>
    /// <param name="search">The account number search criteria.</param>
    /// <returns>The campaign code accounts list.</returns>
    public async Task<IList<CampaignCodeAccount>> GetCampaignCodeAccountsByCampaignCodeIdAsync(
        Guid campaignCodeId,
        bool personalCampaign,
        string search)
    {
        MemberIdentifier memberIdentifier;
        if (string.IsNullOrEmpty(search))
        {
            memberIdentifier = new MemberIdentifier();
        }
        else
        {
            var trimmedSearch = search.TrimStart('0');
            var normalizedSearch = DigitRegex().Replace(search, "");
            memberIdentifier = new MemberIdentifier
            {
                BaseAccountId = trimmedSearch,
                MemberIdHash = CryptoHelper.CalculateSHA256Hash(trimmedSearch),
                // need to use the original (not trimmed) value, because the hashed value can be tax id, which starts with 0
                TaxIdHash = CryptoHelper.CalculateBCryptHash(normalizedSearch, applicationOptions.Value.TaxIdHashSalt)
            };
        }

        var query = qCashContext.CampaignCodeAccounts.Include(o => o.PersonalCampaigns).Where(c => c.CampaignCodeId == campaignCodeId);

        if (new List<string>
            {
                memberIdentifier.BaseAccountId ?? string.Empty,
                memberIdentifier.MemberIdHash ?? string.Empty,
                memberIdentifier.TaxIdHash ?? string.Empty
            }.Any(x => !string.IsNullOrEmpty(x)))
        {
            query = query.FilterByMemberIdentifier(memberIdentifier);
        }

        query = query.OrderBy(c => c.AccountNumber);

        return await query.ToListAsync();
    }

    /// <summary>
    /// Inserts the campaign code accounts.
    /// </summary>
    public async Task InsertCampaignCodeAccountsAsync(IEnumerable<CampaignCodeAccount> campaignCodeAccounts, Guid campaignCodeId)
    {
        //3-step process
        await GetCampaignCodeAccountsByCampaignCodeIdAndAccountStatusAsync(campaignCodeId, nameof(Enums.CampaignCodeAccountStatus.USED));
        await qCashContext.BulkInsertAsync(campaignCodeAccounts);
        await DeleteCampaignCodeAccountsWithIsDeletedValueAsync(campaignCodeId);

        await qCashContext.SaveChangesAsync();
    }

    /// <summary>
    /// Deletes all accounts that have IsDeleted value of 1(true)
    /// </summary>
    /// <param name="campaignCodeId">Campaign code id identifier</param>
    private async Task DeleteCampaignCodeAccountsWithIsDeletedValueAsync(Guid campaignCodeId)
    {
        //Was SQL://DELETE FROM CampaignCodeAccount WHERE CampaignCodeId = @campaignCodeIdParam AND IsDeleted = 1
        var forDeletion = qCashContext.CampaignCodeAccounts.Where(o => o.CampaignCodeId == campaignCodeId && o.IsDeleted);
        qCashContext.RemoveRange(forDeletion);
        await qCashContext.SaveChangesAsync();
    }

    /// <summary>
    /// Inserts the personal campaign on both instances.
    /// </summary>
    /// <param name="personalCampaigns">The entities.</param>
    public async Task InsertPersonalCampaignsAsync(IEnumerable<PersonalCampaign> personalCampaigns) =>
        await qCashContext.BulkInsertAsync(personalCampaigns);

    /// <summary>
    /// Gets CampaignCodeAccounts by CampaignCodeId and Account status.
    /// </summary>
    /// <remarks>Note that this method also marks existing unused entries for deletion. Also, this method does NOT call SaveChanges.</remarks>
    /// <param name="campaignCodeId">Account campaign code id identifier</param>
    /// <param name="status">Campaign code account status identifier</param>
    /// <returns>Returns a list of accounts by campaign code id and campaign code account status</returns>
    public async Task<IEnumerable<CampaignCodeAccount>> GetCampaignCodeAccountsByCampaignCodeIdAndAccountStatusAsync(Guid campaignCodeId, string status)
    {
        //In QC6, this method is only called for the 'USED' status. Steps:
        //(a) for this campaign ID, mark any accounts that are UNUSED or CANCELLED as Deleted
        //(b) return any accounts for this campaign ID that are USED

        //Looking at the way this is used in FinancialInstitutionController.InsertNewCampaignAccounts, it appears as though the csv upload
        //is meant to allow a "re-upload" of the entire list; any accounts that are USED are preserved (stripped from the csv before upload)
        //but all others are marked Deleted in the DB before the list is re-inserted.

        //was SQL
        // UPDATE CampaignCodeAccount
        // SET CampaignCodeAccount.IsDeleted = 1
        // WHERE CampaignCodeAccount.CampaignCodeId = @campaignCodeIdParam AND Status != @campaignCodeAccountStatusParam
        // (SELECT CampaignCodeAccount.AccountNumber
        // FROM CampaignCodeAccount 
        // WHERE CampaignCodeId = @campaignCodeIdParam AND Status = @campaignCodeAccountStatusParam)

        //First step - mark existing as deleted
        var accounts = await qCashContext.CampaignCodeAccounts.Where(p => p.CampaignCodeId == campaignCodeId && p.Status != status).ToListAsync();
        foreach (var account in accounts)
        {
            account.IsDeleted = true;
        }

        return await qCashContext.CampaignCodeAccounts.Where(p => p.CampaignCodeId == campaignCodeId && p.Status == status).ToListAsync();
    }

    /// <summary>
    /// Gets the personal campaign codes for account.
    /// </summary>
    /// <param name="memberIdentifier">Member identifier</param>
    /// <param name="productIds">The product identifier.</param>
    public async Task<IList<CampaignCode>> GetCampaignCodesForAccountAndProductsAsync(
        MemberIdentifier memberIdentifier,
        Guid[] productIds) =>
        await qCashContext.CampaignCodeAccounts.Where(cca => productIds.Contains(cca.CampaignCode.ProductId))
            .FilterByMemberIdentifier(memberIdentifier)
            .Include(cca => cca.CampaignCode)
            .Select(c => c.CampaignCode)
            .ToListAsync();

    /// <summary>
    /// Converts the dates and insert campaign code.
    /// </summary>
    /// <param name="entity">The campaign code entity.</param>
    /// <returns>The instance of <see cref="CampaignCode"/>.</returns>
    public async Task<CampaignCode> ConvertDatesAndInsertCampaignCodeAsync(CampaignCode entity)
    {
        var clientTimeZone = (await qCashContext.Settings.Select(o => o.TimeZone).FirstOrDefaultAsync()) ?? string.Empty;
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);

        entity.Id = guidExtensionService.NewSequentialGuid();
        var effectiveDate = new DateTime(entity.EffectiveDate.Year, entity.EffectiveDate.Month, entity.EffectiveDate.Day, 0, 0, 0, 0);
        var endDate = new DateTime(entity.EndDate.Year, entity.EndDate.Month, entity.EndDate.Day, 23, 59, 59);
        entity.EffectiveDate = effectiveDate.ToUTC(timeZone);
        entity.EndDate = endDate.ToUTC(timeZone);
        entity.IsActive = false;

        qCashContext.CampaignCodes.Add(entity);
        await qCashContext.SaveChangesAsync();

        return entity;
    }

    /// <summary>
    /// Converts the dates and update campaign code.
    /// </summary>
    /// <param name="entity">The entity.</param>
    /// <returns></returns>
    public async Task<bool> ConvertDatesAndUpdateCampaignCodeAsync(CampaignCode entity)
    {
        var clientTimeZone = (await qCashContext.Settings.Select(o => o.TimeZone).FirstOrDefaultAsync()) ?? string.Empty;
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);

        var effectiveDate = new DateTime(entity.EffectiveDate.Year, entity.EffectiveDate.Month, entity.EffectiveDate.Day, 0, 0, 0, 0);
        var endDate = new DateTime(entity.EndDate.Year, entity.EndDate.Month, entity.EndDate.Day, 23, 59, 59);
        entity.EffectiveDate = effectiveDate.ToUTC(timeZone);
        entity.EndDate = endDate.ToUTC(timeZone);

        return await qCashContext.SaveChangesAsync() > 0;
    }

    /// <summary>
    /// Gets the campaign code accounts by account number.
    /// Either accountNumber or memberId will be used, based on the campaign code account data
    /// </summary>
    /// <param name="memberIdentifier">Member identifier</param>
    public async Task<IList<CampaignCodeAccount>> GetCampaignCodeAccountsByMemberIdentifierAsync(MemberIdentifier memberIdentifier) =>
        await qCashContext.CampaignCodeAccounts.FilterByMemberIdentifier(memberIdentifier).ToListAsync();

    /// <summary>
    /// Gets whether to show the campaign code.
    /// </summary>
    /// <param name="memberIdentifier">Member identifier</param>
    /// <param name="activeMilitary">if set to <c>true</c> [active military].</param>
    /// <param name="availableProductIds">The available product ids.</param>
    public async Task<bool> ShowCampaignCodeAsync(
        MemberIdentifier memberIdentifier,
        bool? activeMilitary,
        IEnumerable<Guid> availableProductIds)
    {
        var settings = await qCashContext.Settings.AsNoTracking().FirstAsync();
        if (settings.UseManualCampaignCodeInput && settings.CampaignCode)
        {
            var clientTimeZone = TimeZoneInfo.FindSystemTimeZoneById(settings.TimeZone);
            var clientTime = DateTime.UtcNow.FromUTC(clientTimeZone);

            var accountCampaignCodes = await GetCampaignCodeAccountsByMemberIdentifierAsync(memberIdentifier);
            var productIds = availableProductIds.ToList();
            foreach (var campaignCodeAccount in accountCampaignCodes)
            {
                var campaignCode = await qCashContext.CampaignCodes.FirstOrDefaultAsync(p => p.Id == campaignCodeAccount.CampaignCodeId);
                if (campaignCode != null)
                {
                    var campaignEffectiveDateInClientZone = campaignCode.EffectiveDate.FromUTC(clientTimeZone);
                    var campaignEndDateInClientZone = campaignCode.EndDate.FromUTC(clientTimeZone);

                    if (clientTime >= campaignEffectiveDateInClientZone &&
                        clientTime <= campaignEndDateInClientZone &&
                        productIds.Contains(campaignCode.ProductId) &&
                        campaignCodeAccount.Status == nameof(Enums.CampaignCodeAccountStatus.UNUSED) &&
                        campaignCode.IsActive)
                    {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /// <summary>
    /// Gets the data extract CSV.
    /// </summary>
    public async Task<byte[]?> GetCampaignCodeAccountCsvAsync() =>
        await ToCsvAsync(await GetCampaignCodeAccountCsvDataAsync());

    /// <summary>
    /// Gets the data extract CSV.
    /// </summary>
    /// <param name="campaignCodeId">The campaign code identifier.</param>
    public async Task<byte[]?> GetDataExtractCsvAsync(string campaignCodeId) =>
        await ToCsvAsync(await GetCampaignCodeAccountCsvDataAsync(Guid.Parse(campaignCodeId)));

    private async Task<List<CampaignDataExtractDto>> GetCampaignCodeAccountCsvDataAsync(Guid campaignCodeId)
    {
        var campaignExtract = await qCashContext.CampaignCodeAccounts.Where(o => o.CampaignCodeId == campaignCodeId)
            .Select(p => new { p.Status, p.DateUsed, p.AccountNumber }).ToListAsync();

        List<CampaignDataExtractDto> data = campaignExtract
            .Select(o => new CampaignDataExtractDto(o.Status, o.DateUsed, GetMaskedNumber(o.AccountNumber))).ToList();

        return data;

        //Was SQL
        //SELECT [Status], DateUsed,
        // 'xxxxxx' + CASE WHEN (LEN(AccountNumber) > 3) THEN SUBSTRING(AccountNumber, LEN(AccountNumber) - 3, LEN(AccountNumber)) 
        // ELSE RIGHT('**********' + AccountNumber, 4) END AccountNumber
        // FROM CampaignCodeAccount
        // WHERE CampaignCodeId = '{campaignCodeId}'
    }

    private async Task<List<CampaignDataExtractDto>> GetCampaignCodeAccountCsvDataAsync()
    {
        var campaignExtract = await qCashContext.CampaignCodeAccounts.Select(p => new { p.Status, p.DateUsed, p.AccountNumber }).ToListAsync();

        List<CampaignDataExtractDto> data = campaignExtract
            .Select(o => new CampaignDataExtractDto(o.Status, o.DateUsed, GetMaskedNumber(o.AccountNumber))).ToList();

        return data;

        //Was SQL
        //SELECT [Status], DateUsed,
        // 'xxxxxx' + CASE WHEN (LEN(AccountNumber) > 3) THEN SUBSTRING(AccountNumber, LEN(AccountNumber) - 3, LEN(AccountNumber)) 
        // ELSE RIGHT('**********' + AccountNumber, 4) END AccountNumber
        // FROM CampaignCodeAccount
    }

    public record CampaignDataExtractDto(string Status, DateTime? DateUsed, string? AccountNumber);

    private string GetMaskedNumber(string? number, int cleanCharsTotal = 4, char maskedChar = 'x')
    {
        //already exists in LoanApplicationService, but duplicating here so as not to need that service
        if (string.IsNullOrEmpty(number))
        {
            return string.Empty;
        }

        var diff = number.Length - cleanCharsTotal;
        var zeros = diff < 0 ? Math.Abs(diff) : 0;
        var clean = diff < 0 ? number : number.Substring(diff);
        var maskedNum = new string(maskedChar, 10 - cleanCharsTotal) + new string('0', zeros) + clean;

        return maskedNum;
    }

    private async Task<byte[]?> ToCsvAsync(List<CampaignDataExtractDto> dataExtract, bool convertToFiTimeZone = true)
    {
        //Converted from QC6 ToCSV/SetCSVData - might be better to get a good CSV handling library.
        //This is basic output for this use case, though, so leaving as-is for now.

        var sbData = new StringBuilder();

        if (dataExtract.Count == 0)
        {
            return null;
        }

        var clientTimeZone = (await qCashContext.Settings.Select(o => o.TimeZone).FirstOrDefaultAsync()) ?? string.Empty;

        var timeZone = convertToFiTimeZone ? TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone) : null;

        sbData.AppendLine("Status,DateUsed,AccountNumber");

        foreach (var dr in dataExtract)
        {
            sbData.Append("\"" + dr.Status.Replace("\"", "\"\"")).Append("\",");

            if (dr.DateUsed == null)
            {
                sbData.Append(",");
            }
            else
            {
                var dataValue = timeZone != null ? dr.DateUsed.FromUTC(timeZone) : dr.DateUsed;
                sbData.Append("\"" + dataValue!.ToString()!.Replace("\"", "\"\"")).Append("\",");
            }

            if (dr.AccountNumber == null)
            {
                sbData.Append(",");
            }
            else
            {
                sbData.Append("\"" + dr.AccountNumber.Replace("\"", "\"\"")).Append("\",");
            }

            sbData.Replace(",", Environment.NewLine, sbData.Length - 1, 1);
        }
        return Encoding.ASCII.GetBytes(sbData.ToString());
    }

    [GeneratedRegex(@"\D", RegexOptions.None, matchTimeoutMilliseconds: 1000)]
    private static partial Regex DigitRegex();
}
