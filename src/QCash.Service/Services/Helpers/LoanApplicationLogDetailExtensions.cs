using QCash.Data.Models;
using static QCash.Service.Services.LoanApplication.Enums;

namespace QCash.Service.Services.Helpers;

public static class LoanApplicationLogDetailExtensions
{

    public static void AddLogDetail(this IList<LoanApplicationLogDetail> logs, string actionDescription, bool isTemp = false) =>
        logs.Add(new LoanApplicationLogDetail()
        {
            ActionDateTimeUtc = DateTime.UtcNow,
            IsTemp = isTemp,
            ActionDescription = actionDescription
        });

    public static void AddLogDetail(this IList<LoanApplicationLogDetail> logs, LogType logType, string actionDescription, bool isTemp = false) =>
        logs.Add(new LoanApplicationLogDetail()
        {
            ActionDateTimeUtc = DateTime.UtcNow,
            IsTemp = isTemp,
            LogType = (int)logType,
            ActionDescription = actionDescription
        });

    public static void AddLogDetail(this IList<LoanApplicationLogDetail> logs, LogType logType, LogSortOrder sortOrder, string actionDescription, bool isTemp = false) =>
        logs.Add(new LoanApplicationLogDetail()
        {
            ActionDateTimeUtc = DateTime.UtcNow,
            IsTemp = isTemp,
            LogType = (int)logType,
            SortOrder = (int)sortOrder,
            ActionDescription = actionDescription
        });

    public static void AddLogDetail(this IList<LoanApplicationLogDetail> logs, LogType logType, LogSortOrder sortOrder, LogDetailItemReportGroupEnum reportGroup, string actionDescription, bool isTemp = false) =>
        logs.Add(new LoanApplicationLogDetail()
        {
            ActionDateTimeUtc = DateTime.UtcNow,
            IsTemp = isTemp,
            LogType = (int)logType,
            SortOrder = (int)sortOrder,
            ReportGroup = (int)reportGroup,
            ActionDescription = actionDescription
        });
}