using QCash.Data.Models;
using QCash.Service.Models.LoanApplication;

namespace QCash.Service.Services.Helpers;

public static class LoanApplicationSsoExtensions
{
    /// <summary>
    /// Gets the OriginApp enum.
    /// </summary>
    public static Enums.OriginApp GetOriginAppEnum(this LoanApplicationSso? lasso)
    {
        if (lasso == null)
        {
            return Enums.OriginApp.None;
        }
        if (string.IsNullOrEmpty(lasso.OriginApp))
        {
            return Enums.OriginApp.None;
        }

        return (Enums.OriginApp)Enum.Parse(typeof(Enums.OriginApp), lasso.OriginApp);
    }
}