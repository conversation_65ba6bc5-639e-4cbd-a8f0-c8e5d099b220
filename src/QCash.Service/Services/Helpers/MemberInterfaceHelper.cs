using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Data.Models.Enums;
using QCash.Data.Models.Interfaces;
using QCash.Service.Classes;
using QCash.Service.Classes.DecisionManager;
using QCash.Service.Classes.DecisionManager.Interface;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Utilities.Extensions;
using static QCash.Service.Services.LoanApplication.Enums;

namespace QCash.Service.Services.Helpers;

public partial class MemberInterfaceHelper(
    QCashContext qCashContext,
    ILoanApplicationHelper loanApplicationHelper,
    IGuidExtensionService guidExtensionService,
    ILogger<MemberInterfaceHelper> logger)
    : IMemberInterfaceHelper
{
    private const string LstdAbrv = "LSTD";

    private IEnumerable<MemberAccount>? _awarenessShareMemberAccounts;
    private IEnumerable<MemberAccount>? _loanMemberAccountsFb;
    private IEnumerable<MemberAccount>? _loanMemberAccountsIb;
    private IEnumerable<LoanApplicationFeeRefund>? _loanApplicationFeeRefunds;
    private TilaResponse? _tilaResponse;


    public async Task<Dictionary<string, string>> GetFormattedMemberInterfaceTextsAsync(Guid languageId,
        Data.Models.LoanApplication? loanApplication, params string[] abbreviations)
    {
        var stringified = abbreviations
            .Select(o => "MemberInterfaceText." + o).ToList();
        return await GetFormattedInterfaceTextsAsync(languageId, loanApplication, stringified);
    }

    public async Task<Dictionary<string, string>> GetFormattedInterfaceTextsAsync(Guid languageId,
        Data.Models.LoanApplication? loanApplication, params PaymentOptionTitle[] paymentOptionTitles)
    {
        var stringified = paymentOptionTitles.ToList().Select(o => "PaymentOptionTitle." + o.Abrv).ToList();
        return await GetFormattedInterfaceTextsAsync(languageId, loanApplication, stringified);
    }

    public async Task<Dictionary<string, string>> GetFormattedInterfaceStaticTextsAsync(Guid languageId,
        Data.Models.LoanApplication? loanApplication, params InterfaceDefaultText[] memberInterfaceStaticTexts)
    {
        var stringified = memberInterfaceStaticTexts.Select(o => "MemberInterfaceStaticText." + o.Abrv).ToList();
        return await GetFormattedInterfaceTextsAsync(languageId, loanApplication, stringified);
    }

    public async Task<Dictionary<string, string>> GetFormattedInterfaceTextsAsync(Guid languageId, Data.Models.LoanApplication? loanApplication, List<string> interfaceTexts)
    {
        var textsWithTranslations = await GetInterfaceTextsWithTranslationAsync(languageId, interfaceTexts);
        return loanApplication != null
            ? await ResolveMemberInterfacePlaceholdersAsync(textsWithTranslations, languageId, loanApplication)
            : await ResolveMemberInterfacePlaceholdersAsync(textsWithTranslations);
    }

    public async Task<Dictionary<string, string>> GetFormattedInterfaceTextsAsync(Guid languageId, Data.Models.LoanApplication? loanApplication, string singleInterfaceText)
    {
        var textsWithTranslations = await GetInterfaceTextsWithTranslationAsync(languageId, [singleInterfaceText]);
        return loanApplication != null
            ? await ResolveMemberInterfacePlaceholdersAsync(textsWithTranslations, languageId, loanApplication)
            : await ResolveMemberInterfacePlaceholdersAsync(textsWithTranslations);
    }

    public Task<Dictionary<string, string>> GetFormattedInterfaceTextsAsync(Guid languageId, Data.Models.LoanApplication loanApplication, params IPoco[] memberIST) => throw new NotImplementedException();

    public async Task<Dictionary<string, string>> GetFormattedInterfaceTextsFromMemberAsync(Guid languageId, FinancialInstitutionMember? financialInstitutionMember, List<string> interfaceTexts)
    {
        var textsWithTranslations = await GetInterfaceTextsWithTranslationAsync(languageId, interfaceTexts);
        return financialInstitutionMember != null
            ? await ResolveMemberInterfacePlaceholdersAsync(textsWithTranslations, financialInstitutionMember)
            : await ResolveMemberInterfacePlaceholdersAsync(textsWithTranslations);
    }

    public Dictionary<string, string> GetFormattedInterfaceTextsFromMember(Guid languageId, FinancialInstitutionMember financialInstitutionMember,
        params IPoco[] texts) =>
        throw new NotImplementedException();

    public Dictionary<string, string> GetInterfaceTextsWithTranslation(Guid languageId, params IPoco[] memberIt) => throw new NotImplementedException();

    public async Task<Dictionary<string, string>> ResolveMemberInterfacePlaceholdersAsync(
        Dictionary<string, string> texts, FinancialInstitutionMember financialInstitutionMember)
    {
        var rxPlaceholder = MemberInterfacePlaceholderRegex();

        for (var i = 0; i < texts.Count; i++)
        {
            if (string.IsNullOrEmpty(texts.ElementAt(i).Value)) continue;

            var placeholders = rxPlaceholder.Matches(texts.ElementAt(i).Value).Select(m => m.Value).ToArray();

            foreach (var placeholder in placeholders)
            {
                // Try to resolve placeholder using reflection
                var replacement = GetMemberInterfacePlaceholderReplacementUsingReflection(placeholder, financialInstitutionMember);

                if (string.IsNullOrEmpty(replacement))
                {
                    // Try to resolve placeholder by iterating through general FI settings
                    replacement = await GetMemberInterfacePlaceholderReplacementByFinancialInstitutionAsync(placeholder);

                    if (string.IsNullOrEmpty(replacement))
                    {
                        try
                        {
                            // Try to resolve placeholder by iterating through member data
                            replacement = GetMemberInterfacePlaceholderReplacementByFinancialInstitutionMember(placeholder, financialInstitutionMember);
                        }
                        catch
                        {
                            replacement = "N/A";
                        }
                    }
                }

                if (!string.IsNullOrEmpty(replacement))
                {
                    var item = texts.ElementAt(i);
                    texts[texts.ElementAt(i).Key] = item.Value.Replace(placeholder, replacement);
                }
            }
        }

        return texts;
    }

    /// <summary>
    /// Gets the member interface placeholder replacement by financial institution member.
    /// </summary>
    /// <param name="placeholder">The placeholder.</param>
    /// <param name="financialInstitutionMember">The financial institution member.</param>
    /// <returns></returns>
    private string GetMemberInterfacePlaceholderReplacementByFinancialInstitutionMember(string placeholder, FinancialInstitutionMember financialInstitutionMember)
    {
        var replacement = string.Empty;

        switch (placeholder)
        {
            case "{feeBasedNumberOfLoans}":
            {
                var accounts = GetAwarenessLoanMemberAccountsFB(financialInstitutionMember.Id);
                var response = loanApplicationHelper.GetNumberOfLoansForFinancialInstitutionMember(accounts);
                replacement = response.ToString();
                break;
            }
            case "{interestBasedNumberOfLoans}":
            {
                var accounts = GetAwarenessLoanMemberAccountsIB(financialInstitutionMember.Id);
                var response = loanApplicationHelper.GetNumberOfLoansForFinancialInstitutionMember(accounts);
                replacement = response.ToString();
                break;
            }
            case "{feeBasedTotalAmount}":
            {
                var accounts = GetAwarenessLoanMemberAccountsFB(financialInstitutionMember.Id);
                var response = loanApplicationHelper.GetTotalAmountOfLoansForFinancialInstitutionMember(accounts);
                replacement = response.ToString(InterfaceTextConstants.FORMAT_AMOUNT);
                break;
            }
            case "{interestBasedTotalAmount}":
            {
                var accounts = GetAwarenessLoanMemberAccountsIB(financialInstitutionMember.Id);
                var response = loanApplicationHelper.GetTotalAmountOfLoansForFinancialInstitutionMember(accounts);
                replacement = response.ToString(InterfaceTextConstants.FORMAT_AMOUNT);
                break;
            }
            case "{interestPaidTotalAmount}":
            {
                var accounts = GetAwarenessLoanMemberAccountsIB(financialInstitutionMember.Id);
                var response = loanApplicationHelper.GetTotalInterestPaidForFinancialInstitutionMember(accounts);
                replacement = response.ToString(InterfaceTextConstants.FORMAT_AMOUNT);
                break;
            }
            case "{NSFCount}":
            {
                var awarenesShareMemberAccounts = GetAwarenessCheckingShareMemberAccounts(financialInstitutionMember.Id);
                var response = loanApplicationHelper.GetTotalNSFCountForFinancialInstitutionMember(awarenesShareMemberAccounts);
                replacement = response.ToString();
                break;
            }
            case "{ODPCount}":
            {
                var awarenesShareMemberAccounts = GetAwarenessCheckingShareMemberAccounts(financialInstitutionMember.Id);
                var response = loanApplicationHelper.GetTotalODPCountForFinancialInstitutionMember(awarenesShareMemberAccounts);
                replacement = response.ToString();
                break;
            }
            case "{RefundPercentage}":
            {
                logger.LogWarning("Attempted use of {{RefundPercentage}} placeholder relating to Fee Refunds, which is obsolete");
                //replacement = GetFeeRefundSetting().FeeRefundPercentage.ToString(InterfaceTextConstants.FORMAT_PERCENTAGE);
                replacement = string.Empty;
                break;
            }
            case "{firstName}":
            {
                replacement = financialInstitutionMember.FirstName ?? string.Empty;
                break;
            }
            case "{FraudControlCode}":
            {
                var fiMemberLoginFraudControl = qCashContext.LoginFraudControls.SingleOrDefault(o => o.FinancialInstitutionMemberId == financialInstitutionMember.Id);
                replacement = fiMemberLoginFraudControl?.Code ?? string.Empty;
                break;
            }
            case "{nextAvailableApplicationDate}":
            {
                var numOfDeniedLoans = GetLoanExclusionSetting()?.NumberOfDeniedLoans ?? 0;
                var threshold = GetLoanExclusionSetting()?.DeniedLoansTreshold ?? 0;

                var endTime = DateTime.UtcNow;
                var startTime = endTime.AddDays(-threshold);

                var nextPossibleApplicationDate = loanApplicationHelper
                    .CalculateNextPossibleLoanDate(financialInstitutionMember.MemberIdHash,
                        LstdAbrv, startTime, endTime, numOfDeniedLoans);

                replacement = nextPossibleApplicationDate.AddDays(threshold).ToString("MM/dd/yyyy h:mm tt");
                break;
            }
        }

        return replacement;
    }


    /// <summary>
    /// Resolves the member interface placeholders.
    /// </summary>
    /// <param name="texts">The dictionary with member interface texts.</param>
    /// <returns>Dictionary with resolved placeholders in member interface texts.</returns>
    public async Task<Dictionary<string, string>> ResolveMemberInterfacePlaceholdersAsync(Dictionary<string, string> texts)
    {
        var rxPlaceholder = MemberInterfacePlaceholderRegex();

        for (var i = 0; i < texts.Count; i++)
        {
            if (!string.IsNullOrEmpty(texts.ElementAt(i).Value))
                continue;

            var placeholders = rxPlaceholder.Matches(texts.ElementAt(i).Value).Select(m => m.Value).ToArray();
            foreach (var placeholder in placeholders)
            {
                texts[texts.ElementAt(i).Key] = texts.ElementAt(i).Value.Replace(placeholder, await GetMemberInterfacePlaceholderReplacementByFinancialInstitutionAsync(placeholder));
            }
        }

        return texts;
    }

    /// <summary>
    /// Resolves the member interface placeholders.
    /// </summary>
    /// <param name="texts">The dictionary with member interface texts.</param>
    /// <param name="languageId">The language.</param>
    /// <param name="loanApplication">The loan application.</param>
    /// <returns>Dictionary with resolved placeholders in member interface texts.</returns>
    public async Task<Dictionary<string, string>> ResolveMemberInterfacePlaceholdersAsync(Dictionary<string, string> texts, Guid languageId, Data.Models.LoanApplication loanApplication)
    {
        var rxPlaceholder = MemberInterfacePlaceholderRegex();
        var rxParamName = ParamNameRegex();

        // Get array of all needed products abrv.
        var calcResult = await loanApplicationHelper.GetLoanApplicationResultAllProductsAsync(loanApplication.Id) ?? new ResultAllProducts();
        var productsAbrv = texts
            .Where(p => !string.IsNullOrEmpty(p.Value) && p.Value.Contains(InterfaceTextConstants.PLACEHOLDER_PRODUCT_BY_ABRV))
            .Select(d => rxParamName.Match(d.Value).ToString()
                .Trim(InterfaceTextConstants.PLACEHOLDER_ABRV_START)
                .Trim(InterfaceTextConstants.PLACEHOLDER_ABRV_END)
            ).ToArray();

        // Resolve all products needed.
        IList<Data.Models.Product> products = [];

        if (productsAbrv.Length != 0)
        {
            products = await qCashContext.Products.AsNoTracking().Where(p => productsAbrv.Contains(p.Slug)).ToListAsync();
        }

        // If the selected product entity is not set in LA.
        if (loanApplication is { SelectedProductId: not null, SelectedProduct: null })
        {
            await qCashContext.Entry(loanApplication).Reference(p => p.SelectedProduct).LoadAsync();
            //loanApplication.SelectedProduct = SettingService.GetProduct(loanApplication.SelectedProductId.Value);
        }

        for (var i = 0; i < texts.Count; i++)
        {
            if (string.IsNullOrEmpty(texts.ElementAt(i).Value))
                continue;

            var placeholders = rxPlaceholder.Matches(texts.ElementAt(i).Value).Select(m => m.Value).ToArray();

            foreach (var placeholder in placeholders)
            {
                // Try to resolve placeholder using reflection
                var replacement = GetMemberInterfacePlaceholderReplacementUsingReflection(placeholder, loanApplication, rxParamName, products, calcResult);

                if (string.IsNullOrEmpty(replacement))
                {
                    // Try to resolve placeholder by iterating through general FI settings
                    replacement = await GetMemberInterfacePlaceholderReplacementByFinancialInstitutionAsync(placeholder);

                    if (string.IsNullOrEmpty(replacement))
                    {
                        try
                        {
                            // Try to resolve placeholder by iterating through loan-related data
                            replacement = await GetMemberInterfacePlaceholderReplacementByLoanApplicationAsync(placeholder, languageId, loanApplication);
                        }
                        catch
                        {
                            replacement = "N/A";
                        }
                    }
                }

                if (!string.IsNullOrEmpty(replacement))
                {
                    var item = texts.ElementAt(i);
                    texts[texts.ElementAt(i).Key] = item.Value.Replace(placeholder, replacement);
                }
            }
        }

        return texts;
    }

    /// <summary>
    /// Gets the member interface placeholder replacement by financial institution settings.
    /// </summary>
    /// <param name="placeholder">The placeholder.</param>
    /// <returns>Member interface placeholder replacement.</returns>
    private async Task<string> GetMemberInterfacePlaceholderReplacementByFinancialInstitutionAsync(string placeholder) => placeholder switch
    {
        "{clientFullName}" or "{fiName}" => await qCashContext.FinancialInstitutions.Select(fi => fi.Name)
            .FirstOrDefaultAsync() ?? string.Empty,
        "{client}" => (await qCashContext.FinancialInstitutions.Select(fi => fi.Slug).FirstOrDefaultAsync())?.ToUpper()
                      ?? string.Empty,
        "{fislug}" => (await qCashContext.FinancialInstitutions.Select(fi => fi.Slug).FirstOrDefaultAsync())
            ?.ToLower() ?? string.Empty,
        "{debtAgency}" => await qCashContext.FinancialCoachingSettings.Select(fi => fi.DebtManagementAgencyName)
            .FirstOrDefaultAsync() ?? string.Empty,
        "{deniedLoansPeriod}" => (await qCashContext.LoanExclusionSettings.Select(s => s.DeniedLoansTreshold)
                .FirstOrDefaultAsync())
            .ToString(),
        "{awarenessPeriod}" => (await qCashContext.FinancialCoachingSettings.Select(s => s.AwarenessPeriod)
            .FirstOrDefaultAsync()).ToString(),
        _ => string.Empty,
    };

    /// <summary>
    /// Gets the member interface placeholder replacement by loan application.
    /// </summary>
    /// <param name="placeholder">The placeholder.</param>
    /// <param name="languageId">The language.</param>
    /// <param name="loanApplication">The loan application.</param>
    /// <returns></returns>
    private async Task<string> GetMemberInterfacePlaceholderReplacementByLoanApplicationAsync(string placeholder, Guid languageId, Data.Models.LoanApplication loanApplication)
    {
        var replacement = string.Empty;

        switch (placeholder)
        {
            case "{FundingAccountType}":
            {
                replacement = await GetFundingAccountTypeAsync(loanApplication, languageId);
                break;
            }
            case "{firstPaymentDate}":
            {
                var tilaRequest = await qCashContext.TilaRequests
                    .Select(request => new { request.Id, request.LoanApplicationId, request.FirstPaymentDate })
                    .Where(o => o.LoanApplicationId.Equals(loanApplication.Id))
                    .OrderBy(p => p.Id)
                    .FirstOrDefaultAsync();
                if (tilaRequest?.FirstPaymentDate != null)
                {
                    replacement = tilaRequest.FirstPaymentDate.Value.ToString(InterfaceTextConstants.FORMAT_DATE_MMDDYYYY);
                }

                break;
            }
            case "{finalPaymentDate}":
            {
                var tilaResp = GetTilaResponse(loanApplication.Id);
                if (tilaResp != null)
                {
                    replacement = tilaResp.FinalPaymentDate?.ToString(InterfaceTextConstants.FORMAT_DATE_MMDDYYYY) ?? string.Empty;
                }

                break;
            }
            case "{secondPaymentDate}":
            {
                var tilaResp = GetTilaResponse(loanApplication.Id);
                if (tilaResp != null)
                {
                    replacement = tilaResp.SecondPaymentDate?.ToString(InterfaceTextConstants.FORMAT_DATE_MMDDYYYY) ?? string.Empty;
                }

                break;
            }
            case "{firstPaymentAmount}":
            {
                var tilaResp = GetTilaResponse(loanApplication.Id);
                if (tilaResp != null)
                {
                    replacement = tilaResp.RegularPaymentAmount.ToString(InterfaceTextConstants.FORMAT_AMOUNT);
                }

                break;
            }
            case "{annualPercentageRate}":
            {
                replacement = string.Format($"{loanApplication.AnnualPercentageRate.ToString(InterfaceTextConstants.FORMAT_PERCENTAGE)}");
                break;
            }
            case "{feeBasedNumberOfLoans}":
            {
                var accounts = GetAwarenessLoanMemberAccountsFB(loanApplication.FinancialInstitutionMemberId);
                var response = loanApplicationHelper.GetNumberOfLoansForFinancialInstitutionMember(accounts);
                replacement = response.ToString();
                break;
            }
            case "{interestBasedNumberOfLoans}":
            {
                var accounts = GetAwarenessLoanMemberAccountsIB(loanApplication.FinancialInstitutionMemberId);
                var response = loanApplicationHelper.GetNumberOfLoansForFinancialInstitutionMember(accounts);
                replacement = response.ToString();
                break;
            }
            case "{feeBasedTotalAmount}":
            {
                var accounts = GetAwarenessLoanMemberAccountsFB(loanApplication.FinancialInstitutionMemberId);
                var response = loanApplicationHelper.GetTotalAmountOfLoansForFinancialInstitutionMember(accounts);
                replacement = response.ToString(InterfaceTextConstants.FORMAT_AMOUNT);
                break;
            }
            case "{interestBasedTotalAmount}":
            {
                var accounts = GetAwarenessLoanMemberAccountsIB(loanApplication.FinancialInstitutionMemberId);
                var response = loanApplicationHelper.GetTotalAmountOfLoansForFinancialInstitutionMember(accounts);
                replacement = response.ToString(InterfaceTextConstants.FORMAT_AMOUNT);
                break;
            }
            case "{interestPaidTotalAmount}":
            {
                var accounts = GetAwarenessLoanMemberAccountsIB(loanApplication.FinancialInstitutionMemberId);
                var response = loanApplicationHelper.GetTotalInterestPaidForFinancialInstitutionMember(accounts);
                replacement = response.ToString(InterfaceTextConstants.FORMAT_AMOUNT);
                break;
            }
            case "{NSFCount}":
            {
                var awarenesShareMemberAccounts = GetAwarenessCheckingShareMemberAccounts(loanApplication.FinancialInstitutionMemberId);
                var response = loanApplicationHelper.GetTotalNSFCountForFinancialInstitutionMember(awarenesShareMemberAccounts);
                replacement = response.ToString();
                break;
            }
            case "{ODPCount}":
            {
                var awarenesShareMemberAccounts = GetAwarenessCheckingShareMemberAccounts(loanApplication.FinancialInstitutionMemberId);
                var response = loanApplicationHelper.GetTotalODPCountForFinancialInstitutionMember(awarenesShareMemberAccounts);
                replacement = response.ToString();
                break;
            }
            case "{averageDepositTotalAmount}":
            {
                logger.LogWarning("Attempted use of {{averageDepositTotalAmount}} placeholder relating to Financial Wellness/Awareness, which is obsolete");
                replacement = string.Empty;
                //decimal? avgAmount = loanApplication.AverageMonthlyCheckingDepositBalance ?? await _loanApplicationService.GetAverageDepositAmountForFinancialInstitutionMemberAsync(loanApplication);
                //replacement = (avgAmount != null) ? avgAmount.Value.ToString(InterfaceTextConstants.FORMAT_AMOUNT) : "N/A";
                break;
            }
            case "{numberOfDeniedLoans}":
            {
                replacement = (loanApplication.NumberOfDeniedLoans ?? 0).ToString();
                break;
            }
            case "{AdjustedAPR}":
            {
                if (loanApplication.LoanAmount.HasValue)
                {
                    var tilaResp = GetTilaResponse(loanApplication.Id);
                    if (tilaResp?.AdjustedApr is not null)
                    {
                        replacement = tilaResp.AdjustedApr.Value.ToString(InterfaceTextConstants.FORMAT_PERCENTAGE);
                    }
                }
                break;
            }
            case "{preApproved}":
            {
                replacement = ((loanApplication.LoanApplicationSso != null && loanApplication.LoanApplicationSso.PreApproved.HasValue) ? loanApplication.LoanApplicationSso!.PreApproved!.ToString() : "null") ?? "null";
                break;
            }
            case "{RefundPercentage}":
            {
                logger.LogWarning("Attempted use of {{RefundPercentage}} placeholder relating to Fee Refunds, which is obsolete");
                //replacement = GetFeeRefundSetting().FeeRefundPercentage.ToString(InterfaceTextConstants.FORMAT_PERCENTAGE);
                replacement = string.Empty;
                break;
            }
            case "{QualifiedRefund}":
            {
                //Fee Refund reference - is this obsolete?
                var feeRefunds = GetLoanApplicationFeeRefunds(loanApplication.Id) ?? new List<LoanApplicationFeeRefund>();
                replacement = feeRefunds.Where(p =>
                        p.IsEligible
                        && p.Amount.HasValue
                        && (p.FeeType == nameof(LoanApplicationFeeType.SavingApplicationFeeRefund)
                            || p.FeeType == nameof(LoanApplicationFeeType.SavingLoanOriginationFeeRefund)))
                    .Sum(p => p.Amount!.Value).ToString(InterfaceTextConstants.FORMAT_AMOUNT);
                break;
            }
            case "{CampaignRefund}":
            {
                //Fee Refund reference - is this obsolete?
                var feeRefunds = GetLoanApplicationFeeRefunds(loanApplication.Id) ?? new List<LoanApplicationFeeRefund>();
                replacement = feeRefunds.Where(p =>
                        p.IsEligible
                        && p.Amount.HasValue
                        && (p.FeeType == nameof(LoanApplicationFeeType.CampaignApplicationFeeRefund)))
                    .Sum(p => p.Amount!.Value).ToString(InterfaceTextConstants.FORMAT_AMOUNT);
                break;
            }
            case "{CreditCardId}":
            {
                logger.LogWarning("Attempted use of {{CreditCardId}} placeholder relating to Credit Cards, which is obsolete functionality");
                replacement = string.Empty;
                //var cardOrderingResponse = _loanApplicationService.GetCreditCardOrderingResponsesByLoanApplicationId(loanApplication.Id).FirstOrDefault();
                //replacement = (cardOrderingResponse != null && !string.IsNullOrEmpty(cardOrderingResponse.CardId)) ? cardOrderingResponse.CardId : "N/A";
                break;
            }
            case "{firstName}":
            {
                replacement = loanApplication.FinancialInstitutionMember.FirstName ?? string.Empty;
                break;
            }
            case "{FraudControlCode}":
            {
                replacement = loanApplication.LoanApplicationFraudControl?.Code ?? string.Empty;
                break;
            }
            case "{totalNumberOfPayments}":
            {
                var tilaResp = GetTilaResponse(loanApplication.Id);
                if (tilaResp != null && tilaResp.NumberOfPayments.HasValue)
                {
                    replacement = tilaResp.NumberOfPayments.ToString() ?? string.Empty;
                }

                break;
            }
            case "{nextAvailableApplicationDate}":
            {
                var numOfDeniedLoans = GetLoanExclusionSetting()?.NumberOfDeniedLoans ?? 0;
                var threshold = GetLoanExclusionSetting()?.DeniedLoansTreshold ?? 0;

                var endTime = DateTime.UtcNow;
                var startTime = endTime.AddDays(-threshold);

                var nextPossibleApplicationDate = loanApplicationHelper
                    .CalculateNextPossibleLoanDate(loanApplication.FinancialInstitutionMember.MemberIdHash,
                        LstdAbrv, startTime, endTime, numOfDeniedLoans);

                replacement = nextPossibleApplicationDate.AddDays(threshold).ToString("MM/dd/yyyy h:mm tt");
                break;
            }
            case "{shareID}":
            {
                var accountId = string.Empty;
                var accountDescription = string.Empty;
                if (!guidExtensionService.IsNullOrEmpty(loanApplication.SelectedAccountForTransactionId))
                {
                    var memberAccount = await qCashContext.MemberAccounts.Where(p => p.Id.Equals(loanApplication.SelectedAccountForTransactionId)).OrderBy(s => s.DateOpened).FirstOrDefaultAsync();
                    //var memberAccount = _loanApplicationService.GetMemberAccount(loanApplication.SelectedAccountForTransactionId ?? Guid.Empty);
                    accountId = memberAccount?.AccountId ?? string.Empty;
                    accountDescription = memberAccount?.Description ?? string.Empty;
                }

                replacement = $"{accountId}: {accountDescription}";
                break;
            }

        }

        return replacement;
    }

    private IEnumerable<LoanApplicationFeeRefund>? GetLoanApplicationFeeRefunds(Guid loanApplicationId)
    {
        _loanApplicationFeeRefunds ??= loanApplicationHelper.GetLoanApplicationFeeRefundsByLoanApplicationId(loanApplicationId);

        return _loanApplicationFeeRefunds;
    }

    private string GetMemberInterfacePlaceholderReplacementUsingReflection(string placeholder, FinancialInstitutionMember financialInstitutionMember)
    {
        // Gets the name of the property
        string GetPropertyName(string placeholderValue)
        {
            return placeholderValue
                .Split(InterfaceTextConstants.PLACEHOLDER_DOT_PROPERTY)
                .Last()
                .Trim(InterfaceTextConstants.PLACEHOLDER_END);
        }

        // Fraud Control Settings.
        if (placeholder.StartsWith(InterfaceTextConstants.PLACEHOLDER_FRAUD_CONTROL_PROPERTY, StringComparison.OrdinalIgnoreCase))
        {
            var fraudControlSetting = qCashContext.FraudControlSettings.FirstOrDefault();
            return ReflectionHelper.GetPropertyValueFromObject(placeholder, fraudControlSetting);
            //return ReflectionHelper.GetPropertyValueFromObject(placeholder, GetFraudControlSetting());
        }

        else if (placeholder.StartsWith(InterfaceTextConstants.PLACEHOLDER_Member_PROPERTY, StringComparison.OrdinalIgnoreCase))
        {
            if (GetPropertyName(placeholder).Equals("Email"))
            {
                if (!string.IsNullOrEmpty(financialInstitutionMember.Email))
                {
                    var pattern = @"(?<=[\w]{1})[\w-\._\+%]*(?=[\w]{1}@)";
                    return Regex.Replace(financialInstitutionMember.Email, pattern, m => new string('*', m.Length), RegexOptions.None, TimeSpan.FromSeconds(1));
                }
                else
                {
                    return "N/A";
                }
            }
            else if (GetPropertyName(placeholder).Equals("Phone"))
            {
                if (!string.IsNullOrEmpty(financialInstitutionMember.PhoneNumber) && financialInstitutionMember.PhoneNumber.Length > 4)
                {
                    return financialInstitutionMember.PhoneNumber.Substring(financialInstitutionMember.PhoneNumber.Length - 4);
                }
                else
                {
                    return "N/A";
                }
            }
        }

        return string.Empty;
    }

    /// <summary>
    /// Gets the member interface placeholder replacement using reflection.
    /// </summary>
    /// <param name="placeholder">The placeholder.</param>
    /// <param name="loanApplication">The loan application.</param>
    /// <param name="rxParamName">Name of the rx parameter.</param>
    /// <param name="products">The products.</param>
    /// <param name="calcResult">The calculate result.</param>
    /// <returns></returns>
    private string GetMemberInterfacePlaceholderReplacementUsingReflection(string placeholder, Data.Models.LoanApplication loanApplication, Regex rxParamName, IList<Data.Models.Product> products, IResultAllProducts calcResult)
    {
        // Gets the name of the property
        string GetPropertyName(string placeholderValue)
        {
            return placeholderValue
                .Split(InterfaceTextConstants.PLACEHOLDER_DOT_PROPERTY)
                .Last()
                .Trim(InterfaceTextConstants.PLACEHOLDER_END);
        }

        // This is selected product (case sensitive)
        if (placeholder.StartsWith(InterfaceTextConstants.PLACEHOLDER_PRODUCT_PROPERTY, StringComparison.OrdinalIgnoreCase))
        {
            if (GetPropertyName(placeholder).Equals("LoanOriginationFee"))
            {
                return loanApplicationHelper.CalculateLoanOriginationFee(loanApplication).ToString(InterfaceTextConstants.FORMAT_AMOUNT);
            }
            else
            {
                return ReflectionHelper.GetPropertyValueFromObject(placeholder, loanApplication.SelectedProduct);
            }
        }

        // This is specific product, search by abrv.
        else if (placeholder.StartsWith(InterfaceTextConstants.PLACEHOLDER_PRODUCT_BY_ABRV, StringComparison.OrdinalIgnoreCase))
        {
            // Get specific product abrv.
            var productSlug = rxParamName.Match(placeholder).ToString()
                .Trim(InterfaceTextConstants.PLACEHOLDER_ABRV_START)
                .Trim(InterfaceTextConstants.PLACEHOLDER_ABRV_END);

            var product = products.FirstOrDefault(p => p.Slug != null && p.Slug.Equals(productSlug));
            if (product != null)
            {
                if (GetPropertyName(placeholder).Equals("productAmount"))
                {
                    decimal qualifiedAmount = calcResult.QualifiedProducts.First(p => p.ProductId == loanApplication.SelectedProductId).DecisionEngine.QualifiedAmount.GetValueOrDefault();
                    return qualifiedAmount.ToString(InterfaceTextConstants.FORMAT_AMOUNT);
                }
                else if (GetPropertyName(placeholder).Equals("AutoPayRate"))
                {
                    decimal.TryParse(ReflectionHelper.GetPropertyValueFromObject(placeholder, product), out decimal autoPaymentRate);
                    return autoPaymentRate.ToString(InterfaceTextConstants.FORMAT_PERCENTAGE);
                }
                else
                {
                    return ReflectionHelper.GetPropertyValueFromObject(placeholder, product);
                }
            }
        }

        // Current Loan Application.
        else if (placeholder.StartsWith(InterfaceTextConstants.PLACEHOLDER_LA_PROPERTY, StringComparison.OrdinalIgnoreCase))
        {
            return ReflectionHelper.GetPropertyValueFromObject(placeholder, loanApplication);
        }

        // Current Decision Engine.
        else if (placeholder.StartsWith(InterfaceTextConstants.PLACEHOLDER_DE_PROPERTY, StringComparison.OrdinalIgnoreCase))
        {
            var de = calcResult.QualifiedProducts.First(p => p.ProductId == loanApplication.SelectedProductId).DecisionEngine;
            return ReflectionHelper.GetPropertyValueFromObject(placeholder, de);
        }

        // Fraud Control Settings.
        else if (placeholder.StartsWith(InterfaceTextConstants.PLACEHOLDER_FRAUD_CONTROL_PROPERTY, StringComparison.OrdinalIgnoreCase))
        {
            var fraudControlSetting = qCashContext.FraudControlSettings.FirstOrDefault();
            return ReflectionHelper.GetPropertyValueFromObject(placeholder, fraudControlSetting);
        }

        else if (placeholder.StartsWith(InterfaceTextConstants.PLACEHOLDER_Member_PROPERTY, StringComparison.OrdinalIgnoreCase))
        {
            if (GetPropertyName(placeholder).Equals("Email"))
            {
                if (!string.IsNullOrEmpty(loanApplication.FinancialInstitutionMember.Email))
                {
                    var pattern = @"(?<=[\w]{1})[\w-\._\+%]*(?=[\w]{1}@)";
                    return Regex.Replace(loanApplication.FinancialInstitutionMember.Email, pattern, m => new string('*', m.Length), RegexOptions.None, TimeSpan.FromSeconds(1));
                }
                else
                {
                    return "N/A";
                }
            }
            else if (GetPropertyName(placeholder).Equals("Phone"))
            {
                if (!string.IsNullOrEmpty(loanApplication.FinancialInstitutionMember.PhoneNumber) && loanApplication.FinancialInstitutionMember.PhoneNumber.Length > 4)
                {
                    return loanApplication.FinancialInstitutionMember.PhoneNumber.Substring(loanApplication.FinancialInstitutionMember.PhoneNumber.Length - 4);
                }
                else
                {
                    return "N/A";
                }
            }
        }

        return string.Empty;
    }

    /// <summary>
    /// Gets the product interface text abbreviation.
    /// </summary>
    /// <param name="productName">Name of the product.</param>
    /// <returns>Product description abbreviation.</returns>
    public string GetProductDescriptionAbrvForProduct(string productName) =>
        $"{productName.Replace(" ", "")}{InterfaceTextConstants.PRODUCT_DESCRIPTION_ABRV_SUFFIX}";

    /// <summary>
    /// Gets the automatic payment abrv for product.
    /// </summary>
    /// <param name="productSlug">The product slug.</param>
    /// <returns>Automatic payment abbreviation.</returns>
    public string GetAutoPaymentAbrvForProduct(string productSlug) =>
        $"{productSlug}{InterfaceTextConstants.AUTO_PAYMENT_ABRV_SUFFIX}";

    /// <summary>
    /// Gets the awareness checking share member accounts.
    /// </summary>
    /// <param name="financialInstitutionMemberId">The financial institution member identifier.</param>
    /// <returns></returns>
    private IEnumerable<MemberAccount> GetAwarenessCheckingShareMemberAccounts(Guid financialInstitutionMemberId)
    {
        _awarenessShareMemberAccounts ??= loanApplicationHelper.GetAwarenessCheckingShareMemberAccounts(financialInstitutionMemberId);
        return _awarenessShareMemberAccounts;
    }

    /// <summary>
    /// Gets the TILA response.
    /// </summary>
    /// <param name="loanApplicationId">The loan application identifier.</param>
    /// <returns>TILA response.</returns>
    private TilaResponse? GetTilaResponse(Guid loanApplicationId)
    {
        _tilaResponse ??= loanApplicationHelper.GetTILAResponsesByLoanApplicationId(loanApplicationId).FirstOrDefault();

        return _tilaResponse;
    }

    /// <summary>
    /// Gets the awareness loan member accounts fb.
    /// </summary>
    /// <param name="financialInstitutionMemberId">The financial institution member identifier.</param>
    /// <returns></returns>
    private IEnumerable<MemberAccount> GetAwarenessLoanMemberAccountsFB(Guid financialInstitutionMemberId)
    {
        _loanMemberAccountsFb ??= loanApplicationHelper.GetAwarenessLoanMemberAccountsPerLoanType(financialInstitutionMemberId, LookupService.LoanTypeFeeBasedAbbreviation);
        return _loanMemberAccountsFb;
    }

    /// <summary>
    /// Gets the awareness loan member accounts ib.
    /// </summary>
    /// <param name="financialInstitutionMemberId">The financial institution member identifier.</param>
    /// <returns></returns>
    private IEnumerable<MemberAccount> GetAwarenessLoanMemberAccountsIB(Guid financialInstitutionMemberId)
    {
        _loanMemberAccountsIb ??= loanApplicationHelper.GetAwarenessLoanMemberAccountsPerLoanType(financialInstitutionMemberId, LookupService.LoanTypeInterestBasedAbrv);
        return _loanMemberAccountsIb;
    }


    /// <summary>
    /// Gets the loan exclusion setting.
    /// </summary>
    /// <returns></returns>
    private LoanExclusionSetting? GetLoanExclusionSetting() => qCashContext.LoanExclusionSettings.FirstOrDefault();

    /// <summary>
    /// Gets the interface texts with translation.
    /// </summary>
    /// <param name="languageId">The language.</param>
    /// <param name="interfaceTexts">The interface texts, as strings (ex: "MemberInterfaceTextLookup.LoginPageCardText").</param>
    /// <returns></returns>
    public async Task<Dictionary<string, string>> GetInterfaceTextsWithTranslationAsync(Guid languageId, List<string> interfaceTexts)
    {
        //pass in an array of 'ItemTexts', which could be something like
        //MemberInterfaceTextLookup.LoginPageCardText,
        //MemberInterfaceStaticTextLookup.ExtAuthAccountNumber,

        //return a dictionary of lookup, <lang translation>

        //for each, get the Type (IMemberInterfaceText, IMemberInterfaceStaticText,
        //IMobileMemberInterfaceStaticText, IMobileMemberInterfaceText,
        //IPaymentOptionTitle, IMobilePaymentOptionTitle

        //if key has not already been added,
        //  if language is selected
        //      get translation item of this item (if exists)
        //          if 'FieldValue' not null, set item 'FieldValue' to translation
        //  add <abrv, fieldvalue> to dictionary

        //note that this assumes that each abrv is unique to all the tables above

        //rather than pass the actual IPocos around, since we no longer have the object-ified versions of all of these
        //and are sticking with core EF, we'll convert all of those to passing as strings and just parse them here.
        //should be faster than doing things like GetType, GetInterfaces, .Contains, etc.

        //rather than use the objects on the calling side, which would trigger the GetItemByAbrv, we pass in the strings
        //and do the lookup here.

        var formattedTexts = new Dictionary<string, string>();

        //pull them all down up front - consider having this cached
        var memberInterfaceTexts = new List<InterfaceDefaultText>();
        var memberInterfaceStaticTexts = new List<InterfaceDefaultText>();
        var paymentOptionTitles = new List<PaymentOptionTitle>();

        var memberInterfaceTextTableItems = interfaceTexts.Where(it =>
                it.StartsWith("MemberInterfaceText.", StringComparison.OrdinalIgnoreCase)
                || it.StartsWith("MemberInterfaceTextLookup.", StringComparison.OrdinalIgnoreCase))
            .Select(it => it.Split('.')[1])
            .ToList();
        if (memberInterfaceTextTableItems.Any())
        {
            // This should include the default english content as well as translated content.
            memberInterfaceTexts = await qCashContext.InterfaceDefaultTexts
                .Where(idt => !idt.IsStaticText
                    && memberInterfaceTextTableItems.Contains(idt.Abrv)
                ).ToListAsync();
        }

        var memberInterfaceStaticTextTableItems = interfaceTexts.Where(it =>
                it.StartsWith("MemberInterfaceStaticText", StringComparison.OrdinalIgnoreCase)
                || it.StartsWith("MemberInterfaceStaticTextLookup", StringComparison.OrdinalIgnoreCase))
            .Select(it => it.Split('.')[1]).ToList();
        if (interfaceTexts.Any(o => o.StartsWith("MemberInterfaceStaticText", StringComparison.OrdinalIgnoreCase)))
        {
            memberInterfaceStaticTexts = await qCashContext.InterfaceDefaultTexts
                .Where(idt => idt.IsStaticText
                              && memberInterfaceStaticTextTableItems.Contains(idt.Abrv))
                .ToListAsync();
        }
        if (interfaceTexts.Any(o => o.StartsWith("PaymentOptionTitle", StringComparison.OrdinalIgnoreCase)))
        {
            paymentOptionTitles = qCashContext.PaymentOptionTitles.Include(o => o.TranslationPaymentOptionTitles).ToList();
        }

        //note: removed all mobile (mobileMemberInterfaceStaticTexts, mobileMemberInterfaceTexts, mobilePaymentOptionTitles)
        if (interfaceTexts.Count <= 0)
        {
            return formattedTexts;
        }

        var iotIds = memberInterfaceTexts.Select(idt => idt.Id);
        iotIds = iotIds.Union(memberInterfaceStaticTexts.Select(idt => idt.Id)).ToList();
        var overrides = await qCashContext.InterfaceOverrideTexts.AsNoTracking()
            .Where(iot => iotIds.Contains(iot.InterfaceDefaultTextId))
            .Select(iot => new { iot.InterfaceDefaultTextId, iot.FieldValue })
            .ToListAsync();
        // TODO: We do not have access to the FinancialInstitutionId in the method.
        // Double-check that the query filter is working correctly to only pull this FI's data.

        var languages = qCashContext.Languages.Where(l => l.Id == languageId || l.Name == LanguageEnum.English.Name).ToList();
        var language = languages.SingleOrDefault(l => l.Id == languageId);
        var englishLanguage = languages.SingleOrDefault(l => l.Name == LanguageEnum.English.Name);
        englishLanguage.ThrowIfNull("Cannot find the English language");

        foreach (var interfaceText in interfaceTexts)
        {
            var textCategory = interfaceText.Split('.')[0];
            var textAbrv = interfaceText.Split('.')[1];

            if (formattedTexts.ContainsKey(textAbrv))
            {
                continue;
            }

            var initialFieldValue = string.Empty;

            if (textCategory is "MemberInterfaceText" or "MemberInterfaceTextLookup")
            {
                var item = memberInterfaceTexts
                    .SingleOrDefault(idt => idt.Abrv.Equals(textAbrv) && idt.LanguageId == languageId && !idt.IsStaticText);
                // If we found no data at the requested language.  fallback to English
                item ??= memberInterfaceTexts
                    .SingleOrDefault(idt => idt.Abrv.Equals(textAbrv) && idt.LanguageId == englishLanguage.Id && !idt.IsStaticText);
                initialFieldValue = item?.FieldValue;
                if (item != null)
                {
                    var overrideItem = overrides.SingleOrDefault(o => o.InterfaceDefaultTextId == item.Id);
                    initialFieldValue = overrideItem?.FieldValue ?? initialFieldValue;
                }
            }
            else if (textCategory is "MemberInterfaceStaticText" or "MemberInterfaceStaticTextLookup")
            {
                var item = memberInterfaceStaticTexts
                    .SingleOrDefault(idt => idt.Abrv.Equals(textAbrv) && idt.LanguageId == languageId && idt.IsStaticText);
                // If we found no data at the requested language.  fallback to English
                item ??= memberInterfaceStaticTexts
                    .SingleOrDefault(idt => idt.Abrv.Equals(textAbrv) && idt.LanguageId == englishLanguage.Id && idt.IsStaticText);
                initialFieldValue = item?.FieldValue;
                if (item != null)
                {
                    var overrideItem = overrides.SingleOrDefault(o => o.InterfaceDefaultTextId == item.Id);
                    initialFieldValue = overrideItem?.FieldValue ?? initialFieldValue;
                }
            }
            else if (textCategory is "PaymentOptionTitle" or "PaymentOptionTitleLookup")
            {
                var item = paymentOptionTitles.SingleOrDefault(p => p.Abrv.Equals(textAbrv));
                var translation = item?.TranslationPaymentOptionTitles.FirstOrDefault(p => p.LanguageCode.Equals(language?.LanguageCode));
                if (translation != null && !string.IsNullOrEmpty(translation.FieldValue))
                {
                    initialFieldValue = translation.FieldValue;
                }
                else
                {
                    initialFieldValue = item?.FieldValue;
                }
            }

            formattedTexts.Add(textAbrv, initialFieldValue ?? string.Empty);
        }
        return formattedTexts;
    }

    [GeneratedRegex("{.*?}", RegexOptions.None, matchTimeoutMilliseconds: 1000)]
    private static partial Regex MemberInterfacePlaceholderRegex();
    [GeneratedRegex(@"\[.*?\]", RegexOptions.None, matchTimeoutMilliseconds: 1000)]
    private static partial Regex ParamNameRegex();


    //Original QC6 implementation of GetInterfaceTextsWithTranslation is included here as a reference -
    #region oldGetInterfaceTextsWithTranslationImplementation
    //public Dictionary<string, string> GetInterfaceTextsWithTranslation(Guid languageId, params IPoco[] memberIT)
    //{
    //    Dictionary<string, string> formatedTexts = new Dictionary<string, string>();

    //    if (memberIT != null && memberIT.Length > 0)
    //    {
    //        foreach (var item in memberIT)
    //        {
    //            var type = item.GetType();
    //            var interfaces = type.GetInterfaces();

    //            if (interfaces.Contains(typeof(IMemberInterfaceText)) && !formatedTexts.ContainsKey(((IMemberInterfaceText)item).Abrv))
    //            {
    //                if (!string.IsNullOrEmpty(language))
    //                {
    //                    if (((IMemberInterfaceText)item).Translation_MemberInterfaceText.Count > 0)
    //                    {
    //                        var translation = ((IMemberInterfaceText)item).Translation_MemberInterfaceText.FirstOrDefault(p => p.LanguageCode.Equals(language));
    //                        if (translation != null && !string.IsNullOrEmpty(translation.FieldValue))
    //                            ((IMemberInterfaceText)item).FieldValue = translation.FieldValue;
    //                    }
    //                }
    //                formatedTexts.Add(((IMemberInterfaceText)item).Abrv, ((IMemberInterfaceText)item).FieldValue);
    //            }
    //            else if (interfaces.Contains(typeof(IMemberInterfaceStaticText)) && !formatedTexts.ContainsKey(((IMemberInterfaceStaticText)item).Abrv))
    //            {
    //                if (!string.IsNullOrEmpty(language))
    //                {
    //                    if (((IMemberInterfaceStaticText)item).Translation_MemberInterfaceStaticText.Count > 0)
    //                    {
    //                        var translation = ((IMemberInterfaceStaticText)item).Translation_MemberInterfaceStaticText.FirstOrDefault(p => p.LanguageCode.Equals(language));
    //                        if (translation != null && !string.IsNullOrEmpty(translation.FieldValue))
    //                            ((IMemberInterfaceStaticText)item).FieldValue = translation.FieldValue;
    //                    }
    //                }
    //                formatedTexts.Add(((IMemberInterfaceStaticText)item).Abrv, ((IMemberInterfaceStaticText)item).FieldValue);
    //            }
    //            else if (interfaces.Contains(typeof(IMobileMemberInterfaceStaticText)) && !formatedTexts.ContainsKey(((IMobileMemberInterfaceStaticText)item).Abrv))
    //            {
    //                if (!string.IsNullOrEmpty(language))
    //                {
    //                    if (((IMobileMemberInterfaceStaticText)item).Translation_MobileMemberInterfaceStaticText.Count > 0)
    //                    {
    //                        var translation = ((IMobileMemberInterfaceStaticText)item).Translation_MobileMemberInterfaceStaticText.FirstOrDefault(p => p.LanguageCode.Equals(language));
    //                        if (translation != null && !string.IsNullOrEmpty(translation.FieldValue))
    //                            ((IMobileMemberInterfaceStaticText)item).FieldValue = translation.FieldValue;
    //                    }
    //                }
    //                formatedTexts.Add(((IMobileMemberInterfaceStaticText)item).Abrv, ((IMobileMemberInterfaceStaticText)item).FieldValue);
    //            }
    //            else if (interfaces.Contains(typeof(IMobileMemberInterfaceText)) && !formatedTexts.ContainsKey(((IMobileMemberInterfaceText)item).Abrv))
    //            {
    //                if (!string.IsNullOrEmpty(language))
    //                {
    //                    if (((IMobileMemberInterfaceText)item).Translation_MobileMemberInterfaceText.Count > 0)
    //                    {
    //                        var translation = ((IMobileMemberInterfaceText)item).Translation_MobileMemberInterfaceText.FirstOrDefault(p => p.LanguageCode.Equals(language));
    //                        if (translation != null && !string.IsNullOrEmpty(translation.FieldValue))
    //                            ((IMobileMemberInterfaceText)item).FieldValue = translation.FieldValue;
    //                    }
    //                }
    //                formatedTexts.Add(((IMobileMemberInterfaceText)item).Abrv, ((IMobileMemberInterfaceText)item).FieldValue);
    //            }
    //            else if (interfaces.Contains(typeof(IPaymentOptionTitle)) && !formatedTexts.ContainsKey(((IPaymentOptionTitle)item).Abrv))
    //            {
    //                if (!string.IsNullOrEmpty(language))
    //                {
    //                    if (((IPaymentOptionTitle)item).Translation_PaymentOptionTitle.Count > 0)
    //                    {
    //                        var translation = ((IPaymentOptionTitle)item).Translation_PaymentOptionTitle.FirstOrDefault(p => p.LanguageCode.Equals(language));
    //                        if (translation != null && !string.IsNullOrEmpty(translation.FieldValue))
    //                            ((IPaymentOptionTitle)item).FieldValue = translation.FieldValue;
    //                    }
    //                }
    //                formatedTexts.Add(((IPaymentOptionTitle)item).Abrv, ((IPaymentOptionTitle)item).FieldValue);
    //            }
    //            else if (interfaces.Contains(typeof(IMobilePaymentOptionTitle)) && !formatedTexts.ContainsKey(((IMobilePaymentOptionTitle)item).Abrv))
    //            {
    //                if (!string.IsNullOrEmpty(language))
    //                {
    //                    if (((IMobilePaymentOptionTitle)item).Translation_MobilePaymentOptionTitle.Count > 0)
    //                    {
    //                        var translation = ((IMobilePaymentOptionTitle)item).Translation_MobilePaymentOptionTitle.FirstOrDefault(p => p.LanguageCode.Equals(language));
    //                        if (translation != null && !string.IsNullOrEmpty(translation.FieldValue))
    //                            ((IMobilePaymentOptionTitle)item).FieldValue = translation.FieldValue;
    //                    }
    //                }
    //                formatedTexts.Add(((IMobilePaymentOptionTitle)item).Abrv, ((IMobilePaymentOptionTitle)item).FieldValue);
    //            }
    //        }
    //    }

    //    return formatedTexts;
    //}
    #endregion

    /// <summary>
    /// Get a single member interface text value
    /// This method takes into account the requested language with fallback and overrides if any
    /// </summary>
    /// <param name="abrv"></param>
    /// <param name="isStaticText"></param>
    /// <param name="languageId"></param>
    /// <returns></returns>
    public async Task<string?> GetTextAsync(string abrv, bool isStaticText, Guid? languageId)
    {
        var list = await qCashContext.InterfaceDefaultTexts
            .Where(idt => idt.Abrv == abrv
                          && idt.IsStaticText == isStaticText
                          && (idt.LanguageId == languageId || idt.Language.Name == LanguageEnum.English.Name))
            .Select(idt => new
            {
                idt.Id,
                idt.FieldValue,
                LanguageName = idt.Language.Name,
                idt.LanguageId,
                Override = idt.InterfaceOverrideTexts.Select(a => a.FieldValue).FirstOrDefault(),
            })
            .ToListAsync();

        // first try to get data for the requested language.
        var record = list.FirstOrDefault(p => p.LanguageId == languageId);

        // If the data doesn't exist for the requested language, get the English record as a fallback
        record ??= list.FirstOrDefault(p => p.LanguageName == LanguageEnum.English.Name);

        // prefer the override value, if it exists
        var fieldValue = record?.Override ?? record?.FieldValue;
        return fieldValue;
    }

    //Note: this originally lived in LoanApplicationServiceReports
    public async Task<string> GetFundingAccountTypeAsync(Data.Models.LoanApplication la, Guid languageId)
    {
        if (!la.DefaultAccountForTransactionId.HasValue)
        {
            return "N/A";
        }

        var accForTransaction = la.DefaultAccountForTransaction;
        if (la.SelectedAccountForTransaction != null)
        {
            accForTransaction = la.SelectedAccountForTransaction;
        }

        if (accForTransaction != null)
        {
            //var lkpMemberInterfaceStaticText = _lookupFactory.MemberInterfaceStaticTextlookupCreate();
            if (qCashContext.CheckingAccountTypes.Select(p => p.Value).ToList().IsVersionAllowed(accForTransaction.Type, accForTransaction.SubType ?? string.Empty))
            {
                var fieldValue = await GetTextAsync(InterfaceDefaultTextsEnum.CheckingType_Static.Abrv, true, languageId);
                return fieldValue ?? "";
            }
            if (qCashContext.SavingAccountTypes.Select(p => p.Value).ToList().IsVersionAllowed(accForTransaction.Type, accForTransaction.SubType ?? string.Empty))
            {
                var fieldValue = await GetTextAsync(InterfaceDefaultTextsEnum.SavingType_Static.Abrv, true, languageId);
                return fieldValue ?? "";
            }
        }
        return "N/A";
    }

}
