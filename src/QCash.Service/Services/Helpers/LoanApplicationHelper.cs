using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Classes.DecisionManager.Interface;
using QCash.Service.Classes.DecisionManager;
using QCash.Service.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Models.Enums;
using QCash.LoanApplication;
using Enums = QCash.Service.Models.Core.Enums;
using LoanType = QCash.Data.Models.LoanType;
using Score = QCash.Service.Classes.DecisionManager.Score;
using QCash.Service.Classes;
using QCash.Service.Models.DecisionEngine;
using static QCash.Service.Services.LoanApplication.Enums;

namespace QCash.Service.Services.Helpers;

public class LoanApplicationHelper(
    QCashContext qCashContext,
    IDecisionManagerService decisionManagerService,
    IDistributedCache cacheProvider) : ILoanApplicationHelper
{
    private string FiSlug => qCashContext.FinancialInstitutions.FirstOrDefault()?.Slug ?? "";
    private const string ResultAllProductsKey = "ResultAllProducts";
    /// <summary>
    /// Gets the number of loans for financial institution member.
    /// </summary>
    /// <param name="loanMemberAccounts">The loan member accounts.</param>
    /// <returns>Number of loans for financial institution member</returns>
    public int GetNumberOfLoansForFinancialInstitutionMember(IEnumerable<MemberAccount>? loanMemberAccounts)
    {
        if (loanMemberAccounts != null)
        {
            return loanMemberAccounts.Count();
        }

        return 0;
    }

    /// <summary>
    /// Gets the total amount of loans for financial institution member.
    /// </summary>
    /// <param name="financialInstitutionMemberId">The financial institution member identifier.</param>
    /// <param name="loanTypeLookupAbrv">The loan type lookup abrv.</param>
    /// <returns>Total amount of loans for financial institution member.</returns>
    public decimal GetTotalAmountOfLoansForFinancialInstitutionMember(Guid financialInstitutionMemberId, string loanTypeLookupAbrv)
    {
        if (financialInstitutionMemberId == Guid.Empty)
        {
            throw new ArgumentException(null, nameof(financialInstitutionMemberId));
        }

        if (string.IsNullOrEmpty(loanTypeLookupAbrv))
        {
            throw new ArgumentException(null, nameof(loanTypeLookupAbrv));
        }

        var loanMemberAccounts = GetAwarenessLoanMemberAccountsPerLoanType(financialInstitutionMemberId, loanTypeLookupAbrv);
        return GetTotalAmountOfLoansForFinancialInstitutionMember(loanMemberAccounts);
    }

    /// <summary>
    /// Gets the total amount of loans for financial institution member.
    /// </summary>
    /// <param name="loanMemberAccounts">The loan member accounts.</param>
    /// <returns>Total abount of loans for financial institution member.</returns>
    public decimal GetTotalAmountOfLoansForFinancialInstitutionMember(IEnumerable<MemberAccount>? loanMemberAccounts)
    {
        if (loanMemberAccounts != null && loanMemberAccounts.Any())
        {
            return loanMemberAccounts.Where(p => p.OriginalBalance.HasValue).Sum(p => p.OriginalBalance!.Value);
        }

        return 0;
    }

    /// <summary>
    /// Gets the total interest paid for the financial institution member.
    /// </summary>
    /// <param name="loanMemberAccounts">The loan member accounts.</param>
    /// <returns>Total interest paid for provided member accounts.</returns>
    public decimal GetTotalInterestPaidForFinancialInstitutionMember(IEnumerable<MemberAccount>? loanMemberAccounts)
    {
        if (loanMemberAccounts != null && loanMemberAccounts.Any())
        {
            return loanMemberAccounts.Where(p => p.InterestPaid.HasValue).Sum(p => p.InterestPaid!.Value);
        }

        return 0;
    }

    /// <summary>
    /// Gets the total interest paid for the financial institution member.
    /// </summary>
    /// <param name="financialInstitutionMemberId">The financial institution member identifier.</param>
    /// <param name="loanTypeLookupAbrv">The loan type lookup abrv.</param>
    /// <returns>Total interest paid for Financial Institution Member.</returns>
    public decimal GetTotalInterestPaidForFinancialInstitutionMember(Guid financialInstitutionMemberId, string loanTypeLookupAbrv)
    {
        if (financialInstitutionMemberId == Guid.Empty)
        {
            throw new ArgumentException(null, nameof(financialInstitutionMemberId));
        }

        if (string.IsNullOrEmpty(loanTypeLookupAbrv))
        {
            throw new ArgumentException(null, nameof(loanTypeLookupAbrv));
        }

        var loanMemberAccounts = GetAwarenessLoanMemberAccountsPerLoanType(financialInstitutionMemberId, loanTypeLookupAbrv);
        return GetTotalInterestPaidForFinancialInstitutionMember(loanMemberAccounts);
    }

    /// <summary>
    /// Gets the total NSF count for financial institution member.
    /// </summary>
    /// <param name="awarenesShareMemberAccounts">The awarenes share member accounts.</param>
    /// <returns>Total NSF count for Financial Institution Member.</returns>
    public int GetTotalNSFCountForFinancialInstitutionMember(IEnumerable<MemberAccount>? awarenesShareMemberAccounts)
    {
        if (awarenesShareMemberAccounts != null && awarenesShareMemberAccounts.Any())
        {
            return awarenesShareMemberAccounts.Where(p => p.NsfCount.HasValue).Sum(p => p.NsfCount!.Value);
        }

        return 0;
    }


    /// <summary>
    /// Gets the total odp count for financial institution member.
    /// </summary>
    /// <param name="financialInstitutionMemberId">The financial institution member identifier.</param>
    /// <returns>Total ODP count for Financial Institution Member.</returns>
    public int GetTotalODPCountForFinancialInstitutionMember(Guid financialInstitutionMemberId)
    {
        if (financialInstitutionMemberId == Guid.Empty)
        {
            throw new ArgumentException(null, nameof(financialInstitutionMemberId));
        }

        var shareMemberAccounts = GetAwarenessCheckingShareMemberAccounts(financialInstitutionMemberId);
        return GetTotalODPCountForFinancialInstitutionMember(shareMemberAccounts);
    }

    /// <summary>
    /// Gets the total odp count for financial institution member.
    /// </summary>
    /// <param name="awarenessShareMemberAccounts">The awareness share member accounts.</param>
    /// <returns>Total ODP count for Financial Institution Member.</returns>
    public int GetTotalODPCountForFinancialInstitutionMember(IEnumerable<MemberAccount>? awarenessShareMemberAccounts)
    {
        if (awarenessShareMemberAccounts != null && awarenessShareMemberAccounts.Any())
        {
            return awarenessShareMemberAccounts.Where(p => p.OdpCount.HasValue).Sum(p => p.OdpCount!.Value);
        }

        return 0;
    }

    public DateTime CalculateNextPossibleLoanDate(string memberIdHash, string loanStatusAbrv, DateTime startTime, DateTime endTime, int numberOfDeniedLoans)
    {
        var clientTimeZone = qCashContext.Settings.FirstOrDefault()?.TimeZone ?? string.Empty;

        var result = qCashContext.LoanApplications
            .Where(p =>
                p.FinancialInstitutionMember.MemberIdHash == memberIdHash &&
                p.LoanStatus != null &&
                p.LoanStatus.Abrv == loanStatusAbrv &&
                p.DateCreatedUtc >= startTime &&
                p.DateCreatedUtc <= endTime)
            .OrderByDescending(p => p.DateCreatedUtc)
            .Take(numberOfDeniedLoans)
            .OrderBy(x => x.DateCreatedUtc)
            .Select(x => x.DateCreatedUtc)
            .FirstOrDefault();

        return FromUtc(result, TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone));
    }

    /// <summary>
    /// Gets the loan application ResultAllProducts from cache. Note that if not cached, the EF objects are tracked.
    /// </summary>
    /// <param name="loanApplicationId">The loan application identifier.</param>
    /// <returns><see cref="IResultAllProducts"/> from cache.</returns>
    public async Task<IResultAllProducts?> GetLoanApplicationResultAllProductsAsync(Guid loanApplicationId)
    {
        var fiSlug = FiSlug;
        //var options = new DistributedCacheEntryOptions() { AbsoluteExpiration = absoluteExpiration };
        //await cacheProvider.SetStringAsync($"{fiSlug}-CoreStatus", JsonConvert.SerializeObject(cacheEntry), options);

        var key = $"{ResultAllProductsKey}-{fiSlug}-{loanApplicationId.ToString()}";
        //var cacheAvailable = true;
        try
        {
            var value = await cacheProvider.GetStringAsync(key).ConfigureAwait(false);
            var deserializedValue = value == null ? null : JsonConvert.DeserializeObject<ResultAllProducts>(value, GetJsonSettings());
            if (deserializedValue != null)
            {
                return deserializedValue;
            }
        }
        catch
        {
            //NewRelicLogger.LogException(ex, ("fiSlug", FiSlug));
            //logger.LogError(ex, "fiSlug {FiSlug}", FiSlug);
            //cacheAvailable = false;
        }

        var model = await decisionManagerService.GetCalculatorResultAsync(loanApplicationId);

        //leaving this for now as we may need to switch GetCalculatorResult to return nullable
        if (model != null)    //&& cacheAvailable - removed
        {
            await CacheLoanApplicationResultAllProductsAsync(model, loanApplicationId);
        }

        return model;
    }

    /// <summary>
    /// Caches the loan application ResultAllProducts.
    /// </summary>
    /// <param name="resultAllProducts">The ResultAllProducts.</param>
    /// <param name="loanApplicationId">The loan application identifier.</param>
    public async Task CacheLoanApplicationResultAllProductsAsync(IResultAllProducts resultAllProducts, Guid loanApplicationId)
    {
        var fiSlug = FiSlug;
        var key = $"{ResultAllProductsKey}-{fiSlug}-{loanApplicationId.ToString()}";

        var results = resultAllProducts.Result.Where(p => p.IsStatistical is null or false);
        List<IResult> newResult = new List<IResult>();

        //Clear complex types before caching.
        //The direct approach worked with the QC6 repository model, but would cause issues with EF.
        //To create a new "detached" object, we'll do a quick serialize-deserialize.
        var serialized = JsonConvert.SerializeObject(results.ToList(), GetJsonSettings());
        var deserialized = JsonConvert.DeserializeObject<List<IResult>>(serialized) ?? new List<IResult>();

        foreach (var result in deserialized)
        {
            result.DecisionModel = null;
            result.ModelSelector = null;
            result.Logs = new List<LoanApplicationLogDetail>();
            result.Errors = new List<string>();
            result.AANResultList = new List<string>();
            result.TransformationResultList = new List<string>();
            result.ParametersResultList = new List<string>();
            result.TransformationResultList = new List<string>();

            if (result.Product != null)
            {
                result.Product.ProductDecisionModels = new List<ProductDecisionModel>();
                result.Product.FinancialInstitution = new FinancialInstitution();
                result.Product.LoanType = new LoanType();
                result.Product.InterfaceDefaultTexts = new List<InterfaceDefaultText>();
                result.Product.ModelSelectors = new List<ModelSelector>();
                result.Product.CalcResults = new List<CalcResult>();
                result.Product.GlobalExclusionStates = new List<GlobalExclusionState>();
                result.Product.LoanAmountQualifiers = new List<LoanAmountQualifier>();
                result.Product.LoanApplications = new List<Data.Models.LoanApplication>();
                result.Product.LoanFees = new List<LoanFee>();
                result.Product.ModelTestRunResults = new List<ModelTestRunResult>();
                result.Product.ProductDecisionModels = new List<ProductDecisionModel>();
                result.Product.ProductScores = new List<ProductScore>();
                result.Product.StateRestrictedInterestRates = new List<StateRestrictedInterestRate>();
            }

            result.MemberInGoodStanding.AANReasons = new List<AanReasonForCalcDto>();
            result.MemberInGoodStanding.Errors = new List<string>();
            result.MemberInGoodStanding.Logs = new List<LoanApplicationLogDetail>();

            result.Eligibility.AANReasons = new List<AanReasonForCalcDto>();
            result.Eligibility.Errors = new List<string>();
            result.Eligibility.Logs = new List<LoanApplicationLogDetail>();

            result.DecisionEngine.AANReasons = new List<AanReasonForCalcDto>();
            result.DecisionEngine.Errors = new List<string>();
            result.DecisionEngine.Logs = new List<LoanApplicationLogDetail>();
            result.DecisionEngine.ScorePoints = new List<DecisionEngineScorePoint>();

            newResult.Add(result);
        }

        var resultAll = new ResultAllProducts
        {
            Result = newResult,
        };

        //if (CacheManager.Cache.Contains(key))
        //    CacheManager.Cache.Remove(key);
        //var offset = new DateTimeOffset(DateTime.UtcNow).AddMinutes(15);
        //var json = JsonConvert.SerializeObject(resultAll, GetJsonSettings());
        //CacheManager.Cache.Add(key, json, offset);

        var absoluteExpiration = DateTimeOffset.UtcNow.AddMinutes(15);
        var options = new DistributedCacheEntryOptions() { AbsoluteExpiration = absoluteExpiration };
        await cacheProvider.SetStringAsync(key, JsonConvert.SerializeObject(resultAll, GetJsonSettings()), options);
    }

    /// <summary>
    /// Invalidates the loan application ResultAllProducts form cache.
    /// </summary>
    /// <param name="loanApplicationId">The loan application identifier.</param>
    public async Task InvalidateLoanApplicationResultAllProductsAsync(Guid loanApplicationId)
    {
        var fiSlug = FiSlug;
        var key = $"{ResultAllProductsKey}-{fiSlug}-{loanApplicationId.ToString()}";

        await cacheProvider.RemoveAsync(key);

    }

    /// <summary>
    /// Gets a list of TilaResponse items by specified LoanApplicationId.
    /// </summary>
    /// <param name="value">Value.</param>
    /// <returns>List of elements.</returns>
    public IList<TilaResponse> GetTILAResponsesByLoanApplicationId(Guid value)
    {
        var result = qCashContext.TilaResponses.Where(p => p.LoanApplicationId.Equals(value)).OrderBy(p => p.Id).ToList();
        return result;
    }

    /// <summary>
    /// Gets a list of LoanApplicationFeeRefund items by specified LoanApplicationId.
    /// </summary>
    /// <param name="loanApplicationId">loanApplicationId.</param>
    /// <returns>List of elements.</returns>
    public IList<LoanApplicationFeeRefund> GetLoanApplicationFeeRefundsByLoanApplicationId(Guid loanApplicationId)
    {
        var result = qCashContext.LoanApplicationFeeRefunds
            .Where(p => p.LoanApplicationId.Equals(loanApplicationId))
            .OrderBy(p => p.Id).ToList();
        return result;
    }

    /// <summary>
    /// Calculates loan origination fee.
    /// </summary>
    /// <param name="loanApplication">ILoanApplication entity.</param>
    public decimal CalculateLoanOriginationFee(Data.Models.LoanApplication loanApplication)
    {
        var laCampaignCodes = loanApplication.LoanApplicationCampaignCodes;

        return laCampaignCodes is { Count: > 0 }
            ? laCampaignCodes.First().LoanOriginationFee
            : CalculateLoanOriginationFeePerMilitaryStatus(loanApplication);
    }

    public decimal CalculateLoanOriginationFee(Data.Models.LoanApplication loanApplication, Guid productId, decimal loanAmount)
    {
        var laCampaignCodes = loanApplication.LoanApplicationCampaignCodes;

        return laCampaignCodes is { Count: > 0 }
            ? laCampaignCodes.First().LoanOriginationFee
            : CalculateLoanOriginationFeePerMilitaryStatus(loanApplication, productId, loanAmount);
    }

    /// <summary>
    /// Calculates loan origination fee per military status. Like its sibling but with a specified product and loanAmount.
    /// </summary>
    public decimal CalculateLoanOriginationFeePerMilitaryStatus(Data.Models.LoanApplication loanApplication, Guid productId, decimal loanAmount)
    {
        decimal loanOriginationFee;

        var product = qCashContext.Products.FirstOrDefault(o => o.Id == productId);

        if (product == null)
        {
            return 0;
        }
        if (loanApplication.ActiveDuty.GetValueOrDefault())
        {
            loanOriginationFee = product.MaprLoanOriginationFee;
        }
        else
        {
            if (!product.VariableLoanOriginationFee)
            {
                loanOriginationFee = product.LoanOriginationFee;
            }
            else
            {
                var scoreTypeLoanOriginationFee = qCashContext.ScoreTypes.Include(o => o.Scores).ThenInclude(o => o.ProductScores).SingleOrDefault(p => p.Abrv == "LoanOriginationFee");
                var scores = scoreTypeLoanOriginationFee?.Scores.Where(p => p.ProductScores.Any(d => d.ProductId == productId)).ToList();

                ScoreList scoreList = ConvertScoresToScoreList(scores);

                var variableLoanOriginationFee = Convert.ToInt32(GetScoreValueBetweenMargins(scoreList, loanAmount));
                loanOriginationFee = variableLoanOriginationFee;
            }
        }

        return loanOriginationFee;
    }

    /// <summary>
    /// Calculates loan origination fee per military status.
    /// </summary>
    /// <param name="loanApplication">LoanApplication entity.</param>
    public decimal CalculateLoanOriginationFeePerMilitaryStatus(Data.Models.LoanApplication loanApplication)
    {
        decimal loanOriginationFee;

        if (loanApplication.SelectedProduct == null)
        {
            throw new Exception("Unable to calculate loan origination fee; no Selected Product.");
        }

        if (loanApplication.ActiveDuty.GetValueOrDefault())
        {
            loanOriginationFee = loanApplication.SelectedProduct.MaprLoanOriginationFee;
        }
        else
        {
            if (!loanApplication.SelectedProduct.VariableLoanOriginationFee)
            {
                loanOriginationFee = loanApplication.SelectedProduct.LoanOriginationFee;
            }
            else
            {
                var scoreTypeLoanOriginationFee = qCashContext.ScoreTypes.Include(o => o.Scores).ThenInclude(o => o.ProductScores).SingleOrDefault(p => p.Abrv == "LoanOriginationFee");
                var scores = scoreTypeLoanOriginationFee?.Scores.Where(p => p.ProductScores.Any(d => d.ProductId == loanApplication.SelectedProduct.Id)).ToList();

                //convert to ScoreList - seems like we still need to do this
                ScoreList scoreList = ConvertScoresToScoreList(scores);

                //var scoreTypeLookup = _lookupFactory.ScoreTypelookupCreate();
                //var varialbeProcFeeScores = scoreTypeLookup.LoanOriginationFee.Score.Where(p => p.ProductScore.Any(d => d.ProductId == loanApplication.SelectedProduct.Id));
                //var sl = Mapper.Map<ScoreList>(varialbeProcFeeScores);

                var variableLoanOriginationFee = Convert.ToInt32(GetScoreValueBetweenMargins(scoreList, loanApplication.LoanApplicationMemberSelections.FirstOrDefault()?.AmountBorrowed ?? 0));
                loanOriginationFee = variableLoanOriginationFee;
            }
        }

        return loanOriginationFee;
    }

    /// <summary>
    /// Gets the awareness share member accounts.
    /// </summary>
    /// <param name="financialInstitutionMemberId">The financial institution member identifier.</param>
    /// <returns></returns>
    /// <exception cref="ArgumentException">
    /// financialInstitutionMemberId
    /// </exception>
    public IEnumerable<MemberAccount> GetAwarenessCheckingShareMemberAccounts(Guid financialInstitutionMemberId)
    {
        if (financialInstitutionMemberId == Guid.Empty)
        {
            throw new ArgumentException(null, nameof(financialInstitutionMemberId));
        }

        var financialCoachingSettings = qCashContext.FinancialCoachingSettings.FirstOrDefault();

        //var appCtx = ApplicationContextFactory.GetApplicationContext();
        //var financialCoachingSettings = SettingService.GetFinancialCoachingSettingsByFinancialInstitutionId(appCtx.ApplicationId).FirstOrDefault();

        return GetFilteredCheckingShareMemberAccounts(financialInstitutionMemberId, financialCoachingSettings?.AwarenessPeriod ?? 0);
    }

    /// <summary>
    /// Gets the loan member accounts per product's loan type in defined awareness period.
    /// </summary>
    /// <param name="financialInstitutionMemberId">The financial institution member identifier.</param>
    /// <param name="loanTypeLookupAbrv">The loan type lookup abrv.</param>
    /// <returns></returns>
    public IEnumerable<MemberAccount> GetAwarenessLoanMemberAccountsPerLoanType(Guid financialInstitutionMemberId, string loanTypeLookupAbrv)
    {
        if (financialInstitutionMemberId == Guid.Empty)
        {
            throw new ArgumentException(null, nameof(financialInstitutionMemberId));
        }

        if (string.IsNullOrEmpty(loanTypeLookupAbrv))
        {
            throw new ArgumentException(null, nameof(loanTypeLookupAbrv));
        }

        var financialCoachingSettings = qCashContext.FinancialCoachingSettings.FirstOrDefault();

        //var appCtx = ApplicationContextFactory.GetApplicationContext();
        //var financialCoachingSettings = SettingService.GetFinancialCoachingSettingsByFinancialInstitutionId(appCtx.ApplicationId).FirstOrDefault();

        return GetFilteredLoanMemberAccountsPerLoanType(financialInstitutionMemberId, financialCoachingSettings?.AwarenessPeriod ?? 0, loanTypeLookupAbrv);
    }

    private IEnumerable<MemberAccount> GetFilteredLoanMemberAccountsPerLoanType(Guid financialInstitutionMemberId, int months, string loanTypeLookupAbrv)
    {
        var loanTypeFb = qCashContext.LoanTypes.SingleOrDefault(p => p.Abrv == nameof(LoanTypeAbbreviation.FB))?.Id ?? Guid.Empty;
        var loanTypeIb = qCashContext.LoanTypes.SingleOrDefault(p => p.Abrv == nameof(LoanTypeAbbreviation.IB))?.Id ?? Guid.Empty;
        var loanCatUo = qCashContext.LoanCategories.SingleOrDefault(p => p.Abrv == "UO")?.Id ?? Guid.Empty;

        Guid loanType = loanTypeLookupAbrv == nameof(LoanTypeAbbreviation.FB) ? loanTypeFb : loanTypeIb;

        var products = GetProductsByLoanTypeIdExcludedCategory(loanType, loanCatUo).Select(p => p.Abrv).ToList();

        var loanMemberAccounts = GetMemberAccountsForFinancialInstitutionMember(financialInstitutionMemberId, DateTime.UtcNow.AddMonths(-months), nameof(OwnerShipType.Primary), nameof(Enums.AccountCategory.LOAN));

        return loanMemberAccounts.Where(p => products.IsVersionAllowed(p.Type, p.SubType ?? string.Empty));
    }

    /// <summary>
    /// Gets the products by loan type identifier with excluded loan category.
    /// </summary>
    /// <param name="loanTypeId">The loan type identifier.</param>
    /// <param name="excludeLoanCategoryIds">The exclude loan category ids.</param>
    /// <returns></returns>
    public IEnumerable<Data.Models.Product> GetProductsByLoanTypeIdExcludedCategory(Guid loanTypeId, params Guid[] excludeLoanCategoryIds)
    {
        var products = qCashContext.Products
            .Where(p => p.LoanTypeId == loanTypeId && p.Id != Guid.Empty);
        return products.Where(p => !excludeLoanCategoryIds.Contains(p.LoanCategoryId));
    }

    private IEnumerable<MemberAccount> GetFilteredCheckingShareMemberAccounts(Guid financialInstitutionMemberId, int months)
    {
        var checkingAccountTypes = qCashContext.CheckingAccountTypes.Select(p => p.Value).ToList();
        var sharedAccounts = GetMemberAccountsForFinancialInstitutionMember(financialInstitutionMemberId, DateTime.UtcNow.AddMonths(-months), nameof(OwnerShipType.Primary), nameof(Enums.AccountCategory.SHARE));

        return sharedAccounts.Where(p => checkingAccountTypes.IsVersionAllowed(p.Type, p.SubType ?? string.Empty));
    }

    /// <summary>
    /// Gets the member accounts for financial institution member.
    /// </summary>
    /// <param name="financialInstitutionMemberId">The financial institution member identifier.</param>
    /// <param name="fromDate">From date.</param>
    /// <param name="ownerShipType">Type of the ownership.</param>
    /// <param name="category">The category.</param>
    /// <returns></returns>
    public IEnumerable<MemberAccount> GetMemberAccountsForFinancialInstitutionMember(Guid financialInstitutionMemberId, DateTime fromDate, string ownerShipType, string category)
    {
        var query = qCashContext.MemberBaseAccounts
            .Where(mba => mba.FinancialInstitutionMemberId == financialInstitutionMemberId && mba.OwnerShipType == ownerShipType)
            .SelectMany(s => s.MemberAccounts)
            .Where(ma => ma.Category == category && ma.DateOpened >= fromDate);

        return query.ToList();
    }

    public ScoreList ConvertScoresToScoreList(List<Data.Models.Score>? scores)
    {
        ScoreList scoreList = [];
        //note that we don't set/use ScoreList properties TransformationId and ModelId here

        if (scores == null)
        {
            return scoreList;
        }

        scoreList.AddRange(scores.Select(score => new Score
        {
            Value = score.Value,
            FinancialInstitutionId = score.FinancialInstitutionId,
            From = score.From,
            Id = score.Id,
            To = score.To,
        }));

        return scoreList;
    }

    /// <summary>
    /// Gets the score value between margins.
    /// </summary>
    /// <param name="scoreList">The score list.</param>
    /// <param name="value">The value.</param>
    /// <returns></returns>
    private decimal GetScoreValueBetweenMargins(ScoreList scoreList, decimal value) => Convert.ToDecimal(scoreList.GetScoreValue(Convert.ToDouble(value)));

    private static DateTime FromUtc(DateTime value, TimeZoneInfo destinationZone)
    {
        var time = new DateTime(value.Ticks, DateTimeKind.Utc);
        return TimeZoneInfo.ConvertTimeFromUtc(time, destinationZone);
    }

    private JsonSerializerSettings GetJsonSettings()
    {
        var jsonSettings = new JsonSerializerSettings
        {
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            TypeNameHandling = TypeNameHandling.All,
        };

        jsonSettings.Converters.Add(new ExtraInfoConverter());
        return jsonSettings;
    }

    /// <inheritdoc />
    public async Task<LoginFraudControl?> GetFinancialInstitutionMemberLoginFraudControlByAccountIdAsync(string accountId)
    {
        if (string.IsNullOrEmpty(accountId))
        {
            throw new Exception("Please specify account id!");
        }

        var loginFraudControl = await qCashContext
            .LoginFraudControls
            .AsNoTracking()
            .FirstOrDefaultAsync(p => p.AccountId == accountId);

        return loginFraudControl;
    }

    /// <summary>
    /// Check if show or hide the bankruptcy question.
    /// </summary>
    /// <param name="loanApplication">The loan application.</param>
    /// <returns></returns>
    public bool ShowBankruptcyQuestion(Data.Models.LoanApplication loanApplication)
    {
        var setting = qCashContext.Settings.First();
        return setting.Bankruptcy != Bankruptcy.Off.ToString("G") && !loanApplication.IsBankrupt.HasValue;
    }

    /// <summary>
    /// Check if show or hide the MLA question.
    /// </summary>
    /// <param name="loanApplication">The loan application.</param>
    /// <returns></returns>
    public bool ShowMLAQuestion(Data.Models.LoanApplication loanApplication)
    {
        var setting = qCashContext.Settings.First();
        return setting.Mla != MLA.Off.ToString("G") && !loanApplication.ActiveDuty.HasValue;
    }

    public async Task<FinancialInstitutionMember?> GetLastFinancialInstitutionMemberByMemberIdHashAsync(string memberIdHash)
    {
        if (string.IsNullOrEmpty(memberIdHash))
        {
            throw new ArgumentNullException(nameof(memberIdHash));
        }

        var financialInstitutionMember = await qCashContext.FinancialInstitutionMembers
            .Where(p => p.MemberIdHash == memberIdHash)
            .OrderByDescending(s => s.DateCreatedUtc)
            .ThenByDescending(r => r.Id)
            .FirstOrDefaultAsync();
        return financialInstitutionMember;
    }

    public LoanApplicationFee? GetLoanApplicationFeesByLoanApplicationIdAndFeeType(Guid loanApplicationId, string feeType)
    {
        var result = qCashContext.LoanApplicationFees.Where(p => p.LoanApplicationId.Equals(loanApplicationId) && p.FeeType.Equals(feeType)).OrderByDescending(p => p.DateCreatedUtc).FirstOrDefault();
        return result;
    }

    public async Task<Data.Models.LoanApplication?> GetLoanApplicationByAppIdAsync(int appId) => await
        qCashContext.LoanApplications
            .AsNoTracking()
            .Where(s => s.AppId == appId)
            .Include(s => s.LoanStatus)
            .Include(s => s.MemberConsents)
            .Include(s => s.FinancialInstitutionMember)
            .Include(s => s.SelectedProduct)
            .FirstOrDefaultAsync();


    /// <summary>
    /// Gets a list of Purpose of loan type lookups for Loan Application.
    /// </summary>
    /// <returns>IList of IPurposeOfLoanType objects.</returns>
    public IList<PurposeOfLoanType> GetPurposeOfLoanTypes(bool emptyItem, string pleaseSelect, Data.Models.LoanApplication loanApplication, Guid productId)
    {
        var lst = qCashContext.PurposeOfLoanTypes.Where(p => !p.IsDeleted)
            .Include(p => p.TranslationPurposeOfLoanTypes)
            .OrderBy(p => p.Id).Where(p => p.ProductId == productId).ToList();

        IList<PurposeOfLoanType> lstTranslated = new List<PurposeOfLoanType>();

        foreach (PurposeOfLoanType item in lst)
        {
            var purposeTrans = item.TranslationPurposeOfLoanTypes.ToList();
            var language = qCashContext.Languages.FirstOrDefault(o => o.Id.Equals(loanApplication.SelectedLanguageId))?.LanguageCode ?? string.Empty;
            item.Name = GetPurposeOfLoanText(loanApplication, item.Name, purposeTrans, language);
            lstTranslated.Add(item);
        }

        if (emptyItem)
        {
            lstTranslated.Insert(0, new PurposeOfLoanType() { Id = Guid.Empty, Abrv = "Empty", AppAbrv = "Empty", Name = pleaseSelect });
        }

        return lstTranslated;
    }

    /// <summary>
    /// Formats the field value.
    /// </summary>
    /// <param name="loanApplication">The loan application.</param>
    /// <param name="defaultValue">The default value.</param>
    /// <param name="trans">The trans.</param>
    /// <param name="lang">The language.</param>
    private static string GetPurposeOfLoanText(
        Data.Models.LoanApplication? loanApplication, string defaultValue, IList<TranslationPurposeOfLoanType> trans, string lang)
    {
        if (loanApplication == null)
        {
            return defaultValue;
        }

        var fieldValue = defaultValue;
        if (trans.Count <= 0)
        {
            return fieldValue;
        }

        var tran = trans.FirstOrDefault(p => p.LanguageCode == lang);
        if (tran != null)
        {
            fieldValue = tran.Name;
        }

        return fieldValue;
    }

    public async Task<int> GetDecisionModelUsageCountAsync(Guid decisionModelId) =>
        await qCashContext.LoanApplications.CountAsync(p => p.DecisionModelId == decisionModelId && p.LoanStatus != null && p.LoanStatus.AppAbrv.Equals("LSTCMP"));

    public async Task<Data.Models.LoanApplication?> GetLoanApplicationByFinancialInstitutionMemberIdAsync(Guid financialInstitutionMemberId)
    {
        var result = await qCashContext.LoanApplications
            .Include(o => o.MemberConsents)
            .Include(o => o.FinancialInstitutionMember)
            .Include(o => o.LoanApplicationSso)
            .Where(p => p.FinancialInstitutionMemberId == financialInstitutionMemberId)
            .FirstOrDefaultAsync();
        return result;
    }

    /// <summary>
    /// Changes campaign code accounts status
    /// </summary>
    /// <returns></returns>
    public void BulkUpdateCampaignCodeAccountStatus(Guid[] id, string status)
    {
        var accounts = qCashContext.CampaignCodeAccounts.Where(p => id.Contains(p.Id)).ToList();
        foreach (var account in accounts)
        {
            account.Status = status;
        }
        qCashContext.SaveChanges();
    }

    /// <summary>
    /// Searches the loan application log.
    /// </summary>
    /// <param name="search">The search.</param>
    /// <param name="sortBy">The sort by.</param>
    /// <param name="isDescendingOrder">if set to <c>true</c> [is descending order].</param>
    /// <param name="startDate">The start date.</param>
    /// <param name="endDate">The end date.</param>
    /// <param name="appId">The application identifier.</param>
    /// <param name="excludeStatus">if set to <c>true</c> [exclude status].</param>
    /// <param name="applicationNull">if set to <c>true</c> [application null].</param>
    /// <param name="secondaryInstanceEntries">if set to <c>true</c> [synchronized entries].</param>
    /// <param name="statusId">The status identifier.</param>
    /// <param name="modelId">Decision model id.</param>
    /// <param name="productId">Product id.</param>
    /// <param name="pageNumber">The page number.</param>
    /// <param name="pageSize">Size of the page.</param>
    /// <returns>Loan application log.</returns>
    public async Task<IList<LoanApplicationLog>> SearchLoanApplicationLogAsync(string search, string sortBy, bool isDescendingOrder, DateTime? startDate, DateTime? endDate, int appId,
        bool excludeStatus, bool applicationNull, bool secondaryInstanceEntries, Guid statusId, Guid? modelId, Guid? productId, int pageNumber, int pageSize)
    {
        var query = qCashContext.LoanApplicationLogs
            .Where(p => p.LoanApplication!.LoanStatus!.Abrv != "LSTPOERR" &&
                        p.LoanApplication.LoanStatus.Abrv != "LSTPOCNC" &&
                        p.LoanApplication.LoanStatus.Abrv != "LSTPOCMP" &&
                        p.LoanApplication.LoanStatus.Abrv != "LSTPOINI")
            .Include(s => s.LoanApplication!.FinancialInstitutionMember)
            .Include(s => s.LoanApplication!.LoanStatus)
            .Include(p => p.LoanApplication!.DecisionModel)
            .Include(p => p.LoanApplication!.SelectedProduct)
            .Include(p => p.LoanApplication!.LoanApplicationSso).AsNoTracking();

        if (startDate.HasValue)
        {
            query = query.Where(p => p.LoanApplication!.DateCreatedUtc >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(p => p.DateCreatedUtc <= endDate.Value);
        }

        if (appId != 0)
        {
            query = query.Where(q => q.LoanApplication!.AppId == appId);
        }

        if (secondaryInstanceEntries)
        {
            query = query.Where(q => q.LoanApplication!.LoanApplicationSso!.IsPrimaryRegion == false);
        }

        if (modelId.HasValue)
        {
            query = query.Where(q => q.LoanApplication!.DecisionModelId == modelId);
        }

        if (productId.HasValue)
        {
            query = query.Where(q => q.LoanApplication!.SelectedProductId == productId.Value);
        }

        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(l => l.LoanApplication!.FinancialInstitutionMember.AccountId!.StartsWith(search)
                                     || l.LoanApplication!.FinancialInstitutionMember.FirstName!.StartsWith(search) ||
                                     l.LoanApplication.FinancialInstitutionMember.LastName.StartsWith(search));
        }

        if (excludeStatus)
        {
            query = query.Where(s => s.LoanApplication!.LoanStatusId != statusId);
        }
        else if (statusId != Guid.Empty)
        {
            query = query.Where(s => s.LoanApplication!.LoanStatusId == statusId);
        }

        //Exclude logs without loan application (if want to show only without application use appId=0)
        if (applicationNull)
        {
            query = query.Where(s => s.LoanApplicationId != null);
        }

        switch (sortBy)
        {
            case "FirstName":
                if (isDescendingOrder)
                {
                    query = query.OrderByDescending(l => l.LoanApplication!.FinancialInstitutionMember.FirstName);
                }
                else
                {
                    query = query.OrderBy(l => l.LoanApplication!.FinancialInstitutionMember.FirstName);
                }

                break;

            case "LastName":
                if (isDescendingOrder)
                {
                    query = query.OrderByDescending(l => l.LoanApplication!.FinancialInstitutionMember.LastName);
                }
                else
                {
                    query = query.OrderBy(l => l.LoanApplication!.FinancialInstitutionMember.LastName);
                }

                break;

            case "Application":
                if (isDescendingOrder)
                {
                    query = query.OrderByDescending(l => l.LoanApplication!.SelectedProductId);
                }
                else
                {
                    query = query.OrderBy(l => l.LoanApplication!.SelectedProductId);
                }

                break;

            case "Date":
                if (isDescendingOrder)
                {
                    query = query.OrderByDescending(l => l.DateCreatedUtc);
                }
                else
                {
                    query = query.OrderBy(l => l.DateCreatedUtc);
                }

                break;

            case "Status":
                if (isDescendingOrder)
                {
                    query = query.OrderByDescending(l => l.LoanApplication!.LoanStatus!.Name);
                }
                else
                {
                    query = query.OrderBy(l => l.LoanApplication!.LoanStatus!.Name);
                }

                break;

            default:
                if (isDescendingOrder)
                {
                    query = query.OrderByDescending(l => l.LoanApplication!.FinancialInstitutionMember.FirstName);
                }
                else
                {
                    query = query.OrderBy(l => l.LoanApplication!.FinancialInstitutionMember.FirstName);
                }

                break;
        }

        var result = query.Skip(pageSize * (pageNumber - 1)).Take(pageSize);
        return await result.ToListAsync();
    }

}
