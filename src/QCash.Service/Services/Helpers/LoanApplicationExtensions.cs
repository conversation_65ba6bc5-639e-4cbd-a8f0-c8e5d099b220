using QCash.Data.Models;
using QCash.Service.Services.LoanApplication;

namespace QCash.Service.Services.Helpers;

public static class LoanApplicationExtensions
{
    public static MemberConsent? MemberConsentEConsent(this Data.Models.LoanApplication la) =>
        la.MemberConsents.FirstOrDefault(p => p.AcceptanceType == nameof(Enums.MemberConsentAcceptanceType.EConsent));
    public static MemberConsent? MemberConsentEConsentOrPriorEConsent(this Data.Models.LoanApplication la) =>
        la.MemberConsents.FirstOrDefault(p =>
            p.AcceptanceType is nameof(Enums.MemberConsentAcceptanceType.EConsent) or nameof(Enums.MemberConsentAcceptanceType.PriorEConsent));
    public static MemberConsent? MemberConsentPriorEConsent(this Data.Models.LoanApplication la) =>
        la.MemberConsents.FirstOrDefault(p => p.AcceptanceType == nameof(Enums.MemberConsentAcceptanceType.PriorEConsent));
    public static MemberConsent? MemberConsentLoanDisclosure(this Data.Models.LoanApplication la) =>
        la.MemberConsents.FirstOrDefault(p => p.AcceptanceType == nameof(Enums.MemberConsentAcceptanceType.LoanDisclosure));
    public static MemberConsent? MemberConsentAutomaticPayment(this Data.Models.LoanApplication la) =>
        la.MemberConsents.FirstOrDefault(p => p.AcceptanceType == nameof(Enums.MemberConsentAcceptanceType.AutomaticPayment));
    public static MemberConsent? MemberConsentCreditCard(this Data.Models.LoanApplication la) =>
        la.MemberConsents.FirstOrDefault(p => p.AcceptanceType == nameof(Enums.MemberConsentAcceptanceType.CreditCard));

    public static bool IsEConsentAccepted(this Data.Models.LoanApplication la)
    {
        var eConsentMemberConsent = la.MemberConsents.FirstOrDefault(p => p.AcceptanceType is nameof(Enums.MemberConsentAcceptanceType.EConsent) or nameof(Enums.MemberConsentAcceptanceType.PriorEConsent));

        return eConsentMemberConsent?.Accepted ?? false;
    }
}