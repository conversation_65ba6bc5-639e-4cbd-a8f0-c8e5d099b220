using QCash.Data.Models;
using QCash.Service.Classes.DecisionManager;

namespace QCash.Service.Services.Helpers;

public static class ServiceExtensions
{
    /// <summary>
    /// Get bool value from specified transformation, if transformation not exists return 'null'.
    /// </summary>
    /// <param name="settings">The settings.</param>
    /// <param name="transformationName">Name of the transformation.</param>
    /// <returns>Nullable bool from transformation.</returns>
    public static bool? ToNullableBool(this Dictionary<string, TransformationResult?> settings, string transformationName) =>
        settings.TryGetValue(transformationName, out var val) && val is { Value: not null }
            ? Convert.ToBoolean(val.Value.Value)
            : null;

    /// <summary>
    /// Determines whether version is allowed.
    /// </summary>
    /// <param name="allowedVersions">The allowed versions.</param>
    /// <param name="majorVersion">The major version.</param>
    /// <param name="minorVersion">The minor version.</param>
    /// <returns>
    ///   <c>true</c> if [is version allowed] [the specified major version]; otherwise, <c>false</c>.
    /// </returns>
    public static bool IsVersionAllowed(this List<string> allowedVersions, string majorVersion, string minorVersion)
    {
        foreach (var allowedVersion in allowedVersions)
        {
            var allowedMajor = string.Empty;
            var allowedMinor = string.Empty;

            if (allowedVersion.Contains("."))
            {
                var versions = allowedVersion.Split('.');
                allowedMajor = versions[0];
                allowedMinor = versions[1];
            }
            else
            {
                allowedMajor = allowedVersion;
            }

            if (allowedMajor == majorVersion)
            {
                if (allowedMinor == string.Empty || allowedMinor == minorVersion)
                {
                    return true;
                }
            }
        }
        return false;
    }

    //public static void AddLogDetail(this IList<LoanApplicationLogDetail> logs, string actionDescription, bool isTemp = false)
    //{
    //    logs.Add(new LoanApplicationLogDetail()
    //    {
    //        ActionDateTimeUtc = DateTime.UtcNow,
    //        IsTemp = isTemp,
    //        ActionDescription = actionDescription
    //    });
    //}

    //public static void AddLogDetail(this IList<LoanApplicationLogDetail> logs, LogType logType, string actionDescription, bool isTemp = false)
    //{
    //    logs.Add(new LoanApplicationLogDetail()
    //    {
    //        ActionDateTimeUtc = DateTime.UtcNow,
    //        IsTemp = isTemp,
    //        LogType = (int)logType,
    //        ActionDescription = actionDescription
    //    });
    //}

    //public static void AddLogDetail(this IList<LoanApplicationLogDetail> logs, LogType logType, LogSortOrder sortOrder, string actionDescription, bool isTemp = false)
    //{
    //    logs.Add(new LoanApplicationLogDetail()
    //    {
    //        ActionDateTimeUtc = DateTime.UtcNow,
    //        IsTemp = isTemp,
    //        LogType = (int)logType,
    //        SortOrder = (int)sortOrder,
    //        ActionDescription = actionDescription
    //    });
    //}

    //public static void AddLogDetail(this IList<LoanApplicationLogDetail> logs, LogType logType, LogSortOrder sortOrder, LogDetailItemReportGroupEnum reportGroup, string actionDescription, bool isTemp = false)
    //{
    //    logs.Add(new LoanApplicationLogDetail()
    //    {
    //        ActionDateTimeUtc = DateTime.UtcNow,
    //        IsTemp = isTemp,
    //        LogType = (int)logType,
    //        SortOrder = (int)sortOrder,
    //        ReportGroup = (int)reportGroup,
    //        ActionDescription = actionDescription
    //    });
    //}

    /// <summary>
    /// Determines whether the specified campaign code is a credit card.
    /// </summary>
    /// <param name="campaignCode">The campaign code.</param>
    public static bool IsCreditCard(this CampaignCode campaignCode)
    {
        return !string.IsNullOrEmpty(campaignCode?.CreditCardType);
    }
}