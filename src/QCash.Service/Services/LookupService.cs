using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Services.Interfaces;
using QCash.Service.Utilities.Extensions;

namespace QCash.Service.Services;

public class LookupService(
    ILookupQueries lookupQueries,
    IProductQueries productQueries,
    QCashContext dbContext
    ) : ILookupService
{
    public const string LoanTypeInterestBasedAbrv = "IB";
    private const string DefaultLoanTypeAbrv = LoanTypeInterestBasedAbrv;
    public const string LoanTypeNoProductAbbreviation = "NP";
    public const string LoanTypeFeeBasedAbbreviation = "FB";
    public const string LoanCategoryUnsecuredOpenAbbreviation = "UO";

    public async Task<List<QListItem<Guid>>> GetLoanTypesForDropdownAsync(IQueryable<LoanType> loanTypes)
    {
        var loanTypeDropDownItems = from lt in loanTypes
                                    where lt.Abrv != LoanTypeNoProductAbbreviation
                                    select new QListItem<Guid>() { Text = lt.Name, Value = lt.Id, };
        return await loanTypeDropDownItems.ToListAsync();
    }

    public async Task<List<QListItem<Guid>>> GetLoanTypesForDropdownAsync(string fiSlug)
    {
        var allLoanTypes = lookupQueries.GetLoanTypes(fiSlug);
        return await GetLoanTypesForDropdownAsync(allLoanTypes);
    }

    public LoanType? GetDefaultLoanType(List<LoanType> loanTypes)
    {
        var defaultItem = loanTypes.FirstOrDefault(lt =>
            !string.IsNullOrWhiteSpace(lt.Abrv)
            && lt.Abrv.Equals(DefaultLoanTypeAbrv));
        return defaultItem;
    }

    public async Task<List<QListItem<Guid>>> GetLoanCategoriesForDropdownAsync(string fiSlug, Guid loanTypeId)
    {
        var allLoanCategoriesQ = await lookupQueries.GetLoanCategoriesQAsync(fiSlug, loanTypeId);
        var allLoanCategories = allLoanCategoriesQ.AsNoTracking();
        var data = from lc in allLoanCategories
                   select new QListItem<Guid>() { Text = lc.Name, Value = lc.Id, };
        return await data.ToListAsync();
    }

    public List<QListItem<TEnum>> GetItemsFromEnum<TEnum>() where TEnum : Enum
    {
        var enumValues = Enum.GetValues(typeof(TEnum)).Cast<TEnum>();
        var result = new List<QListItem<TEnum>>();
        foreach (var enumItem in enumValues)
        {
            var desc = enumItem.ToDescriptionString();
            var ddItem = new QListItem<TEnum>()
            {
                Value = enumItem,
                Text = desc ?? $"{enumItem}",
            };
            result.Add(ddItem);
        }
        return result;
    }

    public List<QListItem<TEnum>> GetItemsFromEnumWithEnumString<TEnum>(Func<IEnumerable<TEnum>, IEnumerable<TEnum>>? filter = null) where TEnum : Enum
    {
        var enumValues = Enum.GetValues(typeof(TEnum)).Cast<TEnum>().ToList();
        if (filter != null)
        {
            enumValues = filter(enumValues).ToList();
        }
        if (enumValues.FirstOrDefault()?.GetDisplayOrder() != null)
        {
            // If we need the enum values to be in a specific order other than the default,
            // then use the [Display(Order = x)] attribute
            enumValues = enumValues.OrderBy(a => a.GetDisplayOrder()).ToList();
        }
        return enumValues.Select(enumItem => new QListItem<TEnum>()
        {
            Value = enumItem,
            Text = enumItem.ToDescriptionString() ?? $"{enumItem}",
        }).ToList();
    }

    public async Task<List<QListItem<string>>> GetStatesAsync()
    {
        var statesQ = lookupQueries.GetStatesQueryable();
        var data = await (statesQ.AsNoTracking()
                .OrderBy(a => a.Name)
                .Select(st => new QListItem<string>() { Text = st.Name, Value = st.Abrv, }))
            .ToListAsync();
        // We've had cases where there's more than one state with the same abrv.
        // we want to consider those a single 'state'
        data = data.GroupBy(a => new { a.Value },
                b => new { b.Value, b.Text },
                (key, g) => new QListItem<string>() { Text = g.First().Text, Value = key.Value })
            .ToList();
        return data;
    }

    public async Task<List<QListItem<Guid>>> GetProductDropdownChoicesAsync()
    {
        var productQ = productQueries.GetProducts();
        var data = from st in productQ.AsNoTracking()
                   select new QListItem<Guid>() { Text = st.Name, Value = st.Id, };
        return await data.ToListAsync();
    }

    // public List<QListItem<string>> GetTimeZoneChoicesOldApproach()
    // {
    //     var allZones = TimeZoneInfo.GetSystemTimeZones();
    //
    //     // note: "SA Western Standard Time" is the default time zone that Microsoft uses for Puerto Rico
    //
    //     var usZoneIds = new HashSet<string>
    //     {
    //         "Hawaiian Standard Time", "Alaskan Standard Time", "Pacific Standard Time", "US Mountain Standard Time",
    //         "Mountain Standard Time", "Central Standard Time", "Eastern Standard Time", "US Eastern Standard Time",
    //         "SA Western Standard Time",
    //     };
    //
    //     foreach (var item in
    //              allZones
    //                  .Where(z => usZoneIds.Contains(z.Id))
    //                  .Select(a => " new () { Value = \"" + a.Id + "\", Text = \"" + a.DisplayName + "\"},")
    //             )
    //     {
    //         Console.WriteLine(item);
    //     }
    //
    //     var result = allZones
    //         .Where(z => usZoneIds.Contains(z.Id))
    //         .Select(a => new QListItem<string>() { Text = a.DisplayName, Value = a.Id, })
    //         .ToList();
    //
    //     return result;
    // }

    public List<QListItem<string>> GetTimeZoneChoices()
    {
        var result = new List<QListItem<string>>()
        {
            new() { Value = "Hawaiian Standard Time", Text = "(UTC-10:00) Hawaii" },
            new() { Value = "Alaskan Standard Time", Text = "(UTC-09:00) Alaska" },
            new() { Value = "Pacific Standard Time", Text = "(UTC-08:00) Pacific Time (US & Canada)" },
            new() { Value = "US Mountain Standard Time", Text = "(UTC-07:00) Arizona" },
            new() { Value = "Mountain Standard Time", Text = "(UTC-07:00) Mountain Time (US & Canada)" },
            new() { Value = "Central Standard Time", Text = "(UTC-06:00) Central Time (US & Canada)" },
            new() { Value = "Eastern Standard Time", Text = "(UTC-05:00) Eastern Time (US & Canada)" },
            new() { Value = "US Eastern Standard Time", Text = "(UTC-05:00) Indiana (East)" },
            new() { Value = "SA Western Standard Time", Text = "(UTC-04:00) Georgetown, La Paz, Manaus, San Juan" },
        };
        return result;
    }

    public async Task<List<QListItem<Guid>>> GetDecisionModelTypesAsync()
    {
        var items = from lt in dbContext.DecisionModelTypes.AsNoTracking()
                    select new QListItem<Guid>() { Text = lt.Name, Value = lt.Id, };
        return await items.ToListAsync();
    }

    public async Task<List<QListItem<Guid>>> GetDecisionModelStatusChoicesAsync()
    {
        var items = from lt in dbContext.DecisionModelStatuses.AsNoTracking()
                    select new QListItem<Guid>() { Text = lt.Name, Value = lt.Id, };
        return await items.ToListAsync();
    }

    public List<QListItem<string>> GetFontChoices()
    {
        var result = new List<QListItem<string>>()
        {
            new() { Text = "Arial", Value = "Arial, Helvetica, Geneva, sans-serif" },
            new() { Text = "Verdana", Value = "Verdana, Helvetica, Geneva, sans-serif" },
            new() { Text = "Tahoma", Value = "Tahoma, Helvetica, Geneva, sans-serif" },
            new() { Text = "Trebuchet MS", Value =  "\"Trebuchet MS\", sans-serif" },
            new() { Text = "Georgia", Value = "Georgia, serif" },
            new() { Text = "Times New Roman", Value = "Times New Roman, serif" },
        };
        return result;
    }
}
