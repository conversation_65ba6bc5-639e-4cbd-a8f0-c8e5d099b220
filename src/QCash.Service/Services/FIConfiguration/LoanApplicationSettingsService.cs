using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using static QCash.Service.Utilities.Extensions.EnumExtensions;

namespace QCash.Service.Services.FIConfiguration;

public class LoanApplicationSettingsService(
    IAuthUserService authUserService,
    QCashContext dbContext,
    IEfPocoService efPocoService
) : ILoanApplicationSettingsService
{
    public string RecordName { get; } = "Loan Application General Settings";
    public LoanApplicationSettingsNavHeaderDto GetNavHeader(string fiSlug)
    {
        var isSystemAdmin = authUserService.IsSystemAdmin();
        var isSuperUser = authUserService.IsSuperUser();
        var isFiManager = authUserService.IsFiManager();
        return new LoanApplicationSettingsNavHeaderDto
        {
            FiSlug = fiSlug,
            IsSystemAdmin = isSystemAdmin,
            IsSuperUser = isSuperUser,
            ShowCalcEngineSettings = isSystemAdmin || isSuperUser,
            ShowNSFSettings = isSystemAdmin || isSuperUser || isFiManager,
            ShowFinancialCoachingSettings = isSuperUser || isSystemAdmin,
            ShowInsuranceProductSettings = true, // NOTE that this should have a condition after CoreFeatures are implemented.
        };
    }

    public async Task<LoanApplicationGeneralSettingsDto> GetGeneralSettingsAsync(Guid applicationId)
    {
        var data = await GetGeneralSettingsDataAsync();
        if (data != null)
        {
            return data;
        }
        throw new Exception("Failed to load data for loan application general page.  Most likely the setting record does not exist.");
    }

    public async Task<GetOrCreateRecordResult> SaveSettingsAsync(LoanApplicationGeneralSettingsDto dto, Guid applicationId)
    {
        var msSaveResult = await efPocoService.CreateOrUpdateAsync(dto.MaintenanceSettingId, financialInstitutionId: null, dto.MaintenanceSettingTimeStamp,
            new PerformCreateOrUpdateOptions<MaintenanceSetting>()
            {
                ExecuteRecordChangesFunc = (result) =>
                {
                    if (result.CreatingNewRecord)
                    {
                        result.Record.FinancialInstitutionId = dto.FinancialInstitutionId;
                    }

                    result.Record.IsMaintenanceActive = dto.IsMaintenanceActive;
                    result.Record.StartDate = dto.MaintenanceStartDateUTC;
                    result.Record.EndDate = dto.MaintenanceEndDateUTC;

                },
            });
        if (!msSaveResult.IsSuccessful)
        {
            return msSaveResult.ToUnTyped();
        }

        var fiSaveResult = await efPocoService.CreateOrUpdateAsync(dto.FinancialInstitutionId, financialInstitutionId: applicationId,
            dto.FinancialInstitutionTimeStamp,
            new PerformCreateOrUpdateOptions<FinancialInstitution>()
            {
                ExecuteRecordChangesFunc = (result) =>
                {
                    // R1, starting with Loan Application Fee User GL Code
                    result.Record.GlFeeAccount = dto.GlFeeAccount;
                },
            });
        if (!fiSaveResult.IsSuccessful)
        {
            return fiSaveResult.ToUnTyped();
        }

        var miSettingResult = await SaveMemberIdentifierSettingsAsync(dto, applicationId);
        var miRecordHasChanged = miSettingResult.Value;

        var settingSaveResult = await efPocoService.CreateOrUpdateAsync(dto.SettingId, financialInstitutionId: applicationId, dto.SettingTimeStamp,
            new PerformCreateOrUpdateOptions<Setting>()
            {
                ExecuteRecordChangesFunc = (result) =>
                {
                    // R1, starting with Loan Application Fee User GL Code
                    result.Record.LoanApplicationFeeDescription = dto.LoanApplicationFeeDescription;
                    result.Record.LoanApplicationFee = dto.LoanApplicationFee;
                    // R2, starting with MLA Covered Borrower Loan Application Fee User GL Code
                    result.Record.MaprLoanApplicationFeeGlAccount = dto.MaprLoanApplicationFeeGlAccount;
                    result.Record.MaprLoanApplicationFeeDescription = dto.MaprLoanApplicationFeeDescription;
                    result.Record.MaprLoanApplicationFee = dto.MaprLoanApplicationFee;
                    // R3 starting with Check Available Balance for Loan Application Fee
                    result.Record.LoanApplicationFeeAvailableBalanceCheck = dto.LoanApplicationFeeAvailableBalanceCheck;
                    result.Record.FundingPendingNotificationEnabled = dto.FundingPendingNotificationEnabled;
                    result.Record.AllowMemberAccountSelection = dto.AllowMemberAccountSelection;

                    result.Record.AccountBalanceType = dto.AccountBalanceType.ToString();
                    result.Record.SendOriginationFeeOn = dto.SendOriginationFeeOn;
                    result.Record.DefaultAccountSelectionBlank = dto.DefaultAccountSelectionBlank;

                    result.Record.IncludeDormantAccounts = dto.IncludeDormantAccounts;
                    result.Record.ShouldDenyOnMaxLoans = dto.ShouldDenyOnMaxLoans;
                    result.Record.MaxNumberOfOpenLoans = dto.MaxNumberOfOpenLoans;

                    result.Record.EConsent = dto.EConsent;
                    result.Record.ExcludeVoidedTransactions = dto.ExcludeVoidedTransactions;
                    result.Record.LoanSubmissionEmailAlert = dto.LoanSubmissionEmailAlert;

                    result.Record.LogRng = dto.LogRng;
                    result.Record.MaskInitiateWaitTime = dto.MaskInitiateWaitTime;
                    result.Record.UsePersonalPrivateContainer = dto.UsePersonalPrivateContainer;

                    result.Record.UsePayOff = dto.UsePayOff;
                    result.Record.LoanHub = dto.LoanHub;
                    result.Record.LoanPurposeExclusion = dto.LoanPurposeExclusion;

                    result.Record.TrackCustomLoanData = dto.TrackCustomLoanData;
                    result.Record.LoanIdSource = dto.LoanIdSource.ToString();
                    result.Record.PullExternalAccounts = dto.PullExternalAccounts;

                    result.Record.PullIsTroubledDebt = dto.PullIsTroubledDebt;
                    result.Record.Bankruptcy = dto.Bankruptcy;
                    result.Record.Mla = dto.Mla;
                    result.Record.MlaCheckValid = dto.MlaCheckValid;

                    result.Record.CheckIfJointAccountOnSso = dto.CheckIfJointAccountOnSso;
                    result.Record.MemberInGoodStandingMinimumAge = dto.MemberInGoodStandingMinimumAge;
                    result.Record.IncludeClosedAccountsInCalculations = dto.IncludeClosedAccountsInCalculations;

                    result.Record.ShowMultipleAan = dto.ShowMultipleAan;
                    result.Record.NumberOfAanToShow = dto.NumberOfAanToShow;
                    result.Record.InsertTrackingRecordForDeniedLoan = dto.InsertTrackingRecordForDeniedLoan;

                    result.Record.LoanSuffixMin = dto.LoanSuffixMin;
                    result.Record.LoanSuffixMax = dto.LoanSuffixMax;
                    result.Record.OverrideChargeoff = dto.OverrideChargeoff;

                    result.Record.OverrideChargeoffDescription = dto.OverrideChargeoffDescription;
                    result.Record.RequiredDigits = dto.RequiredDigits;
                    result.Record.FileDownload = dto.FileDownload;

                    result.Record.AutomaticDisclosureEmail = dto.AutomaticDisclosureEmail;
                    result.Record.ShareOneCredentials = dto.ShareOneCredentials;
                    result.Record.RequireTilaView = dto.RequireTilaView;

                    result.Record.UseFirebirdUi = dto.UseFirebirdUi;

                    var entry = dbContext.Entry(result.Record);
                    if (miRecordHasChanged && entry.State == EntityState.Unchanged)
                    {
                        // We want to consider a change to any of the Member Identifier Settings as a change to the setting record.
                        // The MI settings are technically separate records, so the timestamp check isn't automatic.  We need to force it.
                        entry.State = EntityState.Modified;
                    }
                },
            });
        return settingSaveResult.ToUnTyped();
    }

    private async Task<GenericActionResult<bool>> SaveMemberIdentifierSettingsAsync(LoanApplicationGeneralSettingsDto dto, Guid applicationId)
    {
        List<MemberIdentifiersSetting> existingData = [];
        if (dto.MemberIdentifiers.Any(a => a.UpdateRequested))
        {
            // Only load the existing records if there is a need.  Meaning, we know we are modifying an existing record.
            existingData = await dbContext.MemberIdentifiersSettings
                .Where(mis => mis.FinancialInstitutionId == dto.FinancialInstitutionId)
                .AsTracking()
                .ToListAsync();
        }

        var somethingChanged = false;
        GetOrCreateRecordResult<MemberIdentifiersSetting>? updateResult = null;
        foreach (var misDto in dto.MemberIdentifiers)
        {
            var dbRecord = existingData.FirstOrDefault(a => a.Key == (int)misDto.Key);
            updateResult = efPocoService.CreateOrUpdate(dbRecord, applicationId, misDto.TimeStamp, new PerformCreateOrUpdateOptions<MemberIdentifiersSetting>
            {
                ExecuteRecordChangesFunc = result =>
                {
                    result.Record.Used = misDto.IsChecked;
                    if (result.CreatingNewRecord)
                    {
                        result.Record.FinancialInstitutionId = dto.FinancialInstitutionId;
                        result.Record.Label = misDto.Key.ToString();
                        result.Record.Key = (int)misDto.Key;
                    }
                },
            });
            somethingChanged = somethingChanged || updateResult.EditingExistingRecord || updateResult.CreatingNewRecord;
            if (!updateResult.IsSuccessful)
            {
                return new GenericActionResult<bool>() { IsSuccessful = false, ErrorMessage = updateResult.ErrorMessage, Value = somethingChanged, };
            }
        }

        if (updateResult == null)
        {
            // Unsure what to do here.  We didn't even attempt to edit any records.  This means the user didn't see any options?  That should not be possible.
            return new GenericActionResult<bool>() { IsSuccessful = false, ErrorMessage = "No options found in SaveMemberIdentifierSettingsAsync", Value = somethingChanged, };
        }
        return new GenericActionResult<bool>() { IsSuccessful = true, ErrorMessage = updateResult.ErrorMessage, Value = somethingChanged, };
    }

    private async Task<LoanApplicationGeneralSettingsDto?> GetGeneralSettingsDataAsync()
    {
        var dto = await dbContext.Settings
            .Select(s => new LoanApplicationGeneralSettingsDto()
            {
                FinancialInstitutionId = s.FinancialInstitutionId,
                FinancialInstitutionTimeStamp = s.FinancialInstitution.TimeStamp,
                SettingId = s.Id,
                SettingTimeStamp = s.TimeStamp,
                MaintenanceSettingId =
                    s.FinancialInstitution.MaintenanceSetting == null ? Guid.Empty : s.FinancialInstitution.MaintenanceSetting.Id,
                MaintenanceSettingTimeStamp =
                    s.FinancialInstitution.MaintenanceSetting == null
                        ? Array.Empty<byte>()
                        : s.FinancialInstitution.MaintenanceSetting!.TimeStamp,
                TimeZone = s.TimeZone,
                GlFeeAccount = s.FinancialInstitution.GlFeeAccount,
                LoanApplicationFeeDescription = s.LoanApplicationFeeDescription,
                LoanApplicationFee = s.LoanApplicationFee,
                MaprLoanApplicationFeeGlAccount = s.MaprLoanApplicationFeeGlAccount,
                MaprLoanApplicationFeeDescription = s.MaprLoanApplicationFeeDescription,
                MaprLoanApplicationFee = s.MaprLoanApplicationFee,
                LoanApplicationFeeAvailableBalanceCheck = s.LoanApplicationFeeAvailableBalanceCheck,
                FundingPendingNotificationEnabled = s.FundingPendingNotificationEnabled,
                AllowMemberAccountSelection = s.AllowMemberAccountSelection,
                AccountBalanceType = GetEnumByString(s.AccountBalanceType ?? "", Enums.AccountBalanceTypeEnum.Available),
                SendOriginationFeeOn = s.SendOriginationFeeOn,
                DefaultAccountSelectionBlank = s.DefaultAccountSelectionBlank,
                IncludeDormantAccounts = s.IncludeDormantAccounts,
                ShouldDenyOnMaxLoans = s.ShouldDenyOnMaxLoans,
                MaxNumberOfOpenLoans = s.MaxNumberOfOpenLoans,
                EConsent = s.EConsent,
                ExcludeVoidedTransactions = s.ExcludeVoidedTransactions,
                LoanSubmissionEmailAlert = s.LoanSubmissionEmailAlert,
                LogRng = s.LogRng,
                MaskInitiateWaitTime = s.MaskInitiateWaitTime,
                UsePersonalPrivateContainer = s.UsePersonalPrivateContainer,
                UsePayOff = s.UsePayOff,
                LoanHub = s.LoanHub,
                LoanPurposeExclusion = s.LoanPurposeExclusion,
                TrackCustomLoanData = s.TrackCustomLoanData,
                LoanIdSource = GetEnumByString(s.LoanIdSource, Enums.LoanIdSourceEnum.RepGen),
                PullExternalAccounts = s.PullExternalAccounts,
                PullIsTroubledDebt = s.PullIsTroubledDebt,
                Bankruptcy = s.Bankruptcy,
                Mla = s.Mla,
                MlaCheckValid = s.MlaCheckValid,
                CheckIfJointAccountOnSso = s.CheckIfJointAccountOnSso,
                MemberInGoodStandingMinimumAge = s.MemberInGoodStandingMinimumAge,
                IncludeClosedAccountsInCalculations = s.IncludeClosedAccountsInCalculations,
                ShowMultipleAan = s.ShowMultipleAan,
                NumberOfAanToShow = s.NumberOfAanToShow,
                InsertTrackingRecordForDeniedLoan = s.InsertTrackingRecordForDeniedLoan,
                IsMaintenanceActive =
                    s.FinancialInstitution.MaintenanceSetting != null && s.FinancialInstitution.MaintenanceSetting.IsMaintenanceActive,
                MaintenanceStartDateUTC = s.FinancialInstitution.MaintenanceSetting!.StartDate,
                MaintenanceEndDateUTC = s.FinancialInstitution.MaintenanceSetting!.EndDate,
                LoanSuffixMin = s.LoanSuffixMin,
                LoanSuffixMax = s.LoanSuffixMax,
                OverrideChargeoff = s.OverrideChargeoff,
                OverrideChargeoffDescription = s.OverrideChargeoffDescription,
                RequiredDigits = s.RequiredDigits,
                FileDownload = s.FileDownload,
                AutomaticDisclosureEmail = s.AutomaticDisclosureEmail,
                ShareOneCredentials = s.ShareOneCredentials,
                RequireTilaView = s.RequireTilaView,
                UseFirebirdUi = s.UseFirebirdUi,
                MemberIdentifiers = s.FinancialInstitution.MemberIdentifiersSettings
                    .Select(a => new LoanApplicationGeneralSettingMemberIdentifierDto
                    {
                        Id = a.Id,
                        Key = (Enums.MemberIdentifierTypeEnum)a.Key,
                        IsChecked = a.Used,
                        TimeStamp = a.TimeStamp,
                    }).ToList(),
            })
            .AsNoTracking()
            .SingleOrDefaultAsync();
        return dto;
    }
}
