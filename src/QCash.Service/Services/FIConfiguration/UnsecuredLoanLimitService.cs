using Microsoft.EntityFrameworkCore;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Utilities.Extensions;

namespace QCash.Service.Services.FIConfiguration;

public class UnsecuredLoanLimitService(
    IEfPocoService efPocoService
    ) : IUnsecuredLoanLimitService
{
    public string RecordName { get; set; } = "Loan Application Unsecured Loan Limit Setting";

    private static UnsecuredLoanLimitSettingsDto CreateBlankDto(string fiSlug) =>
        new()
        {
            SettingId = Guid.Empty,
            SettingTimeStamp = [],
            FiSlug = fiSlug,
            UnsecuredFiLoanLimit = null,
        };

    public async Task<UnsecuredLoanLimitSettingsDto> GetSettingsAsync(Guid financialInstitutionId, string fiSlug)
    {
        var result = await efPocoService.GetQuery<Setting>(q =>
                q.Where(a => a.FinancialInstitution.Id == financialInstitutionId))
            .AsNoTracking()
            .Select(a => new UnsecuredLoanLimitSettingsDto()
            {
                SettingId = a.Id,
                SettingTimeStamp = a.TimeStamp,
                FiSlug = fiSlug,
                UnsecuredFiLoanLimit = a.UnsecuredFiLoanLimit,
            }).SingleOrDefaultAsync();
        return result ?? CreateBlankDto(fiSlug);
    }

    public async Task<GetOrCreateRecordResult<Setting>> SaveSettingsAsync(UnsecuredLoanLimitSettingsDto dto, Guid applicationId) =>
        await efPocoService.CreateOrUpdateAsync(dto.SettingId, applicationId, dto.SettingTimeStamp,
            new PerformCreateOrUpdateOptions<Setting>()
            {
                ExecuteRecordChangesFunc = (result) =>
                {
                    result.Record.UnsecuredFiLoanLimit = dto.UnsecuredFiLoanLimit;
                },
            });

    public async Task<List<UnsecuredLoanConfigurationListItemDto>> GetAllConfigItemsAsync() =>
        await efPocoService.GetQuery<UnsecuredLoanLimit>()
            .AsNoTracking()
            .Select(a => new UnsecuredLoanConfigurationListItemDto()
            {
                Id = a.Id,
                TimeStamp = a.TimeStamp,
                Code = a.Code,
                Description = a.Description,
                OpenEndLoan = a.OpenEndLoan,
            }).ToListAsync();

    public UnsecuredLoanConfigurationListItemDto CreateBlankItem() =>
        new()
        {
            Id = Guid.Empty,
            TimeStamp = [],
            Code = "",
            Description = "",
            OpenEndLoan = false,
        };

    public async Task<UnsecuredLoanConfigurationListItemDto> GetItemAsync(Guid id)
    {
        var data = await efPocoService.GetQuery<UnsecuredLoanLimit>()
            .Where(a => a.Id == id)
            .AsNoTracking()
            .Select(a => new UnsecuredLoanConfigurationListItemDto()
            {
                Id = a.Id,
                TimeStamp = a.TimeStamp,
                Code = a.Code,
                Description = a.Description,
                OpenEndLoan = a.OpenEndLoan,
            }).SingleOrDefaultAsync();
        data.ThrowIfNull("Item not found");
        return data;
    }

    public async Task<GenericActionResult> SaveAsync(Guid applicationId, UnsecuredLoanConfigurationListItemDto dto) =>
        await efPocoService.CreateOrUpdateAsync(dto.Id, applicationId, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<UnsecuredLoanLimit>()
            {
                ExecuteRecordChangesFunc = result =>
                {
                    result.Record.Code = dto.Code;
                    result.Record.Description = dto.Description ?? "";
                    result.Record.OpenEndLoan = dto.OpenEndLoan;
                },
            });

    public async Task<GenericActionResult> DeleteItemAsync(DeleteItemDto dto) =>
        await efPocoService.DeleteItemAsync<UnsecuredLoanLimit>(dto);
}
