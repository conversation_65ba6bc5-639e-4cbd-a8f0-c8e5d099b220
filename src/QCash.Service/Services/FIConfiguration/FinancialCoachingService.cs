using Microsoft.EntityFrameworkCore;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;

namespace QCash.Service.Services.FIConfiguration;

public class FinancialCoachingService(IEfPocoService efPocoService) : IFinancialCoachingService
{
    public string RecordName { get; set; } = "Loan Application Financial Coaching Settings";

    private static FinancialCoachingSettingDto CreateBlankDto(string fiSlug) =>
        new()
        {
            FiSlug = fiSlug,
            Id = Guid.Empty,
            FinancialInstitutionId = Guid.Empty,
            TimeStamp = [],
            AwarenessPage = false,
            LandingPageDebtManagement = false,
            FeeBasedLoansThreshold = 0,
            InterestBasedLoansThreshold = 0,
            AwarenessPeriod = 0,
            DebtCounselorConfirmation = false,
            DebtManagementAgencyName = null,
            DebtManagementEmail = null,
        };

    /// <summary>
    /// Attempt to get the single record for the requested ApplicationId.
    /// If there is no record in the database, return a blank template record.
    /// </summary>
    /// <param name="financialInstitutionId"></param>
    /// <param name="fiSlug"></param>
    /// <returns></returns>
    public async Task<FinancialCoachingSettingDto> GetSettingsAsync(Guid financialInstitutionId, string fiSlug)
    {
        var result = await efPocoService.GetQuery<FinancialCoachingSetting>(q =>
                q.Where(a => a.FinancialInstitution.Id == financialInstitutionId))
            .AsNoTracking()
            .Select(a => new FinancialCoachingSettingDto()
            {
                FiSlug = a.FinancialInstitution.Slug,
                Id = a.Id,
                FinancialInstitutionId = a.FinancialInstitutionId,
                TimeStamp = a.TimeStamp,
                AwarenessPage = a.AwarenessPage,
                LandingPageDebtManagement = a.LandingPageDebtManagement,
                FeeBasedLoansThreshold = a.FeeBasedLoansThreshold,
                InterestBasedLoansThreshold = a.InterestBasedLoansThreshold,
                AwarenessPeriod = a.AwarenessPeriod,
                DebtCounselorConfirmation = a.DebtCounselorConfirmation,
                DebtManagementAgencyName = a.DebtManagementAgencyName,
                DebtManagementEmail = a.DebtManagementEmail,
            }).FirstOrDefaultAsync();
        return result ?? CreateBlankDto(fiSlug);
    }


    public async Task<GetOrCreateRecordResult<FinancialCoachingSetting>> SaveSettingsAsync(FinancialCoachingSettingDto dto, Guid financialInstitutionId)
    {
        if (dto.Id.Equals(Guid.Empty) && dto.FinancialInstitutionId.Equals(Guid.Empty))
        {
            // If we are creating a new record, we may not know the FinancialInstitutionId
            var fi = efPocoService.GetQuery<FinancialInstitution>(q =>
                    q.Where(a => a.Id == financialInstitutionId))
                .AsNoTracking()
                .Select(a => a.Id)
                .FirstOrDefault();
            dto.FinancialInstitutionId = fi;
        }

        return await efPocoService.CreateOrUpdateAsync(dto.Id, financialInstitutionId: null, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<FinancialCoachingSetting>()
            {
                QueryableCustomization = (query) =>
                    query.Where(a => a.FinancialInstitutionId == dto.FinancialInstitutionId).AsTracking(),
                ExecuteRecordChangesFunc = (result) =>
                {
                    if (result.CreatingNewRecord)
                    {
                        result.Record.FinancialInstitutionId = dto.FinancialInstitutionId;
                    }
                    result.Record.AwarenessPage = dto.AwarenessPage;
                    result.Record.LandingPageDebtManagement = dto.LandingPageDebtManagement;
                    result.Record.FeeBasedLoansThreshold = dto.FeeBasedLoansThreshold;
                    result.Record.InterestBasedLoansThreshold = dto.InterestBasedLoansThreshold;
                    result.Record.AwarenessPeriod = dto.AwarenessPeriod;
                    result.Record.DebtCounselorConfirmation = dto.DebtCounselorConfirmation;
                    result.Record.DebtManagementAgencyName = dto.DebtManagementAgencyName;
                    result.Record.DebtManagementEmail = dto.DebtManagementEmail;
                },
            });
    }
}
