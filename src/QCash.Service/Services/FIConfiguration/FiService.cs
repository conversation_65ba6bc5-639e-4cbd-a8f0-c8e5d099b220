using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;

namespace QCash.Service.Services.FIConfiguration;

public class FiService(
    QCashContext context,
    IEfPocoService efPocoService,
    IFiSettingService settingService,
    IAuthUserService authUserService
    ) : IFiService
{
    public string RecordName { get; } = "Financial Institution General settings";

    public async Task<FiInfoDto?> GetPageDataAsync()
    {
        var data = await context.FinancialInstitutions
            .AsNoTracking()
            .Select(a => new FiInfoDto
            {
                FinancialInstitutionId = a.Id,
                Name = a.Name,
                Address = a.Address,
                City = a.City,
                NoReplySubdomain = a.NoReplySubdomain,
                NoReplyDisplayName = a.NoReplyDisplayName,
                Zip = a.Zip,
                State = a.State,
                ContactName = a.ContactName,
                ContactPhone = a.ContactPhone,
                ContactEmail = a.MailingAddress,
                SupportEmail = a.SupportEmail,
                FinancialInstitutionTimeStamp = a.TimeStamp,
                SettingId = (a.Setting == null) ? null : a.Setting.Id,
                SettingTimeStamp = (a.Setting != null) ? a.Setting.TimeStamp : new byte[] { },
                TimeZone = ((a.Setting == null) ? null : a.Setting.TimeZone) ?? "",
            }).FirstOrDefaultAsync();
        return data;
    }

    public async Task<GenericActionResult> SaveAsync(Guid applicationId, FiInfoDto dto)
    {
        var settingUpdateResult = await efPocoService.CreateOrUpdateAsync(dto.SettingId ?? Guid.Empty, applicationId, dto.SettingTimeStamp,
            new PerformCreateOrUpdateOptions<Setting>
            {
                ExecuteRecordChangesFunc = (performUpdateParam) =>
                {
                    if (performUpdateParam.CreatingNewRecord)
                    {
                        settingService.SetDefaults(performUpdateParam.Record, dto.FinancialInstitutionId);
                    }
                    performUpdateParam.Record.TimeZone = dto.TimeZone;
                },
            });
        if (!settingUpdateResult.IsSuccessful)
        {
            return settingUpdateResult;
        }

        var fiUpdateResult = await efPocoService.CreateOrUpdateAsync(dto.FinancialInstitutionId, applicationId, dto.FinancialInstitutionTimeStamp,
            new PerformCreateOrUpdateOptions<FinancialInstitution>()
            {
                ExecuteRecordChangesFunc = performUpdateParam =>
                {
                    performUpdateParam.Record.Name = dto.Name;
                    performUpdateParam.Record.Address = dto.Address;
                    performUpdateParam.Record.City = dto.City;
                    performUpdateParam.Record.NoReplySubdomain = dto.NoReplySubdomain;

                    performUpdateParam.Record.NoReplyDisplayName = dto.NoReplyDisplayName;
                    performUpdateParam.Record.Zip = dto.Zip;
                    performUpdateParam.Record.State = dto.State;
                    performUpdateParam.Record.ContactName = dto.ContactName;
                    performUpdateParam.Record.ContactPhone = dto.ContactPhone;
                    performUpdateParam.Record.MailingAddress = dto.ContactEmail;
                    performUpdateParam.Record.SupportEmail = dto.SupportEmail;
                },
            });
        return fiUpdateResult;
    }

    public async Task<FiGeneralInfoDto?> GetGeneralInfoAsync()
    {
        var data = await context.Settings
            .Select(s => new FiGeneralInfoDto
            {
                FiSlug = s.FinancialInstitution.Slug,
                SettingId = s.Id,
                SettingTimeStamp = s.TimeStamp,
                FinancialInstitutionId = s.FinancialInstitutionId,
                MonitorAccount = s.MonitorAccount,
                CoreCheckInterval = s.CoreCheckInterval,
                CacheFailedCoreResponses = s.CacheFailedCoreResponses,
                UseTwoFactorAuthentication = s.UseTwoFactorAuthentication,
            })
            .FirstOrDefaultAsync();
        return data;
    }

    public async Task<GenericActionResult> SaveGeneralInfoAsync(Guid applicationId, FiGeneralInfoDto dto)
    {
        var settingUpdateResult = await efPocoService.CreateOrUpdateAsync(dto.SettingId, applicationId, dto.SettingTimeStamp,
            new PerformCreateOrUpdateOptionsAsync<Setting>
            {
                ExecuteRecordChangesFunc = (performUpdateParam) =>
            {
                if (performUpdateParam.CreatingNewRecord)
                {
                    settingService.SetDefaults(performUpdateParam.Record, dto.FinancialInstitutionId);
                }
                performUpdateParam.Record.MonitorAccount = dto.MonitorAccount;

                if (authUserService.IsSuperUser() || authUserService.IsSystemAdmin())
                {
                    performUpdateParam.Record.CoreCheckInterval = dto.CoreCheckInterval;
                    performUpdateParam.Record.CacheFailedCoreResponses = dto.CacheFailedCoreResponses;
                    performUpdateParam.Record.UseTwoFactorAuthentication = dto.UseTwoFactorAuthentication;
                }

                return Task.CompletedTask;
            },
            });
        return settingUpdateResult;
    }
}
