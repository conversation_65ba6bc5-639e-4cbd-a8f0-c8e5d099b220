using Microsoft.EntityFrameworkCore;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;

namespace QCash.Service.Services.FIConfiguration;

public class InvoiceSettingsService(IEfPocoService efPocoService) : IInvoiceSettingsService
{
    public string RecordName { get; } = "Invoice Setting";
    private static InvoiceGeneralSettingsDto CreateBlankDto() =>
        new()
        {
            InvoicePlanId = Guid.Empty,
            InvoicePlanTimeStamp = [],
            CustomerId = null,
            ResellerName = null,
            TransactionType = "",
            MinimumFee = null,
            FeeStartDate = null,
            FeeEndDate = null,
        };

    public async Task<InvoiceGeneralSettingsDto> GetGeneralInfoAsync(Guid applicationId)
    {
        var data = await efPocoService.GetQuery<InvoicePlan>()
            .AsNoTracking()
            .Select(data => new InvoiceGeneralSettingsDto()
            {
                InvoicePlanId = data.Id,
                InvoicePlanTimeStamp = data.TimeStamp,
                CustomerId = data.CustomerId,
                ResellerName = data.ResellerName,
                TransactionType = data.TransactionType,
                MinimumFee = data.MinimumFee,
                FeeStartDate = data.FeeStartDate,
                FeeEndDate = data.FeeEndDate,
            }).FirstOrDefaultAsync();
        return data ?? CreateBlankDto();
    }

    public InvoiceSettingsNavHeaderDto GetNavHeader(string fiSlug) => new() { FiSlug = fiSlug };

    public async Task<GetOrCreateRecordResult<InvoicePlan>> SaveSettingsAsync(InvoiceGeneralSettingsDto dto, Guid applicationId) =>
        await efPocoService.CreateOrUpdateAsync(dto.InvoicePlanId, applicationId, dto.InvoicePlanTimeStamp,
            new PerformCreateOrUpdateOptions<InvoicePlan>()
            {
                ExecuteRecordChangesFunc = (performUpdateParam) =>
                {
                    performUpdateParam.Record.CustomerId = dto.CustomerId;
                    performUpdateParam.Record.ResellerName = dto.ResellerName;
                    performUpdateParam.Record.TransactionType = dto.TransactionType;
                    performUpdateParam.Record.MinimumFee = dto.MinimumFee;
                    performUpdateParam.Record.FeeStartDate = dto.FeeStartDate;
                    performUpdateParam.Record.FeeEndDate = dto.FeeEndDate;
                },
            });

}
