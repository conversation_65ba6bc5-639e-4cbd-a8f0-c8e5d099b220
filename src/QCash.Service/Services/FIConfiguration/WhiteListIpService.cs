using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using Enums = QCash.Service.Models.Core.Enums;

namespace QCash.Service.Services.FIConfiguration;

public class WhiteListIpService(
    IEfPocoService efPocoService,
    IIPAddressService ipAddressService
    ) : IWhiteListIpService
{
    public async Task<List<WhiteListIpDto>> GetAllAsync(Enums.WhiteListIpTypeEnum whiteListIpType) =>
        whiteListIpType switch
        {
            Enums.WhiteListIpTypeEnum.Unknown => throw new ArgumentException("WhiteListIpTypeEnum.Unknown is not supported", nameof(whiteListIpType)),
            Enums.WhiteListIpTypeEnum.AdminUI => await efPocoService.GetQuery<WhiteListIp>()
                .AsNoTracking()
                .Select(a => new WhiteListIpDto()
                {
                    WhiteListIpType = whiteListIpType,
                    Id = a.Id,
                    IpAddress = a.IpAddress,
                    Description = a.Description,
                    Port = null,
                    TimeStamp = a.TimeStamp,
                }).ToListAsync(),
            Enums.WhiteListIpTypeEnum.MemberUI => await efPocoService.GetQuery<IpRestriction>()
                .AsNoTracking()
                .Select(a => new WhiteListIpDto()
                {
                    WhiteListIpType = whiteListIpType,
                    Id = a.Id,
                    IpAddress = a.IpAddress,
                    Description = a.Description,
                    Port = a.Port,
                    TimeStamp = a.TimeStamp,
                }).ToListAsync(),
            _ => throw new ArgumentException($"WhiteListIpTypeEnum of {whiteListIpType} is not supported", nameof(whiteListIpType)),
        };

    public async Task<GetOrCreateRecordResult> SaveAsync(WhiteListIpDto dto, Guid applicationId) =>
        dto.WhiteListIpType switch
        {
            Enums.WhiteListIpTypeEnum.AdminUI => (await efPocoService.CreateOrUpdateAsync(dto.Id, applicationId, dto.TimeStamp,
                new PerformCreateOrUpdateOptions<WhiteListIp>()
                {
                    ExecuteRecordChangesFunc = (a) =>
                    {
                        a.Record.IpAddress = ipAddressService.ParseIP(dto.IpAddress);
                        a.Record.Description = dto.Description;
                    },
                })).ToUnTyped(),
            Enums.WhiteListIpTypeEnum.MemberUI => (await efPocoService.CreateOrUpdateAsync(dto.Id, applicationId, dto.TimeStamp,
                new PerformCreateOrUpdateOptions<IpRestriction>()
                {
                    ExecuteRecordChangesFunc = (a) =>
                    {
                        a.Record.Port = dto.Port.GetValueOrDefault(0);
                        a.Record.IpAddress = ipAddressService.ParseIP(dto.IpAddress);
                        a.Record.Description = dto.Description;
                    },
                })).ToUnTyped(),
            _ => throw new Exception($"WhiteListIpTypeEnum of {dto.WhiteListIpType} is not supported"),
        };

    public async Task<GenericActionResult> DeleteItemAsync(Enums.WhiteListIpTypeEnum whiteListIpType, DeleteItemDto dto) =>
        whiteListIpType switch
        {
            Enums.WhiteListIpTypeEnum.Unknown => throw new Exception($"WhiteListIpTypeEnum of Unknown is not supported"),
            Enums.WhiteListIpTypeEnum.AdminUI => await efPocoService.DeleteItemAsync<WhiteListIp>(dto),
            Enums.WhiteListIpTypeEnum.MemberUI => await efPocoService.DeleteItemAsync<IpRestriction>(dto),
            _ => throw new Exception($"WhiteListIpTypeEnum of {whiteListIpType} is not supported"),
        };

    public void ValidateModel(WhiteListIpDto dto, ModelStateDictionary modelState)
    {
        if (!ipAddressService.IsIPAddressOrRange(dto.IpAddress))
        {
            modelState.AddModelError(nameof(WhiteListIpDto.IpAddress), "Invalid IP Address or Range!");
        }

        if (dto.WhiteListIpType == Enums.WhiteListIpTypeEnum.MemberUI)
        {
            if (dto.Port == null)
            {
                modelState.AddModelError(nameof(WhiteListIpDto.Port), "Port is required.");
            }
        }

        if (dto.Port is > 65535 or < 1)
        {
            modelState.AddModelError(nameof(WhiteListIpDto.Port), "Unsupported port value.");
        }
    }

    public bool ShouldShowPortProperty(Enums.WhiteListIpTypeEnum whiteListIpType) =>
        whiteListIpType == Enums.WhiteListIpTypeEnum.MemberUI;

    public WhiteListIpDto CreateBlankItem(Enums.WhiteListIpTypeEnum whiteListIpType) =>
        new()
        {
            Id = Guid.Empty,
            IpAddress = "",
            Description = "",
            Port = null,
            WhiteListIpType = whiteListIpType,
            TimeStamp = [],
        };

    public string GetListPageTitle(Enums.WhiteListIpTypeEnum whiteListIpType) =>
        whiteListIpType == Enums.WhiteListIpTypeEnum.AdminUI
            ? "Admin Portal Access: Allowed IPs"
            : "Application Access: Allowed IPs";
}
