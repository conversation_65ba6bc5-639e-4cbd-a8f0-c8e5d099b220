using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Utilities.Extensions;

namespace QCash.Service.Services.FIConfiguration;

public class LoanExclusionService(
    IEfPocoService efPocoService,
    IAuthUserService authUserService,
    QCashContext dbContext) : ILoanExclusionService
{
    public string RecordName { get; } = "Loan Exclusion Settings";

    /// <summary>
    /// Assumes we have a setting and financial institution record for this applicationId, and load the setting data.
    /// Optionally, If there is a LoanExclusion record, load that also.
    /// </summary>
    /// <param name="financialInstitutionId"></param>
    /// <returns></returns>
    public async Task<LoanExclusionSettingDto> GetSettingsAsync(Guid financialInstitutionId)
    {
        var q = from s in dbContext.Settings
                where s.FinancialInstitution.Id == financialInstitutionId
                let les = dbContext.LoanExclusionSettings.FirstOrDefault(a => a.FinancialInstitutionId == s.FinancialInstitutionId)
                select new LoanExclusionSettingDto()
                {
                    FinancialInstitutionId = s.FinancialInstitutionId,
                    SettingId = s.Id,
                    SettingTimeStamp = s.TimeStamp,
                    ShouldDenyOnContactInfoChanged = s.ShouldDenyOnContactInfoChanged,
                    MinDaysOnContactInfoChanged = s.MinDaysOnContactInfoChanged,

                    Id = les == null ? Guid.Empty : les.Id,
                    TimeStamp = les == null ? new byte[] { } : les.TimeStamp,
                    DeniedLoansExclusion = les != null && les.DeniedLoansExclusion,
                    NumberOfDeniedLoans = les == null ? 0 : les.NumberOfDeniedLoans,
                    DeniedLoansThreshold = les == null ? 0 : les.DeniedLoansTreshold,
                };
        var loanExclusionSettingInfo = await q
            .AsNoTracking()
            .SingleOrDefaultAsync();
        loanExclusionSettingInfo.ThrowIfNull($"Setting record not found with financialInstitutionId {financialInstitutionId}");
        return loanExclusionSettingInfo;
    }

    public bool HasPermissionForFraudSection()
    {
        var result = authUserService.IsSuperUser()
                 || authUserService.IsSystemAdmin()
                 || authUserService.IsFiManager();
        return result;
    }

    public async Task<GetOrCreateRecordResult> SaveSettingsAsync(LoanExclusionSettingDto dto, Guid applicationId)
    {
        var changeResult = await efPocoService.CreateOrUpdateAsync(dto.Id, financialInstitutionId: null, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<LoanExclusionSetting>()
            {
                QueryableCustomization = (query) =>
                    query.Where(a => a.FinancialInstitutionId == dto.FinancialInstitutionId).AsTracking(),
                ExecuteRecordChangesFunc = (result) =>
                {
                    if (result.CreatingNewRecord)
                    {
                        result.Record.FinancialInstitutionId = dto.FinancialInstitutionId;
                    }
                    result.Record.DeniedLoansExclusion = dto.DeniedLoansExclusion;
                    result.Record.NumberOfDeniedLoans = dto.NumberOfDeniedLoans;
                    result.Record.DeniedLoansTreshold = dto.DeniedLoansThreshold;
                },
            });
        if (!changeResult.IsSuccessful || !HasPermissionForFraudSection())
        {
            return changeResult.ToUnTyped();
        }

        var settingSaveResult = await efPocoService.CreateOrUpdateAsync(dto.SettingId, financialInstitutionId: null, dto.SettingTimeStamp, new PerformCreateOrUpdateOptions<Setting>()
        {
            ExecuteRecordChangesFunc = (a) =>
            {
                if (a.CreatingNewRecord)
                {
                    throw new Exception($"Setting record not found with SettingId {dto.SettingId}");
                }
                a.Record.ShouldDenyOnContactInfoChanged = dto.ShouldDenyOnContactInfoChanged;
                a.Record.MinDaysOnContactInfoChanged = dto.MinDaysOnContactInfoChanged;
            },
        });
        return settingSaveResult.ToUnTyped();
    }
}
