using System.Net;
using System.Text;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using QCash.Data.Models;
using QCash.LoanApplication;
using QCash.Models.Api;
using QCash.Models.Api.Get;
using QCash.Models.Api.Post;
using QCash.Service.Models.General;
using QCash.Service.Models.LoanApplication;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.LoanApplication;
using QCash.Service.Services.LoanApplication.Interfaces;
using static QCash.Service.Models.LoanApplication.Enums;
using Enums = QCash.Service.Models.FIConfiguration.Enums;

namespace QCash.Service.Services.FIConfiguration;

public class QCashConnectService(
    IEfPocoService efPocoService,
    ILoanApplicationHelper loanApplicationHelper,
    IMemberProcessInterfaceService memberProcessInterfaceService,
    ILoanApplicationStepsService loanApplicationStepsService,
    IFinancialInstitutionService financialInstitutionService,
    ILoanApplicationService loanApplicationService,
    ILanguageService languageService,
    ILogger<QCashConnectService> logger) : IQCashConnectService
{
    private const string RequestStatusError = "Error";

    public string RecordName => "QCash Connect Settings";

    private static QCashConnectSettingsDto CreateBlankDto() => new()
    {
        FraudControlSettingId = Guid.Empty,
        FraudControlSettingTimeStamp = [],
        IsFraudControlEnabled = false,
        PreferredDelivery = nameof(Enums.FraudControlPreferredDeliveryMethod.Email),
        Location = "",
        TellerId = "",
        ResendThreshold = 5,
        SubmitThreshold = 5,
        CodeValidityMinutesThreshold = 15,
        ThresholdExceededBlockPeriod = 0,
    };

    public async Task<QCashConnectSettingsDto> GetSettingsAsync() => await efPocoService
        .GetQuery<LoginFraudControlSetting>()
        .AsNoTracking()
        .Select(settings => new QCashConnectSettingsDto
        {
            FraudControlSettingId = settings.Id,
            FraudControlSettingTimeStamp = settings.TimeStamp,
            IsFraudControlEnabled = settings.IsEnabled,
            PreferredDelivery = settings.PreferredDelivery,
            Location = settings.Location,
            TellerId = settings.TellerId,
            ResendThreshold = settings.ResendThreshold,
            SubmitThreshold = settings.SubmitThreshold,
            CodeValidityMinutesThreshold = settings.CodeValidityMinutesThreshold,
            ThresholdExceededBlockPeriod = settings.ThresholdExceededBlockPeriod,
        })
        .SingleOrDefaultAsync() ?? CreateBlankDto();

    public async Task<GetOrCreateRecordResult<LoginFraudControlSetting>> SaveSettingsAsync(QCashConnectSettingsDto dto, Guid financialInstitutionId) =>
        await efPocoService.CreateOrUpdateAsync(dto.FraudControlSettingId, financialInstitutionId: null, dto.FraudControlSettingTimeStamp,
            new PerformCreateOrUpdateOptions<LoginFraudControlSetting>
            {
                ExecuteRecordChangesFunc = (result) =>
                {
                    if (result.CreatingNewRecord)
                    {
                        result.Record.FinancialInstitutionId = financialInstitutionId;
                    }

                    result.Record.IsEnabled = dto.IsFraudControlEnabled;
                    result.Record.PreferredDelivery = dto.PreferredDelivery;
                    result.Record.Location = dto.Location;
                    result.Record.TellerId = dto.TellerId;
                    result.Record.ResendThreshold = dto.ResendThreshold;
                    result.Record.SubmitThreshold = dto.SubmitThreshold;
                    result.Record.CodeValidityMinutesThreshold = dto.CodeValidityMinutesThreshold;
                    result.Record.ThresholdExceededBlockPeriod = dto.ThresholdExceededBlockPeriod;
                },
            });

    public async Task<LoginResponse> LoginQCashConnectAsync(LoginApiModelPost model, Guid correlationId)
    {
        var qCashConnectSettingsDto = await GetSettingsAsync();

        if (!qCashConnectSettingsDto.IsFraudControlEnabled)
            return new LoginResponse(RequestStatusError, "Feature is disabled");

        var ssoInitialModel = new SsoInitiateApiModel
        {
            OriginApp = (int)OriginApp.Web,
            ReturnAsHtml = true,
            BaseAccount = model.AccountNumber,
        };

        FinancialInstitutionMember member;
        try
        {
            var ssoResponse = await loanApplicationStepsService.GetMemberBySsoAsync(ssoInitialModel, correlationId, []);

            if (ssoResponse.FinancialInstitutionMember == null)
            {
                logger.LogWarning("No member found with the specified credentials");
                return new LoginResponse(nameof(HttpStatusCode.Unauthorized), "Invalid credentials");
            }

            member = ssoResponse.FinancialInstitutionMember;
        }
        catch (CoreProviderException coreException)
        {
            logger.LogWarning("Login QCash Connect failed with CoreProviderException exception: {Message}", coreException.Message);
            return new LoginResponse(RequestStatusError, GetErrorMessage(coreException.CoreProviderFault!.StandardCoreProviderError));
        }
        catch (Exception exception)
        {
            logger.LogError(exception, "Login QCash Connect failed");
            if (exception is LoanApplicationException laException &&
                laException.Errors.Any(error => error.StartsWith(nameof(FinancialInstitutionMember.LastName), StringComparison.OrdinalIgnoreCase)))
            {
                return new LoginResponse(RequestStatusError, LoanApplicationStepsService.LastNameMissingError);
            }

            return new LoginResponse(RequestStatusError, "QCash Error");
        }

        if (member.Zip == null || member.Zip.Length < 5 || member.Zip[..5] != model.ZipCode)
        {
            logger.LogWarning("Invalid zip");
            return new LoginResponse(RequestStatusError, "Invalid credentials");
        }

        var loginFraudControl = await loanApplicationHelper.GetFinancialInstitutionMemberLoginFraudControlByAccountIdAsync(model.AccountNumber!);
        var language = await languageService.GetLanguageByIdAsync();

        if (loginFraudControl != null)
        {
            var isSubmitThresholdMet = loginFraudControl.SubmitNo >= loginFraudControl.SubmitThreshold;
            var isResendThresholdMet = loginFraudControl.ResendNo >= loginFraudControl.ResendThreshold;

            if (isSubmitThresholdMet || isResendThresholdMet)
            {
                if (isSubmitThresholdMet)
                    logger.LogTrace("QCash Connect. Submit threshold met");

                if (isResendThresholdMet)
                    logger.LogTrace("QCashConnect. Resend threshold met");

                var thresholdBlockPeriod = loginFraudControl.DateUpdatedUtc
                    .AddHours(qCashConnectSettingsDto.ThresholdExceededBlockPeriod);

                if (DateTime.UtcNow < thresholdBlockPeriod)
                {
                    var fraudControlInterfaceTexts = await memberProcessInterfaceService
                        .GetFormattedInterfaceTextsForLoginFraudControlAsync(member, OriginApp.Api, language.Id);
                    var error = fraudControlInterfaceTexts
                        .First(p => p.Key == "FraudControlSubmitThresholdMet").Value;
                    return new LoginResponse(RequestStatusError, error);
                }

                financialInstitutionService.ResetLoginFraudControl(member, qCashConnectSettingsDto);
            }
        }

        var ssn = await financialInstitutionService.GetTaxIdByFinancialInstitutionMemberIdAsync(member.Id);
        if (ssn == null)
        {
            logger.LogWarning("QCashConnect. Failed to fetch SSN");
            return new LoginResponse(RequestStatusError, "Temporary error. Please try again later.");
        }

        var (isNotificationSent, errorMessage) = await financialInstitutionService
            .SetupLoginFraudControlAndSendNotificationAsync(member, qCashConnectSettingsDto, OriginApp.Api);

        if (!isNotificationSent)
        {
            logger.LogWarning("QCashConnect: Failed to send OTP code. ErrorMessage: {ErrorMessage}", errorMessage);
            return new LoginResponse(RequestStatusError, $"Error sending notification: \n{errorMessage}");
        }

        logger.LogTrace($"QCashConnect. Sent OTP code");
        var interfaceTexts = await memberProcessInterfaceService.GetFormattedInterfaceTextsForLoginFraudControlAsync(member, OriginApp.Api, language.Id);

        return new LoginResponse
        {
            Status = nameof(HttpStatusCode.OK),
            CodeId = member.Id.ToString(),
            FraudControlApiModel = new FraudControlApiModelGet
            {
                DisplayValues = [.. interfaceTexts.Select(text => new DisplayTextApiModel
                {
                    Key = text.Key,
                    Value = text.Value,
                }),],
                SubmitThresholdMet = loginFraudControl != null && loginFraudControl.SubmitNo >= loginFraudControl.SubmitThreshold,
                ResendThresholdMet = loginFraudControl != null && loginFraudControl.ResendNo >= loginFraudControl.ResendThreshold,
            },
        };
    }

    public async Task<LoginFraudControlResponse> LoginFraudControlAsync(LoginFraudControlApiModelPost model)
    {
        var qCashConnectSettingsDto = await GetSettingsAsync();

        if (!qCashConnectSettingsDto.IsFraudControlEnabled)
        {
            logger.LogWarning("QCashConnect. Fraud Control is disabled");
            return new LoginFraudControlResponse
            {
                Status = RequestStatusError,
                Errors = ["Feature is disabled"],
            };
        }

        if (!Guid.TryParse(model.CodeId, out var memberId))
        {
            logger.LogWarning("QCashConnect. Invalid database member Id");
            return new LoginFraudControlResponse
            {
                ValidationResult = (int)LoginFraudControlValidationResult.InvalidCodeId,
                Status = RequestStatusError,
                Errors = ["Invalid code ID"],
            };
        }

        var member = await loanApplicationService.GetFinancialInstitutionMemberByIdAsync(memberId);

        if (member == null || string.IsNullOrEmpty(member.AccountId))
        {
            logger.LogWarning("QCashConnect. Invalid database member Id");
            return new LoginFraudControlResponse
            {
                ValidationResult = (int)LoginFraudControlValidationResult.InvalidCodeId,
                Status = RequestStatusError,
                Errors = ["Invalid code ID"],
            };
        }

        var language = await languageService.GetLanguageByIdAsync();
        var logs = new List<LoanApplicationLogDetail>();
        var (result, error) = await financialInstitutionService
            .ValidateFraudControlAsync(member, qCashConnectSettingsDto, model, language.Id, logs);
        var loanApplication = await loanApplicationHelper.GetLoanApplicationByFinancialInstitutionMemberIdAsync(member.Id);

        if (loanApplication != null && logs.Count != 0)
        {
            await loanApplicationStepsService.SaveLoanApplicationLogMessagesAsync(loanApplication, logs);
        }
        else if (logs.Count != 0)
        {
            var message = new StringBuilder($"Login Fraud Control/QCash Connect logs for member: {member.MemberIdMask}");
            message = logs.Aggregate(message, (sb, log) => sb.AppendLine(log.ActionDescription));
            message.AppendLine($"Result: {result}{(!string.IsNullOrEmpty(error) ? $" Error: {error}" : "")}");
            if (result == LoginFraudControlValidationResult.Success)
                logger.LogInformation(message.ToString());
            else
                logger.LogWarning(message.ToString());
        }

        return new LoginFraudControlResponse
        {
            ValidationResult = (int)result,
            Status = result == LoginFraudControlValidationResult.Success
                ? nameof(HttpStatusCode.OK)
                : RequestStatusError,
            Errors = string.IsNullOrEmpty(error) ? null : [error],
            Location = string.IsNullOrEmpty(qCashConnectSettingsDto.Location) ? "ALT" : qCashConnectSettingsDto.Location,
            TellerId = qCashConnectSettingsDto.TellerId,
        };
    }

    private static string GetErrorMessage(StandardCoreProviderError err) => err switch
    {
        StandardCoreProviderError.UnknownCoreError => "Core Error",
        StandardCoreProviderError.UnknownMiddlewareError or
        StandardCoreProviderError.InvalidParameterMiddlewareError or
        StandardCoreProviderError.MissingParametersMiddlewareError or
        StandardCoreProviderError.CommunicationMiddlewareError => "Middleware Error",
        StandardCoreProviderError.AnInactiveUserNumberWasSpecified or
        StandardCoreProviderError.AnInvalidUserNumberWasSpecified or
        StandardCoreProviderError.AccountNotFound => "Invalid credentials",
        _ => "QCash Error",
    };
}
