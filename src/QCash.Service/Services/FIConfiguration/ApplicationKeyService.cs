using Microsoft.EntityFrameworkCore;
using QCash.Common.Crypto;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Helpers;

namespace QCash.Service.Services.FIConfiguration;

public class ApplicationKeyService(
    IEfPocoService efPocoService,
    ITokenManagerService tokenManagerService) : IApplicationKeyService
{
    public string RecordName { get; } = "Application Info";

    public async Task<ApplicationKeyDto> GetPageDataAsync()
    {
        var data = await efPocoService.GetQuery<ApplicationKey>()
            .Select(appKey => new ApplicationKeyDto
            {
                Id = appKey.Id,
                ApplicationId = appKey.SsoApplicationKey,
                SharedKeyWeb = tokenManagerService.Decrypt(appKey.SsoV2SharedKeyWeb ?? ""),
                SecondarySharedKey = tokenManagerService.Decrypt(appKey.SsoSecondaryAppSharedKeyWeb ?? ""),
                ApplicationSsoKey = tokenManagerService.Decrypt(appKey.SsoV2WebKey),
                SecondaryApplicationSsoKey = tokenManagerService.Decrypt(appKey.SsoSecondaryAppWebKey ?? ""),
                SsoSecondaryEnabled = appKey.SsoSecondaryEnabled,
                TimeStamp = appKey.TimeStamp,
            })
            .SingleOrDefaultAsync();
        return data ?? GetDefaultEmptyDto();
    }

    private ApplicationKeyDto GetDefaultEmptyDto() => new()
    {
        Id = Guid.Empty,
        ApplicationId = Guid.Empty,
        SharedKeyWeb = "",
        SecondarySharedKey = "",
        ApplicationSsoKey = "",
        SecondaryApplicationSsoKey = "",
        SsoSecondaryEnabled = false,
        TimeStamp = [],
    };

    public async Task<GetOrCreateRecordResult<ApplicationKey>> SaveAsync(Guid financialInstitutionId, ApplicationKeyDto dto) =>
        await efPocoService.CreateOrUpdateAsync(dto.Id, financialInstitutionId, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<ApplicationKey>()
            {
                ExecuteRecordChangesFunc = appKey =>
                {
                    if (appKey.CreatingNewRecord)
                    {
                        appKey.Record.SsoApplicationKey = Guid.NewGuid();
                        appKey.Record.FinancialInstitutionId = financialInstitutionId;
                    }
                    appKey.Record.SsoV2SharedKeyWeb = tokenManagerService.Encrypt(dto.SharedKeyWeb ?? "");
                    appKey.Record.SsoSecondaryAppSharedKeyWeb = tokenManagerService.Encrypt(dto.SecondarySharedKey ?? "");
                    appKey.Record.SsoV2WebKey = tokenManagerService.Encrypt(dto.ApplicationSsoKey);
                    appKey.Record.SsoSecondaryAppWebKey = tokenManagerService.Encrypt(dto.SecondaryApplicationSsoKey ?? "");
                    appKey.Record.SsoSecondaryEnabled = dto.SsoSecondaryEnabled;
                },
            });

    public ApplicationKeyDto GenerateSecondaryKeys(ApplicationKeyDto dto)
    {
        dto.SecondarySharedKey = CryptoHelper.GenerateKey(16);
        dto.SecondaryApplicationSsoKey = CryptoHelper.GenerateKey(32);
        return dto;
    }
}
