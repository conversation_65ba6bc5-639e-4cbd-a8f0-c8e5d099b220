using Microsoft.EntityFrameworkCore;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;

namespace QCash.Service.Services.FIConfiguration;

public class NSFService(IEfPocoService efPocoService) : INSFService
{
    public string RecordName { get; } = "Loan Application NSF Settings";
    private static NSFSettingsDto CreateBlankDto() =>
        new()
        {
            NsfSettingId = Guid.Empty,
            NsfSettingTimeStamp = [],
            FinancialInstitutionId = Guid.Empty,
            NsfCountThreshold = 0,
            NsfCountMonthsThreshold = 0,
            NsfCountSelector = null,
        };

    public async Task<NSFSettingsDto> GetSettingsAsync(Guid financialInstitutionId) =>
        await efPocoService.GetQuery<NsfSetting>(q =>
                q.Where(nsf => nsf.FinancialInstitution.Id == financialInstitutionId))
            .AsNoTracking()
            .Select(a => new NSFSettingsDto()
            {
                NsfSettingId = a.Id,
                NsfSettingTimeStamp = a.TimeStamp,
                FinancialInstitutionId = a.FinancialInstitutionId,
                NsfCountThreshold = a.NsfCountThreshold,
                NsfCountMonthsThreshold = a.NsfCountMonthsThreshold,
                NsfCountSelector = a.NsfCountSelector,
            }).SingleOrDefaultAsync() ?? CreateBlankDto();

    public async Task<GetOrCreateRecordResult<NsfSetting>> SaveSettingsAsync(NSFSettingsDto dto, Guid financialInstitutionId)
    {
        if (dto.NsfSettingId.Equals(Guid.Empty) && dto.FinancialInstitutionId.Equals(Guid.Empty))
        {
            // If we are creating a new record, we may not know the FinancialInstitutionId
            var fi = efPocoService.GetQuery<FinancialInstitution>(q =>
                    q.Where(a => a.Id == financialInstitutionId))
                .AsNoTracking()
                .Select(a => a.Id)
                .FirstOrDefault();
            dto.FinancialInstitutionId = fi;
        }

        return await efPocoService.CreateOrUpdateAsync(dto.NsfSettingId, financialInstitutionId: null, dto.NsfSettingTimeStamp,
            new PerformCreateOrUpdateOptions<NsfSetting>()
            {
                ExecuteRecordChangesFunc = (result) =>
                {
                    if (result.CreatingNewRecord)
                    {
                        result.Record.FinancialInstitutionId = dto.FinancialInstitutionId;
                    }

                    result.Record.NsfCountThreshold = dto.NsfCountThreshold;
                    result.Record.NsfCountMonthsThreshold = dto.NsfCountMonthsThreshold;
                    result.Record.NsfCountSelector = dto.NsfCountSelector;
                },
            });
    }
}
