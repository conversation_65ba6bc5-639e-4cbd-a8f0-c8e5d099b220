using System.Net;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Models.Api;
using QCash.Service.Services.FIConfiguration.Interfaces;

namespace QCash.Service.Services.FIConfiguration;

public class FiSettingService(QCashContext qCashContext) : IFiSettingService
{
    /// <inheritdoc />
    public void SetDefaults(Setting setting, Guid financialInstitutionId)
    {
        setting.FinancialInstitutionId = financialInstitutionId;
        setting.LoanInterestAnnualPercentage = 0;
        setting.EConsent = false;
        setting.IsActive = false;
        setting.MilitaryAnnualPercentageRate = 0;
        setting.CampaignCode = false;
        setting.MonitorAccount = "";
        setting.LoanApplicationFee = 0;
        setting.LoanApplicationFeeDescription = "";
        setting.FilenameTila = "";
        setting.FilenameAan = "";
        setting.MlaCheckValid = 0;
        setting.MaprLoanApplicationFee = 0;
        setting.ExcludeVoidedTransactions = false;
        setting.LoanSubmissionEmailAlert = false;
        setting.MaskInitiateWaitTime = false;
        setting.LoanPurposeExclusion = false;
        setting.LogRng = false;
        setting.TrackCustomLoanData = false;
        setting.UseManualCampaignCodeInput = false;
        setting.TilaCalculatorSource = "";
        setting.LoanIdSource = "";
        setting.Bankruptcy = "";
        setting.Mla = "";
        setting.MlaCheckValid = 0;
        setting.AppLogsFileNameTemplate = "";
        setting.FilenameEConsent = "";
        setting.ContractNumber = "";
    }

    /// <inheritdoc />
    public string AdjustAccountNumber(string? accountNumber, int? requiredDigitsParam)
    {
        if (string.IsNullOrWhiteSpace(accountNumber))
        {
            return string.Empty;
        }

        int requiredDigits = requiredDigitsParam ?? 0;

        return requiredDigits switch
        {
            < 0 => accountNumber.TrimStart('0'),
            >= 0 when requiredDigits < accountNumber.Length => accountNumber,
            _ => $"{new string('0', requiredDigits - accountNumber.Length)}" + accountNumber,
        };
    }

    /// <inheritdoc />
    public async Task<FinancialInstitutionSettingsModel?> GetSettingsResponseAsync()
    {
        var maintenanceSettings = await qCashContext
            .MaintenanceSettings
            .AsNoTracking()
            .FirstOrDefaultAsync(settings => settings.IsMaintenanceActive);
        var isMaintenanceActive = maintenanceSettings?.IsMaintenanceActive ?? false;

        var dataCollectionSetting = await qCashContext
            .DataCollectionSettings
            .AsNoTracking()
            .FirstOrDefaultAsync();
        var isDataCollectionEnabled = dataCollectionSetting?.IsDataCollectionEnabled ?? false;

        var settings = await qCashContext
            .Settings
            .AsNoTracking()
            .Select(setting => new FinancialInstitutionSettingsModel
            {
                MaskInitiate = setting.MaskInitiateWaitTime,
                Payoff = setting.UsePayOff == true,
                IsMaintenanceActive = isMaintenanceActive,
                Status = nameof(HttpStatusCode.OK),
                LoanHub = setting.LoanHub,
                UseFirebirdUi = setting.UseFirebirdUi,
                IsDataCollectionEnabled = isDataCollectionEnabled,
            })
            .FirstOrDefaultAsync();

        return settings;
    }

    /// <inheritdoc />
    public async Task<int> GetRequiredDigitsAsync()
    {
        var requiredDigits = await qCashContext
            .Settings
            .Select(setting => setting.RequiredDigits)
            .FirstOrDefaultAsync();

        return requiredDigits;
    }
}
