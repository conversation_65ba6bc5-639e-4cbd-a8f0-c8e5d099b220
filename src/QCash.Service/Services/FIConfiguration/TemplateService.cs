using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using QCash.Data.Models;
using QCash.Data.Models.Enums;
using QCash.Service.FileSystem;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Helpers;
using QCash.Service.Services.Interfaces;
using QCash.Service.Utilities.Extensions;
using Enums = QCash.Service.Models.Core.Enums;

namespace QCash.Service.Services.FIConfiguration;

public class TemplateService(
    ILanguageService languageService,
    IEfPocoService efPocoService,
    IFileSystemProvider fileSystemProvider,
    ILogger<TemplateService> logger,
    IFilenameTemplateService filenameTemplateService,
    IFinancialInstitutionService financialInstitutionService
    ) : ITemplateService
{
    public string RecordName { get; } = "Template Settings";

    public async Task<TemplateSettingsDto> GetSettingsAsync(Guid financialInstitutionId, string? languageCode)
    {
        var languageChoices = await languageService.GetSupportedLanguagesAsync();

        // Default language to English if no languages exist in the database so the page can load
        var language = languageChoices.SingleOrDefault(l => l.LanguageCode == (languageCode ?? ""))
                       ?? new LanguageDto { Name = LanguageEnum.English.Name, LanguageCode = string.Empty };

        var settingData = await efPocoService.GetQuery<Setting>(q =>
                q.Where(a => a.FinancialInstitution.Id == financialInstitutionId))
            .AsNoTracking()
            .Select(s => new
            {
                SettingId = s.Id,
                SettingTimeStamp = s.TimeStamp,
                FiSlug = s.FinancialInstitution.Slug,
                LanguageChoices = languageService.GetSupportedLanguagesQuery().ToList(),
                ShouldShowLanguageChoices = false,      // actual value is set below
                AppLogsFileNameTemplate = s.AppLogsFileNameTemplate,
                FilenameAanTemplate = s.FilenameAan,
                FilenameEConsentTemplate = s.FilenameEConsent,
            })
            .SingleOrDefaultAsync();
        settingData.ThrowIfNull("Setting record not found.");

        var eConsentExists = await CheckIfDocumentTemplateFileExistsAsync(language.LanguageCode, Enums.LoanApplicationDocumentType.EConsentDisclosure);
        var eConsentFileRecord = !eConsentExists ? null
            : (await GetDocumentFileNameByDocumentTypeAndLanguageAsync(Enums.LoanApplicationDocumentType.EConsentDisclosure, language.Id));

        var aanExists = await CheckIfDocumentTemplateFileExistsAsync(language.LanguageCode, Enums.LoanApplicationDocumentType.AdverseActionNotice);
        var aanFileRecord = !aanExists ? null
            : (await GetDocumentFileNameByDocumentTypeAndLanguageAsync(Enums.LoanApplicationDocumentType.AdverseActionNotice, language.Id));

        var settingDto = new TemplateSettingsDto()
        {
            SettingId = settingData.SettingId,
            SettingTimeStamp = settingData.SettingTimeStamp,
            FiSlug = settingData.FiSlug,
            LanguageCode = language.LanguageCode,
            LanguageChoices = settingData.LanguageChoices.Select(a => new QListItem<string>(a.Name, a.LanguageCode)).ToList(),
            ShouldShowLanguageChoices = settingData.LanguageChoices.Count > 1,
            EConsentDocumentId = eConsentFileRecord?.Id,
            EConsentDocumentTimeStamp = eConsentFileRecord?.TimeStamp,
            EConsentFileName = eConsentFileRecord?.FileName,
            AANDocumentId = aanFileRecord?.Id,
            AANDocumentTimeStamp = aanFileRecord?.TimeStamp,
            AANFileName = aanFileRecord?.FileName,
            AppLogsFileNameTemplate = settingData.AppLogsFileNameTemplate,
            AANFileNameTemplate = settingData.FilenameAanTemplate,
            EConsentFileNameTemplate = settingData.FilenameEConsentTemplate,
        };
        return settingDto;
    }

    /// <summary>
    /// Checks if the Electronic Consent Disclosure template exists.
    /// </summary>
    /// <returns>File exists?</returns>
    private async Task<bool> CheckIfDocumentTemplateFileExistsAsync(string languageCode, Enums.LoanApplicationDocumentType docType)
    {
        var templateFileName = GetTemplateName(docType, languageCode);
        try
        {
            await fileSystemProvider.SetupAsync(ContainerTypeEnum.TemplatesPrivate);
            return await fileSystemProvider.FileExistsAsync(templateFileName);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error checking if file exists in CheckIfDocumentTemplateFileExistsAsync");
            return false;
        }
    }

    private string GetTemplateName(Enums.LoanApplicationDocumentType docType, string languageCode)
    {
        var template = docType switch
        {
            Enums.LoanApplicationDocumentType.AdverseActionNotice => "AdverseActionNotice",
            Enums.LoanApplicationDocumentType.EConsentDisclosure => "EConsentDisclosure",
            Enums.LoanApplicationDocumentType.TILA => throw new Exception("For TILA document use GetTilaDocumentTemplateName method"),
            _ => throw new Exception("Not supported document type detected."),
        };

        if (string.IsNullOrEmpty(languageCode))
        {
            template += "_template.pdf";
        }
        else
        {
            template += "_template_" + languageCode + ".pdf";
        }

        return template;
    }

    private async Task<DocumentFileName?> GetDocumentFileNameByDocumentTypeAndLanguageAsync(Enums.LoanApplicationDocumentType documentType, Guid languageId)
    {
        var documentTypeAbrv = documentType.ToDescriptionString();
        var result = await efPocoService.GetQuery<DocumentFileName>()
            .AsNoTracking()
            .OrderByDescending(a => a.DateCreatedUtc)
            .FirstOrDefaultAsync(df =>
                df.LanguageId == languageId
                && df.DocumentType.Abrv == documentTypeAbrv);
        return result;
    }

    public async Task<GetOrCreateRecordResult<DocumentFileName>> SaveFileAsync(Enums.LoanApplicationDocumentType docType,
        Guid financialInstitutionId, string originalfileName,
        IFormFile file, string languageCode, Guid? docId, string? docTimeStamp)
    {
        var language = efPocoService.GetQuery<Language>(q =>
                q.Where(l => l.LanguageCode == languageCode))
            .AsNoTracking().FirstOrDefault();
        language.ThrowIfNull("Language not found.");

        var documentType = await efPocoService.GetQuery<DocumentType>(q =>
                q.Where(dt => dt.Abrv == docType.ToDescriptionString()!))
            .AsNoTracking().SingleOrDefaultAsync();
        documentType.ThrowIfNull("DocumentType not found.");
        var ts = Convert.FromBase64String(docTimeStamp ?? "");

        var saveDataResult = await efPocoService.CreateOrUpdateAsync(docId ?? Guid.Empty, financialInstitutionId, ts,
            new PerformCreateOrUpdateOptions<DocumentFileName>()
            {
                ExecuteRecordChangesFunc = (p) =>
                {
                    p.Record.FileName = Path.GetFileNameWithoutExtension(originalfileName);
                    if (p.CreatingNewRecord)
                    {
                        p.Record.LanguageId = language.Id;
                        p.Record.DocumentTypeId = documentType.Id;
                    }
                },
            });
        if (!saveDataResult.IsSuccessful)
        {
            return saveDataResult;
        }

        var saveFileResult = await SaveFileToFileSystemAsync(docType, file, languageCode);
        return saveFileResult;
    }

    private async Task<GetOrCreateRecordResult<DocumentFileName>> SaveFileToFileSystemAsync(
        Enums.LoanApplicationDocumentType docType, IFormFile file, string languageCode)
    {
        bool storedResultSuccessfully;
        try
        {
            await fileSystemProvider.SetupAsync(ContainerTypeEnum.TemplatesPrivate);
            var fileName = GetTemplateName(docType, languageCode);
            await using var stream = file.OpenReadStream();
            storedResultSuccessfully = await fileSystemProvider.StoreFileAsync(stream, file.ContentType, fileName);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error saving file in SaveFileToFileSystemAsync");
            storedResultSuccessfully = false;
        }

        return new GetOrCreateRecordResult<DocumentFileName>()
        {
            IsSuccessful = storedResultSuccessfully,
            Record = null,
            FoundExistingRecord = false,
            CreatingNewRecord = false,
            EditingExistingRecord = false,
        };
    }

    private async Task<byte[]?> GetTemplateFileContentAsync(string templateFileName)        // previously named GetEConsentTemplate
    {
        await fileSystemProvider.SetupAsync(ContainerTypeEnum.TemplatesPrivate);
        return await fileSystemProvider.GetFileAsync(templateFileName);
    }

    public async Task<DownloadFileResult> DownloadFileAsync(Enums.LoanApplicationDocumentType docType, string? languageCode)
    {
        var templateFileName = GetTemplateName(docType, languageCode ?? "");
        var ms = new MemoryStream(await GetTemplateFileContentAsync(templateFileName) ?? []);
        var result = new DownloadFileResult() { FileContents = ms, FileName = templateFileName, };
        result.FileContents.Position = 0;       // reset position to the beginning of the stream so it can be read again
        return result;
    }

    public async Task<GenericActionResult> SaveSettingsAsync(TemplateSettingsDto dto, Guid financialInstitutionId)
    {
        var settingSaveResult = await efPocoService.CreateOrUpdateAsync(dto.SettingId, financialInstitutionId, dto.SettingTimeStamp,
            new PerformCreateOrUpdateOptions<Setting>()
            {
                ExecuteRecordChangesFunc = (result) =>
                {
                    result.Record.AppLogsFileNameTemplate = dto.AppLogsFileNameTemplate ?? "";
                    result.Record.FilenameAan = dto.AANFileNameTemplate ?? "";
                    result.Record.FilenameEConsent = dto.EConsentFileNameTemplate ?? "";
                },
            });
        return settingSaveResult;
    }

    public async Task ValidateModelAsync(TemplateSettingsDto dto, ModelStateDictionary modelState, Type viewModelType)
    {
        var eConsentExists = await CheckIfDocumentTemplateFileExistsAsync(dto.LanguageCode ?? "", Enums.LoanApplicationDocumentType.EConsentDisclosure);
        if (!eConsentExists)
        {
            modelState.AddModelError(
                nameof(TemplateSettingsDto.EConsentFileName), "Electronic Consent Disclosure pdf template was not uploaded");
        }
        var aanExists = await CheckIfDocumentTemplateFileExistsAsync(dto.LanguageCode ?? "", Enums.LoanApplicationDocumentType.AdverseActionNotice);
        if (!aanExists)
        {
            modelState.AddModelError(
                nameof(TemplateSettingsDto.AANFileName), "Adverse Action Notice pdf template was not uploaded");
        }
        ValidateFilenameTemplate(modelState, dto.AANFileNameTemplate ?? "", nameof(TemplateSettingsDto.AANFileNameTemplate), Enums.LoanApplicationDocumentType.AdverseActionNotice, viewModelType);
        ValidateFilenameTemplate(modelState, dto.EConsentFileNameTemplate ?? "", nameof(TemplateSettingsDto.EConsentFileNameTemplate), Enums.LoanApplicationDocumentType.EConsentDisclosure, viewModelType);
        ValidateFilenameTemplate(modelState, dto.AppLogsFileNameTemplate ?? "", nameof(TemplateSettingsDto.AppLogsFileNameTemplate), Enums.LoanApplicationDocumentType.AppLogs, viewModelType);

        if (dto.EConsentFileNameTemplate?.Contains(FilenameTemplatePlaceholders.Account) == true
            && financialInstitutionService.FinancialInstitution.HasMultipleBaseAccounts())
        {
            modelState.AddModelError(nameof(TemplateSettingsDto.EConsentFileNameTemplate),
                $"EConsent Filename Template must not contain {FilenameTemplatePlaceholders.Account} placeholder if tax ID or individual ID member identifiers are active, ");
        }
    }

    private void ValidateFilenameTemplate(
        ModelStateDictionary modelState,
        string templateContents,
        string templatePropertyName,
        Enums.LoanApplicationDocumentType docType,
        Type viewModelType)
    {
        if (filenameTemplateService.ParseTemplate(
                templateContents, docType, out _, out var notAllowedVariables))
            return;

        var propDisplayName = viewModelType.GetPropertyDisplayAttribute(templatePropertyName);
        var msg = $"{propDisplayName} is not valid";
        if (notAllowedVariables.Count != 0)
        {
            msg += $".  The following variables are not allowed: {string.Join(", ", notAllowedVariables)}, ";
        }
        modelState.AddModelError(templatePropertyName, msg);
    }
}

