using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;

namespace QCash.Service.Services.FIConfiguration.Interfaces;

public interface IFiService
{
    public string RecordName { get; }
    public Task<FiInfoDto?> GetPageDataAsync();
    Task<GenericActionResult> SaveAsync(Guid id, FiInfoDto dto);
    Task<FiGeneralInfoDto?> GetGeneralInfoAsync();
    Task<GenericActionResult> SaveGeneralInfoAsync(Guid id, FiGeneralInfoDto dto);
}
