using QCash.Data.Models;
using QCash.Models.Api;

namespace QCash.Service.Services.FIConfiguration.Interfaces;

public interface IFiSettingService
{
    /// <summary>
    /// Set the default values.
    /// </summary>
    /// <param name="setting">The setting.</param>
    /// <param name="financialInstitutionId">The financial institution identifier.</param>
    public void SetDefaults(Setting setting, Guid financialInstitutionId);

    /// <summary>
    /// Adjusts the account number.
    /// </summary>
    /// <param name="resultBaseAccount">The result base account.</param>
    /// <param name="requiredDigits">The required digits.</param>
    /// <returns>The adjusted account number.</returns>
    string AdjustAccountNumber(string? resultBaseAccount, int? requiredDigits);

    /// <summary>
    /// Gets settings response.
    /// </summary>
    /// <returns>The instance of <see cref="FinancialInstitutionSettingsModel"/>.</returns>
    Task<FinancialInstitutionSettingsModel?> GetSettingsResponseAsync();

    /// <summary>
    /// Gets required digits.
    /// </summary>
    /// <returns>The required digits.</returns>
    Task<int> GetRequiredDigitsAsync();
}
