using Microsoft.EntityFrameworkCore;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;

namespace QCash.Service.Services.FIConfiguration;

public class CalcEngineService(
    IEfPocoService efPocoService
    ) : ICalcEngineService
{
    public string RecordName { get; } = "Loan Application Calc Engine Settings";

    private static CalcEngineSettingsDto CreateBlankDto() =>
        new()
        {
            Id = Guid.Empty,
            FinancialInstitutionId = Guid.Empty,
            AmortMethod = "",
            RoundToFactor = "",
            TimeStamp = [],
        };

    /// <summary>
    /// Attempt to get the single record for the requested ApplicationId.
    /// If there is no record in the database, return a blank template record.
    /// </summary>
    /// <param name="financialInstitutionId"></param>
    /// <returns></returns>
    public async Task<CalcEngineSettingsDto> GetSettingsAsync(Guid financialInstitutionId)
    {
        var result = await efPocoService.GetQuery<CalcEngineSetting>(q =>
                q.Where(a => a.FinancialInstitution.Id == financialInstitutionId))
            .AsNoTracking()
            .Select(a => new CalcEngineSettingsDto()
            {
                Id = a.Id,
                FinancialInstitutionId = a.FinancialInstitutionId,
                AmortMethod = a.AmortMethod,
                RoundToFactor = a.RoundToFactor,
                TimeStamp = a.TimeStamp,
            }).FirstOrDefaultAsync();
        return result ?? CreateBlankDto();
    }

    public async Task<GetOrCreateRecordResult<CalcEngineSetting>> SaveSettingsAsync(CalcEngineSettingsDto dto, Guid financialInstitutionId)
    {
        if (dto.Id.Equals(Guid.Empty) && dto.FinancialInstitutionId.Equals(Guid.Empty))
        {
            // If we are creating a new CalcEngine record, we may not know the FinancialInstitutionId
            var fi = efPocoService.GetQuery<FinancialInstitution>(q =>
                q.Where(a => a.Id == financialInstitutionId))
                .AsNoTracking()
                .Select(a => a.Id)
                .FirstOrDefault();
            dto.FinancialInstitutionId = fi;
        }

        return await efPocoService.CreateOrUpdateAsync(dto.Id, financialInstitutionId: null, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<CalcEngineSetting>()
            {
                QueryableCustomization = (query) =>
                    query.Where(a => a.FinancialInstitutionId == dto.FinancialInstitutionId).AsTracking(),
                ExecuteRecordChangesFunc = (result) =>
                {
                    if (result.CreatingNewRecord)
                    {
                        result.Record.FinancialInstitutionId = dto.FinancialInstitutionId;
                    }
                    result.Record.AmortMethod = dto.AmortMethod;
                    result.Record.RoundToFactor = dto.RoundToFactor;
                },
            });
    }
}
