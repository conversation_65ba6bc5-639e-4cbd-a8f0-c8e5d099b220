using Microsoft.EntityFrameworkCore;
using QCash.Data.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;

namespace QCash.Service.Services.FIConfiguration;

public class FraudControlService(
    IEfPocoService efPocoService) : IFraudControlService
{
    public string RecordName { get; } = "Fraud Control settings";

    private static FraudControlSettingsDto CreateBlankDto() => new()
    {
        Id = Guid.Empty,
        TimeStamp = [],
        FinancialInstitutionId = Guid.Empty,
        FiSlug = string.Empty,
        IsEnabled = false,
        PreferredDelivery = "",
        ResendThreshold = 5,
        SubmitThreshold = 5,
        CodeValidityMinutesThreshold = 15,
    };

    /// <inheritdoc />
    public async Task<FraudControlSettingsDto> GetSettingsAsync()
    {
        var result = await efPocoService.GetQuery<FraudControlSetting>()
            .AsNoTracking()
            .Select(fcs => new FraudControlSettingsDto
            {
                Id = fcs.Id,
                TimeStamp = fcs.TimeStamp,
                FinancialInstitutionId = fcs.FinancialInstitutionId,
                FiSlug = fcs.FinancialInstitution.Slug,
                IsEnabled = fcs.IsEnabled,
                PreferredDelivery = fcs.PreferredDelivery,
                ResendThreshold = fcs.ResendThreshold,
                SubmitThreshold = fcs.SubmitThreshold,
                CodeValidityMinutesThreshold = fcs.CodeValidityMinutesThreshold,
            }).FirstOrDefaultAsync();
        return result ?? CreateBlankDto();
    }

    /// <inheritdoc />
    public async Task<GetOrCreateRecordResult<FraudControlSetting>> SaveSettingsAsync(FraudControlSettingsDto dto, Guid financialInstitutionId)
    {
        if (dto.Id.Equals(Guid.Empty) && dto.FinancialInstitutionId.Equals(Guid.Empty))
        {
            // If we are creating a new CalcEngine record, we may not know the FinancialInstitutionId
            var fi = efPocoService.GetQuery<FinancialInstitution>(q =>
                    q.Where(a => a.Id == financialInstitutionId))
                .AsNoTracking()
                .Select(a => a.Id)
                .FirstOrDefault();
            dto.FinancialInstitutionId = fi;
        }

        return await efPocoService.CreateOrUpdateAsync(dto.Id, financialInstitutionId: null, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<FraudControlSetting>()
            {
                QueryableCustomization = (query) =>
                    query.Where(a => a.FinancialInstitutionId == dto.FinancialInstitutionId).AsTracking(),
                ExecuteRecordChangesFunc = (result) =>
                {
                    if (result.CreatingNewRecord)
                    {
                        result.Record.FinancialInstitutionId = dto.FinancialInstitutionId;
                    }

                    result.Record.IsEnabled = dto.IsEnabled;
                    result.Record.PreferredDelivery = dto.PreferredDelivery;
                    result.Record.ResendThreshold = dto.ResendThreshold;
                    result.Record.SubmitThreshold = dto.SubmitThreshold;
                    result.Record.CodeValidityMinutesThreshold = dto.CodeValidityMinutesThreshold;
                },
            });
    }
}
