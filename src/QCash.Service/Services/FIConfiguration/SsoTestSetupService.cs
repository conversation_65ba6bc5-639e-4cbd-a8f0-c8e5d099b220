using System.Net.Http.Headers;
using System.Text;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using QCash.Common.Crypto;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Models.Api;
using QCash.Service.Core;
using QCash.Service.Models;
using QCash.Service.Models.General;
using QCash.Service.Models.LoanApplication;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.LoanApplication.Interfaces;
using QCash.Utils.Settings;

namespace QCash.Service.Services.FIConfiguration;

public class SsoTestSetupService(
    IEfPocoService efPocoService,
    IFiSettingService settingService,
    QCashContext dbContext,
    IMemberIdentifiersSettingsService memberIdentifiersSettingsService,
    IAuthUserService authUserService,
    ILoanApplicationService loanApplicationService,
    IUnitOfWork unitOfWork,
    IOptions<ApplicationOptions> applicationOptions,
    IHttpClientFactory clientFactory,
    ICryptoService cryptoService,
    ITokenManagerService tokenManagerService) : ISsoTestSetupService
{
    public string RecordName => "QA Test Suite Settings";

    private record SsoResponse(string RedirectUrl);

    public async Task<SsoTestSetupDto?> GetSsoTestDataAsync(Guid financialInstitutionId)
    {
        var getOrCreateRecordResult = await efPocoService.GetOrCreateRecordAsync<SsoTestSetup>(id: null, financialInstitutionId);
        var ssoTestData = getOrCreateRecordResult.Record;
        if (!getOrCreateRecordResult.IsSuccessful || ssoTestData == null)
        {
            throw new Exception($"Failed to create a record for SsoTestData: {getOrCreateRecordResult.ErrorMessage}");
        }

        if (getOrCreateRecordResult.CreatingNewRecord)
        {
            await unitOfWork.CommitAsync(); // If we created the 'SsoTestSetup' record, we need to commit it to get the timestamp.
        }

        var miFlags = await memberIdentifiersSettingsService.GetMemberIdentifierFlagsAsync(financialInstitutionId);
        var result = new SsoTestSetupDto
        {
            Id = ssoTestData.Id,
            TimeStamp = ssoTestData.TimeStamp,
            BaseAccount = ssoTestData.BaseAccount ?? "",
            Location = ssoTestData.Location,
            TellerId = ssoTestData.TellerId,
            Email = ssoTestData.Email,
            CoreDate = ssoTestData.CoreDate,
            PhoneNumber = ssoTestData.PhoneNumber,
            Fund = ssoTestData.Fund,
            UseSecondaryApiKeys = ssoTestData.UseSecondaryApiKeys,
            IsTaxId = miFlags?.IsIdentifiedByTaxId ?? false,
            IsBaseAccountId = miFlags?.IsIdentifiedByBaseAccountId ?? false,
            IsMemberId = miFlags?.IsIdentifiedByMemberId ?? false,
            IsIndividualId = miFlags?.IsIdentifiedByIndividualId ?? false,
            TaxId = "",
            MemberId = ssoTestData.MemberId ?? "",
            ShareOneUsername = ssoTestData.ShareOneUsername,
            ShareOnePassword = ssoTestData.ShareOnePassword,
        };
        return result;
    }

    public async Task<GenericActionResult> SaveAsync(SsoTestSetupDto dto, Guid applicationId)
    {
        var settings = dbContext.Settings.AsNoTracking()
            .Select(s => new { s.RequiredDigits }).FirstOrDefault();
        dto.BaseAccount = settingService.AdjustAccountNumber(dto.BaseAccount, settings?.RequiredDigits);

        var updateResult = await efPocoService.CreateOrUpdateAsync(dto.Id, applicationId, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<SsoTestSetup>
            {
                ExecuteRecordChangesFunc = performUpdateParam =>
                {
                    performUpdateParam.Record.BaseAccount = dto.BaseAccount;
                    performUpdateParam.Record.MemberId = dto.MemberId;
                    performUpdateParam.Record.Email = dto.Email;
                    performUpdateParam.Record.Location = dto.Location;
                    performUpdateParam.Record.TellerId = dto.TellerId;
                    performUpdateParam.Record.CoreDate = dto.CoreDate;
                    performUpdateParam.Record.Fund = dto.Fund;
                    performUpdateParam.Record.PhoneNumber = dto.PhoneNumber;
                    performUpdateParam.Record.UseSecondaryApiKeys = dto.UseSecondaryApiKeys;
                    performUpdateParam.Record.ShareOneUsername = dto.ShareOneUsername;
                    performUpdateParam.Record.ShareOnePassword = dto.ShareOnePassword;
                },
            });
        return updateResult;
    }

    public async Task<GenericActionResult<string?>> InitiateTestConnectionAsync(
        string? email,
        string? taxId,
        string? phoneNumber,
        string fiSlug,
        string currentServerDomain)
    {
        // Get settings and application key information
        var settings = await dbContext.Settings.AsNoTracking()
                           .Select(s => new { s.UseFirebirdUi, s.RequiredDigits, s.ShareOneCredentials }).FirstOrDefaultAsync() ??
                       throw new Exception("Cannot find settings record.");

        var applicationKeyRecord = await dbContext.ApplicationKeys.AsNoTracking()
                                       .FirstOrDefaultAsync() ??
                                   throw new Exception($"Cannot find applicationKeyRecord for {fiSlug}.");

        var testSetup = await dbContext.SsoTestSetups.AsNoTracking().FirstOrDefaultAsync() ??
                        throw new Exception("Cannot find SsoTestSetup record.");

        // Adjust account number based on required digits
        testSetup.BaseAccount = settingService.AdjustAccountNumber(testSetup.BaseAccount, settings.RequiredDigits);

        // Set default values for email and phone number if not provided
        email ??= authUserService.Email;
        phoneNumber ??= authUserService.PhoneNumber;

        // Get member identifier flags
        var memberIdentifiersFlags =
            await memberIdentifiersSettingsService.GetMemberIdentifierFlagsAsync(testSetup.FinancialInstitutionId) ??
            throw new Exception($"Cannot find member identifiers flags record for application id {testSetup.FinancialInstitutionId}");

        // Create the SSO initiate model with appropriate identifiers
        var cred = new SsoInitiateApiModel
        {
            BaseAccount = memberIdentifiersFlags.IsIdentifiedByBaseAccountId ? testSetup.BaseAccount : null,
            TaxId = string.IsNullOrWhiteSpace(taxId) ? null : taxId,
            TellerId = testSetup.TellerId,
            Location = testSetup.Location,
            Email = email,
            PhoneNumber = phoneNumber,
            MemberId = memberIdentifiersFlags.IsIdentifiedByIndividualId || memberIdentifiersFlags.IsIdentifiedByMemberId
                ? testSetup.MemberId
                : null,
        };

        // Build the initiate URL
        var initiateUrl = $"{applicationOptions.Value.WizardURL}/{fiSlug}/loanApplication/";
        if (string.IsNullOrEmpty(initiateUrl))
        {
            return new GenericActionResult<string?> { IsSuccessful = false, ErrorMessage = "Failed to build initiate url" };
        }

        // Create and configure the HTTP client
        var client = clientFactory.CreateClient("SsoTestClient");
        client.BaseAddress = new Uri(initiateUrl);
        client.Timeout = TimeSpan.FromMinutes(3);

        client.DefaultRequestHeaders.Accept.Clear();

        // Handle ShareOne credentials if enabled
        if (settings.ShareOneCredentials)
        {
            try
            {
                var context = await loanApplicationService.GetContextAsync(
                    fiSlug,
                    testSetup.ShareOneUsername ?? "",
                    testSetup.ShareOnePassword ?? "");
                client.DefaultRequestHeaders.TryAddWithoutValidation("X-ContextId", context.ContextId);
                cred.BaseAccount = context.BaseAccount;
            }
            catch (LoanApplicationException ex)
            {
                return new GenericActionResult<string?> { IsSuccessful = false, ErrorMessage = ex.GetErrors() };
            }
            catch (Exception)
            {
                return new GenericActionResult<string?>
                {
                    IsSuccessful = false,
                    ErrorMessage = "Unhandled error during ShareOne authentication"
                };
            }
        }
        else
        {
            client.DefaultRequestHeaders.TryAddWithoutValidation("X-ContextId", "TestContextId");
        }

        // Validate the number of member identifiers
        var numberOfMemberIdentifiers = GetNumberOfMemberIdentifiers(cred);
        switch (numberOfMemberIdentifiers)
        {
            case 0:
                return new GenericActionResult<string?> { IsSuccessful = false, ErrorMessage = "Member identifier is required." };
            case > 1:
                return new GenericActionResult<string?>
                {
                    IsSuccessful = false,
                    ErrorMessage = "Please use only one member identifier at a time.",
                };
        }

        // Validate the SSO request
        var vr = loanApplicationService.ValidateSso(cred);
        if (vr.Errors.Errors.Count == 0)
        {
            // Get the appropriate app keys based on settings
            var (appWebKey, appSharedKeyWeb) = GetAppWebKey(testSetup, applicationKeyRecord);

            // Configure HTTP client headers
            client.DefaultRequestHeaders.TryAddWithoutValidation("X-Correlation-ID", Guid.NewGuid().ToString());

            var body = JsonConvert.SerializeObject(cred);

            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("text/plain"));
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            try
            {
                // Set authorization header if implementation available
                client.DefaultRequestHeaders.TryAddWithoutValidation(
                    "Authorization",
                    cryptoService.GetAppAuthorizationToken(
                        applicationKeyRecord.Id,
                        body,
                        tokenManagerService.Decrypt(appWebKey),
                        tokenManagerService.Decrypt(appSharedKeyWeb),
                        DateTime.UtcNow));
            }
            catch (Exception)
            {
                vr.Errors.Errors.Add("Failed to generate Authorization header");
                var err = vr.Errors.Errors.Aggregate(new StringBuilder(),
                    (current, error) => current.AppendLine(error));
                return new GenericActionResult<string?> { IsSuccessful = false, ErrorMessage = err.ToString() };
            }

            client.DefaultRequestHeaders.TryAddWithoutValidation("IsSSOTest", "1");
            client.DefaultRequestHeaders.Add("Origin", currentServerDomain);

            var contentEncoded = new ByteArrayContent(Encoding.UTF8.GetBytes(body));

            try
            {
                // Make the HTTP request
                var response = await client.PostAsync("Initiate", contentEncoded);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var redirectUrl = string.Empty;

                    // Parse the response to get redirect URL
                    var rsp = JsonConvert.DeserializeObject<SsoResponse>(content);
                    if (rsp != null && !string.IsNullOrEmpty(rsp.RedirectUrl))
                    {
                        redirectUrl = rsp.RedirectUrl;
                    }
                    else
                    {
                        vr.Errors.Errors.Add("An error has occurred: " + content);
                    }

                    if (!string.IsNullOrEmpty(redirectUrl))
                    {
                        return new GenericActionResult<string?> { IsSuccessful = true, Value = redirectUrl };
                    }
                }
                else
                {
                    vr.Errors.Errors.Add("An error has occurred: " + response.ReasonPhrase);
                }
            }
            catch (Exception ex)
            {
                vr.Errors.Errors.Add("An error has occurred: " + ex.Message);
            }
        }

        // Compile all errors and return
        var errors = vr.Errors.Errors.Aggregate(new StringBuilder(),
            (current, error) => current.AppendLine(error));

        return new GenericActionResult<string?> { IsSuccessful = false, ErrorMessage = errors.ToString() };
    }

    private static (string appWebKey, string appSharedKeyWeb) GetAppWebKey(
        SsoTestSetup testSetup,
        ApplicationKey applicationKeyRecord)
    {
        string appWebKey;
        string appSharedKeyWeb;

        if (testSetup.UseSecondaryApiKeys ?? false)
        {
            appWebKey = applicationKeyRecord.SsoSecondaryAppWebKey ?? "";
            appSharedKeyWeb = applicationKeyRecord.SsoSecondaryAppSharedKeyWeb ?? "";
        }
        else
        {
            appWebKey = applicationKeyRecord.SsoV2WebKey;
            appSharedKeyWeb = applicationKeyRecord.SsoV2SharedKeyWeb ?? "";
        }

        return (appWebKey, appSharedKeyWeb);
    }

    private static int GetNumberOfMemberIdentifiers(SsoInitiateApiModel model) =>
        (string.IsNullOrWhiteSpace(model.BaseAccount) ? 0 : 1) +
        (string.IsNullOrWhiteSpace(model.MemberId) ? 0 : 1) +
        (string.IsNullOrWhiteSpace(model.TaxId) ? 0 : 1);
}
