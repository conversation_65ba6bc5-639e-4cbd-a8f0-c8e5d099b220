using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.Core;
using QCash.Service.Models.General;
using QCash.Service.Models.ModelManager;
using QCash.Service.Models.Product;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Product.Interfaces;
using QCash.Service.Utilities.Extensions;
using QCash.Utils.Core;

namespace QCash.Service.Services.Product;

public class ModelManagerService(
    QCashContext dbContext,
    IEfPocoService efPocoService,
    ISystemClockService systemClockService,
    IDecisionManagerService decisionManagerService
    ) : IModelManagerService
{
    public string RecordName { get; } = "Decision Model";
    public async Task<DataSourceResult> GetDashboardListAsync(DataSourceRequest request)
    {
        var data = await (
            from dm in dbContext.DecisionModels
            select new ModelManagerListItemDto()
            {
                Id = dm.Id,
                TimeStamp = dm.TimeStamp,
                FIHandle = dm.FiHandle,
                Type = dm.ModelType.Abrv,
                AdThreshold = dm.AdThreshold,
                Status = dm.ModelStatus.Name,
                ProductId = dm.ProductDecisionModels.Select(p => p.ProductId).FirstOrDefault(),
                IsDefault = false,
            }
            ).ToListAsync();

        var productFilterRow = request.Filters.FirstOrDefault(a => (a as Kendo.Mvc.FilterDescriptor)?.Member == "ProductId");
        var productIdString = (productFilterRow as Kendo.Mvc.FilterDescriptor)?.Value as string;
        if (Guid.TryParse(productIdString, out Guid productIdGuid))
        {
            var product = await (
                from p in dbContext.Products
                where p.Id == productIdGuid
                select new { p.Id, p.Name, p.DefaultModelId, }
            ).FirstOrDefaultAsync();
            var defaultModel = data.FirstOrDefault(m => m.Id == product!.DefaultModelId);
            if (defaultModel != null)
            {
                defaultModel.IsDefault = true;
            }
        }

        var result = await data.ToDataSourceResultAsync(request);
        return result;
    }

    public ModelDashboardEditDto InstantiateCreatePageModel(string fiSlug, Guid applicationId, Guid? productId)
    {
        var dto = new ModelDashboardEditDto
        {
            Id = Guid.Empty,
            Description = "",
            TimeStamp = [],
            FIHandle = "",
            TypeId = Guid.Empty,
            ProductId = productId,
            AdThreshold = null,
            FiSlug = fiSlug,
            StatusId = Guid.Empty,
            UsageCount = 0,
        };
        return dto;
    }

    public async Task<GenericActionResult<DecisionModel>> SaveAsync(Guid financialInstitutionId, ModelDashboardEditDto dto)
    {
        if (dto.Id.Equals(Guid.Empty))
        {   // creating a new model
            var inactiveStatus = dbContext.DecisionModelStatuses
                .Select(a => new { a.Id, a.Abrv })
                .FirstOrDefault(o => o.Abrv == nameof(Enums.DecisionModelStatusEnum.INCT));
            inactiveStatus.ThrowIfNull("Unable to find inactive status.  [DecisionModelStatuses]");
            dto.StatusId = inactiveStatus.Id;
        }
        var decisionModelResult = await efPocoService.CreateOrUpdateAsync(dto.Id, financialInstitutionId, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<DecisionModel>
            {
                ExecuteRecordChangesFunc = (performUpdateParam) =>
                {
                    if (performUpdateParam.CreatingNewRecord)
                    {
                        performUpdateParam.Record.StatusDate = systemClockService.GetSystemTimeUtc();
                    }

                    performUpdateParam.Record.FiHandle = dto.FIHandle;
                    performUpdateParam.Record.Description = dto.Description;

                    performUpdateParam.Record.ModelTypeId = dto.TypeId;

                    performUpdateParam.Record.ModelStatusId = dto.StatusId;
                    performUpdateParam.Record.UsageCount = dto.UsageCount;

                    performUpdateParam.Record.AdThreshold = dto.AdThreshold;
                },
            });

        var result = new GenericActionResult<DecisionModel>()
        {
            IsSuccessful = decisionModelResult.IsSuccessful,
            ErrorMessage = decisionModelResult.ErrorMessage,
            Value = decisionModelResult.Record,
        };
        if (!decisionModelResult.IsSuccessful || !decisionModelResult.CreatingNewRecord || dto.ProductId == null)
        {
            return result;
        }

        var productDecisionModelResult = await efPocoService.CreateOrUpdateAsync(Guid.Empty, financialInstitutionId, [],
            new PerformCreateOrUpdateOptions<ProductDecisionModel>
            {
                ExecuteRecordChangesFunc = (performUpdateParam) =>
                {
                    if (performUpdateParam.CreatingNewRecord)
                    {
                        performUpdateParam.Record.DecisionModelId = decisionModelResult.Record!.Id;
                        performUpdateParam.Record.ProductId = dto.ProductId.Value;
                    }
                },
            });
        if (!productDecisionModelResult.IsSuccessful)
        {
            result = new GenericActionResult<DecisionModel>()
            {
                IsSuccessful = productDecisionModelResult.IsSuccessful,
                ErrorMessage = productDecisionModelResult.ErrorMessage,
                Value = decisionModelResult.Record,
            };
        }
        return result;
    }

    public async Task<ModelDashboardEditDto> GetModelAsync(Guid modelId)
    {
        var result = await efPocoService
            .GetQuery<DecisionModel>(q => q.Where(a => a.Id == modelId))
            .Select(a => new ModelDashboardEditDto()
            {
                Id = a.Id,
                Description = a.Description ?? "",
                AdThreshold = a.AdThreshold,
                StatusId = a.ModelStatusId,
                TimeStamp = a.TimeStamp,
                FIHandle = a.FiHandle,
                TypeId = a.ModelTypeId,
                ProductId = a.ProductDecisionModels.Select(p => p.ProductId).FirstOrDefault(),
                FiSlug = a.FinancialInstitution.Slug,
                UsageCount = a.UsageCount,
            })
            .SingleOrDefaultAsync();
        result.ThrowIfNull($"Cannot find requested Decision Model: {modelId} ");
        return result;
    }

    public async Task<GenericActionResult> SetDefaultAsync(ModelManagerListItemDto dto, Guid financialInstitutionId)
    {
        if (dto == null || !dto.ProductId.HasValue || dto.ProductId.Equals(Guid.Empty))
        {
            return new GenericActionResult() { IsSuccessful = false, ErrorMessage = "Product ID is empty." };
        }
        if (dto.Id.Equals(Guid.Empty))
        {
            return new GenericActionResult() { IsSuccessful = false, ErrorMessage = "Model ID is empty." };
        }
        var modelData = await dbContext.DecisionModels
            .Select(a => new { a.Id, a.ModelStatusId, a.ModelStatus.Abrv })
            .FirstOrDefaultAsync(a => a.Id == dto.Id);
        if (modelData == null)
        {
            return new GenericActionResult() { IsSuccessful = false, ErrorMessage = $"No such model could be found: {dto.Id}" };
        }
        if (modelData.Abrv == nameof(Enums.DecisionModelStatusEnum.INCT))
        {
            return new GenericActionResult() { IsSuccessful = false, ErrorMessage = $"Selected model has [Inactive] status and can not be used as default model." };
        }

        await efPocoService.CreateOrUpdateAsync(dto.ProductId.Value, financialInstitutionId, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<Data.Models.Product>()
            {
                ExecuteRecordChangesFunc = (result) =>
                {
                    result.Record.DefaultModelId = dto.Id;
                },
            });
        return new GenericActionResult() { IsSuccessful = true, };
    }

    public async Task<GenericActionResult> SetModelActiveAsync(ModelDashboardEditDto dto, Guid financialInstitutionId)
    {
        var activeStatus = dbContext.DecisionModelStatuses
            .Select(a => new { a.Id, a.Abrv })
            .FirstOrDefault(o => o.Abrv == nameof(Enums.DecisionModelStatusEnum.ACTV));
        activeStatus.ThrowIfNull("Unable to find active status.  [DecisionModelStatuses]");

        string message;
        var isValid = decisionManagerService.ValidateTransformation(financialInstitutionId, dto.Id, out message);
        if (!isValid)
        {
            return new GenericActionResult() { IsSuccessful = false, ErrorMessage = $"This Decision Model has invalid transformations: {message}" };
        }

        await efPocoService.CreateOrUpdateAsync(dto.Id, financialInstitutionId, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<DecisionModel>()
            {
                ExecuteRecordChangesFunc = (result) =>
                {
                    result.Record.StatusDate = systemClockService.GetSystemTimeUtc();
                    result.Record.ModelStatusId = activeStatus.Id;
                },
            });
        return new GenericActionResult() { IsSuccessful = true, };
    }
}
