using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using QCash.Data.Context;
using QCash.Data.Extensions;
using QCash.Data.Models.Enums;
using QCash.Service.Models;
using QCash.Service.Models.General;
using QCash.Service.Models.Product;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.Product.Interfaces;
using QCash.Service.Utilities.Extensions;
using QCash.Utils.Core;

namespace QCash.Service.Services.Product;

public class ProductService(
    ILogger<ProductService> logger,
    QCashContext context,
    IFiQueries fiQueries,
    ILookupService lookupService,
    ILookupQueries lookupQueries,
    ISystemClockService systemClockService,
    IProductQueries productQueries,
    IPermissionDebuggingService permissionDebuggingService,
    IEfPocoService efPocoService
    ) : IProductService
{
    public string RecordName { get; } = "Product";
    public async Task<ProductInfoTabDto?> GetProductInfoPageDataAsync(string fiSlug, string productSlug)
    {
        var productQuery = productQueries.GetProducts();
        productQuery = productQuery.Where(p => p.Slug == productSlug);
        var dto = await GetGeneralProductInfoTabDtoFromProductAsync(productQuery);
        if (dto != null)
        {
            await PopulateMetadataForGeneralProductInfoTabAsync(fiSlug, dto);
        }

        return dto;
    }

    public async Task<ProductInfoTabDto> PopulateMetadataForGeneralProductInfoTabAsync(string fiSlug, ProductInfoTabDto pageModel)
    {
        pageModel.CategoryChoices = await GetAllCategoriesAsync(pageModel);
        return pageModel;
    }

    private async Task<ProductInfoTabDto?> GetGeneralProductInfoTabDtoFromProductAsync(IQueryable<Data.Models.Product> productQ)
    {
        var data = await productQ
            .Select(p => new
            {
                FinancialInstitutionId = p.FinancialInstitution.Id,
                FiSlug = p.FinancialInstitution.Slug,
                Id = p.Id,
                TimeStamp = p.TimeStamp,
                //  'Product Info' tab
                IsActive = p.IsActive,
                Name = p.Name,
                Abrv = p.Abrv,
                DisplayOrder = p.DisplayOrder,
                Description = p.Description ?? "",
                Slug = p.Slug,
                TransferClearingUserGLCode = p.GlTranslationAccount,
                LoanDescription = p.LoanDescription,
                ReferenceCode = p.ReferenceCode,
                CustomBranchNumberActive = p.CustomBranchNumber.GetValueOrDefault(-1) > -1,
                CustomBranchNumber = p.CustomBranchNumber,
                p.LoanCategoryId,
                UseLoanFundingDate = p.UseLoanFundingDate,
                DateCreatedUTC = p.DateCreatedUtc,
                DateUpdatedUTC = p.DateUpdatedUtc,
                TimeZone = (p.FinancialInstitution.Setting != null) ? p.FinancialInstitution.Setting.TimeZone : SystemClockService.DefaultTimeZoneNameId,
                // 'Fees and Interest Rate' Tab.
                MAPRInterestRate = p.MaprInterestRate,
                AnnualPercentage = p.AnnualPercentage,
                AutoPayOption = p.AutopayOption,
                AutoPayRate = p.AutoPayRate,
                PersonalLoanCampaign = p.PersonalLoanCampaign,
                PreApprovedCardCampaign = p.PreApprovedCardCampaign,
                //
                LoanTypeId = p.LoanTypeId,
                IsAdmin = permissionDebuggingService.IsAdmin(),
                IsFiManager = permissionDebuggingService.IsFIManager(),
            })
        .FirstOrDefaultAsync();

        if (data == null)
        {
            return null;
        }
        var pageModel = new ProductInfoTabDto()
        {
            FinancialInstitutionId = data.FinancialInstitutionId,
            FiSlug = data.FiSlug,
            Id = data.Id,
            TimeStamp = data.TimeStamp,
            //  'Product Info' tab
            IsActive = data.IsActive,
            Name = data.Name,
            Abrv = data.Abrv,
            DisplayOrder = data.DisplayOrder,
            Description = data.Description,
            Slug = data.Slug,
            TransferClearingUserGLCode = data.TransferClearingUserGLCode,
            LoanDescription = data.LoanDescription,
            ReferenceCode = data.ReferenceCode,
            CustomBranchNumberActive = data.CustomBranchNumber.GetValueOrDefault(-1) > -1,
            CustomBranchNumber = data.CustomBranchNumber,
            SelectedLoanCategoryId = data.LoanCategoryId,
            UseLoanFundingDate = data.UseLoanFundingDate,
            TimeZone = data.TimeZone,
            InterestRate = data.AnnualPercentage,
            //
            LoanTypeId = data.LoanTypeId,
            IsAdmin = permissionDebuggingService.IsAdmin(),
            IsFiManager = permissionDebuggingService.IsFIManager(),
            DateCreatedLocal = systemClockService.ConvertUTCTimeToLocal(data.DateCreatedUTC, data.TimeZone),
            DateUpdatedLocal = systemClockService.ConvertUTCTimeToLocal(data.DateUpdatedUTC, data.TimeZone),
        };
        return pageModel;
    }

    public async Task PopulateCreatePageDropdownsAsync(ProductCreatePageDto dto)
    {
        var loanTypesQ = lookupQueries.GetLoanTypes(dto.FiSlug);
        var loanTypes = await loanTypesQ.ToListAsync();
        var loanTypeDropdownOptions = await lookupService.GetLoanTypesForDropdownAsync(loanTypesQ);
        var defaultLoanType = lookupService.GetDefaultLoanType(loanTypes);
        dto.LoanTypeId = defaultLoanType?.Id;

        if (dto.LoanTypeId.HasValue)
        {
            var loanCategories = await lookupService.GetLoanCategoriesForDropdownAsync(dto.FiSlug, dto.LoanTypeId.Value);
            dto.LoanCategoryOptions = loanCategories;
        }
        dto.LoanTypeOptions = loanTypeDropdownOptions;
    }

    public async Task<ProductCreatePageDto> GetCreatePageModelAsync(string fiSlug, Guid applicationId)
    {
        var model = new ProductCreatePageDto
        {
            FiSlug = fiSlug,
            FinancialInstitutionId = applicationId,
            IsArchived = false,
            Title = "New Product",
            Description = "",
            IsActive = false,
            SelectedLoanCategoryId = Guid.Empty,
            LoanTypeId = null,
            Name = "",
            Abrv = "",
            PersonalLoanCampaign = false,
        };
        await PopulateCreatePageDropdownsAsync(model);
        return model;
    }

    public async Task<ProductCategoryPartialDto> GetCreatePageModelOnLoanTypeChangeAsync(string fiSlug, Guid loanTypeId, Guid oldSelectedCategoryId)
    {
        var loanCategoriesQ = await lookupQueries.GetLoanCategoriesQAsync(fiSlug, loanTypeId);
        var loanCategories = await loanCategoriesQ.ToListAsync();
        var model = new ProductCategoryPartialDto
        {
            IsActive = false,
            LoanCategoryOptions = loanCategories.Select(lc =>
                new QListItem<Guid> { Text = lc.Name, Value = lc.Id, }).ToList(),

            SelectedLoanCategoryId = oldSelectedCategoryId,
            FinancialInstitutionId = loanCategories.FirstOrDefault()?.FinancialInstitutionId ?? Guid.Empty,
        };
        return model;
    }

    private async Task<List<QListItem<Guid>>> GetAllCategoriesAsync(ProductInfoTabDto pageModel)
    {
        var categories = lookupQueries.GetLoanCategories(pageModel.FinancialInstitutionId);
        var categoryModels = await categories.Select(c =>
            new QListItem<Guid> { Value = c.Id, Text = c.Name, })
            .ToListAsync();
        return categoryModels;
    }

    public List<QListItem<int?>> GetAutoPayChoices()
    {
        return AutopayOptionEnum.Instance.GetAll()
            .Select(a => new QListItem<int?>() { Value = a.Id, Text = a.Name, })
            .ToList();
    }

    private async Task<bool> CheckIfProductWithSlugExistsAsync(Guid financialInstitutionId, string slug) =>
        await productQueries.GetProducts(financialInstitutionId)
            .Where(p => p.Slug.ToLower() == slug.Trim().ToLower()).AnyAsync();

    private static string GetNewProductSlug(ProductCreatePageDto pageData)
    {
        var newSlug = pageData.Name.Trim().Replace(' ', '-');
        return newSlug;
    }

    /// <summary>
    /// Goal: take the supplied information, create a 'Product' record in the database, and return the details for that record.
    /// - If there are validation issues, the result will contain an error message and no details
    /// </summary>
    /// <param name="pageData"></param>
    /// <returns></returns>
    public async Task<ProductCreateResultDto> CreateNewProductAsync(ProductCreatePageDto pageData)
    {
        var newSlug = GetNewProductSlug(pageData);
        if (string.IsNullOrWhiteSpace(newSlug))
        {
            return new ProductCreateResultDto() { IsSuccessful = false, ErrorMessage = "Product must have a name." };
        }
        var fi = fiQueries.GetFIRecordQueryable(pageData.FiSlug)
            .AsNoTracking().Single();
        var productWithSlugExists = await CheckIfProductWithSlugExistsAsync(fi.Id, newSlug);
        if (productWithSlugExists)
        {
            await PopulateCreatePageDropdownsAsync(pageData);
            return new ProductCreateResultDto() { IsSuccessful = false, ErrorMessage = "Product with the same name already exists." };
        }

        if (!pageData.LoanTypeId.HasValue)
        {
            return new ProductCreateResultDto() { IsSuccessful = false, ErrorMessage = "The Loan Type field is required." };
        }

        var loanTypes = await lookupService.GetLoanTypesForDropdownAsync(pageData.FiSlug);
        var loanType = loanTypes.FirstOrDefault(lt => lt.Value == pageData.LoanTypeId);
        var appAbrv = loanType?.Text.Replace(" ", "");

        var loanCategories = await lookupService.GetLoanCategoriesForDropdownAsync(pageData.FiSlug, pageData.LoanTypeId.Value);
        if (!loanCategories.Any())
        {
            return new ProductCreateResultDto() { IsSuccessful = false, ErrorMessage = "No Loan Categories exist." };
        }
        var loanCategoryId = loanCategories.First().Value;
        try
        {
            efPocoService.Add(new Data.Models.Product
            {
                FinancialInstitutionId = fi.Id,
                Abrv = pageData.Abrv,
                AppAbrv = appAbrv ?? "",

                // newDomain.InvoicePlanId = InvoicePlanLookup.PD.Id;
                GlTranslationAccount = "0",
                InvoiceId = "APP",
                Slug = newSlug,
                Margin = 0,
                Description = pageData.Description,
                Name = pageData.Name,
                LoanTypeId = pageData.LoanTypeId ?? Guid.Empty,
                LoanCategoryId = loanCategoryId,
                PersonalLoanCampaign = pageData.PersonalLoanCampaign,
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error attempting to create a product");
            await PopulateCreatePageDropdownsAsync(pageData);
            return new ProductCreateResultDto() { IsSuccessful = false, ErrorMessage = "Error while creating product" };
        }
        return new ProductCreateResultDto { ProductCreatePageDto = pageData, ProductSlug = newSlug, IsSuccessful = true, };
    }

    /// <summary>
    /// TODO: THIS IS NOT FINISHED
    /// </summary>
    /// <param name="input"></param>
    public async Task<GetOrCreateRecordResult<Data.Models.Product>> SaveGeneralProductInfoTabAsync(ProductInfoTabDto input) =>
        // TODO: needs checking for duplication against fields like DisplayOrder
        await efPocoService.CreateOrUpdateAsync(input.Id, input.FinancialInstitutionId, input.TimeStamp, new PerformCreateOrUpdateOptions<Data.Models.Product>()
        {
            ExecuteRecordChangesFunc = (result) =>
            {
                if (result.CreatingNewRecord)
                {
                    result.Record.Slug = input.Slug;
                }
                if (!input.IsActive)
                {   // Field(s) that are only editable if the product is not active:
                    result.Record.Name = input.Name;
                    result.Record.Abrv = input.Abrv;
                    result.Record.LoanCategoryId = input.SelectedLoanCategoryId;
                }
                result.Record.Description = input.Description;
                result.Record.DisplayOrder = input.DisplayOrder;

                result.Record.GlTranslationAccount = input.TransferClearingUserGLCode;
                result.Record.LoanDescription = input.LoanDescription;
                result.Record.ReferenceCode = input.ReferenceCode;
                result.Record.CustomBranchNumber = !input.CustomBranchNumberActive ? null : input.CustomBranchNumber;

                result.Record.UseLoanFundingDate = input.UseLoanFundingDate;
            },
        });

    public async Task<GetOrCreateRecordResult<Data.Models.Product>> SaveGeneralFeesAndInterestRateTabAsync(
        ProductFeesAndInterestRateTabDto input, Guid financialInstitutionId) =>
        // TODO: needs checking for duplication against fields like DisplayOrder
        await efPocoService.CreateOrUpdateAsync(input.Id, financialInstitutionId, input.TimeStamp,
            new PerformCreateOrUpdateOptions<Data.Models.Product>()
            {
                ExecuteRecordChangesFunc = result =>
                {
                    result.Record.AnnualPercentage = input.InterestRate;
                    result.Record.MaprInterestRate = input.MAPRInterestRate;
                    result.Record.AutopayOption = input.AutoPayOption;
                    result.Record.AutoPayRate = input.AutoPayRate;
                    result.Record.PreApprovedCardCampaign = input.PreApprovedCardCampaign;
                    result.Record.PersonalLoanCampaign = input.PersonalLoanCampaign;
                },
            });

    public async Task<ProductsPageDto> GetListAsync(string fiSlug, string? searchValue)
    {
        var products = await productQueries.GetProducts()
            .WhereIf(searchValue, p => p.Name.Contains(searchValue!) || p.Abrv.Contains(searchValue!))
            .Select(p => new ProductListItemDto
            {
                Id = p.Id,
                IsActive = p.IsActive,
                IsArchived = p.IsArchived,
                Name = p.Name,
                Abrv = p.Abrv,
                Description = p.Description ?? "",
                LoanTypeName = p.LoanType.Name,
                Slug = p.Slug,
            }).ToListAsync();
        var model = new ProductsPageDto { Products = products, FiSlug = fiSlug, SearchValue = searchValue, };
        return model;
    }

    public async Task<GenericActionResult> SetActiveAsync(string productSlug, bool newValue, string timeStamp)
    {
        var productInfo = await efPocoService.GetQuery<Data.Models.Product>(q =>
            q.Where(a => a.Slug == productSlug))
            .AsTracking()
            .FirstOrDefaultAsync();
        if (productInfo == null)
        {
            return new GenericActionResult() { IsSuccessful = false, ErrorMessage = $"Can't find product {productSlug}.", };
        }
        if (productInfo.IsActive == newValue)
        {
            // no need to do anything.  value is already set as requested.
            return new GenericActionResult() { IsSuccessful = true, };
        }

        var currentDbTimeStamp = Convert.ToBase64String(productInfo.TimeStamp);
        if (currentDbTimeStamp != timeStamp)
        {
            return new GenericActionResult() { IsSuccessful = false, ErrorMessage = "Timestamp does not match.  Please refresh page and try again.", };
        }
        if (newValue)
        {
            if (productInfo.DefaultModelId == null)
            {
                return new GenericActionResult() { IsSuccessful = false, ErrorMessage = "Can't activate product without a default model.", };
            }

            var prodCatData = await (
                    from lc in context.LoanCategories
                    where lc.Id == productInfo.LoanCategoryId
                    select new { lc.IsOpen })
                .AsNoTracking()
                .FirstOrDefaultAsync();
            if (prodCatData == null)
            {
                return new GenericActionResult() { IsSuccessful = false, ErrorMessage = "At least one payment option must be enabled for every active product.", };
            }
            if (!prodCatData.IsOpen)
            {
                var atLeastOnePaymentOptionIsEnabled = await (
                        from po in context.PaymentOptions
                        where po.Product.Id == productInfo.Id && po.IsEnabled
                        select po)
                    .AnyAsync();
                if (!atLeastOnePaymentOptionIsEnabled)
                {
                    return new GenericActionResult() { IsSuccessful = false, ErrorMessage = "At least one payment option must be enabled for every active product.", };
                }
            }
        }
        productInfo.IsActive = newValue;
        return new GenericActionResult() { IsSuccessful = true, };
    }

    public async Task<ProductFeesAndInterestRateTabDto?> GetProductFeesAndInterestRatePageDataAsync(string fiSlug, string productSlug)
    {
        var productQuery = productQueries.GetProducts();
        productQuery = productQuery.Where(p => p.Slug == productSlug);
        var dto = await (
                from p in productQuery
                select new ProductFeesAndInterestRateTabDto()
                {
                    FiSlug = fiSlug,
                    Id = p.Id,
                    ProductSlug = productSlug,
                    TimeStamp = p.TimeStamp,
                    MAPRInterestRate = p.MaprInterestRate,
                    AutoPayOption = p.AutopayOption,
                    AutoPayRate = p.AutoPayRate,
                    PersonalLoanCampaign = p.PersonalLoanCampaign,
                    PreApprovedCardCampaign = p.PreApprovedCardCampaign,
                    InterestRate = p.AnnualPercentage,              // TODO: VERIFY THIS VALUE
                    LoanTypeId = p.LoanTypeId,
                })
            .SingleOrDefaultAsync();
        dto.ThrowIfNull($"Product {productSlug} not found. ");
        return dto;
    }

}

