using QCash.Service.Models;
using QCash.Service.Models.General;
using QCash.Service.Models.Product;

namespace QCash.Service.Services.Product.Interfaces;

public interface IProductService
{
    public string RecordName { get; }
    public Task<ProductInfoTabDto?> GetProductInfoPageDataAsync(string fiSlug, string productSlug);
    public Task<ProductInfoTabDto> PopulateMetadataForGeneralProductInfoTabAsync(string fiSlug, ProductInfoTabDto pageModel);
    public Task<ProductCreatePageDto> GetCreatePageModelAsync(string fiSlug, Guid applicationId);
    public Task<ProductCategoryPartialDto> GetCreatePageModelOnLoanTypeChangeAsync(string fiSlug, Guid loanTypeId, Guid oldSelectedCategoryId);
    public Task<ProductCreateResultDto> CreateNewProductAsync(ProductCreatePageDto pageData);
    public Task<GetOrCreateRecordResult<Data.Models.Product>> SaveGeneralProductInfoTabAsync(ProductInfoTabDto input);
    public Task PopulateCreatePageDropdownsAsync(ProductCreatePageDto dto);
    public Task<ProductsPageDto> GetListAsync(string fiSlug, string? searchValue);
    Task<GenericActionResult> SetActiveAsync(string productSlug, bool value, string timeStamp);
    public Task<ProductFeesAndInterestRateTabDto?> GetProductFeesAndInterestRatePageDataAsync(string fiSlug, string productSlug);
    public List<QListItem<int?>> GetAutoPayChoices();
    public Task<GetOrCreateRecordResult<Data.Models.Product>> SaveGeneralFeesAndInterestRateTabAsync(ProductFeesAndInterestRateTabDto input,
        Guid financialInstitutionId);
}
