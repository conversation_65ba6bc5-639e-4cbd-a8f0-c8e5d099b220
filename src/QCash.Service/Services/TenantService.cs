using Microsoft.EntityFrameworkCore;
using QCash.Service.Models;
using QCash.Service.Services.Interfaces;

namespace QCash.Service.Services;

public class TenantService(IFiQueries fiQueries) : ITenantService
{
    public async Task<TenantSelectPageDto> GetPageDataAsync()
    {
        var fis = await fiQueries.GetActiveFIs()
            .AsNoTracking()
            .IgnoreQueryFilters()
            .Select(a => new TenantSelectPageTenantDto(a.DateUpdatedUtc)
            {
                FiSlug = a.Slug,
                Name = a.Name,
                City = a.City,
                State = a.State,
                ContactName = a.ContactName,
            }).ToListAsync();

        var tenantIndex = 0;
        foreach (var fi in fis)
        {
            fi.FiSlug = fi.FiSlug?.Replace("-", "");
            fi.Index = tenantIndex++;
        }

        var page = new TenantSelectPageDto { Tenants = fis, };
        return page;
    }
}
