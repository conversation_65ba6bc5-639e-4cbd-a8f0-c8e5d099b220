using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Models.Api;
using QCash.Service.Services.Interfaces;

namespace QCash.Service.Services;

/// <summary>
/// FinancialInstitutionConfigurationService service.
/// In QCash6 this used to be FinancialInstitutionRouteService, which returned IFinancialInstitutionRoutes.
/// </summary>
public class FinancialInstitutionConfigurationService(QCashContext context) : IFinancialInstitutionConfigurationService
{
    //These used to be cached in FinancialInstitutionRouteService. Since it's such a simple DB call, caching was not re-implemented here.
    //Could consider adding back in if it's deemed necessary.

    /// <inheritdoc />
    public async Task<List<FinancialInstitutionConfigurationModel>> GetAllFinancialInstitutionConfigurationsAsync()
    {
        var result = await context.FinancialInstitutionConfigurations
            .IgnoreQueryFilters()
            .AsNoTracking()
            .Where(fic => fic.Active.HasValue && fic.Active.Value)
            .Select(fic => new FinancialInstitutionConfigurationModel
            {
                Id = fic.Id,
                FinancialInstitutionId = fic.FinancialInstitutionId,
                Description = fic.Description ?? string.Empty,
                DateCreated = fic.DateCreatedUtc,
                DateUpdated = fic.DateUpdatedUtc,
                DefaultProviderId = fic.DefaultProviderId,
                LogAuthorization = fic.LogAuthorization ?? false,
                Slug = fic.FinancialInstitution.Slug,
                StorageName = fic.StorageName ?? string.Empty,
                StorageKey = fic.StorageKey ?? string.Empty,
                StorageBlobEndpoint = fic.StorageBlobEndpoint ?? string.Empty,
                MiddlewareCheck = fic.MiddlewareCheck ?? string.Empty,
                ServiceBusType = fic.ServiceBusType ?? string.Empty,
                FIName = fic.FinancialInstitution.Name,
                GatewayType = fic.GatewayType ?? string.Empty,
                EnableInitiateErrorRedirectUrl = fic.EnableInitiateErrorRedirectUrl,
            })
            .ToListAsync();

        return result;
    }

    /// <inheritdoc />
    public async Task<FinancialInstitutionConfiguration> GetFinancialInstitutionConfigurationBySlugAsync(string slug)
    {
        var fiConfig = await context.FinancialInstitutionConfigurations
            .AsNoTracking()
            .Where(o => o.Active.HasValue && o.Active.Value)
            .FirstOrDefaultAsync();

        return fiConfig ?? throw new ArgumentException("No matching Application/FinancialInstitution/FinancialInstitutionConfiguration for slug {slug}", slug);
    }
}
