using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Services.Interfaces;

namespace QCash.Service.Services;

public class FiQueries(QCashContext context) : IFiQueries
{
    public IQueryable<FinancialInstitution> GetFIRecordQueryable(string fiSlug) =>
        context.FinancialInstitutions
            .Where(fi => fi.Slug == fiSlug);

    public IQueryable<FinancialInstitution> GetActiveFIs() =>
        context.FinancialInstitutions
            .Where(a => a.GlLoanAccount.Length > 0)
            .Where(a => a.Setting != null && a.Setting.IsActive);
}
