using QCash.Data.Models;
using QCash.LoanApplication;
using static QCash.Service.Models.LoanApplication.Enums;

namespace QCash.Service.Services.LoanApplication;

internal class GetMLAStatusRequestDTO
{
    /// <summary>
    /// Initializes a new instance of the <see cref="GetMLAStatusRequestDTO"/> class.
    /// </summary>
    /// <param name="financialInstitutionMember">The financial institution member.</param>
    /// <param name="logs">The logs.</param>
    /// <param name="originApp">The origin application.</param>
    /// <param name="taxId">The tax identifier.</param>
    /// <param name="loanApplicationId">The loan application identifier.</param>
    /// <param name="correlationId">The correlation identifier.</param>
    public GetMLAStatusRequestDTO(FinancialInstitutionMember financialInstitutionMember, IList<LoanApplicationLogDetail> logs, OriginApp originApp, string taxId, Guid loanApplicationId, Guid? correlationId)
    {
        Member = new Member
        {
            TaxId = taxId,
            DateOfBirth = financialInstitutionMember.DateOfBirth,
            Name = new Name
            {
                Last = financialInstitutionMember.LastName,
                First = financialInstitutionMember.FirstName,
                Middle = financialInstitutionMember.MiddleName,
                Suffix = financialInstitutionMember.Suffix
            },
            Address = new Address
            {
                City = financialInstitutionMember.City,
                Street1 = financialInstitutionMember.MailingAddress,
                State = financialInstitutionMember.State,
                Zip = financialInstitutionMember.Zip
            }
        };

        OriginApp = originApp;
        Logs = logs;
        LoanApplicationId = loanApplicationId;
        CorrelationId = correlationId;
    }

    public IList<LoanApplicationLogDetail> Logs { get; set; }
    public OriginApp OriginApp { get; set; }
    public Guid LoanApplicationId { get; set; }
    public Guid? CorrelationId { get; set; }
    public Member Member { get; set; }
}