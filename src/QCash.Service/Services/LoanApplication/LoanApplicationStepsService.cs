using System.Collections.Immutable;
using System.Diagnostics;
using System.Globalization;
using System.Net.Mail;
using System.Runtime.ExceptionServices;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Data.Models.Enums;
using QCash.LoanApplication;
using QCash.Models.Api;
using QCash.Models.Api.Get;
using QCash.Models.Api.Post;
using QCash.Service.BusinessLogic.Validators.MemberExclusions;
using QCash.Service.BusinessLogic.Validators.ProductsExclusions;
using QCash.Service.Classes;
using QCash.Service.Classes.DecisionManager;
using QCash.Service.Classes.DecisionManager.Interface;
using QCash.Service.FileSystem;
using QCash.Service.LoanCalculator;
using QCash.Service.Models;
using QCash.Service.Models.General;
using QCash.Service.Models.LoanApplication;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Helpers;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.LoanApplication.Constants;
using QCash.Service.Services.LoanApplication.Interfaces;
using QCash.Service.Utilities.Extensions;
using static QCash.Service.Models.Core.Enums;
using static QCash.Service.Models.FIConfiguration.Enums;
using static QCash.Service.Models.LoanApplication.Enums;
using static QCash.Service.Services.LoanApplication.Enums;
using ApplicationOptions = QCash.Utils.Settings.ApplicationOptions;
using Convert = System.Convert;
using Document = QCash.Data.Models.Document;
using ExtraInfo = QCash.LoanApplication.ExtraInfo;
using LoanType = QCash.LoanApplication.LoanType;
using ValidationResult = QCash.Service.Models.Core.ValidationResult;

namespace QCash.Service.Services.LoanApplication;

public class LoanApplicationStepsService(QCashContext qCashContext,
    ILogger<LoanApplicationStepsService> logger,
    IGuidExtensionService guidExtensionService,
    ITokenService tokenService,
    IFinancialInstitutionService financialInstitutionService,
    ILoanApplicationService loanApplicationService,
    ILoanApplicationHelper loanApplicationHelper,
    IOptions<ApplicationOptions> applicationOptions,
    ErrorHelper errorHelper,
    IProductsExclusionsValidatorRunner productsExclusionsValidatorRunner,
    IFileSystemProvider fileSystemProvider,
    IFilenameTemplateService filenameTemplateService,
    IMemberProcessInterfaceService memberProcessInterfaceService,
    IMemberInterfaceHelper memberInterfaceHelper,
    IDecisionManagerService decisionManagerService,
    IDecisionEngineService decisionEngineService,
    ITaxIdService taxIdService,
    INotificationService notificationService,
    IPaymentOptionService paymentOptionService,
    ILanguageService languageService,
    IMemberExclusionsValidatorRunner memberExclusionsValidatorRunner) : ILoanApplicationStepsService
{

    //NOTE: some clauses in this service are left intact even though their code is commented out -
    //leaving as a reference until some of this can be stepped through more carefully in
    //actual execution.

    public record TranslationValueDTO(string Language, string Value);
    public record DataCollectionItemDTO(List<TranslationValueDTO> Texts, bool IsRequired, int Order);

    private record DataCollectionRecordDto(
        bool IsDataCollectionSkipped,
        bool IsUserAnswerPositive,
        List<DataCollectionEntryDto> DataCollection);

    //renamed from QC6 DataCollectionItemDto as only difference from DataCollectionItemDTO was capitalization
    private record DataCollectionEntryDto(string Name, string Value);

    private static readonly JsonSerializerSettings LogsJsonSettings = new()
    {
        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
    };

    private const string InitiateLoanApplicationError = "This application cannot be completed at this time. " +
                                                        "Please contact your credit union for assistance. Thank you.";
    public const string LastNameMissingError = "There seems to be an issue with your account. " +
                                               "Please contact your Credit Union for assistance. Error 600.";

    private string FiSlug => qCashContext.FinancialInstitutions.FirstOrDefault()?.Slug ?? "";

    /// <summary>
    /// Starts new loan application.
    /// </summary>
    /// <param name="ssoInitiateDTO">SSOInitiateAPIModel Single Sign-On object.</param>
    /// <param name="correlationId">Correlation Id.</param>
    /// <returns>LoanApplication object, Error string.</returns>
    public async Task<(Data.Models.LoanApplication, string)> StartLoanApplicationAsync(
        SsoInitiateApiModel ssoInitiateDTO, Guid correlationId)
    {
        var financialInstitutionConfiguration = await qCashContext.FinancialInstitutionConfigurations.SingleAsync();

        // Create a new LoanApplication for this SSO.
        var loanApplication = CreateLoanApplication();
        var errorDescription = string.Empty;
        var logs = new List<LoanApplicationLogDetail>();
        logs.AddLogDetail(LogType.AdminRequired, $"StartLoanApplication, CorrelationId: {correlationId}");
        try
        {
            loanApplication = await StartLoanApplicationInternalAsync(logs, ssoInitiateDTO, loanApplication, correlationId);
        }
        catch (Exception ex)
        {
            switch (ex)
            {
                case CoreProviderException coreProviderException:
                {
                    var mEx = coreProviderException.CoreProviderFault;
                    logs.Add(new LoanApplicationLogDetail
                    {
                        Id = guidExtensionService.NewSequentialGuid(),
                        ActionDateTimeUtc = DateTime.UtcNow,
                        IsTemp = false,
                        ActionDescription = $"Unexpected provider error: [{mEx?.ErrorMessage}]",
                        TimeStamp = [],
                    });
                    break;
                }
                case LoanApplicationException laException
                    when laException.Errors.Any(x => x.StartsWith(nameof(FinancialInstitutionMember.LastName), StringComparison.OrdinalIgnoreCase)):
                    errorDescription = LastNameMissingError;
                    break;
                default:
                    logs.Add(new LoanApplicationLogDetail
                    {
                        Id = guidExtensionService.NewSequentialGuid(),
                        ActionDateTimeUtc = DateTime.UtcNow,
                        TimeStamp = [],
                        IsTemp = false,
                        ActionDescription = $"Unexpected error: [{ex.Message}]",
                    });
                    break;
            }

            loanApplication = await SaveApplicationDetailsSafeForFailedStartAsync(ssoInitiateDTO, loanApplication, logs);

            if (string.IsNullOrEmpty(errorDescription))
            {
#if DEBUG
                errorDescription = ex.Message + " - " + ex.StackTrace;
#else
                errorDescription = ex.Message;
#endif
            }

            await errorHelper.StoreErrorAsync(ex, financialInstitutionConfiguration, correlationId);
        }

        return (loanApplication, errorDescription);
    }

    /// <summary>
    /// This is only called when a failure bubbles up from StartLoanApplicationInternalAsync. We record some information about this
    /// failed application.
    /// </summary>
    /// <param name="ssoInitiateDTO"></param>
    /// <param name="loanApplication"></param>
    /// <param name="logs"></param>
    /// <returns></returns>
    private async Task<Data.Models.LoanApplication> SaveApplicationDetailsSafeForFailedStartAsync(
        SsoInitiateApiModel ssoInitiateDTO,
        Data.Models.LoanApplication loanApplication,
        IList<LoanApplicationLogDetail> logs)
    {
        try
        {
            var existingLoanApplication = await qCashContext.LoanApplications.FindAsync(loanApplication.Id);
            if (existingLoanApplication == null)
            {
                var loanApplicationSso = CreateLoanApplicationSso(loanApplication.Id, ssoInitiateDTO);
                loanApplication.LoanApplicationSso = loanApplicationSso;
                loanApplication.FinancialInstitutionMember = await GetFailedFailedFiMemberAsync();
                qCashContext.LoanApplications.Add(loanApplication);
                await qCashContext.SaveChangesAsync();
            }

            await SaveLoanApplicationLogMessagesAsync(loanApplication, logs);
            return loanApplication;
        }
        catch (Exception ex)
        {
            logger.LogError(
                ex,
                "Failed to save loan application details for failed start. LoanApplicationId: {LoanApplicationId}",
                loanApplication.Id);
            // ignore exceptions here
            return loanApplication;
        }
    }

    public async Task<FraudControlApiModelGet> GetFraudControlStepAsync(Guid financialInstitutionMemberId, string languageCode)
    {
        var loanApplication = await GetLoanApplicationForWizardAsync(financialInstitutionMemberId);

        var language = await languageService.GetLanguagesQuery().SingleAsync(l => l.LanguageCode == languageCode);
        var formattedInterfaceTexts = await memberProcessInterfaceService
            .GetFormattedInterfaceTextsForStepAsync(loanApplication, language.Id, LoanApplicationStep.FraudControl);

        var fraudControl = loanApplication.LoanApplicationFraudControl;
        var submitThresholdMet = fraudControl?.SubmitNo >= fraudControl?.SubmitThreshold;

        return new FraudControlApiModelGet
        {
            Step = nameof(LoanApplicationStep.FraudControl),
            SelectedLanguageCode = languageCode,
            DisplayValues = [.. formattedInterfaceTexts.Select(text => new DisplayTextApiModel
            {
                Key = text.Key,
                Value = text.Value,
            }),],
            SubmitThresholdMet = submitThresholdMet,
        };
    }

    public async Task<LoanApplicationApiModelBaseGet> GetLoanLandingStepAsync(Guid financialInstitutionMemberId)
    {
        var loanApplication = await GetLoanApplicationForWizardAsync(financialInstitutionMemberId);

        return new LoanApplicationApiModelBaseGet
        {
            Step = loanApplication.CurrentStep.ToString(),
        };
    }

    public async Task<Response> ProcessFraudControlStepAsync(
        Guid financialInstitutionMemberId, FraudControlApiModelPost model) => await ProcessWizardStepAsync(
            financialInstitutionMemberId,
            model,
            "Fraud Control",
            _ => LoanApplicationStep.FraudControl);

    public async Task<Response> ProcessCancelApplicationAsync(
        Guid financialInstitutionMemberId, ApplicationApiModelPost model) => await ProcessWizardStepAsync(
        financialInstitutionMemberId,
        model,
        "Cancellation",
        la => (LoanApplicationStep)la.CurrentStep);

    public async Task<Response> ProcessLoanLandingStepAsync(
        Guid financialInstitutionMemberId, LoanLandingApiModelPost model) => await ProcessWizardStepAsync(
        financialInstitutionMemberId,
        model,
        "Loan Landing",
        la => ((LoanApplicationStep)la.CurrentStep));

    /// <summary>
    /// Gets EConsent disclosure step data for the GET endpoint
    /// </summary>
    /// <param name="financialInstitutionMemberId">Financial institution member ID</param>
    /// <param name="languageCode">Language code</param>
    /// <returns>EConsent disclosure API model for GET requests</returns>
    public async Task<EConsentDisclosureApiModelGet> GetEConsentDisclosureStepAsync(Guid financialInstitutionMemberId, string languageCode)
    {
        var loanApplication = await GetLoanApplicationForWizardAsync(financialInstitutionMemberId);

        var language = await languageService.GetLanguagesQuery().SingleAsync(l => l.LanguageCode == languageCode);
        var formattedInterfaceTexts = await memberProcessInterfaceService
            .GetFormattedInterfaceTextsForStepAsync(loanApplication, language.Id, LoanApplicationStep.EConsentDisclosure);
        var settings = await qCashContext.Settings.AsNoTracking().FirstAsync();

        return new EConsentDisclosureApiModelGet
        {
            Step = nameof(LoanApplicationStep.EConsentDisclosure),
            SelectedLanguageCode = languageCode,
            DisplayValues = [..formattedInterfaceTexts.Select(text => new DisplayTextApiModel
            {
                Key = text.Key,
                Value = text.Value,
            })],
            AppId = loanApplication.AppId,
            LoanApplicationId = loanApplication.Id,
            LogicValues =
            [
                new()
                {
                    Key = nameof(Common.Enums.DisplayLogic.FileDownload),
                    Value = settings.FileDownload,
                }
            ],
        };
    }

    /// <summary>
    /// Processes EConsent disclosure step submission for the POST endpoint
    /// </summary>
    /// <param name="financialInstitutionMemberId">Financial institution member ID</param>
    /// <param name="model">EConsent disclosure POST model</param>
    /// <returns>Response indicating the result of processing</returns>
    public async Task<Response> ProcessEConsentDisclosureStepAsync(
        Guid financialInstitutionMemberId, StatementAcceptedApiModelPost model)
    {
        var loanApplication = await GetLoanApplicationForWizardAsync(financialInstitutionMemberId);
        var memberConsentEConsent = loanApplication.MemberConsentEConsent();
        if (memberConsentEConsent == null)
            throw new InvalidOperationException("Member consent EConsent is not initialized for the loan application.");

        memberConsentEConsent.PreviouslyAccepted = model.StatementAccepted;
        memberConsentEConsent.Accepted = model.StatementAccepted;

        qCashContext.Entry(memberConsentEConsent).State = EntityState.Modified;
        await qCashContext.SaveChangesAsync();

        return await ProcessWizardStepAsync(
            financialInstitutionMemberId,
            model,
            "EConsent Disclosure",
            _ => LoanApplicationStep.EConsentDisclosure);
    }

    /// <summary>
    /// Updates loan landing status for a member.
    /// </summary>
    /// <param name="financialInstitutionMemberId">Financial institution member ID</param>
    /// <returns>Response indicating the result of the status update</returns>
    public async Task<MaskWaitResponse> UpdateLoanLandingStatusAsync(Guid financialInstitutionMemberId)
    {
        try
        {
            var loanApplication = await GetLoanApplicationForWizardAsync(financialInstitutionMemberId);

            await UpdateLoanApplicationStatusAsync(loanApplication, LoanApplicationStep.LoanLanding);

            return new MaskWaitResponse
            {
                Action = true,
                Status = "OK"
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating loan landing status for member {MemberId}", financialInstitutionMemberId);
            return new MaskWaitResponse
            {
                Action = false,
                Status = "Error",
                Description = "An error occurred while updating loan landing status",
                Errors = [ex.Message]
            };
        }
    }

    /// <summary>
    /// Processes a wizard step and handles common error handling and response creation.
    /// </summary>
    /// <param name="financialInstitutionMemberId">The member ID to retrieve loan application for</param>
    /// <param name="model">The model containing data for the step being processed</param>
    /// <param name="stepDescription">The step description in a user-understandable way.</param>
    /// <param name="fallbackStepProvider">A function that provides the fallback step name in case of an error</param>
    /// <returns>A response object with the next step or error information</returns>
    private async Task<Response> ProcessWizardStepAsync(
        Guid financialInstitutionMemberId,
        LoanApplicationApiModelBasePost model,
        string stepDescription,
        Func<Data.Models.LoanApplication, LoanApplicationStep> fallbackStepProvider)
    {
        var loanApplication = await GetLoanApplicationForWizardAsync(financialInstitutionMemberId);
        var logs = new List<LoanApplicationLogDetail>();

        try
        {
            var nextStep = await ExecuteWizardStepAsync(loanApplication, model, false, logs);

            return new Response { Step = nextStep.ToString(), Status = "OK" };
        }
        catch (Exception ex)
        {
            // Log the error and return a response indicating failure
            loanApplicationService.AddLogMessage(logs, $"{stepDescription} Processing Error: {ex.Message}", DateTime.UtcNow);
            await SaveLoanApplicationLogMessagesAsync(loanApplication, logs);

            return new Response
            {
                Step = fallbackStepProvider(loanApplication).ToString(),
                Errors = [ex.Message]
            };
        }
    }

    private async Task<FinancialInstitutionMember> GetFailedFailedFiMemberAsync()
    {
        //each FI has a "Failed Failed" member to attach to a failed SSO attempt, so that the data can be logged.
        //Previously in QC6, this was an empty (0's) Guid; this would no longer work, so this method retrieves or adds a 'Failed Failed'
        //member for the FI.

        //Note that this process could definitely be improved upon.

        var existingFailed = await
            qCashContext.FinancialInstitutionMembers.AsNoTracking()
                .FirstOrDefaultAsync(o => o.FirstName == "Failed" && o.LastName == "Failed" && string.IsNullOrWhiteSpace(o.MemberIdHash)
                                          && string.IsNullOrWhiteSpace(o.MemberIdMask));

        if (existingFailed != null)
        {
            return existingFailed;
        }

        var fiId = await qCashContext.FinancialInstitutions.AsNoTracking().Select(o => o.Id).FirstAsync();
        return new FinancialInstitutionMember
        {
            FirstName = "Failed",
            LastName = "Failed",
            MemberIdHash = string.Empty,
            MemberIdMask = string.Empty,
            Id = guidExtensionService.NewSequentialGuid(),
            DateCreatedUtc = DateTime.UtcNow,
            DateUpdatedUtc = DateTime.UtcNow,
            TimeStamp = [],
            IsOnBlocklist = false,
            IsTroubledDebt = false,
            IsBadAddress = false,
            IsBadEmail = false,
            FinancialInstitutionId = fiId,
        };
    }

    private async Task<Data.Models.LoanApplication> StartLoanApplicationInternalAsync(
        List<LoanApplicationLogDetail> logs,
        SsoInitiateApiModel ssoInitiateDto,
        Data.Models.LoanApplication loanApplication,
        Guid correlationId)
    {
        DateTime startTime = DateTime.UtcNow, executionTime = DateTime.UtcNow;

        var loanStatusLsti = await qCashContext.LoanStatusTypes.AsNoTracking().SingleAsync(o => o.Abrv == "LSTI");

        var settings = await qCashContext.Settings.AsNoTracking().SingleAsync();
        var settingsLog = new List<LoanApplicationSettingsLog>();

        // Log execution time for loading the settings.
        logs.AddLogDetail(
            $"Settings load finished: {(DateTime.UtcNow - executionTime).TotalSeconds.ToString(CultureInfo.InvariantCulture)}s");
        executionTime = DateTime.UtcNow;

        // Make a copy of the SSO object, mask sensitive information and log it.
        var ssoJson = JsonConvert.SerializeObject(ssoInitiateDto);
        var ssoLog = JsonConvert.DeserializeObject<SsoInitiateApiModel>(ssoJson)!;
        ssoLog.BaseAccount = loanApplicationService.GetMaskedNumber(ssoLog.BaseAccount);
        ssoLog.TaxId = loanApplicationService.GetMaskedNumber(ssoLog.TaxId);
        ssoLog.MemberId = loanApplicationService.GetMaskedNumber(ssoLog.MemberId);
        logs.AddLogDetail(LogType.AdminRequired, $"SSO Info Captured: {JsonConvert.SerializeObject(ssoLog)}");

        // Validate SSO object and log execution time.
        var ssoValid = loanApplicationService.ValidateSso(ssoInitiateDto);
        logs.AddLogDetail(
            $"SSO validated finished: {(DateTime.UtcNow - executionTime).TotalSeconds.ToString(CultureInfo.InvariantCulture)}s");

        // If SSO object is not valid, throw and error.
        if (ssoValid.Errors?.Errors is { Count: > 0 })
        {
            throw new Exception(string.Join(',', ssoValid.Errors.Errors));
        }

        FinancialInstitutionMember member;
        var getMemberTimer = Stopwatch.StartNew();
        logs.AddLogDetail(LogType.AdminRequired, $@"GetMember from middleware started: Id = {loanApplication.Id}, CorrelationId = {correlationId}");

        try
        {
            var response = await GetMemberBySsoAsync(ssoInitiateDto, correlationId, logs);

            member = response.FinancialInstitutionMember ?? throw new Exception("Response object or Member object from GetMemberBySSOAsync was null");
            var getMemberDuration = response.Elapsed;
            logs.AddLogDetail(
                LogType.AdminRequired,
                $"GetMember from middleware finished: {(DateTime.UtcNow - executionTime).TotalSeconds.ToString(CultureInfo.InvariantCulture)}s {correlationId}");

            logger.LogInformation("GetMemberDuration {Duration} {Origin} {Correlation} {Application} {Success} {Role} {FiSlug}", getMemberDuration, (OriginApp)ssoInitiateDto.OriginApp, correlationId.ToString(), loanApplication.Id, true, "WebRole", FiSlug);
        }
        catch (CoreProviderException ex)
        {
            getMemberTimer.Stop();
            logger.LogInformation("GetMemberDuration {Duration} {Origin} {Correlation} {Application} {Success} {Role} {FiSlug}", getMemberTimer.ElapsedMilliseconds, (OriginApp)ssoInitiateDto.OriginApp, correlationId.ToString(), loanApplication.Id, false, "WebRole", FiSlug);
            logs.AddLogDetail(
                LogType.Clean,
                LogSortOrder.MemberInfoCaptured,
                $"Failed to get member info from middleware. Received an error: {ex.CoreProviderFault?.StandardCoreProviderError} {ex.CoreProviderFault?.ErrorMessage}");
            ExceptionDispatchInfo.Capture(ex).Throw();
            throw;
        }
        catch (Exception ex)
        {
            getMemberTimer.Stop();
            logger.LogInformation("GetMemberDuration {Duration} {Origin} {Correlation} {Application} {Success} {Role} {FiSlug}", getMemberTimer.ElapsedMilliseconds, (OriginApp)ssoInitiateDto.OriginApp, correlationId.ToString(), loanApplication.Id, false, "WebRole", FiSlug);
            logs.AddLogDetail(LogType.Clean, LogSortOrder.MemberInfoCaptured, "Member Info Capturing Failed");
            ExceptionDispatchInfo.Capture(ex).Throw();
            throw;
        }

        executionTime = DateTime.UtcNow;

        logs.AddLogDetail(
            $"Restart loan application finished: {(DateTime.UtcNow - executionTime).TotalSeconds.ToString(CultureInfo.InvariantCulture)}s");
        executionTime = DateTime.UtcNow;

        loanApplication.FeeRefundStep = nameof(FeeRefundStep.None);
        loanApplication.AccountId =
            !string.IsNullOrEmpty(member.AccountId) ? member.AccountId : ssoInitiateDto.BaseAccount;
        loanApplication.IsInProgress = true;
        loanApplication.DateCreatedUtc = DateTime.UtcNow;
        loanApplication.MatureDateUtc = DateTime.UtcNow;
        loanApplication.SelectedProductId = Guid.Empty;
        loanApplication.FinancialInstitutionMemberId = member.Id;
        loanApplication.FinancialInstitutionMember = member;
        loanApplication.LoanStatusId = loanStatusLsti.Id;

        // Set account for transaction.
        loanApplication.DefaultAccountForTransactionId = loanApplicationService.GetAccountForTransaction(
            loanApplication.FinancialInstitutionMember.Id,
            loanApplication.AccountId,
            loanApplication.ActiveDuty.GetValueOrDefault(),
            settings)?.Id;

        LogAccountInfo(logs, ssoInitiateDto, loanApplication, member);

        // Log finished creating LoanApplication.
        logs.AddLogDetail(
            $"Create new loan application finished: {(DateTime.UtcNow - executionTime).TotalSeconds.ToString(CultureInfo.InvariantCulture)}s");

        loanApplication = await SetInitialStepAndInsertLoanApplicationAsync(loanApplication, ssoInitiateDto, settingsLog, logs);

        // Log StartLoanApplication method finished.
        logs.AddLogDetail(LogType.Admin, LogSortOrder.None,
            $"StartLoanApplication Service Finished: {(DateTime.UtcNow - startTime).TotalSeconds.ToString(CultureInfo.InvariantCulture)}");

        // Save logs.
        await SaveLoanApplicationLogMessagesAsync(loanApplication, logs);
        logs.Clear();
        loanApplicationService.SaveSettingsLog(settingsLog);

        return loanApplication;
    }

    private async Task<Data.Models.LoanApplication> SetInitialStepAndInsertLoanApplicationAsync(
        Data.Models.LoanApplication loanApplication,
        SsoInitiateApiModel ssoInitiateDto,
        List<LoanApplicationSettingsLog> settingsLog,
        List<LoanApplicationLogDetail> logs)
    {
        // Log current settings.
        var loanStatusLstm = await qCashContext.LoanStatusTypes.AsNoTracking().SingleAsync(o => o.Abrv == "LSTM");

        var setting = await qCashContext.Settings.AsNoTracking().FirstAsync();
        var maintenanceSetting = await qCashContext.MaintenanceSettings.AsNoTracking().FirstOrDefaultAsync();

        var ssoTestSetup = financialInstitutionService.GetSSOTest();

        loanApplicationService.InsertLoanApplicationSettingsLog(settingsLog, loanApplication.Id, LoanApplicationSettingType.MaskWaitTime, setting.MaskInitiateWaitTime);
        loanApplicationService.InsertLoanApplicationSettingsLog(settingsLog, loanApplication.Id, LoanApplicationSettingType.Payoff, setting.UsePayOff.GetValueOrDefault());

        var loanApplicationSso = CreateLoanApplicationSso(loanApplication.Id, ssoInitiateDto);
        loanApplication.LoanApplicationSso = loanApplicationSso;

        if (ssoInitiateDto.IsSsoTest
            && loanApplicationSso.Location != null
            && !loanApplicationSso.Location.Equals(nameof(SsoType.NETTELLER), StringComparison.CurrentCultureIgnoreCase))
        {
            loanApplicationSso.PreApproved = GetFlagFromSsoCredentials(Convert.ToInt32(ssoTestSetup.PreApproved).ToString());
        }

        if (CheckIsUnderMaintenance(maintenanceSetting))
        {
            loanApplication.CurrentStep = (int)LoanApplicationStep.Maintenance;
            loanApplication.LoanStatusId = loanStatusLstm.Id;
            InsertLoanApplicationAndSso(ref loanApplication, loanApplicationSso);
            loanApplicationService.InsertLoanApplicationSettingsLog(settingsLog, loanApplication.Id, LoanApplicationSettingType.MaintenanceSetting, maintenanceSetting!.IsMaintenanceActive);
            return loanApplication;
        }

        // Log selected language.
        loanApplication.SelectedLanguageId = GetLanguagePreference(loanApplication.FinancialInstitutionMember.MemberIdHash);
        if (!guidExtensionService.IsNullOrEmpty(loanApplication.SelectedLanguageId))
        {
            var languageName = (await qCashContext.Languages.AsNoTracking().SingleOrDefaultAsync(o => o.Id == loanApplication.SelectedLanguageId))?.Name ?? "Unknown";
            logs.AddLogDetail(
                LogType.Clean,
                LogSortOrder.LanguageSelection,
                LogDetailItemReportGroupEnum.None,
                string.Format($"Language: {languageName}"));
        }

        if (setting.MaskInitiateWaitTime)
        {
            if (setting.UsePayOff.GetValueOrDefault())
            {
                loanApplication.CurrentStep = (int)LoanApplicationStep.LoanHub;
            }
            else
            {
                loanApplication.CurrentStep = (int)LoanApplicationStep.LoanLanding;
            }

            InsertLoanApplicationAndSso(ref loanApplication, loanApplicationSso);
        }
        else
        {
            if (setting.LoanHub && !loanApplication.LoanApplicationSso.PreApproved.GetValueOrDefault())
            {
                if (loanApplication.LoanApplicationSso.PreApproved.GetValueOrDefault())
                {
                    loanApplication.CurrentStep = (int)LoanApplicationStep.Application;
                    loanApplication.SelectedLoanHubItemId = LoanHubItems.PreapprovedMailer;
                }
                else
                {
                    loanApplication.CurrentStep = (int)LoanApplicationStep.LoanHub;
                }
                InsertLoanApplicationAndSso(ref loanApplication, loanApplicationSso);
                return loanApplication;
            }

            // Inserting of LoanApplication is handled in this method (if payOff is off).
            loanApplication = await GetNextStepAfterExclusionsAsync(
                loanApplication,
                logs,
                settingsLog);
        }

        return loanApplication;
    }

    private Guid? GetLanguagePreference(string memberIdHash)
    {

        if (string.IsNullOrEmpty(memberIdHash))
        {
            return null;
        }

        var la = qCashContext.LoanApplications.Where(p => p.FinancialInstitutionMember.MemberIdHash == memberIdHash)
            .OrderByDescending(q => q.AppId)
            .FirstOrDefault();

        return la?.SelectedLanguageId;
    }

    /// <summary>
    /// Checks if system is currently under maintenance.
    /// </summary>
    /// <param name="maintenanceSetting"></param>
    /// <returns></returns>
    public bool CheckIsUnderMaintenance(MaintenanceSetting? maintenanceSetting)
    {
        if (maintenanceSetting != null)
        {
            return maintenanceSetting.IsMaintenanceActive && (!maintenanceSetting.StartDate.HasValue || maintenanceSetting.StartDate <= DateTime.UtcNow && maintenanceSetting.EndDate > DateTime.UtcNow);
        }
        return false;
    }

    public async Task SaveLoanApplicationLogMessagesAsync(Data.Models.LoanApplication loanApplication, IList<LoanApplicationLogDetail>? logs)
    {
        if (logs == null || logs.Count == 0)
        {
            return;
        }

        var loanApplicationId = loanApplication.Id;
        var loanStatusTypeLookup = await qCashContext.LoanStatusTypes.AsNoTracking().ToListAsync();

        //Note - this section probably slated for removal (Payoff)
        // Check for payoff.
        // NOTE: If we are doing payoff, we need to get the LoanApplication for Payoff.
        var isPayoff = loanApplication.LoanStatusId == loanStatusTypeLookup.Single(o => o.Abrv == "LSTPOINI").Id
                       || loanApplication.LoanStatusId == loanStatusTypeLookup.Single(o => o.Abrv == "LSTPOCMP").Id
                       || loanApplication.LoanStatusId == loanStatusTypeLookup.Single(o => o.Abrv == "LSTPOCNC").Id
                       || loanApplication.LoanStatusId == loanStatusTypeLookup.Single(o => o.Abrv == "LSTPOERR").Id;

        if (isPayoff)
        {
            var applicationPayOffs = await GetLoanApplicationPayOffsByLoanApplicationIdAsync(loanApplication.Id);

            if (applicationPayOffs.Count != 0 && !guidExtensionService.IsNullOrEmpty(applicationPayOffs[0].PayOffLoanApplicationId))
            {
                loanApplicationId = applicationPayOffs[0].PayOffLoanApplicationId.GetValueOrDefault();
            }
        }

        var loanApplicationLog = qCashContext.LoanApplicationLogs.AsNoTracking().FirstOrDefault(p => p.LoanApplicationId == loanApplicationId);

        if (loanApplicationLog == null)
        {
            loanApplicationLog = new LoanApplicationLog
            {
                Id = guidExtensionService.NewSequentialGuid(),
                TimeStamp = [],
                LoanApplicationId = loanApplicationId,
            };
            qCashContext.LoanApplicationLogs.Add(loanApplicationLog);
            await qCashContext.SaveChangesAsync();
        }

        var logsToSave = applicationOptions.Value.SaveAdminLogs
            ? logs
            : logs.Where(p => p.LogType is (int)LogType.AdminRequired or (int)LogType.Clean).ToList();

        if (logsToSave.Count == 1)
        {
            var item = logsToSave[0];
            var laLogDetail = new LoanApplicationLogDetail
            {
                Id = guidExtensionService.NewSequentialGuid(),
                TimeStamp = [],
                LoanApplicationLogId = loanApplicationLog.Id,
                ActionDateTimeUtc = item.ActionDateTimeUtc,
                ActionDescription = item.ActionDescription,
                LogType = item.LogType,
                IsTemp = item.IsTemp,
                SortOrder = item.SortOrder,
                ReportGroup = item.ReportGroup,
                IsStatistical = item.IsStatistical,
            };
            laLogDetail.ActionDateTimeUtc = item.ActionDateTimeUtc.Year < 2000
                ? DateTime.UtcNow.AddMilliseconds(1)
                : item.ActionDateTimeUtc.AddMilliseconds(1);

            qCashContext.LoanApplicationLogDetails.Add(laLogDetail);
            await qCashContext.SaveChangesAsync();

            return;
        }

        var counter = 0;
        var logsToSerialize = logsToSave.Select(item => new JsonLogItemModel
        {
            ActionDescription = item.ActionDescription,
            LogType = item.LogType,
            SortOrder = item.SortOrder,
            ReportGroup = item.ReportGroup,
            IsStatistical = item.IsStatistical,
            ActionDateTimeUtc = item.ActionDateTimeUtc.Year < 2000
                ? DateTime.UtcNow.AddMilliseconds(++counter)
                : item.ActionDateTimeUtc.AddMilliseconds(++counter),
        })
        .ToList();

        var jsonItem = new LoanApplicationLogDetail
        {
            LogType = (int)LogType.Clean, //Enable fetch for clean items
            Id = guidExtensionService.NewSequentialGuid(),
            LoanApplicationLogId = loanApplicationLog.Id,
            TimeStamp = [],
            ActionDateTimeUtc = DateTime.UtcNow,
            ActionDescription = JsonConvert.SerializeObject(logsToSerialize),
        };

        qCashContext.LoanApplicationLogDetails.Add(jsonItem);
        await qCashContext.SaveChangesAsync();

        logs.Clear();
    }

    private async Task<List<LoanApplicationPayOff>> GetLoanApplicationPayOffsByLoanApplicationIdAsync(Guid loanApplicationId) =>
        //probably slated for removal - payoff related
        await qCashContext.LoanApplicationPayOffs
            .AsNoTracking()
            .Where(p => p.LoanApplicationId.Equals(loanApplicationId))
            .OrderBy(p => p.Id)
            .ToListAsync();

    private void LogAccountInfo(
        IList<LoanApplicationLogDetail> logs,
        SsoInitiateApiModel ssoInitiateDTO,
        Data.Models.LoanApplication loanApplication,
        FinancialInstitutionMember member)
    {
        var accForFund = loanApplicationService.GetMemberBaseAccount(
            loanApplication.FinancialInstitutionMemberId,
            loanApplication.AccountId ?? string.Empty);
        var accountNumberFormatted = "\"Account\":\"" + loanApplicationService.GetMaskedNumber(member.AccountId) + "\"";

        // Log Member Info Captured custom object.
        var memberInfoCustomObject = new
        {
            member.FirstName,
            member.MiddleName,
            LastName = string.IsNullOrWhiteSpace(member.Suffix)
                ? member.LastName
                : $"{member.LastName} {member.Suffix}",
            member.MailingAddress,
            member.City,
            member.State,
            member.Zip,
            Email = member.Email,
            member.PhoneNumber,
            MemberId = member.MemberIdMask,
            Branch = accForFund != null ? accForFund.BranchNumber : string.Empty,
            Account = loanApplicationService.GetMaskedNumber(member.AccountId),
        };

        var customObjectString = JsonConvert.SerializeObject(memberInfoCustomObject)
            .Replace(accountNumberFormatted, $"<strong>{accountNumberFormatted}</strong>");

        var actionDescription = $"Member Info Captured: {customObjectString}";

        logs.AddLogDetail(LogType.Clean, LogSortOrder.MemberInfoCaptured, actionDescription);

        // Log Teller Info Captured custom object.
        var tellerIdFormatted = "\"TellerId\":\"" + ssoInitiateDTO.TellerId + "\"";
        var tellerInfoCustomObject = new
        {
            ssoInitiateDTO.TellerId,
            ssoInitiateDTO.Location,
            ssoInitiateDTO.ReturnUrl,
            ssoInitiateDTO.KeepAliveUrl,
            ssoInitiateDTO.TimeoutUrl,
            ssoInitiateDTO.LogoutUrl,
        };
        var tellerInfoCustomObjectString = JsonConvert.SerializeObject(tellerInfoCustomObject)
            .Replace(tellerIdFormatted, $"<strong>{tellerIdFormatted}</strong>");
        actionDescription = $"Teller Info Captured: {tellerInfoCustomObjectString}";

        logs.AddLogDetail(LogType.Clean, LogSortOrder.TellerInfoCaptured, actionDescription);
    }

    private void InsertLoanApplicationAndSso(ref Data.Models.LoanApplication loanApplication, LoanApplicationSso loanApplicationSso)
    {
        loanApplication.NextStep = loanApplication.CurrentStep;
        loanApplication.FirstStep = loanApplication.CurrentStep;
        loanApplication.LoanApplicationSso = loanApplicationSso;

        //This is also the point at which the LoanApplicationSSO gets added to the database
        qCashContext.LoanApplications.Add(loanApplication);
        qCashContext.SaveChanges();

        //old repo methodology here - nulling out FiMember and LaSso before saving, then re-adding - odd
        //var fiMember = loanApplication.FinancialInstitutionMember;
        //loanApplication.FinancialInstitutionMember = null;
        //loanApplication.LoanApplicationSso = null;
        //loanApplication.NextStep = loanApplication.CurrentStep;
        //loanApplication.FirstStep = loanApplication.CurrentStep;
        //loanApplication = InsertLoanApplication(loanApplication);

        //loanApplicationSso.LoanApplication = null;
        //InsertLoanApplicationSSO(loanApplicationSso);

        //loanApplication.FinancialInstitutionMember = fiMember;
        //loanApplication.LoanApplicationSso = loanApplicationSso;
    }

    private Data.Models.LoanApplication CreateLoanApplication()
    {
        var loanApplication = new Data.Models.LoanApplication
        {
            Id = guidExtensionService.NewSequentialGuid(),
            FeeRefundStep = nameof(FeeRefundStep.None),
            DateCreatedUtc = DateTime.UtcNow,
            TimeStamp = [],
            MatureDateUtc = DateTime.UtcNow,
            SelectedProductId = Guid.Empty,
            PaymentGuardReportStatusId = (int)Enums.PaymentGuardReportStatus.NotCovered,
        };

        return loanApplication;
    }

    private LoanApplicationSso CreateLoanApplicationSso(Guid id, SsoInitiateApiModel ssoInitiateDTO)
    {
        var loanApplicationSso = new LoanApplicationSso();

        //map from SSOInitiateApiModel to LoanApplicationSso
        //loanApplicationSso.BaseAccount = ssoInitiateDTO.BaseAccount;
        loanApplicationSso.Email = ssoInitiateDTO.Email;
        loanApplicationSso.Location = ssoInitiateDTO.Location;
        loanApplicationSso.TellerId = ssoInitiateDTO.TellerId;
        //loanApplicationSso.TaxId = ssoInitiateDTO.TaxId;
        //loanApplicationSso.OriginApp = ssoInitiateDTO.OriginApp;
        //loanApplicationSso.IsMobile = ssoInitiateDTO.IsMobile;
        //loanApplicationSso.PreApproved = ssoInitiateDTO.PreApproved;
        loanApplicationSso.KeepAliveUrl = ssoInitiateDTO.KeepAliveUrl;
        loanApplicationSso.LogoutUrl = ssoInitiateDTO.LogoutUrl;
        loanApplicationSso.ReturnUrl = ssoInitiateDTO.ReturnUrl;
        loanApplicationSso.TimeoutUrl = ssoInitiateDTO.TimeoutUrl;
        loanApplicationSso.IsTest = ssoInitiateDTO.IsSsoTest;
        //loanApplicationSso.JointAccount = ssoInitiateDTO.IsJointAccount;
        loanApplicationSso.PhoneNumber = ssoInitiateDTO.PhoneNumber;
        loanApplicationSso.AdditionalHeaders = ssoInitiateDTO.AdditionalHeaders;

        loanApplicationSso.Id = id; //note that this is the loanApplicationId
        loanApplicationSso.OriginApp = ((OriginApp)ssoInitiateDTO.OriginApp).ToString();
        loanApplicationSso.PreApproved = GetFlagFromSsoCredentials(ssoInitiateDTO.PreApproved);

#pragma warning disable CS0618 // Type or member is obsolete
        loanApplicationSso.JointAccount = ssoInitiateDTO.IsJointAccount ?? GetFlagFromSsoCredentials(ssoInitiateDTO.JointAccount);
#pragma warning restore CS0618 // Type or member is obsolete
        loanApplicationSso.BaseAccount = string.IsNullOrWhiteSpace(ssoInitiateDTO.BaseAccount)
            ? null
            : tokenService.EncryptValue(ssoInitiateDTO.BaseAccount);
        loanApplicationSso.TaxId = string.IsNullOrWhiteSpace(ssoInitiateDTO.TaxId)
            ? null
            : tokenService.EncryptValue(ssoInitiateDTO.TaxId);
        loanApplicationSso.IsPrimaryRegion = applicationOptions.Value.IsPrimaryInstance;

        loanApplicationSso.TimeStamp = [];

        return loanApplicationSso;
    }


    private async Task<Data.Models.LoanApplication> GetNextStepAfterExclusionsAsync(
        Data.Models.LoanApplication loanApplication,
        IList<LoanApplicationLogDetail> logs,
        IList<LoanApplicationSettingsLog> settingsLog)
    {
        var member = loanApplication.FinancialInstitutionMember;

        var originApp = loanApplication.LoanApplicationSso.GetOriginAppEnum();
        await loanApplicationService.CheckMLAStatusAsync(logs, loanApplication, originApp, settingsLog);

        var memberExclusionsDTO = new MemberExclusionsValidatorDTO(loanApplication, member, settingsLog, logs, financialInstitutionService.FinancialInstitution, FiSlug);
        var validExclusions = await memberExclusionsValidatorRunner.ValidateAsync(memberExclusionsDTO);
        var exclusionValidatorResult = memberExclusionsDTO.ExclusionValidatorResult;

        var setting = await qCashContext.Settings.FirstAsync();

        ProductsExclusionsValidatorDTO? productsExclusionsDTO = null;
        if (validExclusions)
        {
            var activeProducts = await GetAllActiveProductsAsync();

            // same filter as in GetAvailableProductsAfterProductsExclusions
            if (setting.LoanHub && loanApplication.LoanApplicationSso != null && !loanApplication.LoanApplicationSso.PreApproved.GetValueOrDefault())
            {
                activeProducts = loanApplicationService.GetAvailableProductsWhenLoanHubActive(activeProducts, loanApplication.SelectedLoanHubItemId);
            }

            productsExclusionsDTO = new ProductsExclusionsValidatorDTO(loanApplication, activeProducts, setting, logs);
            validExclusions = await productsExclusionsValidatorRunner.ValidateAsync(productsExclusionsDTO);
            exclusionValidatorResult = productsExclusionsDTO.ExclusionValidatorResult;
        }

        var createEConsent = false;

        if (!validExclusions)
        {
            loanApplication.CurrentStep = (int)exclusionValidatorResult!.NewLoanApplicationStep;
            loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, exclusionValidatorResult.LoanStatusType, exclusionValidatorResult.LogSortOrder, LogDetailItemReportGroupEnum.None);
            await loanApplicationService.FinishLoanAsync(loanApplication, logs);
        }
        else
        {
            logs.AddLogDetail(LogType.AdminRequired, LogSortOrder.IsStateExcluded, "Loan application started.");

            var aprod = string.Empty;
            productsExclusionsDTO!.AvailableProducts.ForEach(p => aprod += p.Name + " | ");
            aprod = aprod.TrimEnd("| ".ToCharArray());

            logs.AddLogDetail(LogType.Clean, LogSortOrder.AvailableNonExcludedProducts, LogDetailItemReportGroupEnum.Bold, "Available Non-Excluded Products: " + aprod);
            loanApplicationService.InsertLoanApplicationSettingsLog(settingsLog, loanApplication.Id, LoanApplicationSettingType.Payoff, setting.UsePayOff.GetValueOrDefault());

            var loanApplicationStep = ValidateInitiate(loanApplication, originApp, logs, settingsLog).NextStep;
            loanApplication.CurrentStep = (int)loanApplicationStep;
            if (loanApplicationStep == LoanApplicationStep.EConsentDisclosure)
            {
                createEConsent = true;
            }
        }

        loanApplication.NextStep = loanApplication.CurrentStep;
        loanApplication.FirstStep = loanApplication.CurrentStep;

        //Commenting these lines - old method nulled out these before saving, then re-added - with EF they'll get saved when LoanApplication is saved to DB,
        //which should be OK
        //loanApplication.FinancialInstitutionMember = null;
        //loanApplication.LoanApplicationSso = null;

        var loanApplicationSSO = loanApplication.LoanApplicationSso;

        var st = new Stopwatch();
        st.Start();

        try
        {
            // If MaskInitiateWaitTime and UsePayOff are 'true', the object is already in the DB, just need to update it, otherwise insert it.
            if (setting.MaskInitiateWaitTime || (setting.LoanHub && !(loanApplicationSSO!.PreApproved.GetValueOrDefault())))
            {
                //none of what was in here should be necessary anymore - when we call SaveChanges below all will commit to DB
                UpdateLoanApplicationInWizard(loanApplication); //see note in method body
            }
            else
            {
                //insert here
                //loanApplication = InsertLoanApplication(loanApplication);
                qCashContext.LoanApplications.Add(loanApplication);
            }
            //regardless of whether we inserted here or called UpdateLoanApplicationInWizard, need to now commit changes
            //this should be the first point at which new LA and everything underneath it is saved to DB
            await qCashContext.SaveChangesAsync();
        }
        catch (OperationCanceledException)
        {
            await SaveLoanApplicationLogMessagesAsync(loanApplication, logs);
            loanApplicationService.SaveSettingsLog(settingsLog);
            throw;
        }
        catch (DbUpdateConcurrencyException concurrencyException)
        {
            //NewRelicLogger.LogWarning("DbUpdateConcurrencyException in StartLoanApplication. Retry saving.", FiSlug);
            logger.LogWarning("DbUpdateConcurrencyException in StartLoanApplication. Retry saving");
            var entry = concurrencyException.Entries.Single();
            entry.OriginalValues.SetValues((await entry.GetDatabaseValuesAsync())!);
            await qCashContext.SaveChangesAsync();
            //NewRelicLogger.LogWarning("DbUpdateConcurrencyException in StartLoanApplication. Retry saving completed.", FiSlug);
            logger.LogWarning("DbUpdateConcurrencyException in StartLoanApplication. Retry saving completed");
        }

        st.Stop();
        logs.AddLogDetail($"Loan application insert finished: {st.Elapsed.TotalSeconds}s");
        st.Restart();

        //Don't need to do this "reattach" operation anymore
        //loanApplication.FinancialInstitutionMember = member;
        //loanApplication.LoanApplicationSso = loanApplicationSSO;

        var loanStatusTypeLookup = await qCashContext.LoanStatusTypes.ToListAsync();
        logs.AddLogDetail(
            LogType.Clean,
            LogSortOrder.Inititate,
            LogDetailItemReportGroupEnum.Red,
            $"Loan Status: <strong>{loanStatusTypeLookup.Single(o => o.Abrv == "LSTI").Name} - App ID {loanApplication.AppId}</strong>");

        //Code placed here because of inserting new loan application
        if (createEConsent)
        {
            await StoreEConsentDocumentAsync(logs, loanApplication);
        }

        //var timeZone = SettingService.GetTimeZone();
        var clientTimeZone = (await qCashContext.Settings.FirstOrDefaultAsync())?.TimeZone ?? string.Empty;

        var actionDescription = $"Loan application created: {JsonConvert.SerializeObject(
            new
            {
                LoanApplicationId = loanApplication.AppId,
                DateTime = loanApplication.DateCreatedUtc.FromUTC(TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone)),
                //NewRelicId = (ShortGuid)loanApplication.Id
            })}";

        st.Stop();
        logs.AddLogDetail(LogType.Admin, LogSortOrder.None, actionDescription);
        logs.AddLogDetail($"StartLoanApplication finished: {st.Elapsed.TotalSeconds}s");

        //Save settings log collection and clear list
        loanApplicationService.SaveSettingsLog(settingsLog);
        settingsLog.Clear();
        return loanApplication;
    }


    /// <summary>
    /// Stores TILA document to the directory where their document imaging system pulls documents.
    /// </summary>
    private async Task StoreTILADocumentAsync(IList<LoanApplicationLogDetail> logs, Data.Models.LoanApplication loanApplication, IList<LoanApplicationSettingsLog> settingsLog, Setting setting)
    {
        await fileSystemProvider.SetupAsync(ContainerTypeEnum.DocumentsPrivate);
        var doc = loanApplicationService.GetTilaDocument(loanApplication);
        if (doc == null)
        {
            return;
        }

        var docData = await fileSystemProvider.GetFileAsync(doc.Filename);
        if (docData == null || docData.Length == 0)
        {
            return;
        }

        var product = await qCashContext.Products.FirstAsync(o => o.Id == loanApplication.SelectedProductId.GetValueOrDefault());
        if (product.PersonalLoanCampaign)
        {
            loanApplicationService.InsertLoanApplicationSettingsLog(settingsLog, loanApplication.Id, LoanApplicationSettingType.CampaignCode, setting.UsePersonalPrivateContainer);

            if (setting.UsePersonalPrivateContainer)
            {
                await fileSystemProvider.SetupAsync(ContainerTypeEnum.PersonalPrivate);
            }
            else
            {
                await fileSystemProvider.SetupAsync(ContainerTypeEnum.Private);
            }
        }
        else
        {
            await fileSystemProvider.SetupAsync(ContainerTypeEnum.Private);
        }

        await StoreDocumentAsync(logs, docData, loanApplication, LoanApplicationDocumentType.TILA);
    }

    private async Task<bool> StoreLoanDisclosureDocumentAsync(IList<LoanApplicationLogDetail> logs,
        Data.Models.LoanApplication? loanApplication,
        string fundsAccount,
        ValidationResult vr,
        bool isRegenerating = false)
    {
        if (loanApplication == null)
        {
            return false;
        }

        var language = loanApplication.SelectedLanguageId.HasValue
            ? await qCashContext.Languages.SingleOrDefaultAsync(l => l.Id == loanApplication.SelectedLanguageId)
            : await qCashContext.Languages.SingleOrDefaultAsync(l => l.Name == LanguageEnum.English.Name);
        language.ThrowIfNull($"Language {loanApplication.SelectedLanguageId} not found");
        var languageCode = language.LanguageCode;
        var languageId = language.Id;

        var clientTimeZone = (await qCashContext.Settings.FirstOrDefaultAsync())?.TimeZone ?? string.Empty;
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);

        var product = loanApplication.SelectedProduct;

        if (product == null)
        {
            return false;
        }

        var ptime = DateTime.UtcNow;
        loanApplicationService.AddLogMessage(logs, "TILA Request started: " + ptime.FromUTC(timeZone).ToShortTimeString(), DateTime.UtcNow);

        TilaReportDto dto;

        var loanTypeIB = await qCashContext.LoanTypes.SingleAsync(o => o.Abrv == nameof(LoanTypeAbbreviation.IB));
        var loanTypeFB = await qCashContext.LoanTypes.SingleAsync(o => o.Abrv == nameof(LoanTypeAbbreviation.FB));
        if (product.LoanTypeId == loanTypeFB.Id)
        {
            dto = await GetFeeBasedTILAAsync(loanApplication.AppId);
        }
        else if (product.LoanTypeId == loanTypeIB.Id)
        {
            dto = await GetInterestBasedTILAAsync(loanApplication.AppId);
        }
        else
        {
            return false;
        }

        //credit card functionality slated for removal

        //var campaignCode = await loanApplicationService.GetMarketingCampaignCodeByLoanApplicationAsync(loanApplication);
        //if (campaignCode.IsCreditCard())
        //{
        //    var personalCampaignValues = await loanApplicationService.GetPersonalCampaignValuesByCodeAndMemberIdentifierAsync(
        //        loanApplication.MarketingCampaignCode, loanApplicationService.GetMemberIdentifier(loanApplication));
        //    dto.PeriodicRate = personalCampaignValues.PeriodicRate;
        //}

        var accountId = string.Empty;
        var accountDescription = string.Empty;

        var selectedAccountForTransactionId = loanApplication.SelectedAccountForTransactionId;
        if (!guidExtensionService.IsNullOrEmpty(selectedAccountForTransactionId))
        {
            var memberAccount = await qCashContext.MemberAccounts.Where(p => p.Id.Equals(selectedAccountForTransactionId)).OrderBy(s => s.DateOpened).FirstOrDefaultAsync();
            accountId = memberAccount?.AccountId ?? string.Empty;
            accountDescription = memberAccount?.Description ?? string.Empty;
        }

        dto.ShareId = $"{accountId}: {accountDescription}";
        dto.FundingAccountType = await memberInterfaceHelper.GetFundingAccountTypeAsync(loanApplication, languageId);
        dto.Margin = await loanApplicationService.GetMarginForLoanApplicationAsync(loanApplication);
        dto.AccountId = loanApplication.AccountId;
        dto.DNAAccountNumber = fundsAccount;
        dto.DateOfBirth = loanApplication.FinancialInstitutionMember.DateOfBirth;
        dto.TaxId = await taxIdService.GetTaxIdByFinancialInstitutionMemberIdAsync(loanApplication.FinancialInstitutionMemberId);

        if (product.DocStamp)
        {
            dto.DocStampFeeRate = product.DocStampFeeRate;
            dto.DocStampTax = loanApplicationHelper.GetTILAResponsesByLoanApplicationId(loanApplication.Id).First().DocStampTax;
            dto.DocStamp = product.DocStamp;
        }
        loanApplicationService.AddLogMessage(logs, "TILA Request finished: " + (DateTime.UtcNow - ptime).TotalSeconds + "s", DateTime.UtcNow);

        loanApplication.FinanceCharge = dto.FinanceCharge;
        loanApplication.TotalOfPayments = dto.TotalOfPayments;
        loanApplication.AmountFinanced = dto.AmountFinanced;
        loanApplication.MatureDateUtc = dto.MaturityDate.ToUTC(timeZone);

        //removed TilaTemplateProvider as it's only used here; methods moved into this service
        var file = await GetTilaFileTemplateForLoanApplicationAsync(loanApplication, languageCode);

        if (file == null)
        {
            const string tilaTemplateNotFound = "Error: TILA template not found";
            throw new FileNotFoundException(tilaTemplateNotFound);
        }

        // Populate TILA fields.
        file = PopulateTILAFields(dto, file);

        var loanStatusTypeLSTCMP = await qCashContext.LoanStatusTypes.SingleAsync(p => p.Abrv == "LSTCMP");
        var loanStatusTypeLSTFP = await qCashContext.LoanStatusTypes.SingleAsync(p => p.Abrv == "LSTFP");
        var loanStatusTypeLSTESP = await qCashContext.LoanStatusTypes.SingleAsync(p => p.Abrv == "LSTESP");
        var loanStatusTypeLSTQCFA = await qCashContext.LoanStatusTypes.SingleAsync(p => p.Abrv == "LSTQCFA");

        var completedId = loanStatusTypeLSTCMP.Id;
        var pendingId = loanStatusTypeLSTFP.Id;
        var esignaturePendingId = loanStatusTypeLSTESP.Id;
        var result = true;

        //alerts - IsTilaCalculationValid can only return false if the alert is enabled.
        //If Alerts functionality is removed, then the first path of the if clause below can be removed.
        var isTilaCalculationValid = true; //IsTilaCalculationValid(logs, loanApplication, dto);
        if (!isTilaCalculationValid)
        {
            loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLSTQCFA, LogSortOrder.QCFAudit);
            vr.NextStep = LoanApplicationStep.QCFAudit;
            loanApplication.NextStep = (int)LoanApplicationStep.QCFAudit;
            loanApplication.CurrentStep = loanApplication.NextStep;

            //alerts slated for removal
            //var fi = FinancialInstitutionService.FinancialInstitution;
            //var alertMessage = $"New QCash Alert has been triggered for {fi.Name}, application id: {loanApplication.AppId}. Please check Admin Portal logs for more details.";
            //_alertService.SendGeneralAlertEmailAlert(alertMessage);
            //_alertService.SendSmsAlerts(alertMessage);
        }
        else
        {
            if (loanApplication.LoanStatusId == completedId
                || loanApplication.LoanStatusId == pendingId
                || loanApplication.LoanStatusId == esignaturePendingId)
            {
                await fileSystemProvider.SetupAsync(ContainerTypeEnum.Private);
                result = await StoreDocumentAsync(logs, file, loanApplication, LoanApplicationDocumentType.TILA);
            }

            if (!(loanApplication.LoanStatusId == completedId
                  || loanApplication.LoanStatusId == pendingId
                  || loanApplication.LoanStatusId == esignaturePendingId) || isRegenerating)
            {
                await fileSystemProvider.SetupAsync(ContainerTypeEnum.DocumentsPrivate);
                result = await StoreDocumentAsync(logs, file, loanApplication, LoanApplicationDocumentType.TILA);
            }
        }

        await qCashContext.SaveChangesAsync();

        return result;
    }

    private async Task<TemplateGroup?> GetGroupByStateAbrvAsync(string? stateAbrv)
    {
        var groupId = await qCashContext.TemplateGroupRegions.Where(x => x.State.Abrv.ToLower() == stateAbrv).Select(x => x.TemplateGroupId).FirstOrDefaultAsync();
        var group = await qCashContext.TemplateGroups.Where(g => g.Id == groupId).FirstOrDefaultAsync();

        return group;
    }

    private async Task<byte[]?> GetTilaFileTemplateForLoanApplicationAsync(Data.Models.LoanApplication loanApplication, string languageCode)
    {
        await fileSystemProvider.SetupAsync(ContainerTypeEnum.TemplatesPrivate);

        var stateAbrv = loanApplication.FinancialInstitutionMember.State;

        var groupByAbrv = await GetGroupByStateAbrvAsync(stateAbrv);

        var activeGroupExists = groupByAbrv?.IsActive ?? false;
        var groupName = groupByAbrv?.Name;

        var isMilitary = loanApplication.ActiveDuty.GetValueOrDefault();
        var productSlug = loanApplication.SelectedProduct?.Slug ?? string.Empty;

        var productBySlug = await qCashContext.Products.SingleOrDefaultAsync(p => p.Slug == productSlug);

        var useStateTilaTemplates = productBySlug?.UseStateTilaTemplates;

        const TilaDocumentTemplateType defaultType = TilaDocumentTemplateType.Default;
        var desiredType = isMilitary ? TilaDocumentTemplateType.MLA : defaultType;
        byte[]? file;

        if (activeGroupExists && useStateTilaTemplates != null && useStateTilaTemplates.Value)
        {
            file = await GetTilaFileByTemplateTypeAsync(desiredType, defaultType, productSlug, languageCode, groupName);
        }
        else
        {
            file = await GetTilaFileByTemplateTypeAsync(desiredType, defaultType, productSlug, languageCode, string.Empty);
            if (file == null && isMilitary)
            {
                file = await GetTilaFileByTemplateTypeAsync(
                    TilaDocumentTemplateType.MLA,
                    TilaDocumentTemplateType.Default,
                    productSlug,
                    languageCode,
                    string.Empty);
            }

            file ??= await GetTilaFileByTemplateTypeAsync(
                TilaDocumentTemplateType.Default,
                TilaDocumentTemplateType.Default,
                productSlug,
                languageCode,
                string.Empty);
        }

        return file;
    }

    private async Task<byte[]?> GetTilaFileByTemplateTypeAsync(
        TilaDocumentTemplateType desiredTemplate,
        TilaDocumentTemplateType defaultTemplate,
        string productSlug,
        string languageCode,
        string? groupName)
    {
        // Local func:
        // Try get with translation.
        // If there is no desired template with translation, try get without translation.
        async Task<byte[]?> GetTilaFileWithLanguageAsync(TilaDocumentTemplateType tilaDocumentTemplateType)
        {
            var lFile = await GetTilaFileAsync(tilaDocumentTemplateType, languageCode, productSlug, groupName);
            if (lFile == null && !string.IsNullOrWhiteSpace(languageCode))
            {
                lFile = await GetTilaFileAsync(tilaDocumentTemplateType, string.Empty, productSlug, groupName);
            }

            return lFile;
        }

        // Try get desired document.
        var file = await GetTilaFileWithLanguageAsync(desiredTemplate) ?? await GetTilaFileWithLanguageAsync(defaultTemplate);

        return file;
    }

    private async Task<byte[]?> GetTilaFileAsync(TilaDocumentTemplateType tilaDocumentTemplateType, string language,
        string productSlug, string? groupName)
        => await fileSystemProvider.GetFileAsync(GetTilaDocumentTemplateName(tilaDocumentTemplateType,
            productSlug, language, groupName));

    /// <summary>
    /// Gets the name of the tila document template.
    /// </summary>
    /// <param name="tilaDocumentTemplateType">Type of the tila document template.</param>
    /// <param name="productSlug">The product slug.</param>
    /// <param name="langCode">The language code.</param>
    /// <returns>The name of the tila document template.</returns>
    /// <exception cref="System.Exception">Product Slug must be defined for TILA templates.</exception>
    public string GetTilaDocumentTemplateName(TilaDocumentTemplateType tilaDocumentTemplateType, string productSlug, string languageCode, string? groupName)
    {
        var template = new StringBuilder();
        switch (tilaDocumentTemplateType)
        {
            case TilaDocumentTemplateType.MLA:
                template.Append("TILA_MLA");
                break;

            case TilaDocumentTemplateType.Default:
            default:
                template.Append("TILA");
                break;
        }
        if (!string.IsNullOrEmpty(groupName))
        {
            template.Append($"_{groupName}");
        }

        if (string.IsNullOrEmpty(productSlug))
        {
            throw new Exception("Product Slug must be defined for TILA templates.");
        }
        else
        {
            template.Append("_").Append(productSlug);
        }

        if (string.IsNullOrEmpty(languageCode))
        {
            template.Append("_template.pdf");
        }
        else
        {
            template.Append("_template_").Append(languageCode).Append(".pdf");
        }

        return template.ToString();
    }

    /// <summary>
    /// Gets TilaReportDto by appId.
    /// </summary>
    /// <param name="appId">LoanApplication AppId.</param>
    /// <returns>TilaReportDto entity.</returns>
    private async Task<TilaReportDto> GetFeeBasedTILAAsync(int appId)
    {
        var loanApplication = await GetLoanApplicationWithChildsAsync(appId);

        if (loanApplication == null || loanApplication.LoanApplicationMemberSelections == null || loanApplication.LoanApplicationMemberSelections.Count == 0)
        {
            throw new Exception("Additional application questions not set for this Loan Application");
        }

        var paymentOption = await paymentOptionService.GetProductPaymentOptionByTypeAsync(
            loanApplication.SelectedProductId,
            loanApplication.LoanApplicationMemberSelections.First().PaymentOptionTypeId);

        var dto = await CreateTILAReportDtoAsync(loanApplication);
        dto.FooterInfo = $"{FiSlug} | q-cash_tila_{DateTime.UtcNow:yyyy-MM-dd}";
        dto.TermLength = paymentOption?.OneTime?.FirstPaymentDateThreshold ?? 0;

        var memberBaseAccount = loanApplicationService.GetMemberBaseAccount(loanApplication.MemberBaseAccountId.GetValueOrDefault());
        if (memberBaseAccount == null)
        {
            throw new Exception("MemberBaseAccount is null");
        }

        var tilaRequest = await CreateTILARequestAsync(loanApplication, memberBaseAccount.AccountId);

        var corrId = guidExtensionService.NewSequentialGuid();
        var trForLog = JsonConvert.DeserializeObject<TILARequest>(JsonConvert.SerializeObject(tilaRequest));

        if (trForLog != null)
        {
            trForLog.BaseAccountId = loanApplicationService.GetMaskedNumber(trForLog.BaseAccountId);
            await loanApplicationService.SaveLogMessageAsync(loanApplication,
                $"TILA Request executing {corrId.ToString()} - {JsonConvert.SerializeObject(trForLog, LogsJsonSettings)}",
                DateTime.UtcNow, LogType.AdminRequired);
        }

        var tilaResponse = await TILARequestCallAsync(tilaRequest, loanApplication, corrId);

        var clientTimeZone = (await qCashContext.Settings.FirstAsync()).TimeZone;
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);

        dto.FinanceCharge = tilaResponse.FinanceCharge;
        dto.TotalOfPayments = tilaResponse.TotalOfPayments;
        dto.AdjustedAPR = tilaResponse.AdjustedAPR;
        dto.AmountFinanced = tilaResponse.AmountFinanced;
        dto.TableData = new List<TilaTableDto>();
        dto.FirstPaymentDate = tilaRequest.FirstPaymentDate;
        dto.SecondPaymentDate = tilaResponse.SecondPaymentDate;
        dto.MaturityDate = tilaResponse.FinalPaymentDate;

        dto.TableData.Add(new TilaTableDto
        {
            NumberOfPayments = "1",
            AmountOfPayments = tilaResponse.RegularPaymentAmount.ToString("0.00"),
            PaymentDueDate = loanApplication.LoanApplicationMemberSelections.First().FirstPaymentDateUtc.GetValueOrDefault().FromUTC(timeZone).ToShortDateString(),
        });

        return dto;
    }

    private async Task<Data.Models.LoanApplication?> GetLoanApplicationWithChildsAsync(int appId) =>
        await qCashContext.LoanApplications
            .Where(s => s.AppId == appId)
            .Include(s => s.LoanStatus)
            .Include(s => s.FinancialInstitutionMember)
            .Include(s => s.LoanApplicationMemberSelections)
            .Include(s => s.SelectedProduct)
            .ThenInclude(s => s!.LoanCategory)
            .Include(s => s.LoanApplicationCampaignCodes)
            .Include(s => s.LoanApplicationSso)
            .Include(s => s.MemberConsents)
            .FirstOrDefaultAsync();

    private static TILAResponse ReturnTILA(TILARequest request)
    {
        var req = JsonConvert.DeserializeObject<TILARequest>(JsonConvert.SerializeObject(request))!;

        if (req.LoanType == LoanType.InterestBased)
        {
            // Just return empty object if it's an LOC product
            if (req.IsOpenEndedLoan)
            {
                return new TILAResponse
                {
                    FinalPaymentAmount = req.AmountBorrowed,
                    InterestRate = req.InterestRate,
                    RegularPaymentAmount = req.AmountBorrowed,
                    FinanceCharge = 0,
                    TotalOfPayments = req.AmountBorrowed,
                    AmountFinanced = req.AmountBorrowed,
                    AdjustedAPR = req.InterestRate,
                    FinalPaymentDate = req.FirstPaymentDate,
                };
            }

            var aprCalculator = new CunaAprCalc();
            return aprCalculator.ReturnTila(req);
        }

        var fee = req.AmountBorrowed / req.PerBorrowed * req.LoanFee;
        var response = new TILAResponse
        {
            AmountFinanced = req.AmountBorrowed,
            FinalPaymentAmount = req.AmountBorrowed + fee,
            RegularPaymentAmount = req.AmountBorrowed + fee,
            FinanceCharge = fee,
            TotalOfPayments = req.AmountBorrowed + fee,
            InterestRate = 0.0m,
            NumberOfPayments = 1,
            FinalPaymentDate = req.FirstPaymentDate,
        };
        return response;
    }

    private async Task<TILAResponse> TILARequestCallAsync(TILARequest tilaRequest, Data.Models.LoanApplication la, Guid correlationId)
    {
        await SaveTilaRequestAsync(la.Id, correlationId, tilaRequest);

        var tilaResponse = ReturnTILA(tilaRequest);

        await loanApplicationService.SaveLogMessageAsync(
            la,
            $"TILA Response {correlationId.ToString()} - {JsonConvert.SerializeObject(tilaResponse, LogsJsonSettings)}",
            DateTime.UtcNow,
            LogType.AdminRequired);

        await SaveTilaResponseAsync(la.Id, tilaResponse, tilaRequest.IsOpenEndedLoan);
        return tilaResponse;
    }


    private async Task SaveTilaResponseAsync(Guid loanApplicationId, TILAResponse tilaResponse, bool isOpenEnded)
    {
        var tilaDb = loanApplicationHelper.GetTILAResponsesByLoanApplicationId(loanApplicationId).FirstOrDefault();
        var isNew = false;
        if (tilaDb == null)
        {
            tilaDb = new TilaResponse
            {
                Id = guidExtensionService.NewSequentialGuid(),
                LoanApplicationId = loanApplicationId,
                DateCreatedUtc = DateTime.UtcNow,
                TimeStamp = [],
            };
            isNew = true;
        }

        //map from TILAResponse (model) to TilaResponse (db)

        tilaDb.InterestRate = tilaResponse.InterestRate;
        tilaDb.DateUpdatedUtc = DateTime.UtcNow;
        tilaDb.AdjustedApr = tilaResponse.AdjustedAPR;
        tilaDb.AmountFinanced = tilaResponse.AmountFinanced;
        tilaDb.NumberOfPayments = tilaResponse.NumberOfPayments;
        tilaDb.SecondPaymentDate = tilaResponse.SecondPaymentDate;
        tilaDb.FinalPaymentDate = tilaResponse.FinalPaymentDate;
        tilaDb.DocStampTax = tilaResponse.DocStampTax;

        tilaDb.RegularPaymentAmount = Math.Round(tilaResponse.RegularPaymentAmount, 2);
        tilaDb.FinalPaymentAmount = Math.Round(tilaResponse.FinalPaymentAmount, 2);
        tilaDb.FinanceCharge = Math.Round(tilaResponse.FinanceCharge, 2);
        tilaDb.TotalOfPayments = Math.Round(tilaResponse.TotalOfPayments, 2);

        if (isOpenEnded)
        {
            tilaDb.FinalPaymentDate = null;
        }

        if (isNew)
        {
            qCashContext.TilaResponses.Add(tilaDb);
        }

        await qCashContext.SaveChangesAsync();
    }

    private async Task SaveTilaRequestAsync(Guid loanApplicationId, Guid correlationId, TILARequest coreTilaRequest)
    {
        var tilaRequest = await qCashContext.TilaRequests.Where(o => o.LoanApplicationId.Equals(loanApplicationId)).OrderBy(p => p.Id)
            .FirstOrDefaultAsync();
        var isNew = false;
        if (tilaRequest == null)
        {
            tilaRequest = new TilaRequest
            {
                Id = guidExtensionService.NewSequentialGuid(),
                LoanApplicationId = loanApplicationId,
                DateCreatedUtc = DateTime.UtcNow,
                TimeStamp = [],
            };
            isNew = true;
        }

        //map from TILARequest (model) to TilaRequest (db)
        tilaRequest.BaseAccountId = coreTilaRequest.BaseAccountId ?? string.Empty;
        tilaRequest.Abrv = coreTilaRequest.Abrv ?? string.Empty;
        tilaRequest.LoanFee = coreTilaRequest.LoanFee;
        tilaRequest.PerBorrowed = coreTilaRequest.PerBorrowed;
        tilaRequest.AmountBorrowed = coreTilaRequest.AmountBorrowed;
        tilaRequest.InterestRate = coreTilaRequest.InterestRate;
        tilaRequest.LoanOriginationFee = coreTilaRequest.LoanOriginationFee;
        tilaRequest.FirstPaymentDayOfMonth = coreTilaRequest.FirstPaymentDayOfMonth;
        tilaRequest.LoanTermInMonths = coreTilaRequest.LoanTermInMonths;
        tilaRequest.IsOpenEndedLoan = coreTilaRequest.IsOpenEndedLoan;
        tilaRequest.LoanOpenDate = coreTilaRequest.LoanOpenDate;
        tilaRequest.PaymentFrequency = coreTilaRequest.PaymentFrequency.ToString();
        tilaRequest.PaymentAcceleration = coreTilaRequest.PaymentAcceleration.ToString();
        tilaRequest.DocStampFeeRate = coreTilaRequest.DocStampFeeRate;

        tilaRequest.DateUpdatedUtc = DateTime.UtcNow;

        tilaRequest.DateOfBirth = coreTilaRequest.DateOfBirth.ToNullableDateTime();
        tilaRequest.FirstPaymentDate = coreTilaRequest.FirstPaymentDate.ToNullableDateTime();
        tilaRequest.RoundToIndicator = coreTilaRequest.RoundToIndicator.ToString();
        tilaRequest.AmortizationMethod = coreTilaRequest.AmortizationMethod.ToString();
        tilaRequest.CorrelationId = correlationId;

        if (isNew)
        {
            qCashContext.TilaRequests.Add(tilaRequest);
        }
        await qCashContext.SaveChangesAsync();
    }

    /// <summary>
    /// Gets TilaReportDto by appId.
    /// </summary>
    /// <param name="appId">LoanApplication AppId.</param>
    /// <returns>TilaReportDto entity.</returns>
    private async Task<TilaReportDto> GetInterestBasedTILAAsync(int appId)
    {
        var loanApplication = await GetLoanApplicationWithChildsAsync(appId);

        if (loanApplication == null)
        {
            throw new Exception("LoanApplication is null");
        }

        var dto = await CreateTILAReportDtoAsync(loanApplication);
        dto.FooterInfo = $"{FiSlug} | q-cash_plus_tila_{DateTime.UtcNow:yyyy-MM-dd}";

        var memberBaseAccount = loanApplicationService.GetMemberBaseAccount(loanApplication.MemberBaseAccountId.GetValueOrDefault());
        if (memberBaseAccount == null)
        {
            throw new Exception("MemberBaseAccount is null");
        }

        var tilaRequest = await CreateTILARequestAsync(loanApplication, memberBaseAccount.AccountId);

        dto.PaymentFrequency = Enum.IsDefined(typeof(PaymentAcceleration), tilaRequest.PaymentAcceleration) &&
                               tilaRequest.PaymentAcceleration != PaymentAcceleration.None ? tilaRequest.PaymentAcceleration.ToString() : tilaRequest.PaymentFrequency.ToString();

        var corrId = guidExtensionService.NewSequentialGuid();
        var trForLog = JsonConvert.DeserializeObject<TILARequest>(JsonConvert.SerializeObject(tilaRequest));
        trForLog!.BaseAccountId = loanApplicationService.GetMaskedNumber(trForLog.BaseAccountId);

        if (!tilaRequest.IsOpenEndedLoan)
        {
            await loanApplicationService.SaveLogMessageAsync(loanApplication,
                $"TILA Request executing {corrId.ToString()} - {JsonConvert.SerializeObject(trForLog, LogsJsonSettings)}", DateTime.UtcNow, LogType.AdminRequired);
        }

        var tilaResponse = await TILARequestCallAsync(tilaRequest, loanApplication, corrId);

        dto.FinanceCharge = tilaResponse.FinanceCharge;
        dto.TotalOfPayments = tilaResponse.TotalOfPayments;
        dto.AmountFinanced = tilaResponse.AmountFinanced;
        dto.MaturityDate = tilaResponse.FinalPaymentDate;

        var memberConsentAutomaticPayment = loanApplication.MemberConsentAutomaticPayment();
        if (memberConsentAutomaticPayment != null && !memberConsentAutomaticPayment.Accepted && loanApplication.SelectedProduct != null)
        {
            dto.AutoPayRate = loanApplication.SelectedProduct.AutoPayRate;
        }

        dto.InterestAmount = CalculateInterestAmount(tilaResponse.FinanceCharge, tilaRequest.LoanOriginationFee);
        dto.PrincipleAndInterest = CalculatePrincipleAndInterest(tilaResponse.AmountFinanced, dto.InterestAmount);

        if (tilaRequest.IsOpenEndedLoan)
        {
            return dto;
        }

        if (loanApplication.LoanApplicationMemberSelections == null || loanApplication.LoanApplicationMemberSelections.Count == 0)
        {
            throw new Exception("Loan Application Member Selection not set for this Loan Application");
        }

        #region TILA fields

        dto.AdjustedAPR = tilaResponse.AdjustedAPR;
        dto.FirstPaymentDate = tilaRequest.FirstPaymentDate;
        dto.SecondPaymentDate = tilaResponse.SecondPaymentDate;

        var laMemberSelection = loanApplication.LoanApplicationMemberSelections.First();
        dto.TableData = new List<TilaTableDto>();

        var language = loanApplicationService.GetLanguage(loanApplication.SelectedLanguageId);
        await FormatTILADocumentAsync(laMemberSelection, dto, tilaResponse, tilaRequest.FirstPaymentDate, language.LanguageCode, language.Id);

        #endregion TILA fields

        return dto;
    }

    public async Task<TILARequest> CreateTILARequestAsync(Data.Models.LoanApplication loanApplication, string baseAccountId)
    {
        if (loanApplication == null)
        {
            throw new Exception("Loan Application cannot be null!");
        }

        var laMemberSelection = loanApplication.LoanApplicationMemberSelections.FirstOrDefault();
        if (laMemberSelection == null)
        {
            throw new Exception("LoanApplicationMemberSelection is null!");
        }

        var selectedProduct = await qCashContext.Products.Include(o => o.LoanCategory).FirstOrDefaultAsync(o => o.Id == loanApplication.SelectedProductId.GetValueOrDefault());
        if (selectedProduct == null)
        {
            throw new Exception("Selected product cannot be null!");
        }

        var result = await CreateTILARequestWithSelectionInfoAsync(loanApplication, selectedProduct, laMemberSelection, baseAccountId);

        return result;
    }

    private async Task<TILARequest> CreateTILARequestWithSelectionInfoAsync(Data.Models.LoanApplication loanApplication, Data.Models.Product selectedProduct, LoanApplicationMemberSelection laMemberSelection, string baseAccountId)
    {
        //var lkpLoanType = _lookupFactory.LoanTypelookupCreate();
        var settings = await qCashContext.Settings.FirstAsync();
        var calcEngineSetting = await qCashContext.CalcEngineSettings
            .Where(o => o.FinancialInstitutionId == settings.FinancialInstitutionId).OrderBy(p => p.Id).FirstAsync();

        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(settings.TimeZone);

        var loanTypeFB = await qCashContext.LoanTypes.FirstOrDefaultAsync(o => o.Abrv == nameof(LoanTypeAbbreviation.FB));
        var isFeeBasedProduct = selectedProduct.LoanTypeId == loanTypeFB?.Id;
        var isOpenEndedProduct = await loanApplicationService.IsProductOpenEndedAsync(selectedProduct);
        var amortMethod = (CalcEngineAmortMethod)Enum.Parse(typeof(CalcEngineAmortMethod), calcEngineSetting.AmortMethod);
        var roundToIndicator = (CalcEngineRoundToIndicator)Enum.Parse(typeof(CalcEngineRoundToIndicator), calcEngineSetting.RoundToFactor);

        var tilaRequest = new TILARequest
        {
            AppId = $"{FiSlug}.{loanApplication.AppId.ToString()}",
            Abrv = selectedProduct.Abrv,
            IsOpenEndedLoan = isOpenEndedProduct,
            AmountBorrowed = loanApplication.AmountBorrowed,
            BaseAccountId = baseAccountId,
            InterestRate = await loanApplicationService.CalculateFinalInterestRateAsync(loanApplication),
            DateOfBirth = loanApplication.FinancialInstitutionMember.DateOfBirth ?? DateTime.MinValue,
            LoanOriginationFee = loanApplicationHelper.CalculateLoanOriginationFee(loanApplication),
            AmortizationMethod = MapWebRoleAmortMethodToCore(amortMethod),
            RoundToIndicator = MapWebRoleRoundToIndicatorToCore(roundToIndicator),
            LoanOpenDate = loanApplication.DateCreatedUtc.FromUTC(timeZone),
            ExtraInfo = GetExtraInfoForReturnTILA(loanApplication),
            LoanType = isFeeBasedProduct ? LoanType.FeeBased : LoanType.InterestBased,
            DocStampFeeRate = selectedProduct.DocStamp ? selectedProduct.DocStampFeeRate ?? 0 : 0,
            ApplicationId = loanApplication.AppId,
            ClientTimeZone = settings.TimeZone,
            FirstPaymentDate = loanApplication.DateCreatedUtc.FromUTC(timeZone),
        };

        var paymentOptionTypeOT = await qCashContext.PaymentOptionTypes.SingleAsync(o => o.Abrv == "OT");
        var paymentOptionTypeBW = await qCashContext.PaymentOptionTypes.SingleAsync(o => o.Abrv == "BW");
        var paymentOptionTypeNone = await qCashContext.PaymentOptionTypes.SingleAsync(o => o.Abrv == "NONE");


        // One-time payment option.
        if (laMemberSelection.PaymentOptionTypeId == paymentOptionTypeOT.Id)
        {
            var laCampaignCode = loanApplication.LoanApplicationCampaignCodes.FirstOrDefault();
            tilaRequest.LoanFee = laCampaignCode?.LoanAmount ?? selectedProduct.LoanAmount;
            tilaRequest.PerBorrowed = laCampaignCode?.LoanPerBorrowed ?? selectedProduct.LoanPerBorrowed;
            tilaRequest.FirstPaymentDate = laMemberSelection.FirstPaymentDateUtc.GetValueOrDefault().FromUTC(timeZone);
        }

        // Bi-Weekly payment option.
        else if (laMemberSelection.PaymentOptionTypeId == paymentOptionTypeBW.Id)
        {
            tilaRequest.LoanTermInMonths = laMemberSelection.LoanTermInMonths.GetValueOrDefault();
            tilaRequest.FirstPaymentDate = laMemberSelection.FirstPaymentDateUtc.GetValueOrDefault().FromUTC(timeZone);
            tilaRequest.PaymentFrequency = PaymentFrequency.Monthly;
            tilaRequest.PaymentAcceleration = PaymentAcceleration.BiWeekly;
        }

        // Other non-opend ended payment options.
        else if (laMemberSelection.PaymentOptionTypeId != paymentOptionTypeNone.Id)
        {
            tilaRequest.LoanTermInMonths = laMemberSelection.LoanTermInMonths.GetValueOrDefault();
            tilaRequest.FirstPaymentDayOfMonth = (short)laMemberSelection.SelectedPaymentDay.GetValueOrDefault();
            tilaRequest.FirstPaymentDate = laMemberSelection.FirstPaymentDateUtc.GetValueOrDefault().FromUTC(timeZone);
            tilaRequest.PaymentFrequency = PaymentFrequency.Monthly;
        }

        return tilaRequest;
    }
    private static AmortizationMethod MapWebRoleAmortMethodToCore(CalcEngineAmortMethod amortMethod) => amortMethod switch
    {
        CalcEngineAmortMethod.UNITPERIOD => AmortizationMethod.UnitPeriod,
        CalcEngineAmortMethod.ACTUALDAY => AmortizationMethod.ActualDay,
        _ => AmortizationMethod.ActualDay_365,
    };

    private static RoundToIndicator MapWebRoleRoundToIndicatorToCore(CalcEngineRoundToIndicator roundToIndicator)
    {
        switch (roundToIndicator)
        {
            case CalcEngineRoundToIndicator.Up:
                return RoundToIndicator.RoundUp;
            case CalcEngineRoundToIndicator.Down:
                return RoundToIndicator.RoundDown;
            case CalcEngineRoundToIndicator.Nearest:
            default:
                return RoundToIndicator.ToNearest;
        }
    }
    private ExtraInfo GetExtraInfoForReturnTILA(Data.Models.LoanApplication loanApplication)
    {
        var extraInfo = loanApplicationService.GetBaseExtraInfo(loanApplication);
        extraInfo.Infoes.Add("UseCUNA", "true");

        return extraInfo;
    }

    private static decimal CalculateInterestAmount(decimal financeCharge, decimal loanOriginationFee)
    {
        var interestAmount = financeCharge - loanOriginationFee;
        return interestAmount;
    }

    private static decimal CalculatePrincipleAndInterest(decimal amountFinanced, decimal interestAmount)
    {
        var principleAndInterest = amountFinanced + interestAmount;
        return principleAndInterest;
    }

    private async Task<TilaReportDto> CreateTILAReportDtoAsync(Data.Models.LoanApplication loanApplication)
    {
        var clientTimeZone = (await qCashContext.Settings.FirstOrDefaultAsync())?.TimeZone ?? string.Empty;
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);

        var fiMember = loanApplication.FinancialInstitutionMember;
        await loanApplicationHelper.GetLoanApplicationResultAllProductsAsync(loanApplication.Id);

        var dto = new TilaReportDto
        {
            ApplicationDate = loanApplication.DateCreatedUtc.FromUTC(timeZone),
            AppId = loanApplication.AppId,
            FirstName = fiMember.FirstName,
            LastName = string.IsNullOrWhiteSpace(fiMember.Suffix) ? fiMember.LastName : $"{fiMember.LastName} {fiMember.Suffix}",
            MiddleName = fiMember.MiddleName ?? string.Empty,
            MailingAddress = fiMember.MailingAddress,
            City = fiMember.City,
            State = fiMember.State,
            Zip = fiMember.Zip,
            LoanDate = loanApplication.DateCreatedUtc.FromUTC(timeZone),
            APR = await loanApplicationService.CalculateFinalInterestRateAsync(loanApplication),
            AmountBorrowed = loanApplication.AmountBorrowed,
        };

        if (!await loanApplicationService.IsProductOpenEndedAsync(loanApplication.SelectedProduct))    //should be async
        {
            dto.MaturityDate = loanApplication.MatureDateUtc.FromUTC(timeZone);
        }

        var existingApplicationFeeItem = await qCashContext
            .LoanApplicationFees
            .AsNoTracking()
            .Where(laf => laf.LoanApplicationId == loanApplication.Id
                          && laf.FeeType == nameof(LoanApplicationFeeType.ApplicationFee))
            .OrderByDescending(p => p.DateCreatedUtc)
            .FirstOrDefaultAsync();

        dto.ApplicationFee = existingApplicationFeeItem?.FeeAmount ?? 0;
        dto.LoanOriginationFee = loanApplicationHelper.CalculateLoanOriginationFee(loanApplication);

        return dto;
    }

    private async Task FormatTILADocumentAsync(LoanApplicationMemberSelection loanApplicationMemberSelection, TilaReportDto dto, TILAResponse tilaResponse, DateTime firstPaymentDate, string languageCode, Guid languageId)
    {
        var selectedPaymentDay = loanApplicationMemberSelection.SelectedPaymentDay.GetValueOrDefault(1);

        var paymentsTilaTable = new TilaTableDto
        {
            NumberOfPayments = tilaResponse.NumberOfPayments == 1 ? "1" : (tilaResponse.NumberOfPayments - 1).ToString(),
            AmountOfPayments = tilaResponse.RegularPaymentAmount.ToString("0.00"),
        };

        var paymentOptionTypeBw = await qCashContext.PaymentOptionTypes.SingleAsync(o => o.Abrv == "BW");

        // ONE-TIME
        if (tilaResponse.NumberOfPayments == 1)
        {
            paymentsTilaTable.PaymentDueDate = HtmlHelpers.StripTags(tilaResponse.FinalPaymentDate.ToShortDateString());
            dto.TableData.Add(paymentsTilaTable);
            return;
        }
        var abrv = (loanApplicationMemberSelection.PaymentOptionTypeId == paymentOptionTypeBw.Id)
            ? InterfaceDefaultTextsEnum.TILADuePaymentsBiWeek.Abrv
            : InterfaceDefaultTextsEnum.TILADuePayments.Abrv;
        var fieldValue = await memberInterfaceHelper.GetTextAsync(abrv, false, languageId);

        // BI-WEEKLY
        if (loanApplicationMemberSelection.PaymentOptionTypeId == paymentOptionTypeBw.Id)
        {
            paymentsTilaTable.PaymentDueDate = string.Empty;
            if (!string.IsNullOrEmpty(fieldValue))
            {
                var dayString = memberProcessInterfaceService.GetFormattedDayOfWeekByDate(firstPaymentDate, languageCode);
                paymentsTilaTable.PaymentDueDate = HtmlHelpers.StripTags(Regex.Replace(fieldValue, "{Bi-WeekDay}", dayString, RegexOptions.IgnoreCase, TimeSpan.FromSeconds(1)));
            }

            dto.TableData.Add(paymentsTilaTable);
        }
        else     // OTHER
        {
            paymentsTilaTable.PaymentDueDate = string.Empty;
            if (!string.IsNullOrEmpty(fieldValue))
            {
                var paymentDayDescription = await GetTranslatedPaymentDayAsync(selectedPaymentDay, languageCode);
                paymentsTilaTable.PaymentDueDate = HtmlHelpers.StripTags(Regex.Replace(fieldValue, "{PaymentDay}", (paymentDayDescription != null) ? paymentDayDescription : selectedPaymentDay.ToString(), RegexOptions.IgnoreCase, TimeSpan.FromSeconds(1)));
            }
            dto.TableData.Add(paymentsTilaTable);
        }

        var finalPaymentTilaTable = new TilaTableDto
        {
            NumberOfPayments = "1",
            AmountOfPayments = tilaResponse.FinalPaymentAmount.ToString("0.00"),
            PaymentDueDate = tilaResponse.FinalPaymentDate.ToShortDateString(),
        };
        dto.TableData.Add(finalPaymentTilaTable);
    }

    public async Task<string?> GetTranslatedPaymentDayAsync(int selectedPaymentDay, string languageCode)
    {
        var paymentDate = await qCashContext.PaymentDates.Include(o => o.TranslationPaymentDates).FirstOrDefaultAsync(p => p.Value == selectedPaymentDay);
        if (paymentDate != null)
        {
            var translation = paymentDate.TranslationPaymentDates.FirstOrDefault(p => p.LanguageCode == languageCode);
            if (translation != null && translation.Description != null)
            {
                return translation.Description;
            }

            return paymentDate.Description;
        }

        return null;
    }

    /// <summary>
    /// Stores EConsent document.
    /// </summary>
    /// <param name="logs">LoanApplicationLogDetails.</param>
    /// <param name="loan">LoanApplication with childs.</param>
    /// <param name="isAccepted">Is EConsent accepted?</param>
    /// <returns>Document stored?</returns>
    private async Task<bool> StoreEConsentDocumentAsync(IList<LoanApplicationLogDetail> logs, Data.Models.LoanApplication loan, bool isAccepted = false)
    {
        await fileSystemProvider.SetupAsync(ContainerTypeEnum.TemplatesPrivate);
        var language = loanApplicationService.GetLanguage(loan.SelectedLanguageId);
        var file = await fileSystemProvider.GetFileAsync(
            loanApplicationService.GetTemplateName(LoanApplicationDocumentType.EConsentDisclosure, language.LanguageCode));
        var fileName = await filenameTemplateService.GetDocumentNameAsync(LoanApplicationDocumentType.EConsentDisclosure, loan);

        var clientTimeZone = qCashContext.Settings.FirstOrDefault()?.TimeZone ?? string.Empty;
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);
        var footerInfo = $"q-cash_e_consent_disclosure_{DateTime.UtcNow.FromUTC(timeZone):yyyy-MM-dd}";

        file ??= await fileSystemProvider.GetFileAsync(
            loanApplicationService.GetTemplateName(LoanApplicationDocumentType.EConsentDisclosure,
                string.Empty));
        file = PopulateEConsentFields(footerInfo, fileName, file);

        file ??= [];

        if (!isAccepted)
        {
            //Store document with copy
            await fileSystemProvider.SetupAsync(ContainerTypeEnum.DocumentsPrivate);

            //inserts/updates a Document record and might add/modify the link in LoanApplication
            var result = await StoreDocumentAsync(logs, file, loan, LoanApplicationDocumentType.EConsentDisclosure);
            //StoreDocument calls SaveChanges so there is nothing else to do here

            return result;
        }

        loan = await GetLoanApplicationForWizardAsync(loan.FinancialInstitutionMemberId);
        await fileSystemProvider.SetupAsync(ContainerTypeEnum.Private);
        return await StoreDocumentAsync(logs, file, loan, LoanApplicationDocumentType.EConsentDisclosure);
    }

    public byte[] PopulateTILAFields(TilaReportDto dto, byte[] file)
    {
        return file;

        //TODO - existing PDF package is old; determine how this will be done in QC7

        //using var stream = new MemoryStream(100);
        //var pdfReader = new PdfReader(file);
        //var pdfStamper = new PdfStamper(pdfReader, stream);
        //var pdfFormFields = pdfStamper.AcroFields;
        //pdfReader.RemoveUsageRights();
        //pdfFormFields.SetField("AccountNumber", FormattingHelpers.GetMaskedNumber(dto.AccountId));
        //pdfFormFields.SetField("DNAAccountNumber", FormattingHelpers.GetMaskedNumber(dto.DNAAccountNumber));
        //pdfFormFields.SetField("ApplicationDate1", dto.ApplicationDateShort);
        //pdfFormFields.SetField("ApplicationDate2", dto.ApplicationDateShort);
        //pdfFormFields.SetField("FirstPaymentDate", dto.FirstPaymentDateShort);
        //pdfFormFields.SetField("SecondPaymentDate", dto.SecondPaymentDateShort);
        //pdfFormFields.SetField("DateOfBirth", dto.DateOfBirth.HasValue ? dto.DateOfBirth.GetValueOrDefault().ToShortDateString() : string.Empty);
        //pdfFormFields.SetField("AppID", dto.AppId.ToString());
        //pdfFormFields.SetField("FullName", dto.FullName);
        //pdfFormFields.SetField("FirstName", dto.FirstName);
        //pdfFormFields.SetField("LastName", dto.LastName);
        //pdfFormFields.SetField("MiddleName", dto.MiddleName);
        //pdfFormFields.SetField("MailingAddress", dto.MailingAddress);
        //pdfFormFields.SetField("Zip", dto.Zip);
        //pdfFormFields.SetField("State", dto.State);
        //pdfFormFields.SetField("City", dto.City);
        //pdfFormFields.SetField("FullAddress", dto.FullAddress);
        //pdfFormFields.SetField("LoanDate", dto.LoanDateShort);
        //pdfFormFields.SetField("MatureDate", dto.MaturityDateShort);
        //pdfFormFields.SetField("SSN", FormattingHelpers.GetMaskedNumber(dto.TaxId));
        //pdfFormFields.SetField("APR", dto.APR.ToString("0.00"));
        //pdfFormFields.SetField("AdjustedAPR", dto.AdjustedAPR.ToString("0.00"));
        //pdfFormFields.SetField("FinanceCharge", dto.FinanceCharge.ToString("0.00"));
        //pdfFormFields.SetField("AmountFinanced1", dto.AmountFinanced.ToString("0.00"));
        //pdfFormFields.SetField("LoanAmount", dto.AmountBorrowed.ToString("0.00"));
        //pdfFormFields.SetField("TotalPayments", dto.TotalOfPayments.ToString("0.00"));
        //pdfFormFields.SetField("Margin", dto.Margin.HasValue ? dto.Margin.Value.ToString("0.00") : string.Empty);
        //pdfFormFields.SetField("PeriodicRate", dto.PeriodicRate.HasValue ? dto.PeriodicRate.Value.ToString("0.000000") : string.Empty);
        //pdfFormFields.SetField("PaymentFrequency", dto.PaymentFrequency);
        //pdfFormFields.SetField("InterestAmount", dto.InterestAmount.ToString("0.00"));
        //pdfFormFields.SetField("PrincipleAndInterest", dto.PrincipleAndInterest.ToString("0.00"));
        //pdfFormFields.SetField("ShareID", dto.ShareId);
        //pdfFormFields.SetField("FundingAccountType", dto.FundingAccountType);

        //if (dto.DocStamp)
        //{
        //    pdfFormFields.SetField("DocStampRate", dto.DocStampFeeRate.HasValue ? dto.DocStampFeeRate.Value.ToString("0.00") : string.Empty);
        //    pdfFormFields.SetField("DocStampTax", dto.DocStampTax.HasValue ? dto.DocStampTax.Value.ToString("0.00") : string.Empty);
        //}
        //if (dto.AutoPayRate.HasValue)
        //    pdfFormFields.SetField("AutoPayRate", dto.AutoPayRate.Value.ToString("0.000"));

        //if (dto.TableData != null)
        //{
        //    pdfFormFields.SetField("Payments1", dto.TableData.FirstOrDefault().NumberOfPayments);
        //    pdfFormFields.SetField("AmountPayments1", dto.TableData.FirstOrDefault().AmountOfPayments);
        //    pdfFormFields.SetField("PaymentDueDate1", dto.TableData.FirstOrDefault().PaymentDueDate);
        //    int.TryParse(dto.TableData.FirstOrDefault().NumberOfPayments, out int totalNumberOfPayments);

        //    if (dto.TableData.Count > 1)
        //    {
        //        pdfFormFields.SetField("Payments2", dto.TableData.LastOrDefault().NumberOfPayments);
        //        pdfFormFields.SetField("AmountPayments2", dto.TableData.LastOrDefault().AmountOfPayments);
        //        pdfFormFields.SetField("PaymentDueDate2", dto.TableData.LastOrDefault().PaymentDueDate);
        //        int.TryParse(dto.TableData.LastOrDefault().NumberOfPayments, out int totalNumberOfPayments2);
        //        totalNumberOfPayments += totalNumberOfPayments2;
        //    }
        //    else
        //    {
        //        pdfFormFields.SetField("Payments2", string.Empty);
        //        pdfFormFields.SetField("AmountPayments2", string.Empty);
        //        pdfFormFields.SetField("PaymentDueDate2", string.Empty);

        //    }
        //    pdfFormFields.SetField("TotalNumberOfPayments", totalNumberOfPayments.ToString());
        //}

        //pdfFormFields.SetField("AmountGiven", dto.AmountFinanced.ToString("0.00"));
        //pdfFormFields.SetField("ApplicationFee", dto.LoanOriginationFee.ToString("0.00"));
        //pdfFormFields.SetField("DEApplicationFee", dto.ApplicationFee.ToString("0.00"));
        //pdfFormFields.SetField("TermLength", dto.TermLength.ToString("0"));

        //pdfStamper.FormFlattening = true;
        //pdfStamper.Close();
        //file = stream.ToArray();

        //return file;
    }

    /// <summary>
    /// Populate Electronic Consent Disclosure pdf fields.
    /// </summary>
    /// <returns>The populated file content in bytes.</returns>
    private byte[]? PopulateEConsentFields(string footerInfo, string documentName, byte[]? file)
    {
        if (file == null)
        {
            return file;
        }

        return file;

        //TODO - existing PDF package is old; determine how this will be done in QC7
        /*
        using var stream = new MemoryStream();
        var pdfReader = new PdfReader(file);
        var pdfStamper = new PdfStamper(pdfReader, stream);
        var pdfFormFields = pdfStamper.AcroFields;
        pdfReader.RemoveUsageRights();
        pdfFormFields.SetField("DocumentName", footerInfo);

        for (var page = 1; page <= pdfReader.NumberOfPages; page++)
        {
            var pageSize = pdfReader.GetPageSize(page);
            const int marginX = 40;
            const int marginY = 30;
            const int height = 20;

            // Create signature appearance rectangle
            var rect = new iTextSharp.text.Rectangle(
                pageSize.Left + marginX,
                pageSize.Bottom + marginY,
                pageSize.Right - marginX,
                pageSize.Bottom + marginY + height);

            // Add signature text/visual indicator
            var over = pdfStamper.GetOverContent(page);
            over.BeginText();
            var baseFont = BaseFont.CreateFont(
                BaseFont.HELVETICA,
                BaseFont.CP1252,
                BaseFont.NOT_EMBEDDED);
            over.SetFontAndSize(baseFont, 10);
            over.SetTextMatrix(rect.Left, rect.Bottom + 15);
            over.ShowText(documentName);
            over.EndText();
        }

        pdfStamper.FormFlattening = true;
        pdfStamper.Close();
        file = stream.ToArray();
        return file;
        */
    }

    private async Task<Data.Models.LoanApplication> GetLoanApplicationForWizardAsync(Guid financialInstitutionMemberId) =>
        await qCashContext.LoanApplications.Where(p => p.FinancialInstitutionMemberId.Equals(financialInstitutionMemberId))
            .Include(s => s.LoanStatus)
            .Include(s => s.FinancialInstitutionMember)
            .Include(s => s.SelectedAccountForTransaction)
            .Include(s => s.DefaultAccountForTransaction)
            .Include(s => s.LoanApplicationMemberSelections)
            .Include(s => s.SelectedProduct)
            .Include(s => s.LoanApplicationCampaignCodes)
            .ThenInclude(s => s.CampaignCode)
            .Include(s => s.LoanApplicationSso)
            .Include(s => s.LoanApplicationFraudControl)
            .Include(s => s.MemberConsents)
            .OrderBy(p => p.Id)
            .FirstAsync();

    private async Task<bool> StoreDocumentAsync(IList<LoanApplicationLogDetail> logs, byte[] docData, Data.Models.LoanApplication loanApplication, LoanApplicationDocumentType docType)
    {
        Stream stream = new MemoryStream(docData);

        var fileName = await filenameTemplateService.GetDocumentNameAsync(docType, loanApplication, null);

        string fileNameLog;
        var sortOrder = LogSortOrder.None;

        Document? doc = null;
        var docId = Guid.Empty;
        var docTypeId = Guid.Empty;
        var isNew = false;
        var documentTypeLookup = qCashContext.DocumentTypes.ToList();
        switch (docType)
        {
            case LoanApplicationDocumentType.AdverseActionNotice:

                fileNameLog = "Display Adverse Action Notice: ";
                sortOrder = LogSortOrder.DisplayAdverseActionNotice;

                docId = loanApplication.AdverseActionNoticeDocumentId.GetValueOrDefault();
                doc = qCashContext.Documents.FirstOrDefault(o => o.Id == docId);
                if (doc == null || doc.Id == Guid.Empty)
                {
                    doc = new Document { Id = guidExtensionService.NewSequentialGuid() };
                    isNew = true;
                }
                //loanApplication.AdverseActionNoticeDocumentId = doc.Id;
                loanApplication.AdverseActionNoticeDocument = doc;
                docTypeId = documentTypeLookup.Single(o => o.Abrv == "DTLD").Id;
                break;

            case LoanApplicationDocumentType.EConsentDisclosure:
                fileNameLog = "eConsent Disclosure: ";
                sortOrder = LogSortOrder.EConsentDisclosure;

                var eConsentMemberConsent = loanApplication.MemberConsents
                    .FirstOrDefault(p => p.AcceptanceType == nameof(MemberConsentAcceptanceType.EConsent));
                if (eConsentMemberConsent != null)
                {
                    //need to review this; does not appear to do anything at all
                    docId = eConsentMemberConsent.DocumentId.GetValueOrDefault();
                    doc = qCashContext.Documents.FirstOrDefault(o => o.Id == docId);
                    if (doc != null && doc.Id != Guid.Empty)
                    {
                        eConsentMemberConsent.DocumentId = doc.Id;
                        //UpdateMemberConsent(eConsentMemberConsent);
                        docTypeId = documentTypeLookup.Single(o => o.Abrv == "DTEDD").Id;
                        break;
                    }
                }
                doc = new Document { Id = guidExtensionService.NewSequentialGuid() };
                docTypeId = documentTypeLookup.Single(o => o.Abrv == "DTEDD").Id;
                isNew = true;

                eConsentMemberConsent = new MemberConsent
                {
                    Id = guidExtensionService.NewSequentialGuid(),
                    Document = doc, //eConsentMemberConsent.DocumentId = doc.Id;
                    LoanApplicationId = loanApplication.Id,
                    AcceptanceType = nameof(MemberConsentAcceptanceType.EConsent),
                    TimeStamp = [],
                };
                loanApplication.MemberConsents.Add(eConsentMemberConsent);
                //qCashContext.SaveChanges();
                break;

            case LoanApplicationDocumentType.TILA:
                fileNameLog = "TILA Disclosure: ";
                sortOrder = LogSortOrder.TILADisclosure;

                var memberConsentLoanDisclosure = loanApplication.MemberConsentLoanDisclosure();
                if (memberConsentLoanDisclosure != null)
                {
                    doc = qCashContext.Documents.FirstOrDefault(o => o.Id == memberConsentLoanDisclosure.DocumentId.GetValueOrDefault());
                    docTypeId = documentTypeLookup.Single(o => o.Abrv == "DTTILA").Id;
                    fileName = doc?.Filename ?? string.Empty;
                    break;
                }
                docTypeId = documentTypeLookup.Single(o => o.Abrv == "DTTILA").Id;
                doc = new Document { Id = guidExtensionService.NewSequentialGuid() };
                isNew = true;

                memberConsentLoanDisclosure = new MemberConsent
                {
                    Id = guidExtensionService.NewSequentialGuid(),
                    AcceptanceType = nameof(MemberConsentAcceptanceType.LoanDisclosure),
                    LoanApplicationId = loanApplication.Id,
                    //memberConsentLoanDisclosure.DocumentId = doc.Id;
                    Document = doc,
                    TimeStamp = [],
                };
                loanApplication.MemberConsents.Add(memberConsentLoanDisclosure);
                //qCashContext.SaveChanges();
                break;

            default:
                fileNameLog = "Created New Document: ";
                break;
        }

        fileNameLog += await filenameTemplateService.GetDocumentNameAsync(docType, loanApplication, redactInformation: true);

        // delete old document
        if (docId != Guid.Empty)
        {
            if (doc != null)
            {
                try
                {
                    await fileSystemProvider.DeleteFileAsync(doc.Filename);
                }
                catch
                {
                    //silent fail ok here
                }
            }
        }

        if (doc == null)
        {
            //this cannot be allowed at this point
            throw new Exception("Document type not handled");
        }

        doc.DocumentTypeId = docTypeId;
        var clientTimeZone = qCashContext.Settings.FirstOrDefault()?.TimeZone ?? string.Empty;
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);
        if (isNew)
        {
            doc.DateCreatedUtc = DateTime.UtcNow.FromUTC(timeZone);
        }

        doc.DateUpdatedUtc = DateTime.UtcNow.FromUTC(timeZone);
        doc.Description = string.Empty;
        doc.DocumentCreatedUtc = DateTime.UtcNow;
        var version = 2;
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
        var files = fileSystemProvider.GetListOfFileNames(fileNameWithoutExtension);
        while (files.Contains(fileName))
        {
            fileName = $"{fileNameWithoutExtension}_{version}.pdf";
            version++;
        }
        doc.Filename = fileName;
        await fileSystemProvider.StoreFileAsync(stream, "application/pdf", fileName);

        var isLocalContainer = fileSystemProvider.ContainerType == ContainerTypeEnum.DocumentsPrivate;

        if (isNew)
        {
            if (!string.IsNullOrEmpty(fileNameLog))
            {
                if (isLocalContainer)
                {
                    loanApplicationService.AddLogMessage(logs, fileNameLog, DateTime.UtcNow, (docType == LoanApplicationDocumentType.TILA) ? LogType.Admin : LogType.Clean, sortOrder);
                }
            }
            //Repository.InsertDocument(doc);   //handle with SaveChanges below
        }
        else
        {
            if (isLocalContainer)
            {
                loanApplicationService.AddLogMessage(logs, $"Update document - {fileNameLog}", DateTime.UtcNow);
            }
            //Repository.UpdateDocument(doc);   //handle with SaveChanges below
        }

        await qCashContext.SaveChangesAsync();

        return true;
    }

    private void UpdateLoanApplicationInWizard(Data.Models.LoanApplication loanApplication)
    {
        //Temporarily keeping this around for reference - some games going on here with the repo model.
        //As far as I can tell, none of this should be necessary if we're dealing with the EF LoanApplication
        //the whole time. Once we've stepped through the process logic a few times with some real sample data,
        //remove this method and any calls to it.

        return;

        //var uow = Repository.CreateUnitOfWork();

        //var loanStatusTypeId = loanApplication.LoanStatusId;
        //loanApplication.LoanStatus = null;

        //var fiMember = loanApplication.FinancialInstitutionMember;
        //loanApplication.FinancialInstitutionMember = null;

        //var la = Repository.GetLoanApplication(loanApplication.Id);

        //var appId = la.AppId;
        //Mapper.Map(loanApplication, la);
        //la.AppId = appId;
        //la.LoanStatusId = loanStatusTypeId;

        //UpdateLoanApplication(uow, la);

        //JM - this should all be take care of when the LA is committed to the DB

        //if (loanApplication.LoanApplicationMemberSelections is { Count: > 0 })
        //{
        //    var laMemberSelection = GetLoanApplicationMemberSelectionsByLoanApplicationId(loanApplication.Id).FirstOrDefault();
        //    var isNewAddQ = false;
        //    if (laMemberSelection == null)
        //    {
        //        isNewAddQ = true;
        //        laMemberSelection = CreateLoanApplicationMemberSelection();
        //        laMemberSelection.Id = GuidExtension.NewSequentialGuid();
        //    }

        //    var aId = laMemberSelection.Id;
        //    Mapper.Map(loanApplication.LoanApplicationMemberSelection.FirstOrDefault(), laMemberSelection);
        //    laMemberSelection.Id = aId;

        //    if (isNewAddQ)
        //    {
        //        InsertLoanApplicationMemberSelection(uow, laMemberSelection);
        //    }
        //    else
        //    {
        //        UpdateLoanApplicationMemberSelection(uow, laMemberSelection);
        //    }
        //}

        //uow.Commit();

        //loanApplication.FinancialInstitutionMember = fiMember;
        //loanApplication.LoanStatusId = loanStatusTypeId;
        //loanApplication.LoanStatus = _lookupFactory.LoanStatusTypelookupCreate().GetItems().FirstOrDefault(i => i.Id.Equals(loanStatusTypeId.Value));
    }

    private ValidationResult ValidateInitiate(Data.Models.LoanApplication loanApplication, OriginApp originApp, IList<LoanApplicationLogDetail> logs, IList<LoanApplicationSettingsLog> settingsLog)
    {
        var result = new ValidationResult();

        //var settings = SettingService.GetSettingsByFinancialInstitutionId(ApplicationContextFactory.GetApplicationContext().ApplicationId).FirstOrDefault();
        var settings = qCashContext.Settings.First();

        var clientTimeZone = qCashContext.Settings.FirstOrDefault()?.TimeZone ?? string.Empty;

        loanApplicationService.InsertLoanApplicationSettingsLog(settingsLog, loanApplication.Id, LoanApplicationSettingType.EConsent, settings.EConsent);
        var eConsentOrPriorEConsent = loanApplication.MemberConsentEConsentOrPriorEConsent();

        loanApplicationService.AddLogMessage(logs, $"EConsent Status: {(settings.EConsent ? "Enabled" : "Disabled")}", DateTime.UtcNow);

        if (settings.EConsent)
        {
            var eConsentDisclosureAcceptance =
                qCashContext.EConsentDisclosureAcceptances.FirstOrDefault(o =>
                    o.MemberIdHash == loanApplication.FinancialInstitutionMember.MemberIdHash);

            var showEConsent = eConsentDisclosureAcceptance is null;
            loanApplicationService.AddLogMessage(logs, $"EConsent Displayed: {(showEConsent ? "Yes" : "No")}", DateTime.UtcNow);

            if (showEConsent)
            {
                result.NextStep = LoanApplicationStep.EConsentDisclosure;
            }
            else
            {
                var previousAcceptanceDateTime = eConsentDisclosureAcceptance!.DateCreatedUtc.FromUTC(TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone)).ToString(CultureInfo.InvariantCulture);
                loanApplicationService.AddLogMessage(logs, $"EConsent Previous Acceptance: {previousAcceptanceDateTime}", DateTime.UtcNow);

                if (eConsentOrPriorEConsent != null)
                {
                    eConsentOrPriorEConsent.PreviouslyAccepted = true;
                    //UpdateMemberConsent(eConsentOrPriorEConsent); //will be saved when SaveChanges is called in GetNextStepAfterExclusionsAsync
                    result.NextStep = LoanApplicationStep.Application;
                }
                else
                {
                    var memberConsent = new MemberConsent
                    {
                        Id = guidExtensionService.NewSequentialGuid(),
                        AcceptanceType = nameof(MemberConsentAcceptanceType.PriorEConsent),
                        LoanApplicationId = loanApplication.Id,
                        PreviouslyAccepted = true,
                        TimeStamp = [],
                    };

                    if (loanApplication.LoanApplicationSso != null && (settings.MaskInitiateWaitTime
                                                                       || (settings.LoanHub
                                                                           && !loanApplication.LoanApplicationSso.PreApproved.GetValueOrDefault())))
                    {
                        //InsertMemberConsent(memberConsent); //will be saved when SaveChanges is called in GetNextStepAfterExclusionsAsync
                    }

                    loanApplication.MemberConsents.Add(memberConsent);
                    result.NextStep = LoanApplicationStep.Application;
                }
            }
        }
        else
        {
            if (eConsentOrPriorEConsent != null)
            {
                eConsentOrPriorEConsent.PreviouslyAccepted = true;
                //UpdateMemberConsent(eConsentOrPriorEConsent); //will be saved when SaveChanges is called in GetNextStepAfterExclusionsAsync
            }
            else
            {
                var memberConsent = new MemberConsent
                {
                    Id = guidExtensionService.NewSequentialGuid(),
                    AcceptanceType = nameof(MemberConsentAcceptanceType.PriorEConsent),
                    LoanApplicationId = loanApplication.Id,
                    PreviouslyAccepted = false,
                    TimeStamp = [],
                };

                if (loanApplication.LoanApplicationSso != null && (settings.MaskInitiateWaitTime
                                                                   || (settings.LoanHub
                                                                       && !loanApplication.LoanApplicationSso.PreApproved.GetValueOrDefault())))
                {
                    //InsertMemberConsent(memberConsent); //will be saved when SaveChanges is called in GetNextStepAfterExclusionsAsync
                }

                loanApplication.MemberConsents.Add(memberConsent);
            }

            result.NextStep = LoanApplicationStep.Application;
        }

        return result;
    }


    /// <summary>
    /// Returns member from SSO. Member is always saved new to DB.
    /// </summary>
    /// <returns>GetSsoResponse wrapper around FinancialInstitutionMember.</returns>
    public async Task<GetSsoResponse> GetMemberBySsoAsync(SsoInitiateApiModel sso, Guid correlationId, List<LoanApplicationLogDetail> logs)
    {
        //TODO - note that IApplicationContext appCtx was removed as a parameter.  Verify that this is actually no longer needed (that the
        //  EF tenant global query filter still applies when this is called through web api, etc)

        var settings = await qCashContext.Settings.AsNoTracking().FirstAsync();

        //removing
        //var financialCoachingSettings = SettingService.GetFinancialCoachingSettingsByFinancialInstitutionId(appCtx.ApplicationId).FirstOrDefault();

        var isIndividualId = financialInstitutionService.FinancialInstitution.IsIdentifiedByIndividualId() &&
                             !string.IsNullOrWhiteSpace(sso.MemberId);

        var memberData = new GetMemberParameter
        {
            AccountDescriptionMapping = await qCashContext.AccountDescriptionMappings.AsNoTracking().ToDictionaryAsync(p => p.Name, p => p.Description),
            IsCreditJointOwnerOnLoansSupported = true,
            BaseAccountId = string.IsNullOrEmpty(sso.BaseAccount) ? null : sso.BaseAccount,
            Codes = new Codes
            {
                ClosedAccountCode = await qCashContext.ClosedAccountCodes.AsNoTracking().Select(p => p.Name).ToArrayAsync(),
                ExcludedAccountsCodes = await qCashContext.ExcludedAccountCodes.AsNoTracking().Select(p => p.Name).ToArrayAsync(),
                LoanCodes = await qCashContext.LoanCodes.AsNoTracking().Select(p => p.Name).ToArrayAsync(),
                InactiveAccountCodes = await qCashContext.InactiveAccountCodes.AsNoTracking().Select(p => p.Name).ToArrayAsync(),
                LoanCodesForJointOwners = await qCashContext.LoanCodeForJointOwners.AsNoTracking().Select(p => p.Name).ToArrayAsync(),
            },
            TaxId = string.IsNullOrEmpty(sso.TaxId) ? null : sso.TaxId,
            MemberId = string.IsNullOrWhiteSpace(sso.MemberId) ? null : sso.MemberId,
            GetAccountsOption = GetAccountsOption.DepositOnly | GetAccountsOption.LoanOnly, //Nothing also returns all accounts 01/29/2014
            ExtraInfo = loanApplicationService.GetExtraInfoForGetMember(settings, isIndividualId),
            ExcludedBaseAccountTypes = await qCashContext.ExclusionBaseAccountTypeLoanInitiates.AsNoTracking().Where(o => !o.IsDeleted).Select(p => p.Name).ToArrayAsync(),
            ExcludedSubAccountTypes = await qCashContext.ExclusionSubAccountTypeLoanInitiates.AsNoTracking().Where(o => !o.IsDeleted).Select(p => p.Name).ToArrayAsync(),
            MemberRestrictionCodes = await qCashContext.MemberRestrictionCodeLoanInitiates.AsNoTracking().Where(o => !o.IsDeleted).Select(p => p.Name).ToArrayAsync(),
            CustomCoreDate = sso.IsSsoTest ? financialInstitutionService.GetSSOTest().CoreDate : null,
            ExcludedJointAccountCodes = await qCashContext.MemberRestrictionCodeRelationships.AsNoTracking().Where(o => !o.IsDeleted).Select(p => p.Name).ToArrayAsync(),
            MemberControlCodes = await qCashContext.MemberControlCodes.AsNoTracking().Where(o => !o.IsDeleted).Select(p => p.Name).ToArrayAsync(),
            ShareRestrictionCodes = await qCashContext.ShareRestrictiveCodeLoanInitiates.AsNoTracking().Where(o => !o.IsDeleted).Select(p => p.Name).ToArrayAsync(),
            LoanRestrictionCodes = await qCashContext.LoanRestrictiveCodeLoanInitiates.AsNoTracking().Where(o => !o.IsDeleted).Select(p => p.Name).ToArrayAsync(),
        };

        var fiLogAuthorization = (await qCashContext.FinancialInstitutionConfigurations.AsNoTracking().FirstAsync()).LogAuthorization ?? false;
        var extendedMember = await loanApplicationService.CoreProviderGetMemberBySsoAsync(memberData, logs, fiLogAuthorization, correlationId.ToString(), (OriginApp)sso.OriginApp);

        var fiMember = extendedMember.Member;

        if (fiMember == null || extendedMember == null)
        {
            throw new Exception("No valid financial institution member data returned from core.");
        }

        ValidateMember(fiMember);

        var response = new GetSsoResponse
        {
            Elapsed = extendedMember.Elapsed ?? 0,
        };

        var member = new FinancialInstitutionMember
        {
            Id = guidExtensionService.NewSequentialGuid(),
            FinancialInstitutionId = financialInstitutionService.FinancialInstitution.Id,
        };

        BaseAccount? account = null;
        if (!string.IsNullOrEmpty(memberData.BaseAccountId))
        {
            account = fiMember.BaseAccounts?.Where(p => p.Id == memberData.BaseAccountId).OrderBy(s => s.OpenDate).FirstOrDefault();
            if (account == null)
            {
                account = fiMember.BaseAccounts?
                    .Where(p => p.Id?.TrimStart('0') == memberData.BaseAccountId.TrimStart('0'))
                    .OrderBy(s => s.OpenDate)
                    .FirstOrDefault();
                if (account != null)
                {
                    account.Id = memberData.BaseAccountId;
                }
            }
        }
        else if (!financialInstitutionService.FinancialInstitution.HasMultipleBaseAccounts())
        {
            account = fiMember.BaseAccounts?
                .OrderBy(s => s.OpenDate)
                .FirstOrDefault();
        }

        member.AccountId = account?.Id;
        member.MemberIdMask = loanApplicationService.GetMaskedNumber(fiMember.MemberId);
        member.MemberIdHash = CryptoHelper.CalculateSHA256Hash(fiMember.MemberId ?? string.Empty);

        MapMember(fiMember, member);

        member.Email = string.IsNullOrEmpty(sso.Email) ? fiMember.Email : sso.Email;
        member.PhoneNumber = string.IsNullOrEmpty(sso.PhoneNumber) ? fiMember.PhoneNumber : sso.PhoneNumber;

        member.TimeStamp = [];

        var accounts = financialInstitutionService.FinancialInstitution.HasMultipleBaseAccounts()
            ? fiMember.BaseAccounts?.Where(o => o.Accounts != null).SelectMany(c => c.Accounts!)
            : account?.Accounts;
        await CheckIfMemberIsNewBorrowerAsync(member, accounts);

        var mlaMember = new TempMemberDTO
        {
            TaxId = fiMember.TaxId ?? string.Empty,
            MemberId = fiMember.MemberId ?? string.Empty,
        };

        await tokenService.StoreTempMemberDataAsync(
            member.Id.ToString(),
            tokenService.EncryptValue(JsonConvert.SerializeObject(mlaMember)));

        const string memberTokenKey = "MemberToken";
        var extraInfo = fiMember.ExtraInfo;
        if (extraInfo != null && extraInfo.Infoes.TryGetValue(memberTokenKey, out var info))
        {
            member.MemberToken = info;
        }

        //begin using context
        qCashContext.FinancialInstitutionMembers.Add(member);

        if (!string.IsNullOrEmpty(member.AccountId) || financialInstitutionService.FinancialInstitution.HasMultipleBaseAccounts())
        {
            SyncBaseAccounts(member, fiMember);
        }

        //Save data
        try
        {
            await qCashContext.SaveChangesAsync();
        }
        catch (DbUpdateException ex)
        {
            var er = new LoanApplicationException();
            foreach (var eve in ex.Entries)
            {
                logs.Add(new LoanApplicationLogDetail
                {
                    Id = guidExtensionService.NewSequentialGuid(),
                    ActionDateTimeUtc = DateTime.UtcNow,
                    TimeStamp = [],
                    IsTemp = false,
                    ActionDescription = $"Error updating entry {eve}",
                    ReportGroup = (int?)LogDetailItemReportGroupEnum.Red,
                });

                er.AddError($"Error updating entry {eve}");
            }

            throw er;
        }

        response.FinancialInstitutionMember = member;
        return response;
    }

    private void MapMember(Member memberData, FinancialInstitutionMember member)
    {
        //map data from core Member object to db FinancialInstitutionMember

        // fields that are trimmed sometimes come in with extra whitespace
        member.FirstName = memberData.Name?.First;
        member.MiddleName = memberData.Name?.Middle;
        member.LastName = memberData.Name?.Last ?? string.Empty;
        member.DateOfBirth = memberData.DateOfBirth;
        member.State = memberData.Address?.State;
        member.City = memberData.Address?.City?.Trim();
        member.Zip = memberData.Address?.Zip;
        member.MailingAddress = memberData.Address?.Street1?.Trim() + " " + memberData.Address?.Street2?.Trim();
        //member.MemberWarningCodes = new List<MemberWarningCode>();
        member.PhoneNumberChangedDate = memberData.PhoneNumberChangedDate;
        member.EmailChangedDate = memberData.EmailChangedDate;
        member.IsOnBlocklist = memberData.IsOnBlocklist;
        member.IsBadEmail = memberData.IsBadEmail;
        member.IsBadAddress = memberData.IsBadAddress;

        if (!string.IsNullOrWhiteSpace(memberData.Name?.Suffix))
        {
            member.Suffix = memberData.Name.Suffix;
        }

        // Temp fix: https://nebo.finivation.com/pm/issues/17555
        if (!string.IsNullOrEmpty(memberData.PhoneNumber))
        {
            member.PhoneNumber = memberData.PhoneNumber.StartsWith("+1", StringComparison.CurrentCultureIgnoreCase) ? memberData.PhoneNumber : $"+1{memberData.PhoneNumber}";
        }

        // Store and encrypt the member's SSN (the last 4 digits)
        if (string.IsNullOrWhiteSpace(memberData.TaxId))
        {
            return;
        }

        var taxId = memberData.TaxId;
        if (taxId.Length > 4)
        {
            taxId = taxId.Substring(taxId.Length - 4);
        }

        member.TaxIdLastFourDigitsEncrypted = tokenService.EncryptValue(taxId);

        if (memberData.WarningCodes == null)
        {
            return;
        }

        foreach (var wc in memberData.WarningCodes)
        {
            var newWc = new MemberWarningCode
            {
                ExpirationDateUtc = wc.ExpirationDate,
                Code = wc.Code ?? string.Empty,
                FinancialInstitutionMemberId = member.Id,
                IsExpired = wc.IsExpired,
            };
            member.MemberWarningCodes.Add(newWc);
        }
    }

    private async Task CheckIfMemberIsNewBorrowerAsync(FinancialInstitutionMember member, IEnumerable<Account>? accounts)
    {
        var activeNonPersonalLoanProducts = (await GetAllActiveProductsAsync()).Where(p => !p.PersonalLoanCampaign).ToList();
        var memberLoanAccounts = accounts?.Where(a => a.Category == nameof(AccountCategory.LOAN)).ToList();

        foreach (var prod in activeNonPersonalLoanProducts)
        {
            if (prod.Abrv.Contains('.'))
            {
                var type = prod.Abrv.Split('.')[0];
                var subType = prod.Abrv.Split('.')[1];
                member.IsNewBorrower = !memberLoanAccounts?.Any(m => m.Type == type && m.SubType == subType);

                if (!member.IsNewBorrower.GetValueOrDefault())
                {
                    break;
                }
            }
            else
            {
                member.IsNewBorrower = !memberLoanAccounts?.Any(m => m.Type == prod.Abrv);

                if (!member.IsNewBorrower.GetValueOrDefault())
                {
                    break;
                }
            }
        }
    }

    private static void ValidateMember(Member fiMember)
    {
        if (fiMember == null)
        {
            throw new Exception(InitiateLoanApplicationError);
        }

        if (fiMember.BaseAccounts == null || fiMember.BaseAccounts.Length < 1)
        {
            throw new Exception("No account data for member.");
        }
    }
    private static bool? GetFlagFromSsoCredentials(string? ssoFlagValue)
    {
        if (ssoFlagValue == null)
        {
            return null;
        }

        var parsed = int.TryParse(ssoFlagValue, out var ssoFlagValueParsed);
        return parsed switch
        {
            true when ssoFlagValueParsed == 1 => true,
            true when ssoFlagValueParsed == 0 => false,
            _ => null,
        };
    }

    private void SyncBaseAccounts(FinancialInstitutionMember financialInstitutionMember, Member member)
    {
        //Includes MemberAccounts
        var existingMemberAccounts = loanApplicationService.GetMemberBaseAccounts(financialInstitutionMember.Id);

        //Sync other
        var list = member.BaseAccounts != null ? member.BaseAccounts.ToList() : [];
        var count = 0;
        foreach (var baseAccount in list)
        {
            count++;
            Debug.WriteLine($"Account {count}/{list.Count}");
            var isNew = false;
            var ourAccount = existingMemberAccounts.FirstOrDefault(p => p.AccountId == baseAccount.Id);
            if (ourAccount == null)
            {
                isNew = true;
                //Create new if not exist
                ourAccount = new MemberBaseAccount { Id = guidExtensionService.NewSequentialGuid(), TimeStamp = [] };
            }
            MapBaseAccount(financialInstitutionMember.Id, baseAccount, ourAccount);

            if (isNew)
            {
                financialInstitutionMember.MemberBaseAccounts.Add(ourAccount);
            }
            else
            {
                //shouldn't be necessary anymore as we directly modify the EF object above in MapBaseAccount
                //UpdateMemberBaseAccount(uow, ourAccount);
            }

            SyncWarningCodes(baseAccount, ourAccount);
            SyncAccounts(baseAccount, ourAccount);
        }
    }

    private void SyncAccounts(BaseAccount baseAccount, MemberBaseAccount ourBaseAccount)
    {
        var list = baseAccount.Accounts != null ? baseAccount.Accounts.OrderBy(p => p.OpenDate).ToList() : [];
        foreach (Account account in list)
        {
            var isNew = false;
            var ourAccount = ourBaseAccount.MemberAccounts.Where(p => p.AccountId == account.AccountId && p.Category == account.Category).OrderBy(s => s.DateOpened).FirstOrDefault();
            if (ourAccount == null)
            {
                //Create new if not exist
                isNew = true;
                ourAccount = new MemberAccount { Id = guidExtensionService.NewSequentialGuid(), TimeStamp = [] };
            }

            MapCoreAccountToMemberAccount(account, ourAccount, ourBaseAccount.Id);

            if (isNew)
            {
                ourBaseAccount.MemberAccounts.Add(ourAccount);
            }
            else
            {
                //shouldn't be necessary anymore as we directly modify the EF object above in MapCoreAccountToQCMemberAccount
                //UpdateMemberAccount(uow, ourAccount);
            }

            SyncWarningCodes(account, ourAccount);
        }
    }


    private void MapCoreAccountToMemberAccount(Account coreAccount, MemberAccount memberAccount, Guid baseAccountId)
    {
        //map from Account record (from Core) to MemberAccount record in DB
        memberAccount.MemberBaseAccountId = baseAccountId;
        memberAccount.Category = coreAccount.Category ?? string.Empty;
        memberAccount.AccountId = coreAccount.AccountId ?? string.Empty;
        memberAccount.AvailableBalance = coreAccount.AvailableBalance.GetValueOrDefault();
        memberAccount.LedgerBalance = coreAccount.LedgerBalance.GetValueOrDefault();
        memberAccount.IsClosed = coreAccount.IsClosed;
        memberAccount.DateClosedUtc = coreAccount.CloseDate;
        memberAccount.IsInactive = coreAccount.IsInactive;
        memberAccount.IsChargedOff = coreAccount.IsChargedOff;
        memberAccount.DateChargedOffUtc = coreAccount.ChargeOffDate;
        memberAccount.Type = coreAccount.Type ?? string.Empty;
        memberAccount.SubType = coreAccount.SubType;
        memberAccount.Description = coreAccount.Description;
        memberAccount.DateOpened = coreAccount.OpenDate;
        memberAccount.OriginalBalance = coreAccount.OriginalBalance;
        memberAccount.PayOffAmount = coreAccount.PayoffAmount;
        memberAccount.PayOffBalanceAsOfDate = coreAccount.PayoffBalanceAsOfDate;
        memberAccount.InitialLoanAmount = coreAccount.InitialLoanAmount;
        memberAccount.NsfCount = coreAccount.NSFCount;
        memberAccount.OdpCount = coreAccount.ODPCount;
        memberAccount.InterestPaid = coreAccount.InterestPaid;
        memberAccount.IsExcluded = coreAccount.IsExcluded;
        memberAccount.PaymentDueDateUtc = coreAccount.PaymentDueDate;

        const string isTroubledDebtKey = "IsTroubledDebt";
        if (coreAccount.ExtraInfo?.Infoes != null
            && coreAccount.ExtraInfo.Infoes.TryGetValue(isTroubledDebtKey, out var isTroubledDebtValue)
            && bool.TryParse(isTroubledDebtValue, out var isTroubledDebt))
        {
            memberAccount.IsTroubledDebt = isTroubledDebt;
        }
    }

    private void MapBaseAccount(Guid memberId, BaseAccount baseAccount, MemberBaseAccount ourAccount)
    {
        //update MemberBaseAccount (in db) data from core data we just got (BaseAccount)

        //these should already match
        ourAccount.AccountId = baseAccount.Id ?? string.Empty;
        ourAccount.FinancialInstitutionMemberId = memberId;

        ourAccount.OwnerShipType = baseAccount.OwnerShip.ToString();
        ourAccount.OpenDateUtc = baseAccount.OpenDate;
        ourAccount.CloseDateUtc = baseAccount.CloseDate;
        ourAccount.Type = baseAccount.Classification ?? string.Empty;
        ourAccount.IsExcluded = baseAccount.IsExcluded;

        var branchNumber = string.Empty;
        if (baseAccount.ExtraInfo != null)
        {
            baseAccount.ExtraInfo.Infoes.TryGetValue("BranchNumber", out branchNumber);
        }

        ourAccount.BranchNumber = branchNumber;
    }

    private void SyncWarningCodes(BaseAccount baseAccount, MemberBaseAccount ourBaseAccount)
    {
        //Delete old warning codes
        var coreAccountWarningCodes = baseAccount.WarningCode?.Select(wc => wc.Code).ToList() ?? [];
        var wcOld = ourBaseAccount.MemberBaseAccountWarningCodes.Where(p => !coreAccountWarningCodes.Contains(p.Code)).ToList();

        foreach (var item in wcOld)
        {
            //warning code exists on MemberAccount but not on Account - delete
            ourBaseAccount.MemberBaseAccountWarningCodes.Remove(item);
        }

        //Sync other
        var list = baseAccount.WarningCode != null ? baseAccount.WarningCode.ToList() : [];
        foreach (var warningCode in list)
        {
            var isNew = false;
            var ourCode = ourBaseAccount.MemberBaseAccountWarningCodes.FirstOrDefault(p => p.Code == warningCode.Code);

            if (ourCode == null)
            {
                //Create new if not exist
                isNew = true;
                ourCode = new MemberBaseAccountWarningCode { Id = guidExtensionService.NewSequentialGuid(), TimeStamp = [] };
            }

            ourCode.MemberBaseAccountId = ourBaseAccount.Id;    //this should already be the same

            ourCode.Code = warningCode.Code ?? string.Empty;
            ourCode.ExpirationDateUtc = warningCode.ExpirationDate;

            ourCode.IsExpired = warningCode.IsExpired;
            if (isNew)
            {
                ourBaseAccount.MemberBaseAccountWarningCodes.Add(ourCode);
                //InsertMemberBaseAccountWarningCode(ourCode);
            }
            else
            {
                //shouldn't be necessary anymore as we directly modify the EF object above
                //UpdateMemberBaseAccountWarningCode(ourCode);
            }
        }
    }

    private void SyncWarningCodes(Account account, MemberAccount ourAccount)
    {
        //make sure warning codes for DB record MemberAccount reflect core data in Account record

        //Delete old warning codes
        var coreAccountWarningCodes = account.WarningCode?.Select(wc => wc.Code).ToList() ?? [];
        var wcOld = ourAccount.MemberAccountWarningCodes.Where(p => !coreAccountWarningCodes.Contains(p.Code)).ToList();

        foreach (var item in wcOld)
        {
            //warning code exists on MemberAccount but not on Account - delete
            ourAccount.MemberAccountWarningCodes.Remove(item);
        }

        //Sync other
        var list = account.WarningCode != null ? account.WarningCode.ToList() : [];
        foreach (var warningCode in list)
        {
            var isNew = false;
            var ourCode = ourAccount.MemberAccountWarningCodes.FirstOrDefault(p => p.Code == warningCode.Code);

            if (ourCode == null)
            {
                //Create new if not exist
                isNew = true;
                ourCode = new MemberAccountWarningCode { Id = guidExtensionService.NewSequentialGuid() };
            }

            ourCode.MemberAccountId = ourAccount.Id;    //this should already be the same

            ourCode.Code = warningCode.Code ?? string.Empty;
            if (warningCode.ExpirationDate.GetValueOrDefault().Year == 1)
            {
                ourCode.ExpirationDateUtc = null;
            }
            else
            {
                ourCode.ExpirationDateUtc = warningCode.ExpirationDate;
            }

            ourCode.IsExpired = warningCode.IsExpired;
            if (isNew)
            {
                ourAccount.MemberAccountWarningCodes.Add(ourCode);
                //InsertMemberAccountWarningCode(ourCode);
            }
            else
            {
                //shouldn't be necessary anymore as we directly modify the EF object above
                //UpdateMemberAccountWarningCode(ourCode);
            }
        }
    }

    private async Task<List<Data.Models.Product>> GetAllActiveProductsAsync()
    {
        var allProducts = qCashContext.Products.Where(o => o.IsActive)
            .Include(o => o.LoanCategory)
            .Include(o => o.InterfaceDefaultTexts)
            .ThenInclude(o => o.InterfaceOverrideTexts);
        return await allProducts.ToListAsync();
    }

    public async Task<LoanApplicationStep> ExecuteWizardStepAsync(
        Data.Models.LoanApplication loanApplication,
        LoanApplicationApiModelBasePost postModel,
        bool? sendEmail,
        List<LoanApplicationLogDetail> logs)
    {
        var loanStatusTypeLookup = await qCashContext.LoanStatusTypes.AsNoTracking().ToListAsync();

        var financialInstitution = financialInstitutionService.FinancialInstitution;

        var settings = await qCashContext.Settings.AsNoTracking().FirstAsync();
        var fraudControlSettings = financialInstitution.FraudControlSettings.First();

        var clientTimeZone = settings.TimeZone;
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);

        var vr = new ValidationResult();
        var settingsLog = new List<LoanApplicationSettingsLog>();

        var currentStep = (LoanApplicationStep)loanApplication.CurrentStep;
        vr.NextStep = currentStep;

        switch (currentStep)
        {
            case LoanApplicationStep.LoanHub:
                if (!loanApplication.ActionResult)
                {
                    var loanStatusLSTCBD = loanStatusTypeLookup.Single(o => o.Abrv == "LSTCBD");

                    vr.NextStep = LoanApplicationStep.ApplicationCancellationFinal;
                    await loanApplicationService.FinishLoanAsync(loanApplication, logs);
                    loanApplication = loanApplicationService.SetLoanApplicationStatus(
                        logs,
                        loanApplication,
                        loanStatusLSTCBD,
                        LogSortOrder.LoanStatusLoanCancelledBeforeDE,
                        LogDetailItemReportGroupEnum.None);
                }
                else if (loanApplication.SelectedLoanHubItemId == LoanHubItems.Payoff)
                {
                    //No longer supported - treating as cancel. If .PAYOFF is removed from LoanHubItems, just remove this whole clause.
                    var loanStatusLSTCBD = loanStatusTypeLookup.Single(o => o.Abrv == "LSTCBD");

                    vr.NextStep = LoanApplicationStep.ApplicationCancellationFinal;
                    await loanApplicationService.FinishLoanAsync(loanApplication, logs);
                    loanApplication = loanApplicationService.SetLoanApplicationStatus(
                        logs,
                        loanApplication,
                        loanStatusLSTCBD,
                        LogSortOrder.LoanStatusLoanCancelledBeforeDE,
                        LogDetailItemReportGroupEnum.None);
                    /*
                    try
                    {
                        vr = InitiatePayOff(loanApplication, logs);
                    }
                    catch (Exception ex)
                    {
                        var fiRoute = financialInstitution.FinancialInstitutionConfigurations.First();
                        await errorHelper.StoreErrorAsync(ex, fiRoute);
                        #if DEBUG
                        vr.Errors.AddError(ex.StackTrace);
                        #else
                        vr.Errors.AddError(ex.Message);
                        #endif
                    }
                    try
                    {
                        GetMemberPayoffAccounts(loanApplication.FinancialInstitutionMemberId);
                    }
                    catch
                    {
                        var loanStatusLSTPOERR = loanStatusTypeLookup.Single(o => o.Abrv == "LSTPOERR");
                        loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusLSTPOERR);
                        vr.NextStep = LoanApplicationStep.PayoffError;
                    }
                    */
                }
                else
                {
                    (vr, loanApplication) = await ValidateFirstPageAsync(logs, loanApplication, settingsLog);
                }

                break;

            case LoanApplicationStep.PayoffList:
                await EnsureOldFormIsNotPostedAsync(currentStep, vr, loanApplication);

                //No longer supported - treating as cancel. If .PayoffList is removed from LoanApplicationSteps, just remove this whole clause.
                var loanStatusLSTPOCNC1 = loanStatusTypeLookup.Single(o => o.Abrv == "LSTPOCNC");
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusLSTPOCNC1);
                vr.NextStep = LoanApplicationStep.PayoffExit;

                //if (loanApplication.ActionResult)
                //{
                //    vr = await PreparePayoffAsync(loanApplication, logs);
                //}
                //else
                //{
                //    var loanStatusLSTPOCNC = loanStatusTypeLookup.Single(o => o.Abrv == "LSTPOCNC");
                //    loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusLSTPOCNC);
                //    vr.NextStep = LoanApplicationStep.PayoffExit;
                //}
                break;

            case LoanApplicationStep.Payoff:
                await EnsureOldFormIsNotPostedAsync(currentStep, vr, loanApplication);

                //No longer supported - treating as cancel. If .Payoff is removed from LoanApplicationSteps, just remove this whole clause.
                var loanStatusLSTPOCNC = loanStatusTypeLookup.Single(o => o.Abrv == "LSTPOCNC");
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusLSTPOCNC, LogSortOrder.None, LogDetailItemReportGroupEnum.None);
                vr.NextStep = LoanApplicationStep.PayoffExit;

                //if (loanApplication.ActionResult)
                //{
                //    vr.NextStep = LoanApplicationStep.PayoffConfirmation;
                //}
                //else
                //{
                //    var loanStatusLSTPOCNC = loanStatusTypeLookup.Single(o => o.Abrv == "LSTPOCNC");
                //    loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusLSTPOCNC, 0, 0);
                //    vr.NextStep = LoanApplicationStep.PayoffExit;
                //}
                break;

            case LoanApplicationStep.PayoffConfirmation:

                //No longer supported - treating as cancel. If .PayoffConfirmation is removed from LoanApplicationSteps, just remove this whole clause.
                var loanStatusLSTPOCNC3 = loanStatusTypeLookup.Single(o => o.Abrv == "LSTPOCNC");
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusLSTPOCNC3, LogSortOrder.None, LogDetailItemReportGroupEnum.None);
                vr.NextStep = LoanApplicationStep.PayoffExit;

                //if (loanApplication.ActionResult)
                //{
                //    vr = await PayOffLoanAsync(loanApplication, logs);
                //}
                //else
                //{
                //    vr.NextStep = LoanApplicationStep.Payoff;
                //}

                break;

            case LoanApplicationStep.EConsentDisclosure:
                if (!postModel.ActionResult)
                {
                    var loanStatusLSTDEE = loanStatusTypeLookup.Single(o => o.Abrv == "LSTDEE");
                    vr.NextStep = LoanApplicationStep.ApplicationCancellationFinal;
                    await loanApplicationService.FinishLoanAsync(loanApplication, logs);
                    loanApplication = loanApplicationService.SetLoanApplicationStatus(
                        logs,
                        loanApplication,
                        loanStatusLSTDEE,
                        LogSortOrder.LoanStatusDeclienEConsent,
                        LogDetailItemReportGroupEnum.None);
                }
                else if (await ValidatePendingLoansAsync(loanApplication, vr, logs))
                {
                    await EnsureOldFormIsNotPostedAsync(currentStep, vr, loanApplication);

                    vr = await ValidateEConsentAsync(loanApplication);

                    var memEconsent = loanApplication.MemberConsentEConsent()?.Accepted ?? false;
                    loanApplicationService.AddLogMessage(logs,
                        $"eConsent Disclosure {(memEconsent ? "Accepted" : "Declined")} {DateTime.UtcNow.FromUTC(timeZone).ToString()}", DateTime.UtcNow);
                    if (vr.NextStep == LoanApplicationStep.Application)
                    {
                        await loanApplicationService.CheckMLAStatusAsync(
                            logs,
                            loanApplication,
                            loanApplication.LoanApplicationSso.GetOriginAppEnum(),
                            settingsLog);
                        var maskedFileName = "eConsent Disclosure: " +
                                             await filenameTemplateService.GetDocumentNameAsync(
                                                 LoanApplicationDocumentType.EConsentDisclosure,
                                                 loanApplication);
                        loanApplicationService.AddLogMessage(
                            logs,
                            maskedFileName,
                            DateTime.UtcNow,
                            LogType.Clean,
                            LogSortOrder.EConsentDisclosure);
                        var loanStatusLSTAE = loanStatusTypeLookup.Single(o => o.Abrv == "LSTAE");
                        loanApplication = loanApplicationService.SetLoanApplicationStatus(
                            logs,
                            loanApplication,
                            loanStatusLSTAE,
                            LogSortOrder.LoanStatusAcceptEConsent,
                            LogDetailItemReportGroupEnum.None);
                    }
                }
                break;

            case LoanApplicationStep.Application:
                var post = postModel as ApplicationApiModelPost;
                if (post?.ActionResult == false)
                {
                    vr.NextStep = LoanApplicationStep.ApplicationCancellationFinal;
                    await loanApplicationService.FinishLoanAsync(loanApplication, logs);
                    var loanStatusLSTCBD = loanStatusTypeLookup.Single(o => o.Abrv == "LSTCBD");
                    loanApplication = loanApplicationService.SetLoanApplicationStatus(
                        logs,
                        loanApplication,
                        loanStatusLSTCBD,
                        LogSortOrder.LoanStatusLoanCancelledBeforeDE,
                        LogDetailItemReportGroupEnum.None);
                }
                else if (await ValidatePendingLoansAsync(loanApplication, vr, logs))
                {
                    await EnsureOldFormIsNotPostedAsync(currentStep, vr, loanApplication);
                    (vr, loanApplication) = await ValidateApplicationAsync(
                        logs,
                        loanApplication,
                        settingsLog,
                        settings,
                        fraudControlSettings,
                        post?.PrioreConsentAcceptanceStatement ?? false,
                        post?.SmsConsentAcceptanceStatement ?? false);
                }
                break;

            case LoanApplicationStep.Awareness:
                //No longer supported - treating as cancel. If .Awareness is removed from LoanApplicationSteps, just remove this whole clause.
                vr.NextStep = LoanApplicationStep.ApplicationCancellationFinal;
                await loanApplicationService.FinishLoanAsync(loanApplication, logs);
                var loanStatusLSTCA = loanStatusTypeLookup.Single(o => o.Abrv == "LSTCA");
                loanApplication = loanApplicationService.SetLoanApplicationStatus(
                    logs,
                    loanApplication,
                    loanStatusLSTCA,
                    LogSortOrder.LoanStatusCancelledAwareness);

                //if (!loanApplication.ActionResult)
                //{
                //    vr.NextStep = LoanApplicationStep.ApplicationCancellationFinal;
                //    loanApplicationService.FinishLoanAsync(loanApplication, logs);
                //    var loanStatusLSTCA = loanStatusTypeLookup.Single(o => o.Abrv == "LSTCA");
                //    loanApplication = loanApplicationService.SetLoanApplicationStatus(
                //        logs,
                //        loanApplication,
                //        loanStatusLSTCA,
                //        LogSortOrder.LoanStatusCancelledAwareness);
                //}
                //else if (await ValidatePendingLoansAsync(loanApplication, vr, logs))
                //{
                //    EnsureOldFormIsNotPosted(currentStep, vr, loanApplication);
                //    (vr, loanApplication) = await ValidateAwarenessAsync(logs, loanApplication, settingsLog, settings, fraudControlSettings);
                //}
                break;

            case LoanApplicationStep.FraudControl:
                if (await ValidatePendingLoansAsync(loanApplication, vr, logs))
                {
                    await EnsureOldFormIsNotPostedAsync(currentStep, vr, loanApplication);
                    var fraudControlPostModel = postModel as FraudControlApiModelPost;

                    if (!loanApplication.ActionResult)
                    {
                        vr.Errors.Errors.Clear();

                        if (fraudControlPostModel is { ResendCodeAction: true })
                        {
                            (vr.NextStep, var errors) =
                                await SetNextStepFraudControlAndSendNotificationAsync(
                                    loanApplication,
                                    fraudControlSettings,
                                    logs);
                            if (!string.IsNullOrWhiteSpace(errors))
                            {
                                vr.Errors.AddError(errors);
                            }
                        }
                        else
                        {
                            vr.NextStep = LoanApplicationStep.ApplicationCancellationFinal;
                            await loanApplicationService.FinishLoanAsync(loanApplication, logs);
                            var loanStatusLSTCFC = loanStatusTypeLookup.Single(o => o.Abrv == "LSTCFC");
                            loanApplication = loanApplicationService.SetLoanApplicationStatus(
                                logs,
                                loanApplication,
                                loanStatusLSTCFC,
                                LogSortOrder.LoanStatusCancelledFraudControl);
                        }

                        break;
                    }

                    (vr, loanApplication) = await ValidateFraudControlAsync(loanApplication, fraudControlPostModel!, logs, settingsLog, settings);
                }
                break;

            case LoanApplicationStep.LoanLanding:
                if (postModel.ActionResult)
                {
                    await EnsureOldFormIsNotPostedAsync(currentStep, vr, loanApplication);
                    (vr, loanApplication) = await ValidateFirstPageAsync(logs, loanApplication, settingsLog);
                }
                else
                {
                    vr.NextStep = LoanApplicationStep.ApplicationCancellationFinal;
                    await loanApplicationService.FinishLoanAsync(loanApplication, logs);
                    var loanStatusLSTCPA = loanStatusTypeLookup.Single(o => o.Abrv == "LSTCPA");
                    loanApplication = loanApplicationService.SetLoanApplicationStatus(
                        logs,
                        loanApplication,
                        loanStatusLSTCPA,
                        LogSortOrder.LoanStatusCancelPreApp);
                }
                break;

            case LoanApplicationStep.ApplicationQualified:
                if (!loanApplication.ActionResult)
                {
                    vr.NextStep = LoanApplicationStep.ApplicationCancellationFinal;
                    await loanApplicationService.FinishLoanAsync(loanApplication, logs);
                    var loanStatusLSTCAD = loanStatusTypeLookup.Single(o => o.Abrv == "LSTCAD");
                    loanApplication = loanApplicationService.SetLoanApplicationStatus(
                        logs,
                        loanApplication,
                        loanStatusLSTCAD,
                        LogSortOrder.LoanStatusLoanCancelledAfterDE);
                    break;
                }
                if (await ValidatePendingLoansAsync(loanApplication, vr, logs))
                {
                    await EnsureOldFormIsNotPostedAsync(currentStep, vr, loanApplication);
                    try
                    {
                        vr = await ValidateApplicationQualifiedAsync(loanApplication, logs, settingsLog);

                        if (!guidExtensionService.IsNullOrEmpty(loanApplication.PurposeOfLoanId))
                        {
                            var purp = await qCashContext.PurposeOfLoanTypes.Where(p => !p.IsDeleted)
                                .FirstOrDefaultAsync(i => i.Id.Equals(loanApplication.PurposeOfLoanId.GetValueOrDefault()));
                            if (purp != null)
                            {
                                loanApplicationService.AddLogMessage(logs, "Purpose of Loan: " + purp.Name, DateTime.UtcNow);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var fiRoute = financialInstitution.FinancialInstitutionConfigurations.First();
                        await errorHelper.StoreErrorAsync(ex, fiRoute);
#if DEBUG
                        vr.Errors.AddError(ex.StackTrace ?? string.Empty);
#else
                        vr.Errors.AddError(ex.Message);
#endif
                    }

                    if (vr.Errors.Errors.Count > 0)
                    {
                        loanApplication = await ClearSelectedProductAsync(loanApplication);
                    }
                }
                break;

            case LoanApplicationStep.TILAEmail:
            case LoanApplicationStep.TILA:
                if (!loanApplication.ActionResult && !sendEmail.GetValueOrDefault())
                {
                    vr.NextStep = LoanApplicationStep.TILACancellationFinal;
                    await EnsureOldFormIsNotPostedAsync(currentStep, vr, loanApplication);
                    await loanApplicationService.FinishLoanAsync(loanApplication, logs);
                    var loanStatusLSTDET = loanStatusTypeLookup.Single(o => o.Abrv == "LSTDET");
                    loanApplication = loanApplicationService.SetLoanApplicationStatus(
                        logs,
                        loanApplication,
                        loanStatusLSTDET,
                        LogSortOrder.LoanStatusDeclineTILA,
                        LogDetailItemReportGroupEnum.None);
                    break;
                }

                // Validate Create and Fund request.
                if (await qCashContext.CreateAndFundRequests.CountAsync(p => p.Id.Equals(loanApplication.Id)) > 0)
                {
                    logs.AddLogDetail("Member tried to post TILA multiple times");
                    await SaveLoanApplicationLogMessagesAsync(loanApplication, logs);

                    var value = await memberInterfaceHelper.GetTextAsync(InterfaceDefaultTextsEnum.PostToOldForm_Static.Abrv, true, loanApplication.SelectedLanguageId);
                    vr.Errors.AddError(value ?? "PostToOldForm");
                    throw vr.Errors;
                }

                // Break if not valid.
                if (!await ValidatePendingLoansAsync(loanApplication, vr, logs))
                {
                    break;
                }

                // Break if email sent.
                if (sendEmail.GetValueOrDefault())
                {
                    vr.NextStep = LoanApplicationStep.TILAEmail;
                    break;
                }

                //this section probably can just be removed - credit card related
                if (loanApplication.MemberConsentCreditCard() != null)
                {
                    if (!loanApplication.MemberConsentCreditCard()!.Accepted)
                    {
                        var language = loanApplicationService.GetLanguage(loanApplication.SelectedLanguageId);
                        var formattedInterfaceTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(language.Id, null, "MemberInterfaceStaticTextLookup.MustAcceptCreditCardStatement");
                        vr.Errors.AddError(formattedInterfaceTexts.First(p => p.Key == "MustAcceptCreditCardStatement").Value);
                    }
                    else
                    {
                        logs.AddLogDetail(LogType.Clean, LogSortOrder.AcceptCreditCard, string.Format($"Credit Card Acceptance: {(loanApplication.MemberConsentCreditCard() != null && loanApplication.MemberConsentCreditCard()!.Accepted ? "Yes" : "No")}"));
                    }
                }

                loanApplicationService.AddLogMessage(logs,
                    $"TILA {(loanApplication.ActionResult ? "Accepted" : "Declined")} {DateTime.UtcNow.FromUTC(timeZone).ToString()}", DateTime.UtcNow);
                if (vr.Errors.Errors.Count < 1)
                {
                    await EnsureOldFormIsNotPostedAsync(currentStep, vr, loanApplication);

                    var maskedName = "TILA Disclosure: " + await filenameTemplateService.GetDocumentNameAsync(LoanApplicationDocumentType.TILA, loanApplication, redactInformation: true);
                    var loanStatusLSTAT = loanStatusTypeLookup.Single(o => o.Abrv == "LSTAT");
                    loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusLSTAT, LogSortOrder.LoanStatusAcceptTILA, LogDetailItemReportGroupEnum.None);

                    loanApplicationService.AddLogMessage(logs, maskedName, DateTime.UtcNow, LogType.Clean, LogSortOrder.TILADisclosure);
                    var dataCollectionSettings = financialInstitution.DataCollectionSettings.First();
                    if (dataCollectionSettings.IsDataCollectionEnabled && settings.UseFirebirdUi)
                    {
                        vr.NextStep = LoanApplicationStep.DataCollection;
                    }
                    else
                    {
                        //enable SendNextDisbursementDate with #8341
                        //var newMWLoan = await CreateNewLoanAsync(loanApplication, settings.SendNextDisbursementDate);
                        var newMWLoan = await CreateNewLoanAsync(loanApplication, false);

                        if (loanApplication.LoanApplicationSso != null && loanApplication.LoanApplicationSso.IsTest)
                        {
                            // If this is a SSO Test loan, check if we need to call CreateAndFund call.
                            var ssoTestSetup = financialInstitutionService.GetSSOTest();
                            vr.NextStep = ssoTestSetup.Fund
                                ? await loanApplicationService.CreateAndFundLoanAsync(logs, loanApplication, newMWLoan, settingsLog)
                                : LoanApplicationStep.FundingComplete;
                        }
                        else
                        {
                            vr.NextStep = await loanApplicationService.CreateAndFundLoanAsync(logs, loanApplication, newMWLoan, settingsLog);
                        }

                        if (settings.UseFirebirdUi && settings.PaymentGuard &&
                            vr.NextStep is LoanApplicationStep.FundingComplete or LoanApplicationStep.FundingPending)
                        {
                            loanApplicationService.AddLogMessage(
                                logs,
                                "Is covered by TruStage (Payment Guard) insurance!",
                                DateTime.UtcNow,
                                LogType.AdminRequired);
                            loanApplication.PaymentGuardReportStatusId = (int)Enums.PaymentGuardReportStatus.NotSent;
                            if (settings.AutomaticDisclosureEmail)
                            {
                                await SendPaymentGuardEmailAsync(loanApplication);
                            }
                        }
                    }

                    await StoreTILADocumentAsync(logs, loanApplication, settingsLog, settings);
                }
                else
                {
                    vr.NextStep = LoanApplicationStep.TILA;
                }

                break;

            case LoanApplicationStep.DataCollection:
                if (!loanApplication.ActionResult && !sendEmail.GetValueOrDefault())
                {
                    vr.NextStep = LoanApplicationStep.DataCollectionDeclined;
                    await EnsureOldFormIsNotPostedAsync(currentStep, vr, loanApplication);
                    await loanApplicationService.FinishLoanAsync(loanApplication, logs);
                    var loanStatusLSTDEDC = loanStatusTypeLookup.Single(o => o.Abrv == "LSTDEDC");
                    loanApplication = loanApplicationService.SetLoanApplicationStatus(
                        logs,
                        loanApplication,
                        loanStatusLSTDEDC,
                        LogSortOrder.DataCollectionDeclined,
                        LogDetailItemReportGroupEnum.None);
                    break;
                }

                // Break if not valid.
                if (!await ValidatePendingLoansAsync(loanApplication, vr, logs))
                {
                    break;
                }

                if (!ValidateDataCollection(financialInstitution, postModel, vr, logs))
                {
                    break;
                }

                await ProcessDataCollectionAsync(financialInstitution, (DataCollectionApiModelPost)postModel, loanApplication);

                //enable SendNextDisbursementDate with #8341
                var newLoan = await CreateNewLoanAsync(loanApplication, false);
                //var newLoan = await CreateNewLoanAsync(loanApplication, settings.SendNextDisbursementDate);

                if (loanApplication.LoanApplicationSso != null && loanApplication.LoanApplicationSso.IsTest)
                {
                    // If this is a SSO Test loan, check if we need to call CreateAndFund call.
                    var ssoTestSetup = financialInstitutionService.GetSSOTest();
                    vr.NextStep = ssoTestSetup.Fund
                        ? await loanApplicationService.CreateAndFundLoanAsync(logs, loanApplication, newLoan, settingsLog)
                        : LoanApplicationStep.FundingComplete;
                }
                else
                {
                    vr.NextStep = await loanApplicationService.CreateAndFundLoanAsync(logs, loanApplication, newLoan, settingsLog);
                }

                if (settings.UseFirebirdUi
                    && settings.PaymentGuard
                    && vr.NextStep is LoanApplicationStep.FundingComplete or LoanApplicationStep.FundingPending)
                {
                    loanApplicationService.AddLogMessage(
                        logs,
                        "Is covered by TruStage (Payment Guard) insurance!",
                        DateTime.UtcNow,
                        LogType.AdminRequired);
                    loanApplication.PaymentGuardReportStatusId = (int)Enums.PaymentGuardReportStatus.NotSent;
                    if (settings.AutomaticDisclosureEmail)
                    {
                        await SendPaymentGuardEmailAsync(loanApplication);
                    }
                }

                break;
            case LoanApplicationStep.AANEmail:
            case LoanApplicationStep.AdverseAction:
                vr.NextStep = sendEmail.GetValueOrDefault() ? LoanApplicationStep.AANEmail : LoanApplicationStep.AdverseAction;
                break;
            case LoanApplicationStep.Maintenance:
                var loanStatusLSTM = loanStatusTypeLookup.Single(o => o.Abrv == "LSTM");
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusLSTM, LogSortOrder.None, LogDetailItemReportGroupEnum.None);
                break;
            case LoanApplicationStep.QCFAudit:
                var loanStatusLSTQCFA = loanStatusTypeLookup.Single(o => o.Abrv == "LSTQCFA");
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusLSTQCFA, LogSortOrder.None, LogDetailItemReportGroupEnum.None);
                break;
            case LoanApplicationStep.FundingPending:
                break;
            default:
                await ValidatePendingLoansAsync(loanApplication, vr, logs);
                break;
        }

        if (vr.Errors != null && vr.Errors.Errors.Count > 0)
        {
            foreach (var item in vr.Errors.Errors)
            {
                loanApplicationService.AddLogMessage(logs, item, DateTime.UtcNow);
            }

            await SaveLoanApplicationLogMessagesAsync(loanApplication, logs);
            loanApplicationService.SaveSettingsLog(settingsLog);

            throw vr.Errors;
        }

        #region Persist wizard data

        await EnsureOldFormIsNotPostedAsync(currentStep, vr, loanApplication);
        loanApplication.CurrentStep = (int)vr.NextStep;
        loanApplication.NextStep = loanApplication.CurrentStep;
        var nextStep = vr.NextStep;

        await qCashContext.SaveChangesAsync();
        UpdateLoanApplicationInWizard(loanApplication); //see note in method body - this should just be SaveChanges now

        loanApplicationService.SaveSettingsLog(settingsLog);

        #region Store documents

        switch (vr.NextStep)
        {
            case LoanApplicationStep.EConsentDisclosure:
                loanApplication = await GetLoanApplicationForWizardAsync(loanApplication.FinancialInstitutionMemberId);
                await StoreEConsentDocumentAsync(logs, loanApplication);
                break;

            case LoanApplicationStep.Application:
                var priorEConsent = loanApplication.MemberConsentPriorEConsent()?.PreviouslyAccepted.HasValue == true;
                if (settings.EConsent && !priorEConsent)
                {
                    loanApplication = await GetLoanApplicationForWizardAsync(loanApplication.FinancialInstitutionMemberId);
                    await StoreEConsentDocumentAsync(logs, loanApplication, true);
                }
                break;

            case LoanApplicationStep.TILA:
                try
                {
                    MemberAccount? acc = null;
                    if (loanApplication.LoanApplicationMemberSelections.Count > 0)
                    {
                        var laId = loanApplication.DefaultAccountForTransactionId.GetValueOrDefault();
                        acc = await qCashContext.MemberAccounts.Where(p => p.Id.Equals(laId)).OrderBy(s => s.DateOpened).FirstOrDefaultAsync();
                    }

                    settingsLog = [];
                    await StoreLoanDisclosureDocumentAsync(logs, loanApplication, acc != null ? acc.AccountId : string.Empty, vr);

                    nextStep = vr.NextStep;
                    loanApplicationService.SaveSettingsLog(settingsLog);
                }
                catch (Exception ex)
                {
                    loanApplication.CurrentStep = loanApplication.NextStep = (int)LoanApplicationStep.ApplicationQualified;

                    loanApplication = await ClearSelectedProductAsync(loanApplication);

                    var fiRoute = financialInstitution.FinancialInstitutionConfigurations.First();
                    await errorHelper.StoreErrorAsync(ex, fiRoute);
#if DEBUG
                    var errorMessage = ex.Message + ex.StackTrace;
#else
                    var errorMessage = ex.Message;
#endif
                    loanApplicationService.AddLogMessage(logs, errorMessage, DateTime.UtcNow, group: LogDetailItemReportGroupEnum.Red);
                    await SaveLoanApplicationLogMessagesAsync(loanApplication, logs);

                    if (vr.Errors == null)
                    {
                        vr.Errors = new LoanApplicationException();
                    }
                    vr.Errors.AddError(errorMessage);

                    throw vr.Errors;
                }

                break;

            case LoanApplicationStep.AdverseAction:
                var ptimeAAN = DateTime.UtcNow;
                await StoreAdverseActionDocumentAsync(logs, loanApplication);
                await tokenService.ClearTempMemberDataAsync(loanApplication.FinancialInstitutionMemberId.ToString());

                if (settings.AutomaticDisclosureEmail)
                {
                    var language = loanApplicationService.GetLanguage(loanApplication.SelectedLanguageId);
                    var formattedInterfaceTexts = await memberProcessInterfaceService.GetFormattedInterfaceTextsForEmailsAsync(loanApplication, language.Id);
                    await loanApplicationService.SendDocumentEmailAsync(
                        loanApplication,
                        loanApplicationService.FormatEmailValue(formattedInterfaceTexts.First(p => p.Key == "DocEmailTemplate").Value, LoanApplicationEmailType.AAN, loanApplication.FinancialInstitutionMember),
                        loanApplicationService.FormatEmailValue(formattedInterfaceTexts.First(p => p.Key == "DocEmailSubject").Value, LoanApplicationEmailType.AAN, loanApplication.FinancialInstitutionMember),
                        LoanApplicationEmailType.AAN
                    );
                }
                loanApplicationService.AddLogMessage(logs, $"Generate AAN finished: {(DateTime.UtcNow - ptimeAAN).TotalSeconds}s", DateTime.UtcNow);
                break;

            default:
                break;
        }

        #endregion Store documents

        string message = string.Format($"Posted {currentStep.ToString()} {DateTime.UtcNow.FromUTC(timeZone).ToString()}");
        if (vr.NextStep.ToString() != "TILACancel" && vr.NextStep.ToString() != "ApplicationQualifiedCancel" && vr.NextStep.ToString() != "ApplicationCancel")
        {
            loanApplicationService.AddLogMessage(logs, message, DateTime.UtcNow, LogType.AdminRequired);
        }

        #endregion Persist wizard data

        var loanStatusLSTCMP = loanStatusTypeLookup.Single(o => o.Abrv == "LSTCMP");
        var loanStatusLSTFP = loanStatusTypeLookup.Single(o => o.Abrv == "LSTFP");
        if (loanApplication.LoanStatusId == loanStatusLSTCMP.Id || loanApplication.LoanStatusId == loanStatusLSTFP.Id)
        {
            await IncreaseModelUsageCountAsync(loanApplication, logs);
        }

        await SaveLoanApplicationLogMessagesAsync(loanApplication, logs);
        return nextStep;
    }

    private async Task IncreaseModelUsageCountAsync(Data.Models.LoanApplication la, IList<LoanApplicationLogDetail> logs)
    {
        //was direct sql
        //update DecisionModel set UsageCount = UsageCount + 1, DateUpdated = GetDate() WHERE DecisionModel.Id = @DecisionModelId
        //Update LoanApplicationTrace set AmountBorrowed = @amountBorrowed, DateUpdated = GetDate() where LoanApplicationId = @loanApplicationId
        //  and DecisionModelId = @decisionModelId

        var decisionModel = await qCashContext.DecisionModels.FirstOrDefaultAsync(o => o.Id == la.DecisionModelId);
        if (decisionModel != null)
        {
            decisionModel.UsageCount += 1;
            decisionModel.DateUpdatedUtc = DateTime.UtcNow;

            var loanApplicationTrace = await qCashContext.LoanApplicationTraces.FirstOrDefaultAsync(o => o.LoanApplicationId == la.Id && o.DecisionModelId == decisionModel.Id);
            if (loanApplicationTrace != null)
            {
                loanApplicationTrace.AmountBorrowed = la.AmountBorrowed;
                loanApplicationTrace.DateUpdatedUtc = DateTime.UtcNow;
            }

        }

        try
        {
            await qCashContext.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            logs.AddLogDetail($"Problem with updating Model UsageCount and LoanApplicationTrace: {ex.Message.Replace('\'', '"')}");
        }
    }

    private async Task<(ValidationResult, Data.Models.LoanApplication)> ValidateFirstPageAsync(
        List<LoanApplicationLogDetail> logs,
        Data.Models.LoanApplication loanApplication,
        IList<LoanApplicationSettingsLog> settingsLog)
    {
        //removing allowing loanApplication nullable as it doesn't make sense in the only place this is currently used, ExecuteWizardStepAsync
        //if (loanApplication == null)
        //{
        //    return (new ValidationResult(), null);
        //}

        loanApplication = await GetNextStepAfterExclusionsAsync(loanApplication, logs, settingsLog);

        return (new ValidationResult { NextStep = (LoanApplicationStep)loanApplication.NextStep }, loanApplication);
    }

    private async Task<(ValidationResult, Data.Models.LoanApplication)> ValidateFraudControlAsync(
        Data.Models.LoanApplication loanApplication,
        FraudControlApiModelPost fraudControlPostModel,
        IList<LoanApplicationLogDetail> logs,
        IList<LoanApplicationSettingsLog> settingsLog,
        Setting setting)
    {
        var result = new ValidationResult();
        var isValid = true;

        var fraudControl = loanApplication.LoanApplicationFraudControl;

        if (fraudControl == null)
        {
            logs.AddLogDetail(LogType.Admin, "Fraud Control: Missing");
            result.NextStep = LoanApplicationStep.FraudControl;
            return (result, loanApplication);
        }

        fraudControl.SubmitNo++;
        await qCashContext.SaveChangesAsync();

        if (fraudControl.CodeExpirationDate <= DateTime.UtcNow)
        {
            logs.AddLogDetail(LogType.Admin, "Fraud Control: Code Expired");
            result.NextStep = LoanApplicationStep.FraudControlCodeExpired;
            return (result, loanApplication);
        }

        logs.AddLogDetail(
            LogType.Admin,
            $"Fraud Control: Submit attempt {fraudControl.SubmitNo} of {fraudControl.SubmitThreshold}");

        if (fraudControl.SubmitNo > fraudControl.SubmitThreshold)
        {
            logs.AddLogDetail(LogType.Admin, "Fraud Control: Submit threshold exceeded");
            result.NextStep = LoanApplicationStep.FraudControl;
            return (result, loanApplication);
        }

        const int ssnCodeLength = 5;
        var ssn = await taxIdService.GetTaxIdByFinancialInstitutionMemberIdAsync(loanApplication.FinancialInstitutionMember.Id);
        ssn = ssn[^ssnCodeLength..];

        if (string.IsNullOrEmpty(fraudControlPostModel.SSNCode)
            || fraudControlPostModel.SSNCode.Length != ssnCodeLength
            || ssn != fraudControlPostModel.SSNCode)
        {
            if (string.IsNullOrEmpty(fraudControlPostModel.SSNCode) || fraudControlPostModel.SSNCode.Length != ssnCodeLength)
            {
                logs.AddLogDetail(LogType.Admin, "Fraud Control: SSN length does not match");
            }
            else
            {
                logs.AddLogDetail(LogType.Admin, "Fraud Control: Wrong SSN");
            }

            var language = loanApplicationService.GetLanguage(loanApplication.SelectedLanguageId);
            var formattedInterfaceTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(
                language.Id,
                null,
                "MemberInterfaceStaticText.FraudControlWrongSSN");
            result.Errors.AddError(formattedInterfaceTexts.First(p => p.Key == "FraudControlWrongSSN").Value);

            isValid = false;
        }

        const int validationCodeLength = 4;

        if (string.IsNullOrEmpty(fraudControlPostModel.ValidationCode)
            || fraudControlPostModel.ValidationCode.Length != validationCodeLength
            || fraudControlPostModel.ValidationCode != fraudControl.Code)
        {
            if (string.IsNullOrEmpty(fraudControlPostModel.ValidationCode) || fraudControlPostModel.ValidationCode.Length != validationCodeLength)
            {
                logs.AddLogDetail(LogType.Admin, "Fraud Control: Validation Code length does not match");
            }
            else
            {
                logs.AddLogDetail(LogType.Admin, "Fraud Control: Wrong Validation Code");
            }

            var language = loanApplicationService.GetLanguage(loanApplication.SelectedLanguageId);
            var formattedInterfaceTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(
                language.Id,
                loanApplication: null,
                "MemberInterfaceStaticText.FraudControlWrongVerificationCode");
            result.Errors.AddError(formattedInterfaceTexts.First(p => p.Key == "FraudControlWrongVerificationCode").Value);

            isValid = false;
        }

        // Just redirect to the FraudControl step without errors.
        if (!isValid && fraudControl.SubmitNo == fraudControl.SubmitThreshold)
        {
            result.Errors.Errors.Clear();
        }

        if (isValid)
        {
            var availableProducts = await loanApplicationService.GetAvailableProductsAfterProductsExclusionsAsync(loanApplication, setting);
            (result, loanApplication) = await CallDecisionEngineAsync(logs, loanApplication, result, settingsLog, availableProducts);
        }
        else
        {
            result.NextStep = LoanApplicationStep.FraudControl;
        }

        return (result, loanApplication);
    }

    private async Task<(LoanApplicationStep, string)> SetNextStepFraudControlAndSendNotificationAsync(
        Data.Models.LoanApplication loanApplication,
        FraudControlSetting fraudControlSetting,
        IList<LoanApplicationLogDetail> logs)
    {
        string errors = string.Empty;
        if (loanApplication.LoanApplicationFraudControl == null)
        {
            var laFraudControl = new LoanApplicationFraudControl
            {
                Id = loanApplication.Id,
                ResendThreshold = fraudControlSetting.ResendThreshold,
                SubmitThreshold = fraudControlSetting.SubmitThreshold,
                Code = CodeGenerator.Generate(4),
                CodeExpirationDate = DateTime.UtcNow.AddMinutes(fraudControlSetting.CodeValidityMinutesThreshold),
            };

            loanApplication.LoanApplicationFraudControl = laFraudControl;
            await qCashContext.SaveChangesAsync();
        }
        else
        {
            loanApplication.LoanApplicationFraudControl.Code = CodeGenerator.Generate(4);
            loanApplication.LoanApplicationFraudControl.CodeExpirationDate = DateTime.UtcNow.AddMinutes(fraudControlSetting.CodeValidityMinutesThreshold);
            loanApplication.LoanApplicationFraudControl.ResendNo++;

            //UpdateLoanApplicationFraudControl(loanApplication.LoanApplicationFraudControl);
            await qCashContext.SaveChangesAsync();

            if (loanApplication.LoanApplicationFraudControl.ResendNo >=
                loanApplication.LoanApplicationFraudControl.ResendThreshold)
            {
                errors = "Amount of resend OTP reached its limit.";
                return (LoanApplicationStep.FraudControl, errors);
            }
        }

        var preferredDelivery = fraudControlSetting.PreferredDelivery;

        if (!Enum.TryParse(preferredDelivery, out FraudControlPreferredDeliveryMethod deliveryMethod))
        {
            deliveryMethod = FraudControlPreferredDeliveryMethod.Both;
        }

        var language = loanApplicationService.GetLanguage(loanApplication.SelectedLanguageId);
        var notification = await memberProcessInterfaceService
            .GetFormattedInterfaceTextsForFraudControlNotificationAsync(loanApplication, language.Id);
        var subject = notification["FraudControlNotificationSubject"];
        var body = notification["FraudControlNotificationTemplate"];

        switch (deliveryMethod)
        {
            case FraudControlPreferredDeliveryMethod.Email:
            {
                var trySend = await TrySendOtpByEmailAsync(loanApplication, logs, subject, body);
                if (!trySend.success)
                {
                    if (!TrySendOtpBySms(loanApplication, logs, body, out var smsErrorMessage))
                    {
                        errors += trySend.errorMessage + $"<br/>{smsErrorMessage}";
                        return (LoanApplicationStep.FraudControl, errors);
                    }
                }
                break;
            }
            case FraudControlPreferredDeliveryMethod.SMS:
            {
                if (!TrySendOtpBySms(loanApplication, logs, body, out errors))
                {
                    var trySend = await TrySendOtpByEmailAsync(loanApplication, logs, subject, body);
                    if (!trySend.success)
                    {
                        errors += $"<br/>{trySend.errorMessage}";
                        return (LoanApplicationStep.FraudControl, errors);
                    }
                }
                break;
            }
            case FraudControlPreferredDeliveryMethod.Both:
            {
                TrySendOtpBySms(loanApplication, logs, body, out var smsErrorMessage);

                var trySend = await TrySendOtpByEmailAsync(loanApplication, logs, subject, body);

                if (!string.IsNullOrWhiteSpace(trySend.errorMessage) && !string.IsNullOrWhiteSpace(smsErrorMessage))
                {
                    errors = $"{trySend.errorMessage}<br/>{smsErrorMessage}";
                    return (LoanApplicationStep.FraudControl, errors);
                }
                break;
            }
        }

        errors = "";
        return (LoanApplicationStep.FraudControl, errors);
    }


    private async Task<(bool success, string errorMessage)> TrySendOtpByEmailAsync(
        Data.Models.LoanApplication loanApplication,
        IList<LoanApplicationLogDetail> logs,
        string subject, string body)
    {
        var destination = loanApplication.FinancialInstitutionMember.Email;
        if (string.IsNullOrWhiteSpace(destination))
        {
            var errorMessage = "Email is not specified";
            return (false, errorMessage);
        }

        var notification = new LoanApplicationNotification
        {
            LoanApplicationId = loanApplication.Id,
            NotificationChanel = nameof(FraudControlPreferredDeliveryMethod.Email),
            NotificationType = nameof(LoanApplicationEmailType.FraudControl),
        };

        var financialInstitution = financialInstitutionService.FinancialInstitution;
        var mailFrom = new MailAddress(financialInstitutionService.GetFinancialInstitutionNoReplyAddress());
        if (!string.IsNullOrWhiteSpace(financialInstitution.NoReplyDisplayName))
        {
            mailFrom = new MailAddress(
                financialInstitutionService.GetFinancialInstitutionNoReplyAddress(),
                financialInstitution.NoReplyDisplayName);
        }

        var mailMessage = new MailMessage
        {
            From = mailFrom,
            Subject = subject,
            Body = body,
            IsBodyHtml = true,
        };

        mailMessage.To.Add(destination);
        var trySend = await notificationService.TrySendEmailAsync(mailMessage);
        notification.Sent = trySend.Item1;

        qCashContext.LoanApplicationNotifications.Add(notification);
        await qCashContext.SaveChangesAsync();

        if (notification.Sent)
        {
            logs.AddLogDetail(LogType.AdminRequired, $"{notification.NotificationChanel} sent to: {destination}");
        }
        else
        {
            var errorMessage =
                $"Error sending {notification.NotificationChanel} to: {destination}";
            logs.AddLogDetail(LogType.AdminRequired, $"{errorMessage}. Failed with error: {trySend.Item2}");
            return (false, errorMessage);
        }

        return (true, string.Empty);
    }

    private bool TrySendOtpBySms(
        Data.Models.LoanApplication loanApplication,
        IList<LoanApplicationLogDetail> logs,
        string body,
        out string errorMessage)
    {
        var destination = loanApplication.FinancialInstitutionMember.PhoneNumber;
        if (string.IsNullOrWhiteSpace(destination))
        {
            errorMessage = "Phone number is not specified";
            return false;
        }

        var notification = new LoanApplicationNotification
        {
            LoanApplicationId = loanApplication.Id,
            NotificationChanel = nameof(FraudControlPreferredDeliveryMethod.SMS),
            NotificationType = nameof(LoanApplicationEmailType.FraudControl),
            Sent = notificationService.TrySendSMS(applicationOptions.Value.QCashNotificationPhoneNumber,
                destination,
                body.StripHtmlTags(),
                out var sendErrorMessage),
        };

        qCashContext.LoanApplicationNotifications.Add(notification);
        qCashContext.SaveChanges();

        if (notification.Sent)
        {
            logs.AddLogDetail(LogType.AdminRequired, $"{notification.NotificationChanel} sent to: {destination}");
        }
        else
        {
            errorMessage =
                $"Error sending {notification.NotificationChanel} to: {destination}";
            logs.AddLogDetail(LogType.AdminRequired, $"{errorMessage}. Failed with error: {sendErrorMessage}");
            return false;
        }

        errorMessage = "";
        return true;
    }

    private async Task<(ValidationResult, Data.Models.LoanApplication)> ValidateApplicationAsync(
        IList<LoanApplicationLogDetail> logs,
        Data.Models.LoanApplication loanApplication,
        IList<LoanApplicationSettingsLog> settingsLog,
        Setting? setting,
        FraudControlSetting fraudControlSetting,
        bool eConsentStatementAccepted,
        bool smsConsentStatementAccepted)
    {
        var result = new ValidationResult();

        var language = loanApplicationService.GetLanguage(loanApplication.SelectedLanguageId);
        if (setting == null)
        {
            var formattedInterfaceTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(language.Id, null, "MemberInterfaceStaticText.NoSettings");
            result.Errors.AddError(formattedInterfaceTexts.First(p => p.Key == "NoSettings").Value);

            return (result, loanApplication);
        }

        var shareMemberAccounts =
            GetShareMemberAccountsByFIMemberIdAndAccountId(loanApplication.FinancialInstitutionMemberId,
                    loanApplication.AccountId,
                    setting.IncludeDormantAccounts,
                    setting.LoanApplicationFeeAvailableBalanceCheck ? setting.LoanApplicationFee : null)
                .Select(x => x.Id)
                .ToList();

        if (loanApplication.DefaultAccountForTransactionId.HasValue
            && !shareMemberAccounts.Contains(loanApplication.DefaultAccountForTransactionId.Value))
        {
            shareMemberAccounts.Add(loanApplication.DefaultAccountForTransactionId.Value);
        }

        var selectedAccount = loanApplication.SelectedAccountForTransactionId;

        if (!setting.AllowMemberAccountSelection || loanApplication.SelectedAccountForTransactionId == null)
        {
            loanApplication.SelectedAccountForTransactionId = loanApplication.DefaultAccountForTransactionId;
        }
        else if ((!shareMemberAccounts.Any() && !guidExtensionService.IsNullOrEmpty(selectedAccount))
                 || (shareMemberAccounts.Any() && (guidExtensionService.IsNullOrEmpty(selectedAccount) ||
                                                   !shareMemberAccounts.Contains(selectedAccount!.Value))))
        {
            var formattedInterfaceTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(
                language.Id, null, "MemberInterfaceStaticText.SelectedAccountErrorMessage");
            result.Errors.AddError(formattedInterfaceTexts
                .First(p => p.Key == "SelectedAccountErrorMessage").Value);
        }

        var showSmsConsent = fraudControlSetting.IsEnabled
                             && (fraudControlSetting.PreferredDelivery == nameof(FraudControlPreferredDeliveryMethod.SMS)
                                 || fraudControlSetting.PreferredDelivery == nameof(FraudControlPreferredDeliveryMethod.Both));
        if (showSmsConsent && !smsConsentStatementAccepted)
        {
            var formattedInterfaceTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(
                language.Id, null, "MemberInterfaceStaticText.MustAcceptSmsConsentStatement");
            result.Errors.AddError(formattedInterfaceTexts
                .First(p => p.Key == "MustAcceptSmsConsentStatement").Value);
        }

        if (!(loanApplication.MemberConsentPriorEConsent() == null && setting.EConsent) &&
            !eConsentStatementAccepted)
        {
            var formattedInterfaceTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(
                language.Id, null, "MemberInterfaceStaticText.MustAcceptEConsentStatement");
            result.Errors.AddError(formattedInterfaceTexts
                .First(p => p.Key == "MustAcceptEConsentStatement").Value);
        }

        if (setting.Bankruptcy != nameof(Bankruptcy.Off))
        {
            if (loanApplication.IsBankrupt.HasValue)
            {
                loanApplicationService.AddLogMessage(
                    logs,
                    $"IsBankrupt: {(loanApplication.IsBankrupt.Value ? "Yes" : "No")}",
                    DateTime.UtcNow,
                    LogType.Clean,
                    LogSortOrder.IsBankrupt);
            }
            else
            {
                var formattedInterfaceTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(
                    language.Id, null, "MemberInterfaceStaticText.ProvideBankruptcyStatus");
                result.Errors.AddError(formattedInterfaceTexts
                    .First(p => p.Key == "ProvideBankruptcyStatus").Value);
            }
        }

        if (setting.Mla != nameof(MLA.Off) && !loanApplication.ActiveDuty.HasValue)
        {
            var formattedInterfaceTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(
                language.Id, null, "MemberInterfaceStaticText.ProvideMLAStatus");
            result.Errors.AddError(formattedInterfaceTexts.First(p => p.Key == "ProvideMLAStatus")
                .Value);
        }

        if (loanApplication.LoanApplicationSso != null && loanApplication.LoanApplicationSso.PreApproved.GetValueOrDefault()
                                                       && string.IsNullOrEmpty(loanApplication.MarketingCampaignCode)
                                                       && setting.UseManualCampaignCodeInput)
        {
            var formattedInterfaceTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(
                language.Id, null, "MemberInterfaceStaticText.MissingCampaignCodeValidationNotification");
            result.Errors.AddError(formattedInterfaceTexts
                .First(p => p.Key == "MissingCampaignCodeValidationNotification").Value);
        }

        //validate campaign code
        if (result.Errors.Errors.Count < 1 && !string.IsNullOrEmpty(loanApplication.MarketingCampaignCode))
        {
            var campaignCodeLogMessage = "";
            var lookupTextArray = new List<string>
            {
                $"MemberInterfaceStaticText.{InterfaceDefaultTextsEnum.InactiveCampaignCodeNotification_Static.Abrv}",
                $"MemberInterfaceStaticText.{InterfaceDefaultTextsEnum.InvalidCampaignCodeNotification_Static.Abrv}",
                $"MemberInterfaceStaticText.{InterfaceDefaultTextsEnum.NotAllowedToUseCampaignCodeNotification_Static.Abrv}",
                $"MemberInterfaceStaticText.{InterfaceDefaultTextsEnum.CampaignCodeUsedNotification_Static.Abrv}",
            };

            var formattedInterfaceTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(
                language.Id,
                loanApplication,
                lookupTextArray);

            // check if code exists
            var campaignCode = await qCashContext.CampaignCodes.FirstOrDefaultAsync(p => p.Code == loanApplication.MarketingCampaignCode);
            if (campaignCode == null)
            {
                result.Errors.AddError(formattedInterfaceTexts["InvalidCampaignCodeNotification"]);
                campaignCodeLogMessage = "Invalid Campaign Code";
            }
            else
            {
                // check if code expired
                var clientTimeZone = qCashContext.Settings.FirstOrDefault()?.TimeZone ?? string.Empty;
                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);
                var clientTime = DateTime.UtcNow.FromUTC(timeZone);
                var campaignEffectiveDateInClientZone = campaignCode.EffectiveDate.FromUTC(timeZone);
                var campaignEndDateInClientZone = campaignCode.EndDate.FromUTC(timeZone);

                var expired = clientTime < campaignEffectiveDateInClientZone ||
                              clientTime > campaignEndDateInClientZone;
                if (expired || !campaignCode.IsActive)
                {
                    result.Errors.AddError(formattedInterfaceTexts["InactiveCampaignCodeNotification"]);
                    campaignCodeLogMessage = "Inactive Campaign Code";
                }
                else
                {
                    // check if user is allowed to use this code
                    var campaignCodeAccount = loanApplicationService.GetCampaignCodeAccountByIdAndMemberIdentifier(
                        campaignCode.Id,
                        await loanApplicationService.GetMemberIdentifierAsync(loanApplication));
                    if (campaignCodeAccount == null)
                    {
                        result.Errors.AddError(formattedInterfaceTexts["NotAllowedToUseCampaignCodeNotification"]);
                        campaignCodeLogMessage = "User not allowed to use this Campaign Code";
                    }
                    else
                    {
                        // check if user already used code
                        if (campaignCodeAccount.Status == nameof(CampaignCodeAccountStatus.USED))
                        {
                            result.Errors.AddError(formattedInterfaceTexts["CampaignCodeUsedNotification"]);
                            campaignCodeLogMessage = "User already used this Campaign Code";
                        }

                        if (campaignCodeAccount.Status == nameof(CampaignCodeAccountStatus.CANCELLED))
                        {
                            result.Errors.AddError(formattedInterfaceTexts["CampaignCodeUsedNotification"]);
                            campaignCodeLogMessage = "This Campaign Code is cancelled for the user.";
                        }
                    }
                }
            }

            loanApplicationService.AddLogMessage(
                logs,
                $"<b> Entered Campaign Code: {loanApplication.MarketingCampaignCode}</b>, " +
                $"Validation Result: {(string.IsNullOrEmpty(campaignCodeLogMessage) ? "Passed" : campaignCodeLogMessage)}",
                DateTime.UtcNow,
                LogType.Clean,
                LogSortOrder.ChosenCampaignCode
            );
        }

        if (result.Errors.Errors.Count < 1)
        {
            if (loanApplication.IsBankrupt.HasValue && loanApplication.IsBankrupt.Value)
            {
                result.NextStep = LoanApplicationStep.ExclusionByBankruptcyStatus;
                await loanApplicationService.FinishLoanAsync(loanApplication, logs);
            }
            else
            {
                loanApplicationService.InsertLoanApplicationSettingsLog(
                    settingsLog,
                    loanApplication.Id,
                    LoanApplicationSettingType.UseManualCampaignCodeInput,
                    setting.UseManualCampaignCodeInput);

                var militaryCheckString =
                    Enum.TryParse(setting.Mla, true, out MLA mlaSetting) && mlaSetting == MLA.Off
                        ? "Check set to OFF in Admin Portal"
                        : loanApplication.ActiveDuty.GetValueOrDefault()
                            ? "Yes"
                            : "No";

                logs.AddLogDetail(
                    LogType.Clean,
                    LogSortOrder.ActiveMilitary,
                    LogDetailItemReportGroupEnum.None,
                    $"Active Military: {militaryCheckString}");

                var activeProducts = await GetAllActiveProductsAsync();
                if (setting.LoanHub)
                {
                    activeProducts = loanApplicationService.GetAvailableProductsWhenLoanHubActive(activeProducts, loanApplication.SelectedLoanHubItemId);
                }

                var dto = new ProductsExclusionsValidatorDTO(
                    loanApplication, activeProducts, setting, new List<LoanApplicationLogDetail>(), true);

                if (!await productsExclusionsValidatorRunner.ValidateAsync(dto))
                {
                    result.NextStep = dto.ExclusionValidatorResult!.NewLoanApplicationStep;
                    await loanApplicationService.FinishLoanAsync(loanApplication, logs);
                }
                else
                {
                    //financial coaching / awareness slated for removal

                    //var financialCoachingSetting = SettingService.GetFinancialCoachingSettingsByFinancialInstitutionId(appCtx.ApplicationId).First();
                    //loanApplicationService.InsertLoanApplicationSettingsLog(
                    //    settingsLog,
                    //    loanApplication.Id,
                    //    LoanApplicationSettingType.AwarenessPage,
                    //    financialCoachingSetting.AwarenessPage);
                    loanApplicationService.InsertLoanApplicationSettingsLog(
                        settingsLog,
                        loanApplication.Id,
                        LoanApplicationSettingType.FraudControl,
                        fraudControlSetting.IsEnabled);

                    //if (financialCoachingSetting.AwarenessPage
                    //    && IsSuitableForFinancialCoaching(loanApplication, logs))
                    //{
                    //    result.NextStep = LoanApplicationStep.Awareness;
                    //}
                    //else
                    if (fraudControlSetting.IsEnabled)
                    {
                        (result.NextStep, var errors) = await SetNextStepFraudControlAndSendNotificationAsync(
                            loanApplication,
                            fraudControlSetting,
                            logs);
                        if (!string.IsNullOrWhiteSpace(errors))
                        {
                            result.Errors.AddError(errors);
                        }
                    }
                    else
                    {
                        (result, loanApplication) = await CallDecisionEngineAsync(
                            logs,
                            loanApplication,
                            result,
                            settingsLog,
                            dto.AvailableProducts);
                    }
                }
            }
        }
        else
        {
            result.NextStep = LoanApplicationStep.Application; //Return to current step
        }

        return (result, loanApplication);
    }

    public IReadOnlyList<ShareAccountApiModel> GetShareMemberAccountsByFIMemberIdAndAccountId(
        Guid financialInstitutionMemberId, string? accountId, bool includeDormantAccounts, decimal? loanApplicationFee)
    {
        if (financialInstitutionMemberId == Guid.Empty)
        {
            throw new ArgumentNullException(nameof(financialInstitutionMemberId), $"{nameof(financialInstitutionMemberId)} can not be null or empty!");
        }

        var hasMultipleBaseAccounts = financialInstitutionService.FinancialInstitution.HasMultipleBaseAccounts();
        if (!hasMultipleBaseAccounts && string.IsNullOrEmpty(accountId))
        {
            throw new ArgumentNullException(nameof(accountId), $"{nameof(accountId)} can not be null or empty!");
        }

        if (loanApplicationFee < 0)
        {
            throw new Exception($"{nameof(loanApplicationFee)} should be more than zero");
        }

        IReadOnlyList<MemberAccount> memberAccounts;

        if (hasMultipleBaseAccounts)
        {
            memberAccounts = loanApplicationService.GetMemberBaseAccounts(financialInstitutionMemberId)
                .Where(c => c.OwnerShipType == nameof(OwnerShipType.Primary))
                .SelectMany(c => c.MemberAccounts)
                .ToList();
        }
        else
        {
            memberAccounts = loanApplicationService.GetMemberAccountsByBaseAccount(financialInstitutionMemberId, accountId!).ToList();
        }

        if (memberAccounts.Count <= 0)
        {
            return [];
        }

        List<ShareAccountApiModel> checkingAccountsList;
        List<ShareAccountApiModel> savingAccountsList;

        if (loanApplicationFee.HasValue)
        {
            checkingAccountsList = MapFromMemberAccountsToShareAccountApiModels(financialInstitutionService
                .FilterCheckingAccounts(memberAccounts, loanApplicationFee.Value, includeDormantAccounts));
            savingAccountsList = MapFromMemberAccountsToShareAccountApiModels(financialInstitutionService
                .FilterSavingAccounts(memberAccounts, loanApplicationFee.Value, includeDormantAccounts));

            if (!checkingAccountsList.Any() && !savingAccountsList.Any())
            {
                checkingAccountsList = MapFromMemberAccountsToShareAccountApiModels(financialInstitutionService
                    .FilterCheckingAccounts(memberAccounts, includeDormantAccounts));
                savingAccountsList = MapFromMemberAccountsToShareAccountApiModels(financialInstitutionService
                    .FilterSavingAccounts(memberAccounts, includeDormantAccounts));
            }
        }
        else
        {
            checkingAccountsList = MapFromMemberAccountsToShareAccountApiModels(financialInstitutionService
                .FilterCheckingAccounts(memberAccounts, includeDormantAccounts));
            savingAccountsList = MapFromMemberAccountsToShareAccountApiModels(financialInstitutionService
                .FilterSavingAccounts(memberAccounts, includeDormantAccounts));
        }

        foreach (var account in checkingAccountsList)
        {
            account.OptionGroup = nameof(AccountType.CHECKING);
        }

        foreach (var account in savingAccountsList)
        {
            account.OptionGroup = nameof(AccountType.SAVINGS);
        }

        IEnumerable<ShareAccountApiModel> list = checkingAccountsList
            .Concat(savingAccountsList)
            .OrderBy(p => p.OptionGroup)
            .ThenBy(p => p.DateOpened);

        return list.ToList();
    }

    private static List<ShareAccountApiModel> MapFromMemberAccountsToShareAccountApiModels(IEnumerable<MemberAccount> memberAccounts) =>
    [
        ..memberAccounts.Select(account => new ShareAccountApiModel
        {
            Id = account.Id,
            AccountId = account.AccountId,
            AvailableBalance = account.AvailableBalance,
            DateCreated = account.DateCreatedUtc,
            DateOpened = account.DateOpened,
            Description = account.Description,
            LedgerBalance = account.LedgerBalance,
            //share.OptionGroup
            //share.DisplayBalance
            Type = account.Type,
        }),
    ];

    private async Task<(ValidationResult, Data.Models.LoanApplication)> CallDecisionEngineAsync(IList<LoanApplicationLogDetail> logs, Data.Models.LoanApplication loanApplication, ValidationResult result, IList<LoanApplicationSettingsLog> settingsLog, IList<Data.Models.Product> availableProducts)
    {
        var settings = await qCashContext.Settings.FirstAsync();

        try
        {
            // Apply loan application fee
            await loanApplicationService.ApplyFeeAsync(logs, loanApplication, LoanApplicationFeeType.ApplicationFee);

            //Validate Decision Engine
            loanApplicationService.AddLogMessage(logs, "Loan Status: <strong>Decision Engine</strong>", DateTime.UtcNow, LogType.Clean, LogSortOrder.SubmitDecisionEngine, LogDetailItemReportGroupEnum.Red);

            (result, loanApplication, var partialTransactionsPulled) = await ValidateDecisionEngineAsync(loanApplication, logs, settingsLog, availableProducts);
            if (result.NextStep == LoanApplicationStep.ApplicationQualified)
            {
                var calcResult = await loanApplicationHelper.GetLoanApplicationResultAllProductsAsync(loanApplication.Id);

                var productsMessage = calcResult?.QualifiedProducts.Aggregate(
                    new StringBuilder(),
                    (current, de) => current.Append($" {de.Product?.Name} $ {decimal.Round(de.DecisionEngine.QualifiedAmount ?? 0, 2):N2}"));

                var loanStatusLSTA = await qCashContext.LoanStatusTypes.SingleAsync(o => o.Abrv == "LSTA");
                loanApplication.LoanStatusId = loanStatusLSTA.Id;

                loanApplicationService.AddLogMessage(
                    logs,
                    $"Loan Status: <strong>{loanStatusLSTA.Name}: {productsMessage}</strong>",
                    DateTime.UtcNow,
                    LogType.Clean,
                    LogSortOrder.LoanStatusApproved,
                    LogDetailItemReportGroupEnum.Red
                );

                if (calcResult == null || calcResult.QualifiedProducts.Count == 0)
                {
                    loanApplicationService.AddLogMessage(logs, "No qualified products!", DateTime.UtcNow);
                }
                else
                {
                    loanApplicationService.AddLogMessage(logs, "Qualified products: " + string.Join(", ", calcResult.QualifiedProducts.Select(p => p.Product?.Name)), DateTime.UtcNow);
                }


                //Everything below relates to Alerts - functionality slated for removal

                //var isMaxNumberOfOpenLoansReached = IsMaximumOfOpenLoansReached(logs, loanApplication, calcResult);
                //var isMaxLoanAmountReached = IsMaximumLoanAmountReached(logs, loanApplication, calcResult);
                //var isNumberOfLoansForHistoryPeriodLimitReached = IsNumberOfLoansForHistoryPeriodLimitReached(logs, loanApplication, calcResult);

                //var isPartialTransactionsPulledAlertRaised = RaisePartialTransactionsPulledAlert(logs, loanApplication, partialTransactionsPulled);
                //UpdateLoanApplication(loanApplication);
                //if (isMaxNumberOfOpenLoansReached || isMaxLoanAmountReached || isNumberOfLoansForHistoryPeriodLimitReached || isPartialTransactionsPulledAlertRaised)
                //{
                //    loanApplicationService.AddLogMessage(logs, "Starting Alert process.", DateTime.UtcNow);
                //    var fiMember = await loanApplicationService.GetRepeatedMemberInfoAsync(loanApplication);
                //    loanApplicationService.AddLogMessage(logs, "Repeated Get Member Finished:", DateTime.UtcNow);
                //    var member = GetFinancialInstitutionMemberById(loanApplication.FinancialInstitutionMemberId);

                //    if (member != null && fiMember != null)
                //    {
                //        var uow = Repository.CreateUnitOfWork();
                //        MapMember(fiMember, member);
                //        SyncBaseAccounts(uow, member.Id, fiMember);
                //        try
                //        {
                //            uow.Commit();
                //        }
                //        catch (DbEntityValidationException ex)
                //        {
                //            var er = new LoanApplicationException();
                //            foreach (var eve in ex.EntityValidationErrors)
                //                foreach (var item in eve.ValidationErrors)
                //            {
                //                er.AddError(item.PropertyName + " - " + item.ErrorMessage);
                //            }

                //            throw er;
                //        }
                //    }

                //    loanApplication.FinancialInstitutionMember = GetFinancialInstitutionMemberById(loanApplication.FinancialInstitutionMemberId);

                //    var fi = financialInstitutionService.FinancialInstitution;
                //    isMaxNumberOfOpenLoansReached = IsMaximumOfOpenLoansReached(logs, loanApplication, calcResult, true);
                //    isMaxLoanAmountReached = IsMaximumLoanAmountReached(logs, loanApplication, calcResult, true);
                //    isNumberOfLoansForHistoryPeriodLimitReached = IsNumberOfLoansForHistoryPeriodLimitReached(logs, loanApplication, calcResult, true);

                //    if (isMaxNumberOfOpenLoansReached || isMaxLoanAmountReached || isNumberOfLoansForHistoryPeriodLimitReached || isPartialTransactionsPulledAlertRaised)
                //    {
                //        var alertMessage = $"New QCash Alert has been triggered for {fi.Name}, application id: {loanApplication.AppId}. Please check Admin Portal logs for more details.";

                //        _alertService.SendGeneralAlertEmailAlert(alertMessage);
                //        _alertService.SendSmsAlerts(alertMessage);

                //        loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.LSTQCFA, LogSortOrder.QCFAudit);
                //        result.NextStep = LoanApplicationStep.QCFAudit;
                //    }
                //    else
                //    {
                //        loanApplicationService.AddLogMessage(logs, "Aborting Alert process.", DateTime.UtcNow);
                //        _alertService.SetAlertToClosedByAppId(loanApplication.AppId);
                //    }
                //}
            }
            else if (result.NextStep == LoanApplicationStep.AdverseAction)
            {
                await loanApplicationService.FinishLoanAsync(loanApplication, logs, false);
                var loanStatusLSTD = await qCashContext.LoanStatusTypes.SingleAsync(o => o.Abrv == "LSTD");
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusLSTD, LogSortOrder.LoanStatusDenied);

                if (settings.InsertTrackingRecordForDeniedLoan)
                {
                    await loanApplicationService.InsertDeniedTrackingRecordsAsync(loanApplication, logs);
                }
            }
            else
            {
                throw new Exception("Unexpected loan application step after decision engine");
            }
        }
        catch (Exception ex)
        {
            var financialInstitutionConfiguration = await qCashContext.FinancialInstitutionConfigurations.SingleAsync();
            await errorHelper.StoreErrorAsync(ex, financialInstitutionConfiguration);
#if DEBUG
            result.Errors.AddError(ex.GetDetailedErrorMessage("<br />"));
#else
            var fieldValue = await memberInterfaceHelper.GetTextAsync(InterfaceDefaultTextsEnum.MiddlewareError.Abrv, true, null);
            result.Errors.AddError(fieldValue ?? InterfaceDefaultTextsEnum.MiddlewareError.Abrv);
#endif
            result.NextStep = (LoanApplicationStep)loanApplication.CurrentStep;
        }
        return (result, loanApplication);
    }

    /// <summary>
    /// Validates the decision engine.
    /// </summary>
    /// <param name="loanApplication">The la database.</param>
    /// <param name="logs">The logs.</param>
    /// <param name="settingsLog">The settings log.</param>
    /// <param name="availableProducts">The available products.</param>
    /// <returns></returns>
    public async Task<(ValidationResult, Data.Models.LoanApplication, bool)> ValidateDecisionEngineAsync(
        Data.Models.LoanApplication loanApplication, IList<LoanApplicationLogDetail> logs, IList<LoanApplicationSettingsLog> settingsLog, IList<Data.Models.Product> availableProducts)
    {
        var settings = await qCashContext.Settings.FirstAsync();

        var ptime = DateTime.UtcNow;
        var result = new ValidationResult();

        loanApplicationService.AddLogMessage(logs, "ValidateDecisionEngine TimeZone finished: " + (DateTime.UtcNow - ptime).TotalSeconds.ToString() + "s", DateTime.UtcNow);

        if (settings.LoanHub && !(loanApplication.LoanApplicationSso != null && loanApplication.LoanApplicationSso.PreApproved.GetValueOrDefault()))
        {
            availableProducts = loanApplicationService.GetAvailableProductsWhenLoanHubActive(availableProducts.ToList(), loanApplication.SelectedLoanHubItemId);
        }

        // availableProducts = ValidateAndFilterAvailableProducts(loanApplication, availableProducts, settings.MilitaryAnnualPercentageRate, settings.UseManualCampaignCodeInput);
        var productIds = availableProducts.Select(p => p.Id).ToArray();

        var activeModelSelectors = await qCashContext.ModelSelectors.Where(p => productIds.Contains(p.ProductId) && p.IsTestPlan.Equals(false) && p.IsActive.Equals(true)).Include(o => o.ModelSelectorDecisionModels).ThenInclude(o => o.DecisionModel).Include(o => o.Product).ToListAsync();

        var nsfSetting = await qCashContext.NsfSettings.FirstOrDefaultAsync();

        var productsWithModelSelection = new List<ProductAvailableResult>();
        foreach (var prod in availableProducts)
        {
            var modelSelector = activeModelSelectors.FirstOrDefault(p => p.ProductId == prod.Id);

            //Because A:B is being removed, this is no longer needed (ModelSelectors should go away entirely at some point) - we just return the default Model
            //var decisionModels = decisionManagerService.GetValidDecisionModelsFromModelSelector(modelSelector, prod.DefaultModelId.Value, loanApplication.FinancialInstitutionMember.MemberIdHash);
            var defaultModel = await qCashContext.DecisionModels.Include(o => o.ModelType).FirstOrDefaultAsync(o => o.Id == prod.DefaultModelId);
            var decisionModels = new List<DecisionModelSelection>();
            if (defaultModel != null)
            {
                decisionModels.Add(new DecisionModelSelection(defaultModel.Id, defaultModel.ModelType.Slug == "statistical"));
            }

            // Get transformation for all models, need them for the DecisionEngineParameters.
            foreach (var model in decisionModels)
            {
                if (!model.IsStatistical && model.RngNumber.HasValue && settings.LogRng)
                {
                    loanApplicationService.AddLogMessage(logs, $"RNG Result for {modelSelector?.Name}: {model.RngNumber.Value.ToString()}", DateTime.UtcNow, LogType.Admin);
                    loanApplicationService.AddLogMessage(logs, $"Selected Model for {prod.Name} per RNG calculations: {model.DMName}", DateTime.UtcNow, LogType.Admin);
                }
                model.CachedTransformations = decisionManagerService.GetTransformationMaster(model.ModelId);
            }

            // Get personal campaign if product is 'PersonalLoanCampaign'.
            string? marketingCampaignCode = loanApplication.MarketingCampaignCode;
            CampaignCodeAccount? campaignCodeAccount = null;

            // Get all valid campaign code accounts.
            var memberCampaignCodeAccounts = await GetValidCampaignCodeAccountsForMemberAsync(
                await loanApplicationService.GetMemberIdentifierAsync(loanApplication),
                productIds);
            if (settings.UseManualCampaignCodeInput && !string.IsNullOrEmpty(marketingCampaignCode))
            {
                campaignCodeAccount = memberCampaignCodeAccounts.FirstOrDefault(p => p.CampaignCode.Code.Equals(marketingCampaignCode));
            }

            else if (!settings.UseManualCampaignCodeInput && memberCampaignCodeAccounts.Any(p => p.CampaignCode.ProductId == prod.Id))
            {
                campaignCodeAccount = memberCampaignCodeAccounts.First(p => p.CampaignCode.ProductId == prod.Id);
            }

            productsWithModelSelection.Add(new ProductAvailableResult(prod, modelSelector != null ? modelSelector.Id : (Guid?)null, decisionModels, campaignCodeAccount));
        }

        var deParams = await loanApplicationService.GetDecisionEngineParametersFromMiddlewareAsync(loanApplication, logs, settingsLog, productsWithModelSelection);
        loanApplicationService.AddLogMessage(logs, "ValidateDecisionEngine FromMiddleware finished: " + (DateTime.UtcNow - ptime).Seconds.ToString() + "s", DateTime.UtcNow);
        ptime = DateTime.UtcNow;

        result.NextStep = LoanApplicationStep.ApplicationQualified;
        loanApplicationService.AddLogMessage(logs, "ValidateDecisionEngine CreateDE finished: " + (DateTime.UtcNow - ptime).TotalSeconds.ToString() + "s", DateTime.UtcNow);
        ptime = DateTime.UtcNow;

        #region Validate DecisionEngine

        var laFinancialInstitutionMemberId = loanApplication.FinancialInstitutionMemberId;

        var decisionEngineDTO = new DecisionEngineDTO { FinancialInstitutionMemberId = laFinancialInstitutionMemberId };

        //Add accounts to calculator settings
        var accs = await qCashContext.MemberBaseAccounts
            .Where(p => p.FinancialInstitutionMemberId == laFinancialInstitutionMemberId &&
                        (p.CloseDateUtc == null || p.CloseDateUtc > DateTime.UtcNow)).Include(o => o.MemberAccounts).ToListAsync();
        //var accs = GetMemberBaseAccountsByFinancialInstitutionMemberId(loanApplication.FinancialInstitutionMemberId);

        var memberBaseAccounts = loanApplication.FinancialInstitutionMember.MemberBaseAccounts;
        foreach (var item in accs)
        {
            (await qCashContext.MemberBaseAccounts.Where(p => p.Id == item.Id).Include(p => p.MemberAccounts).OrderBy(p => p.OpenDateUtc).ToListAsync()).ForEach(p => memberBaseAccounts.Add(p));
            //GetMemberBaseAccountsByIdWithMemberAccounts(item.Id).ToList().ForEach(p => memberBaseAccounts.Add(p));
        }

        // Add DEParams to calculator settings
        decisionEngineDTO.DecisionEngineParameters = JsonConvert.DeserializeObject<DecisionEngineParameters>(deParams);
        var partialTransactionsPulled = decisionEngineDTO.DecisionEngineParameters?.PartialTransactionsPulled ?? false;
        decisionEngineDTO.AvailableProducts = productsWithModelSelection;
        decisionEngineDTO.LoanApplication = loanApplication;
        decisionEngineDTO.NSFSetting = nsfSetting;

        //NOTE Runs transformations for all products - by initial requirements
        var resultCalc = await decisionEngineService.GenerateResultAsync(decisionEngineDTO, logs);

        //Add logs to loan application
        foreach (IResult item in resultCalc.Result)
        {
            //each of these used to check for null first (if (item.Eligibility != null && item.Eligibility.Logs != null && ...)
            //so it may be good to review whether those should actually be nullable
            if (item.Eligibility.Logs.Count > 0)
            {
                loanApplicationService.AddLogMessage(logs, item.Eligibility.Logs.ToList(), LogType.Clean);
            }
            if (item.MemberInGoodStanding.Logs.Count > 0)
            {
                loanApplicationService.AddLogMessage(logs, item.MemberInGoodStanding.Logs.ToList(), LogType.Clean);
            }
            if (item.DecisionEngine.Logs.Count > 0)
            {
                loanApplicationService.AddLogMessage(logs, item.DecisionEngine.Logs.ToList(), LogType.Clean);
            }
            if (item.Logs.Count > 0)
            {
                loanApplicationService.AddLogMessage(logs, item.Logs.ToList(), LogType.Clean);
            }
        }

        loanApplicationService.AddLogMessage(logs, "ValidateDecisionEngine Calculate finished: " + (DateTime.UtcNow - ptime).TotalSeconds.ToString() + "s", DateTime.UtcNow);
        ptime = DateTime.UtcNow;

        // Get AAN Reasons for all non-statistical models.
        var nonStatisticalResults = resultCalc.Result.Where(p => p.IsStatistical == false).ToList();
        var loanApplicationAANs = new List<LoanApplicationAan>();
        foreach (var nResult in nonStatisticalResults)
        {
            foreach (var aanResult in nResult.AANReasons)
            {
                var laAAN = new LoanApplicationAan { LoanApplicationId = loanApplication.Id, AanReasonId = aanResult.Id, ProductId = nResult.ProductId };
                loanApplicationAANs.Add(laAAN);
            }
        }

        // Validate if there are any qualified products.
        if (resultCalc.QualifiedProducts.Count == 0)
        {
            var aanIds = loanApplicationAANs.Select(p => p.AanReasonId).ToList();
            var aanReasonLookups = await qCashContext.AanReasonDefaultTexts
                .Where(a => aanIds.Contains(a.Id))
                .Select(a => new { a.Id, a.Description, })
                .ToListAsync();
            var aanReasonOverrides = await qCashContext.AANReasonOverrideTexts
                .Where(a => aanIds.Contains(a.AanReasonDefaultTextId))
                .Select(a => new { a.AanReasonDefaultTextId, a.Description, })
                .ToListAsync();

            var aans = new List<string>();
            result.NextStep = LoanApplicationStep.AdverseAction;

            foreach (var laAAN in loanApplicationAANs)
            {
                var product = availableProducts.First(p => p.Id == laAAN.ProductId);
                var aanReason = aanReasonLookups.First(p => p.Id == laAAN.AanReasonId);
                var aanReasonOverride = aanReasonOverrides.SingleOrDefault(p => p.AanReasonDefaultTextId == laAAN.AanReasonId);
                var desc = aanReasonOverride?.Description ?? aanReason.Description;
                aans.Add($"{product.Name} - {desc}");
            }

            logs.Add(new LoanApplicationLogDetail()
            {
                Id = guidExtensionService.NewSequentialGuid(),
                ActionDateTimeUtc = DateTime.UtcNow,
                TimeStamp = [],
                IsTemp = true,
                ActionDescription = "AAN Reasons: " + string.Join(" | ", aans),
                LogType = (int)LogType.Clean,
                SortOrder = (int)LogSortOrder.AANReasons,
                ReportGroup = (int)LogDetailItemReportGroupEnum.None,
            });

            foreach (var nResult in nonStatisticalResults)
            {
                var laProductReasons = loanApplicationAANs.Where(p => p.ProductId == nResult.ProductId).ToList();

                int numOfReasons = 1;
                if (settings.ShowMultipleAan)
                {
                    numOfReasons = settings.NumberOfAanToShow > laProductReasons.Count ? laProductReasons.Count : settings.NumberOfAanToShow;
                }

                for (int i = 0; i < numOfReasons; i++)
                {
                    laProductReasons[i].ShowInAanDocument = true;
                }
            }

            loanApplicationService.AddLogMessage(logs, "ValidateDecisionEngine Checks finished: " + (DateTime.UtcNow - ptime).TotalSeconds.ToString() + "s", DateTime.UtcNow);
        }
        else
        {
            result.NextStep = LoanApplicationStep.ApplicationQualified;
        }

        loanApplicationService.AddLogMessage(logs, "ValidateDecisionEngine Checks finished: " + (DateTime.UtcNow - ptime).TotalSeconds.ToString() + "s", DateTime.UtcNow);
        ptime = DateTime.UtcNow;

        loanApplicationService.AddLogMessage(logs, "ValidateDecisionEngine Amounts to Log finished: " + (DateTime.UtcNow - ptime).TotalSeconds.ToString() + "s", DateTime.UtcNow);
        ptime = DateTime.UtcNow;

        #endregion Validate DecisionEngine

        //Persist data
        loanApplicationService.AddLogMessage(logs, "ValidateDecisionEngine UpdateData finished: " + (DateTime.UtcNow - ptime).TotalSeconds.ToString() + "s", DateTime.UtcNow);
        ptime = DateTime.UtcNow;

        if (loanApplicationAANs.Any())
        {
            foreach (var laAan in loanApplicationAANs)
            {
                loanApplication.LoanApplicationAans.Add(laAan);
            }
        }
        await qCashContext.SaveChangesAsync();
        UpdateLoanApplicationInWizard(loanApplication); //see note in method body - this should just be SaveChanges now

        loanApplicationService.AddLogMessage(logs, "ValidateDecisionEngine UpdateLA finished: " + (DateTime.UtcNow - ptime).TotalSeconds.ToString() + "s", DateTime.UtcNow);
        ptime = DateTime.UtcNow;

        loanApplication = await GetLoanApplicationForWizardAsync(loanApplication.FinancialInstitutionMemberId);
        loanApplicationService.AddLogMessage(logs, "ValidateDecisionEngine Reload finished: " + (DateTime.UtcNow - ptime).TotalSeconds.ToString() + "s", DateTime.UtcNow);
        ptime = DateTime.UtcNow;
        loanApplicationService.AddLogMessage(logs, "ValidateDecisionEngine finished: " + (DateTime.UtcNow - ptime).TotalSeconds.ToString() + "s", DateTime.UtcNow);

        await loanApplicationHelper.CacheLoanApplicationResultAllProductsAsync(resultCalc, loanApplication.Id);

        return (result, loanApplication, partialTransactionsPulled);
    }

    private async Task<List<CampaignCodeAccount>> GetValidCampaignCodeAccountsForMemberAsync(MemberIdentifier memberIdentifier, Guid[] productIds)
    {
        var query = qCashContext.CampaignCodeAccounts.Where(
                c =>
                    c.Status == nameof(CampaignCodeAccountStatus.UNUSED)
                    && c.CampaignCode.IsActive
                    && DateTime.UtcNow >= c.CampaignCode.EffectiveDate
                    && DateTime.UtcNow <= c.CampaignCode.EndDate)
            .Include(o => o.CampaignCode)
            .Include(o => o.PersonalCampaigns)
            .FilterByMemberIdentifier(memberIdentifier);

        if (productIds.Length > 0)
        {
            query = query.Where(p => productIds.Contains(p.CampaignCode.ProductId));
        }

        return (await query.ToListAsync());
    }

    private async Task<ValidationResult> ValidateEConsentAsync(Data.Models.LoanApplication loanApplication)
    {
        var result = new ValidationResult();

        var memberConsentEConsentOrPriorEConsent = loanApplication.MemberConsentEConsentOrPriorEConsent();
        if (memberConsentEConsentOrPriorEConsent?.PreviouslyAccepted == true)
        {
            if (!memberConsentEConsentOrPriorEConsent.Accepted)
            {
                var fieldValue = await memberInterfaceHelper.GetTextAsync(InterfaceDefaultTextsEnum.MustAcceptEConsentStatement_Static.Abrv, true, loanApplication.SelectedLanguageId);
                result.Errors.AddError(fieldValue ?? InterfaceDefaultTextsEnum.MustAcceptEConsentStatement_Static.Abrv);
            }

            result.NextStep = result.Errors.Errors.Count < 1
                ? LoanApplicationStep.Application
                : LoanApplicationStep.EConsentDisclosure; //Return to current step
        }
        else
        {
            result.NextStep = LoanApplicationStep.EConsentDisclosure;
        }
        return result;
    }

    private bool ValidateDataCollection(
        FinancialInstitution financialInstitution,
        LoanApplicationApiModelBasePost postModel,
        ValidationResult vr,
        IList<LoanApplicationLogDetail> logs)
    {
        DataCollectionSetting dataCollectionSetting = financialInstitution.DataCollectionSettings.First();
        if (postModel is not DataCollectionApiModelPost dataCollectionPostModel)
        {
            const string invalidDataCollectionModel = "Invalid data collection model";
            vr.Errors.Errors.Add(invalidDataCollectionModel);
            loanApplicationService.AddLogMessage(logs, invalidDataCollectionModel, DateTime.UtcNow);
            return false;
        }

        if (dataCollectionSetting.IsDataCollectionRequired && dataCollectionPostModel.IsDataGatheringSkipped)
        {
            const string dataCollectionRequired = "Data Collection is required";
            vr.Errors.Errors.Add(dataCollectionRequired);
            loanApplicationService.AddLogMessage(logs, dataCollectionRequired, DateTime.UtcNow);
            return false;
        }

        if (dataCollectionSetting.IsBypassQuestionEnabled
            && dataCollectionPostModel.IsUserAnswerPositive == dataCollectionSetting.IsPositiveAllowBypass)
        {
            return true;
        }

        if (dataCollectionPostModel.IsDataGatheringSkipped && !dataCollectionSetting.IsDataCollectionRequired)
        {
            return true;
        }

        var dataCollectionSettings =
            JsonConvert.DeserializeObject<List<DataCollectionItemDTO>>(dataCollectionSetting.CollectionInfo ?? string.Empty);
        if (dataCollectionPostModel.DataCollection == null && dataCollectionSettings != null && dataCollectionSettings.Any(item => item.IsRequired))
        {
            const string dataCollectionEmpty = "Data collection is empty";
            vr.Errors.Errors.Add(dataCollectionEmpty);
            loanApplicationService.AddLogMessage(logs, dataCollectionEmpty, DateTime.UtcNow);
            return false;
        }

        if (dataCollectionSettings != null)
        {
            foreach (var errorMessage in dataCollectionSettings
                         .Where(item =>
                             item.IsRequired &&
                             !dataCollectionPostModel.DataCollection!.Any(dc => item.Texts.Any(t => t.Value == dc.Name)))
                         .Select(item => $"Data collection item {item.Texts.First().Value} is required"))
            {
                vr.Errors.Errors.Add(errorMessage);
                loanApplicationService.AddLogMessage(logs, errorMessage, DateTime.UtcNow);
            }
        }

        return vr.Errors.Errors.Count == 0;
    }

    private async Task ProcessDataCollectionAsync(
        FinancialInstitution financialInstitution,
        DataCollectionApiModelPost postModel,
        Data.Models.LoanApplication loanApplication)
    {
        var dataCollectionSetting = financialInstitution.DataCollectionSettings.First();

        var dataCollectionItems = postModel.DataCollection?
            .Select(dc => new DataCollectionEntryDto(dc.Name, dc.Value))
            .ToList();
        var dataCollectionSettings =
            JsonConvert.DeserializeObject<List<DataCollectionItemDTO>>(dataCollectionSetting.CollectionInfo ?? string.Empty);

        if (dataCollectionSettings == null || dataCollectionItems == null)
        {
            return;
        }

        AdjustCollectionItemNames(dataCollectionItems, dataCollectionSettings, loanApplication);

        var dataCollectionRecord = new DataCollectionRecordDto(
            postModel.IsDataGatheringSkipped,
            postModel.IsUserAnswerPositive,
            dataCollectionItems);

        var dataCollectionRecordDb = new DataCollectionRecord { LoanApplicationId = loanApplication.Id, CollectedInformation = JsonConvert.SerializeObject(dataCollectionRecord) };

        qCashContext.DataCollectionRecords.Add(dataCollectionRecordDb);
        await qCashContext.SaveChangesAsync();
    }

    private static void AdjustCollectionItemNames(
        IList<DataCollectionEntryDto> dataCollectionItems,
        IReadOnlyCollection<DataCollectionItemDTO> dataCollectionSettings,
        Data.Models.LoanApplication loanApplication)
    {
        var language = loanApplication.SelectedLanguage?.LanguageCode == string.Empty
            ? "en-US"
            : loanApplication.SelectedLanguage?.LanguageCode ?? GetMostMatchedLanguage(dataCollectionItems, dataCollectionSettings);

        if (string.Equals(language, "en-US", StringComparison.InvariantCultureIgnoreCase))
        {
            return;
        }

        // Replace the name of the data collection item with the name from the most matched language setting to the english
        for (var index = 0; index < dataCollectionItems.Count; index++)
        {
            var collectionItem = dataCollectionItems[index];
            var dataCollectionSettingItem = dataCollectionSettings
                .FirstOrDefault(item => item.Texts.Any(t =>
                    string.Equals(t.Language, language, StringComparison.InvariantCultureIgnoreCase)
                    && string.Equals(t.Value, collectionItem.Name, StringComparison.InvariantCultureIgnoreCase)));

            if (dataCollectionSettingItem != null)
            {
                dataCollectionItems[index] = collectionItem with
                {
                    Name = dataCollectionSettingItem.Texts.First(t =>
                        string.Equals(t.Language, "en-US", StringComparison.InvariantCultureIgnoreCase)).Value,
                };
            }
        }
    }

    private static string GetMostMatchedLanguage(
        IEnumerable<DataCollectionEntryDto> dataCollectionItems,
        IReadOnlyCollection<DataCollectionItemDTO> dataCollectionSettings)
    {
        // Create a dictionary to store the count of matches for each language
        var languageMatches = new Dictionary<string, int>();

        // Iterate over each data collection item
        foreach (var matchingText in dataCollectionItems
                     .SelectMany(_ => dataCollectionSettings,
                         (collectionItem, dataCollectionSettingItem) => new { collectionItem, dataCollectionSettingItem })
                     .Select(t1 => new { t1, item = t1.collectionItem })
                     .Select(t1 => t1.t1.dataCollectionSettingItem.Texts.FirstOrDefault(t =>
                         string.Equals(t.Value, t1.item.Name, StringComparison.OrdinalIgnoreCase)))
                     .Where(matchingText => matchingText != null))
        {
            if (matchingText != null)
            {
                // If a match is found, increment the count for the language
                if (!languageMatches.TryAdd(matchingText.Language, 1))
                {
                    languageMatches[matchingText.Language]++;
                }
            }
        }

        // Find the language with the highest count
        var mostMatchedLanguage = languageMatches.OrderByDescending(x => x.Value).FirstOrDefault().Key;
        return mostMatchedLanguage;
    }

    private async Task<ValidationResult> ValidateApplicationQualifiedAsync(
        Data.Models.LoanApplication loanApplication,
        IList<LoanApplicationLogDetail> logs,
        IList<LoanApplicationSettingsLog> settingsLog)
    {
        var loanTypeFb = await qCashContext.LoanTypes.FirstOrDefaultAsync(o => o.Abrv == nameof(LoanTypeAbbreviation.FB));

        //var appCtx = ApplicationContextFactory.GetApplicationContext();
        var setting = await qCashContext.Settings.FirstAsync();
        var result = new ValidationResult();
        var language = loanApplicationService.GetLanguage(loanApplication.SelectedLanguageId);
        if (guidExtensionService.IsNullOrEmpty(loanApplication.SelectedProductId))
        {
            var formattedInterfaceTextsPns = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(
                language.Id,
                loanApplication,
                "MemberInterfaceStaticText.ProductNotSelected");

            result.Errors.AddError(formattedInterfaceTextsPns["ProductNotSelected"]);

            return result;
        }

        var laMemberSelection = loanApplication.LoanApplicationMemberSelections.FirstOrDefault();
        if (laMemberSelection == null)
        {
            laMemberSelection = new LoanApplicationMemberSelection
            {
                Id = guidExtensionService.NewSequentialGuid(),
                LoanApplicationId = loanApplication.Id,
            };
            loanApplication.LoanApplicationMemberSelections.Add(laMemberSelection);
        }

        var qualifiedProductResult = (await loanApplicationHelper.GetLoanApplicationResultAllProductsAsync(loanApplication.Id))?.QualifiedProducts
            .First(p => p.ProductId == loanApplication.SelectedProductId.GetValueOrDefault());
        var product = qualifiedProductResult?.Product;

        if (product == null || qualifiedProductResult == null)
        {
            throw new Exception("Product not found");
        }

        loanApplication.SelectedProductId = product.Id;

        loanApplicationService.InsertLoanApplicationSettingsLog(
            settingsLog,
            loanApplication.Id,
            LoanApplicationSettingType.LoanPurpose,
            product.PurposeOfLoanEnabled);
        if (product.PurposeOfLoanEnabled && guidExtensionService.IsNullOrEmpty(loanApplication.PurposeOfLoanId))
        {
            var formattedInterfaceTextsSpol = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(
                language.Id, null, "MemberInterfaceStaticText.SelectPurposeOfLoan");
            result.Errors.AddError(formattedInterfaceTextsSpol.First(p => p.Key == "SelectPurposeOfLoan").Value);
        }

        //Validate amount
        var qamount = qualifiedProductResult.DecisionEngine.QualifiedAmount.GetValueOrDefault();

        var lookupTextArray = new List<string>
        {
            $"MemberInterfaceStaticText.{InterfaceDefaultTextsEnum.QualifiedAmountExceeded_Static.Abrv}",
            $"MemberInterfaceStaticText.{InterfaceDefaultTextsEnum.MaximumAmountErrorNotification_Static.Abrv}",
            $"MemberInterfaceStaticText.{InterfaceDefaultTextsEnum.MinimumAmountErrorNotification_Static.Abrv}",
        };

        var formattedInterfaceTextsAmounts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(
            language.Id,
            loanApplication,
            lookupTextArray);

        if (qamount < laMemberSelection.AmountBorrowed)
        {
            result.Errors.AddError(formattedInterfaceTextsAmounts[InterfaceDefaultTextsEnum.QualifiedAmountExceeded_Static.Abrv]);
        }
        else if ((loanApplication.FinancialInstitutionMember.IsNewBorrower.GetValueOrDefault() &&
                  product.NewBorrowerMaximumLoanAmount > 0
                     ? product.NewBorrowerMaximumLoanAmount
                     : product.MaximumLoanAmount) < laMemberSelection.AmountBorrowed)
        {
            result.Errors.AddError(formattedInterfaceTextsAmounts[InterfaceDefaultTextsEnum.MaximumAmountErrorNotification_Static.Abrv]);
        }
        else if (laMemberSelection.AmountBorrowed < product.MinimumLoanAmount)
        {
            result.Errors.AddError(formattedInterfaceTextsAmounts[InterfaceDefaultTextsEnum.MinimumAmountErrorNotification_Static.Abrv]);
        }

        var hasPassedValidation = result.Errors.Errors.Count < 1;

        //Insert CampaignCode values
        if (hasPassedValidation && !string.IsNullOrEmpty(loanApplication.MarketingCampaignCode))
        {
            string campaignCodeLogMessage;

            var campaignCode = await qCashContext.CampaignCodes.FirstOrDefaultAsync(p => p.Code == loanApplication.MarketingCampaignCode);
            if (campaignCode?.ProductId != loanApplication.SelectedProductId)
            {
                campaignCodeLogMessage =
                    "Campaign values have not been applied because of a mismatch between selected product and campaign code.";
                loanApplication.MarketingCampaignCode = string.Empty;
            }
            else
            {
                // VALUES THAT WILL BE APPLIED
                var isFeeBased = product.LoanTypeId == loanTypeFb?.Id;
                var campaignCodesOut = await CalculateCampaignCodeReductionsAsync(
                    loanApplication,
                    product,
                    campaignCode,
                    isFeeBased);

                var loanApplicationCampaignCode = campaignCodesOut.Item1;
                campaignCodeLogMessage = campaignCodesOut.Item2;

                loanApplication.LoanApplicationCampaignCodes.Add(loanApplicationCampaignCode);

                // Only log if UseManualCampaignCodeInput is false.
                if (!setting.UseManualCampaignCodeInput)
                {
                    loanApplicationService.AddLogMessage
                    (
                        logs,
                        $"<b> Campaign Code Applied: {loanApplication.MarketingCampaignCode}</b>",
                        DateTime.UtcNow,
                        LogType.Clean,
                        LogSortOrder.ChosenCampaignCode
                    );
                }
            }

            loanApplicationService.AddLogMessage
            (
                logs,
                $"Campaign Code Details:<br/>{campaignCodeLogMessage}",
                DateTime.UtcNow
            );
        }

        loanApplicationService.AddLogMessage(logs, $"Chosen Product: {product.Name}", DateTime.UtcNow,
            hasPassedValidation ? LogType.Clean : LogType.Admin, LogSortOrder.ChosenProduct);
        loanApplicationService.AddLogMessage(logs,
            $"Chosen Loan Amount: $ {decimal.Round(laMemberSelection.AmountBorrowed, 2):N2}",
            DateTime.UtcNow, hasPassedValidation ? LogType.Clean : LogType.Admin, LogSortOrder.ChosenLoanAmount);

        if (hasPassedValidation)
        {
            if (!await loanApplicationService.IsProductOpenEndedAsync(product))
            {
                var clientTimeZone = (await qCashContext.Settings.FirstOrDefaultAsync())?.TimeZone ?? string.Empty;
                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);
                var paymentOptionTypeMPAD = await qCashContext.PaymentOptionTypes.SingleOrDefaultAsync(o => o.Abrv == "MPAD");
                var firstPaymentDay = (laMemberSelection.PaymentOptionTypeId != paymentOptionTypeMPAD?.Id)
                    ? laMemberSelection.FirstPaymentDateUtc?.FromUTC(timeZone).ToString("MM/dd/yyyy")
                    : laMemberSelection.SelectedPaymentDay.ToString();

                loanApplicationService.AddLogMessage
                (
                    logs,
                    "Chosen Loan Payment Day: " + firstPaymentDay,
                    DateTime.UtcNow,
                    LogType.Clean,
                    LogSortOrder.ChosenLoanPaymentDay
                );

                var paymentOptionType = laMemberSelection.PaymentOptionType;

                loanApplicationService.AddLogMessage
                (
                    logs,
                    "Chosen Loan Payment Option: " + paymentOptionType?.Name,
                    DateTime.UtcNow,
                    LogType.Clean,
                    LogSortOrder.ChosenLoanPaymentOption
                );
            }

            loanApplicationService.AddLogMessage(
                logs, $"Autopay Option: {product.AutopayOptionNavigation?.Name}", DateTime.UtcNow, LogType.Clean, LogSortOrder.AutopayOption);
            var optOutAutoPayment = string.Empty;
            if (product.AutopayOption == AutopayOptionEnum.ForcedAutopay.Id)
            {
                optOutAutoPayment = "NO";
            }
            else
            {
                var memberConsentAutomaticPayment = loanApplication.MemberConsentAutomaticPayment();
                if (memberConsentAutomaticPayment != null)
                {
                    optOutAutoPayment = memberConsentAutomaticPayment.Accepted ? "NO" : "YES";
                }
            }

            loanApplicationService.AddLogMessage(logs, $"Autopay Opt Out: {optOutAutoPayment}", DateTime.UtcNow, LogType.Clean,
                LogSortOrder.OptOutAutoPay);

            var loanSubmissionEmailAlertActive = setting.LoanSubmissionEmailAlert;

            loanApplicationService.InsertLoanApplicationSettingsLog(
                settingsLog,
                loanApplication.Id,
                LoanApplicationSettingType.SubmissionEmailAlert,
                loanSubmissionEmailAlertActive);
            if (loanSubmissionEmailAlertActive)
            {
                await SendLoanApplicationToSubmissionEmailAsync(loanApplication);
            }

            result.NextStep = LoanApplicationStep.TILA;
        }
        else
        {
            result.NextStep = LoanApplicationStep.ApplicationQualified;
        }

        if (result.Errors.Errors.Count < 1)
        {
            await CalculateLoanApplicationAsync(logs, loanApplication);
        }

        return result;
    }

    private async Task<NewLoan> CreateNewLoanAsync(Data.Models.LoanApplication loanApplication, bool sendNextDisbursementDate)
    {
        var loanTypeFB = await qCashContext.LoanTypes.FirstOrDefaultAsync(o => o.Abrv == nameof(LoanTypeAbbreviation.FB));

        var clientTimeZone = (await qCashContext.Settings.FirstOrDefaultAsync())?.TimeZone ?? string.Empty;
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);

        var TILAResponse = loanApplicationHelper.GetTILAResponsesByLoanApplicationId(loanApplication.Id).FirstOrDefault();
        if (TILAResponse == null)
        {
            throw new Exception("No TILA document!");
        }

        var laMemberSelection = loanApplication.LoanApplicationMemberSelections.FirstOrDefault();
        var product = loanApplication.SelectedProduct;
        var isOpenEndedProduct = await loanApplicationService.IsProductOpenEndedAsync(product);

        var result = new NewLoan
        {
            IsOpenEndedLoan = isOpenEndedProduct,
            Id = loanApplication.AppId.ToString(),
            Amount = loanApplication.LoanAmount.GetValueOrDefault(),
            Abrv = product?.Abrv,
            Type = product?.LoanTypeId == loanTypeFB?.Id ? LoanType.FeeBased : LoanType.InterestBased,
            Description = product?.LoanDescription,
            CollateralCode = product?.CollateralCode,
            DoNotSetNextDisbursementDate = !sendNextDisbursementDate,
        };

        var memberBaseAccount = loanApplicationService.GetMemberBaseAccount(loanApplication.MemberBaseAccountId.GetValueOrDefault());
        if (memberBaseAccount == null)
        {
            throw new Exception("MemberBaseAccount is null!");
        }

        var tilaRequest = await qCashContext.TilaRequests.Where(o => o.LoanApplicationId.Equals(loanApplication.Id)).OrderBy(p => p.Id)
            .FirstOrDefaultAsync();
        if (tilaRequest != null)
        {
            result.DueDate = tilaRequest.FirstPaymentDate;
            result.PaymentDate = tilaRequest.FirstPaymentDate;
        }

        result.PaymentDueDay = (short)(laMemberSelection?.SelectedPaymentDay ?? 0);

        result.PaymentCounts = TILAResponse.NumberOfPayments ?? 0;
        result.InterestRate = TILAResponse.InterestRate;
        result.MaturityDate = TILAResponse.FinalPaymentDate;
        result.PaymentAmount = TILAResponse.RegularPaymentAmount;
        result.PaymentLoanAmount = TILAResponse.FinalPaymentAmount;
        result.FinanceCharge = TILAResponse.FinanceCharge;
        result.GLCode = product?.GlTranslationAccount;
        result.AdjustedApr = TILAResponse.AdjustedApr;
        result.ApplicationDate = loanApplication.DateCreatedUtc.FromUTC(timeZone);

        return result;
    }

    private async Task<(LoanApplicationCampaignCode, string)> CalculateCampaignCodeReductionsAsync(
        Data.Models.LoanApplication loanApplication, Data.Models.Product product, CampaignCode code, bool isFeeBased)
    {
        var loanApplicationCampaignCode = new LoanApplicationCampaignCode { LoanApplicationId = loanApplication.Id, CampaignCodeId = code.Id };

        var calculatedAprValue = await loanApplicationService.CalculateInterestRateAsync(loanApplication);
        var calculatedLoanOriginationFee = loanApplicationHelper.CalculateLoanOriginationFeePerMilitaryStatus(loanApplication);

        // Loan amount/per borrowed
        loanApplicationCampaignCode.LoanPerBorrowed = product.LoanPerBorrowed;
        loanApplicationCampaignCode.LoanAmount = code.LoanAmount ?? product.LoanAmount;

        var sbLogMessage = new StringBuilder();

        if (isFeeBased)
        {
            var campaignLoanFee = code.LoanAmount.HasValue ? code.LoanAmount.Value.ToString("0.00") : "N/A";
            if (code.LoanAmount.HasValue)
            {
                // If used from CampaignCode.
                sbLogMessage.Append(string.Format($" <strong>Campaign Loan Fee / Per Borrowed: ${campaignLoanFee} / ${product.LoanPerBorrowed:0.00}</strong><br/>"));
                sbLogMessage.Append(string.Format($" Product Loan Fee / Per Borrowed: ${product.LoanAmount:0.00} / ${product.LoanPerBorrowed:0.00}<br/>"));
            }
            else
            {
                // If used from Product.
                sbLogMessage.Append(string.Format($" Campaign Loan Fee / Per Borrowed: ${campaignLoanFee} / {product.LoanPerBorrowed:0.00}<br/>"));
                sbLogMessage.Append(string.Format($" <strong>Product Loan Fee / Per Borrowed: ${product.LoanAmount:0.00} / {product.LoanPerBorrowed:0.00}</strong><br/>"));
            }
        }

        // APR Reduction
        if (product.PersonalLoanCampaign)
        {
            var personalCampaign = await loanApplicationService.GetPersonalCampaignValuesByCodeAndMemberIdentifierAsync(
                code.Code,
                await loanApplicationService.GetMemberIdentifierAsync(loanApplication));
            loanApplicationCampaignCode.AnnualPercentageRate = loanApplicationService.CalculatePersonalCampaignInterestRate(personalCampaign, calculatedAprValue);

            var calculatedAprValueFormated = string.Format($"{calculatedAprValue:0.00}%");
            var annualPercentageRateFormated = string.Format($"{loanApplicationCampaignCode.AnnualPercentageRate:0.00}%");

            if (loanApplicationCampaignCode.AnnualPercentageRate < calculatedAprValue)
            {
                sbLogMessage.Append(string.Format($" <strong>Campaign APR: {annualPercentageRateFormated}</strong><br/>"));
                sbLogMessage.Append(string.Format($" Product APR: {calculatedAprValueFormated}<br/>"));
            }
            else
            {
                sbLogMessage.Append(string.Format($" Campaign APR: {annualPercentageRateFormated}<br/>"));
                sbLogMessage.Append(string.Format($" <strong>Product APR: {calculatedAprValueFormated}</strong><br/>"));
            }
        }
        else
        {
            loanApplicationCampaignCode.AnnualPercentageRate = loanApplicationService.CalculateNonPersonalCampaignInterestRate(code, calculatedAprValue);

            var codeActiveInterestRateReductionFormatted = code.InterestRateReduction.HasValue ? string.Format($"{code.InterestRateReduction.Value:0.00}%") : "N/A";
            var annualPercentageRateFormated = string.Format($"{loanApplicationCampaignCode.AnnualPercentageRate:0.00}%");

            sbLogMessage.Append(string.Format($" Campaign Active Interest Rate Reduction: {codeActiveInterestRateReductionFormatted}<br/>"));
            sbLogMessage.Append(string.Format($" <strong>Calculated APR: {annualPercentageRateFormated}</strong><br/>"));
        }

        // Loan Origination Fee.
        if (code.LoanOriginationFee.HasValue)
        {
            var campaignFee = code.LoanOriginationFee.Value;
            if (calculatedLoanOriginationFee > campaignFee)
            {
                sbLogMessage.Append(string.Format($" <strong>Campaign Loan Origination Fee: ${campaignFee:0.00}</strong><br/>"));
                sbLogMessage.Append(string.Format($" Product Loan Origination Fee: ${calculatedLoanOriginationFee:0.00}"));

                calculatedLoanOriginationFee = campaignFee;
            }
            else
            {
                sbLogMessage.Append(string.Format($" Campaign Loan Origination Fee: ${campaignFee:0.00}<br/>"));
                sbLogMessage.Append(string.Format($" <strong>Product Loan Origination Fee: ${calculatedLoanOriginationFee:0.00}</strong>"));
            }
        }
        else
        {
            sbLogMessage.Append(string.Format(" Campaign Loan Origination Fee: N/A<br/>"));
            sbLogMessage.Append(string.Format($" <strong>Product Loan Origination Fee: ${calculatedLoanOriginationFee:0.00}</strong>"));
        }

        //Below commented out - obsolete functionality
        // Handle Campaign Code Fee Refund.
        //if (code.LoanApplicationFee.HasValue && setting.LoanApplicationFee > 0 && !product.PersonalLoanCampaign && !IsProductOpenEnded(product))
        //{
        //    var refund = setting.LoanApplicationFee > code.LoanApplicationFee ? code.LoanApplicationFee.Value : setting.LoanApplicationFee;
        //    var feeRefund = CreateLoanApplicationFeeRefund();
        //    feeRefund.Amount = refund;
        //    feeRefund.IsEligible = true;
        //    feeRefund.FeeType = LoanApplicationFeeType.CampaignApplicationFeeRefund.ToString();
        //    feeRefund.LoanApplicationId = loanApplication.Id;

        //    InsertLoanApplicationFeeRefund(feeRefund);

        //    var rAmount = feeRefund.Amount.HasValue ? feeRefund.Amount.Value.ToString("0.00") : "N/A";
        //    sbLogMessage.Append($" Campaign Loan Application Fee Refund: ${rAmount}");
        //}

        loanApplicationCampaignCode.LoanOriginationFee = calculatedLoanOriginationFee;
        var campaignCodeLogMessage = sbLogMessage.ToString();

        return (loanApplicationCampaignCode, campaignCodeLogMessage);
    }

    private async Task SendLoanApplicationToSubmissionEmailAsync(Data.Models.LoanApplication loanApplication)
    {
        var language = loanApplicationService.GetLanguage(loanApplication.SelectedLanguageId);
        var lookupTextArray = new List<string>
        {
            $"MemberInterfaceStaticText.{InterfaceDefaultTextsEnum.LoanSubmissionEmailSubject_Static.Abrv}",
            $"MemberInterfaceText.{InterfaceDefaultTextsEnum.LoanSubmissionEmailTemplate.Abrv}",
        };

        var formattedInterfaceTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(language.Id, loanApplication, lookupTextArray);
        await loanApplicationService.SendDocumentEmailAsync(
            loanApplication,
            formattedInterfaceTexts.First(p => p.Key == InterfaceDefaultTextsEnum.LoanSubmissionEmailTemplate.Abrv).Value,
            formattedInterfaceTexts.First(p => p.Key == InterfaceDefaultTextsEnum.LoanSubmissionEmailSubject_Static.Abrv).Value,
            LoanApplicationEmailType.LoanSubmission
        );
    }

    private async Task CalculateLoanApplicationAsync(IList<LoanApplicationLogDetail> logs, Data.Models.LoanApplication la)
    {
        var settings = await qCashContext.Settings.FirstAsync();
        if (la.SelectedProduct == null) //erroneous state
        {
            return;
        }
        var productPaymentOptions = await paymentOptionService.GetPaymentOptionsForProductAsync(la.SelectedProductId!.Value, la.SelectedProduct.LoanTypeId);
        var calcResult = await loanApplicationHelper.GetLoanApplicationResultAllProductsAsync(la.Id);
        if (calcResult == null)
        {
            return;
        }
        var selectedProduct = calcResult.QualifiedProducts.First(p => p.ProductId == la.SelectedProductId).Product;
        var laSelection = la.LoanApplicationMemberSelections.FirstOrDefault();
        if (laSelection == null)
        {
            return;
        }

        la.LoanAmount = decimal.Round(laSelection.AmountBorrowed);
        if (laSelection.AmountBorrowed != la.LoanAmount.Value)
        {
            loanApplicationService.AddLogMessage(logs,
                $"LoanAmount rounded: {la.LoanAmount.Value:#,##0.00}", DateTime.UtcNow, LogType.Admin, LogSortOrder.None, LogDetailItemReportGroupEnum.None);
        }

        var acc = loanApplicationService.GetLatestMemberBaseAccount(la.FinancialInstitutionMemberId, la.AccountId);
        la.MemberBaseAccountId = acc?.Id;

        var accountForTransaction = await qCashContext.MemberAccounts.Where(p => p.Id.Equals(la.DefaultAccountForTransactionId.GetValueOrDefault())).OrderBy(s => s.DateOpened).FirstOrDefaultAsync();
        if (la.SelectedAccountForTransactionId.HasValue)
        {
            accountForTransaction = await qCashContext.MemberAccounts.Where(p => p.Id.Equals(la.SelectedAccountForTransactionId.GetValueOrDefault())).OrderBy(s => s.DateOpened).FirstOrDefaultAsync();
        }

        if (la.DefaultAccountForTransaction == null)
        {
            accountForTransaction = loanApplicationService.GetAccountForTransaction(la.FinancialInstitutionMemberId, la.AccountId, la.ActiveDuty.GetValueOrDefault(), settings);
            la.DefaultAccountForTransactionId = accountForTransaction?.Id;
            la.DefaultAccountForTransaction = accountForTransaction;
        }

        // Payment options:
        la.AmountBorrowed = laSelection.AmountBorrowed;

        // One-Time Payment Option.
        var paymentOptionTypeOT = await qCashContext.PaymentOptionTypes.SingleOrDefaultAsync(o => o.Abrv == "OT");
        var paymentOptionTypeNone = await qCashContext.PaymentOptionTypes.SingleOrDefaultAsync(o => o.Abrv == "NONE");
        if (laSelection.PaymentOptionTypeId == paymentOptionTypeOT?.Id)
        {
            var paymentOption = productPaymentOptions.First(p => p.PaymentOptionTypeId == laSelection.PaymentOptionTypeId);

            if (laSelection.FirstPaymentDateUtc == null)
            {
                laSelection.FirstPaymentDateUtc = la.DateCreatedUtc.AddDays(paymentOption.OneTime?.FirstPaymentDateThreshold ?? 0);
            }
        }
        else if (laSelection.PaymentOptionTypeId != paymentOptionTypeOT?.Id && laSelection.PaymentOptionTypeId != paymentOptionTypeNone?.Id)
        {
            var scoreTypeLookupIBLPP = await qCashContext.ScoreTypes.SingleOrDefaultAsync(o => o.Abrv == "IntrestBaseLoanPaybackPeriod");   //yes it's Intrest
            var paymentOption = productPaymentOptions.First(p => p.PaymentOptionTypeId == laSelection.PaymentOptionTypeId);
            var scores = await GetScoresForPaymentOptionAsync(paymentOption.Id, scoreTypeLookupIBLPP?.Id);
            var sl = loanApplicationHelper.ConvertScoresToScoreList(scores);

            var months = sl.GetScoreValue(Convert.ToDouble(la.AmountBorrowed));
            if (months == 0)
            {
                logs.AddLogDetail($"Non-existing {selectedProduct?.Name} Loan Payback Period range. Please add a new one through Decision Engine parameters.");
                throw new Exception(
                    $"Non-existing {selectedProduct?.Name} Loan Payback Period range. Please add a new one through Decision Engine parameters.");
            }

            laSelection.LoanTermInMonths = Convert.ToInt32(months);
        }

        la.AnnualPercentageRate = await loanApplicationService.CalculateFinalInterestRateAsync(la);

        var accountForLog = accountForTransaction != null ? loanApplicationService.GetMaskedNumber(accountForTransaction.AccountId) : "None";
        loanApplicationService.AddLogMessage(logs, string.Format("Loan Application calculated for {9}: {0}{1}: {2}{0}{3}: {4}{0}{5}: {6}{0}{7}: {8}", Environment.NewLine,
            "FeeDebitAccountId", accountForLog, "PaymentAccountId", accountForLog, "AccountForFundsId", accountForLog,
            "LoanAmount", la.LoanAmount.GetValueOrDefault().ToString("#,##0.00"), selectedProduct?.Name
        ), DateTime.UtcNow, LogType.AdminRequired, LogSortOrder.None, LogDetailItemReportGroupEnum.None);
    }
    private async Task<List<Data.Models.Score>> GetScoresForPaymentOptionAsync(Guid paymentOptionId, Guid? scoreTypeId)
    {
        if (scoreTypeId.HasValue)
        {
            return await qCashContext.Scores.Where(p =>
                p.PaymentOptionScores.Any(d => d.PaymentOptionId == paymentOptionId && d.Score.ScoreTypeId == scoreTypeId)).ToListAsync();
        }
        return await qCashContext.Scores.Where(p => p.PaymentOptionScores.Any(d => d.PaymentOptionId == paymentOptionId)).ToListAsync();
    }

    private async Task<Data.Models.LoanApplication> ClearSelectedProductAsync(Data.Models.LoanApplication loanApplication)
    {
        loanApplication.SelectedProduct = null;
        loanApplication.DecisionModel = null;
        loanApplication.SelectedProductId = Guid.Empty;
        loanApplication.DecisionModelId = null;

        await qCashContext.SaveChangesAsync();

        return loanApplication;
    }

    private async Task EnsureOldFormIsNotPostedAsync(LoanApplicationStep step, ValidationResult vr, Data.Models.LoanApplication loanApplication)
    {
        var currentStep = (LoanApplicationStep)loanApplication.CurrentStep;

        var isPostValid = GetParentSteps(currentStep).Any(p => p == step) || currentStep == step;
        if (isPostValid)
        {
            return;
        }

        var fieldValue = await memberInterfaceHelper.GetTextAsync(InterfaceDefaultTextsEnum.PostToOldForm_Static.Abrv, true, loanApplication.SelectedLanguageId);
        vr.Errors.AddError(fieldValue ?? InterfaceDefaultTextsEnum.PostToOldForm_Static.Abrv);

        throw vr.Errors;
    }

    /// <summary>
    /// Gets parent step for "step".
    /// </summary>
    /// <param name="step">Current step.</param>
    /// <returns>Parent LoanApplicationStep.</returns>
    private static IEnumerable<LoanApplicationStep> GetParentSteps(LoanApplicationStep step)
    {
        switch (step)
        {
            case LoanApplicationStep.ApplicationCancellationFinal:
                yield return LoanApplicationStep.Application;
                yield return LoanApplicationStep.ApplicationQualified;
                yield return LoanApplicationStep.EConsentDisclosure;
                yield return LoanApplicationStep.Awareness;
                yield return LoanApplicationStep.LoanLanding;
                break;

            case LoanApplicationStep.TILAEmail:
                yield return LoanApplicationStep.TILA;
                break;

            case LoanApplicationStep.AANEmail:
                yield return LoanApplicationStep.AdverseAction;
                break;

            case LoanApplicationStep.PayoffConfirmation:
                yield return LoanApplicationStep.Payoff;
                break;

            case LoanApplicationStep.Application:
                yield return LoanApplicationStep.LoanLanding;
                yield return LoanApplicationStep.LoanHub;
                break;

            case LoanApplicationStep.EConsentDisclosure:
                yield return LoanApplicationStep.LoanLanding;
                yield return LoanApplicationStep.LoanHub;
                break;

            case LoanApplicationStep.DataCollection:
                yield return LoanApplicationStep.TILA;
                yield return LoanApplicationStep.TILAEmail;
                break;

            case LoanApplicationStep.AccountExclusionByType:
            case LoanApplicationStep.HaveLoanApplicationInProgress:
            case LoanApplicationStep.ProductExclusionByState:
            case LoanApplicationStep.ProductExclusionByPersonalLoan:
            case LoanApplicationStep.HaveMaximumDeniedLoans:
            case LoanApplicationStep.ExclusionByJointAccount:
            case LoanApplicationStep.ExclusionByBankruptcyStatus:
            case LoanApplicationStep.ExclusionByFraudControlStatus:
            case LoanApplicationStep.ExclusionByMissingEmailAndPhone:
            case LoanApplicationStep.ExclusionByAge:
            case LoanApplicationStep.ExclusionByBadEmail:
            case LoanApplicationStep.ExclusionByBadAddress:
            case LoanApplicationStep.ExclusionByTroubledDebt:
            case LoanApplicationStep.ExclusionByNoEligibleAccounts:
            case LoanApplicationStep.ExclusionByContactInfoChanged:
            case LoanApplicationStep.ExclusionByBlocklist:
            case LoanApplicationStep.MilitaryNoProduct:
            case LoanApplicationStep.ExclusionByLoanApplicationWaitPeriod:
                yield return LoanApplicationStep.LoanLanding;
                yield return LoanApplicationStep.LoanHub;
                break;
        }
    }
    private async Task<bool> ValidatePendingLoansAsync(
        Data.Models.LoanApplication loanApplication,
        ValidationResult vr,
        List<LoanApplicationLogDetail> logs) =>
        await ValidateFundingPendingAsync(loanApplication, vr, logs);

    private async Task<bool> ValidateFundingPendingAsync(Data.Models.LoanApplication loanApplication, ValidationResult vr, List<LoanApplicationLogDetail> logs)
    {
        bool isValid = !await loanApplicationService.HasLoansInProgressAsync(loanApplication.FinancialInstitutionMember.MemberIdHash);
        if (!isValid)
        {
            vr.NextStep = LoanApplicationStep.HaveLoanApplicationInProgress;
            await loanApplicationService.FinishLoanAsync(loanApplication, logs);
            var loanStatusTypeLSTLIP = await qCashContext.LoanStatusTypes.SingleAsync(p => p.Abrv == "LSTLIP");
            _ = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLSTLIP, LogSortOrder.LoanStatusRestrictedDueFundingPending, LogDetailItemReportGroupEnum.None);
        }

        return isValid;
    }

    public async Task SendPaymentGuardEmailAsync(Data.Models.LoanApplication loanApplication)
    {
        var language = loanApplicationService.GetLanguage(loanApplication.SelectedLanguageId);
        var formattedInterfaceTexts =
            await memberProcessInterfaceService.GetFormattedInterfaceTextsForEmailsAsync(loanApplication, language.Id);

        var bodyText = FormatPaymentGuardEmailValue(
            formattedInterfaceTexts,
            "PaymentGuardEmailTemplate",
            loanApplication.FinancialInstitutionMember);

        var subjectText = FormatPaymentGuardEmailValue(
            formattedInterfaceTexts,
            "PaymentGuardEmailSubject",
            loanApplication.FinancialInstitutionMember);

        try
        {
            await loanApplicationService.SendDocumentEmailAsync(
                loanApplication,
                bodyText,
                subjectText,
                LoanApplicationEmailType.PaymentGuard);
        }
        catch (Exception ex)
        {
            var financialInstitutionConfiguration = await qCashContext.FinancialInstitutionConfigurations.SingleAsync();
            await errorHelper.StoreErrorAsync(ex, financialInstitutionConfiguration);

            loanApplicationService.AddLogMessage(loanApplication.Id, "Failed to send payment guard email disclosure", DateTime.UtcNow);
            throw;
        }
    }

    public string FormatPaymentGuardEmailValue(
        IReadOnlyDictionary<string, string> texts,
        string abbrv,
        FinancialInstitutionMember financialInstitutionMember)
    {
        if (!texts.TryGetValue(abbrv, out var text))
        {
            throw new ArgumentException($"Key '{abbrv}' not found in texts.", nameof(abbrv));
        }

        if (texts.TryGetValue("PaymentGuardInsuranceDisclosureTypeTitleAbrv", out var paymentGuardTypeTitle))
        {
            text = text.Replace("{disclosureType}", paymentGuardTypeTitle);
        }

        return loanApplicationService.FormatEmailValue(text, LoanApplicationEmailType.PaymentGuard, financialInstitutionMember);
    }

    /// <summary>
    /// Stores AdverseAction document.
    /// </summary>
    /// <returns>Document stored?</returns>
    public async Task<bool> StoreAdverseActionDocumentAsync(IList<LoanApplicationLogDetail> logs, Data.Models.LoanApplication? loan)
    {
        if (loan == null)
        {
            return false;
        }

        var dto = await GetAdverseActionAsync(loan);
        await fileSystemProvider.SetupAsync(ContainerTypeEnum.TemplatesPrivate);
        var language = loanApplicationService.GetLanguage(loan.SelectedLanguageId);
        var file = await fileSystemProvider.GetFileAsync(loanApplicationService.GetAanTemplateName(language.LanguageCode)) ??
                   await fileSystemProvider.GetFileAsync(loanApplicationService.GetAanTemplateName(string.Empty)); //Return default template
        dto.AccountId = loan.AccountId;

        file = PopulateAANFields(dto, file ?? []);

        //Store document with copy
        await fileSystemProvider.SetupAsync(ContainerTypeEnum.DocumentsPrivate);
        var result = await StoreDocumentAsync(logs, file, loan, LoanApplicationDocumentType.AdverseActionNotice);
        await qCashContext.SaveChangesAsync();

        loan = await GetLoanApplicationForWizardAsync(loan.FinancialInstitutionMemberId);
        if (result)
        {
            await fileSystemProvider.SetupAsync(ContainerTypeEnum.Private);
            return await StoreDocumentAsync(logs, file, loan, LoanApplicationDocumentType.AdverseActionNotice);
        }

        return false;
    }

    private async Task<AdverseActionDTO> GetAdverseActionAsync(Data.Models.LoanApplication loanApplication)
    {
        var financialInstitutionMember = loanApplication.FinancialInstitutionMember;
        var clientTimeZone = qCashContext.Settings.FirstOrDefault()?.TimeZone ?? string.Empty;
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);

        var language = loanApplicationService.GetLanguage(loanApplication.SelectedLanguageId);
        var accountId = string.Empty;
        var accountDescription = string.Empty;

        if (!guidExtensionService.IsNullOrEmpty(loanApplication.SelectedAccountForTransactionId))
        {
            var memberAccount = qCashContext.MemberAccounts.Where(p => p.Id.Equals(loanApplication.SelectedAccountForTransactionId)).OrderBy(s => s.DateOpened).FirstOrDefault();
            accountId = memberAccount?.AccountId;
            accountDescription = memberAccount?.Description;
        }

        var shareId = $"{accountId}: {accountDescription}";
        var fundingAccountType = await memberInterfaceHelper.GetFundingAccountTypeAsync(loanApplication, language.Id);

        var dto = new AdverseActionDTO
        {
            AppId = loanApplication.AppId,
            ApplicationDate = loanApplication.DateCreatedUtc.FromUTC(timeZone),
            City = financialInstitutionMember.City,
            FirstName = financialInstitutionMember.FirstName,
            MiddleName = financialInstitutionMember.MiddleName ?? string.Empty,
            LastName = financialInstitutionMember.LastName,
            Suffix = financialInstitutionMember.Suffix ?? string.Empty,
            SSN = await taxIdService.GetTaxIdByFinancialInstitutionMemberIdAsync(loanApplication.FinancialInstitutionMemberId),
            MailingAddress = financialInstitutionMember.MailingAddress,
            State = financialInstitutionMember.State,
            Zip = financialInstitutionMember.Zip,
            FooterInfo = $"{FiSlug} | q-cash_adverse_action_{DateTime.UtcNow:yyyy-MM-dd}",
            LoanDeniedReason = [],
            ShareId = shareId,
            FundingAccountType = fundingAccountType,
        };

        var loanApplicationAans = await qCashContext.LoanApplicationAans
            .Where(p => p.LoanApplicationId.Equals(loanApplication.Id) && p.ShowInAanDocument).Distinct()
            .OrderBy(p => p.ProductId)
            .Select(a => new
            {
                a.Id,
                ProductName = a.Product.Name,
                AanReasonDesc = a.AanReason.Description,
                AanReasonOverrideDesc = a.AanReason.AANReasonOverrideTexts
                    .SingleOrDefault(o => o.FinancialInstitutionId == financialInstitutionMember.FinancialInstitutionId),
            })
            .ToListAsync();

        foreach (var aan in loanApplicationAans)
        {
            var desc = aan.AanReasonOverrideDesc?.Description ?? aan.AanReasonDesc;
            dto.LoanDeniedReason.Add($"{aan.ProductName} - {desc}");
        }

        dto.LoanDeniedReason = dto.LoanDeniedReason.Distinct().ToList();
        return dto;
    }

    /// <summary>
    /// Populate Adverse Action Notice pdf fields.
    /// </summary>
    /// <param name="dto"></param>
    /// <param name="file"></param>
    /// <returns></returns>
    public byte[] PopulateAANFields(AdverseActionDTO dto, byte[] file)
    {
        //TODO - existing PDF package is old; determine how this will be done in QC7
        return file;

        //using var stream = new MemoryStream(100);
        //var pdfReader = new PdfReader(file);
        //var pdfStamper = new PdfStamper(pdfReader, stream);
        //var pdfFormFields = pdfStamper.AcroFields;
        //var reasons = string.Join(Environment.NewLine, dto.LoanDeniedReason.Distinct().ToArray());
        //pdfReader.RemoveUsageRights();
        //pdfFormFields.SetField("AccountNumber", FormattingHelpers.GetMaskedNumber(dto.AccountId));
        //pdfFormFields.SetField("ApplicationDate1", dto.ApplicationDateShort);
        //pdfFormFields.SetField("AppID", dto.AppId.ToString());
        //pdfFormFields.SetField("AppID2", dto.AppId.ToString());
        //pdfFormFields.SetField("FullName", dto.FullName);
        //pdfFormFields.SetField("FirstName", dto.FirstName);
        //pdfFormFields.SetField("LastName", dto.LastName);
        //pdfFormFields.SetField("FirstName2", dto.FirstName);
        //pdfFormFields.SetField("LastName2", dto.LastName);
        //pdfFormFields.SetField("MailingAddress", dto.MailingAddress);
        //pdfFormFields.SetField("FullAddress", dto.FullAddress);
        //pdfFormFields.SetField("City", dto.City);
        //pdfFormFields.SetField("State", dto.State);
        //pdfFormFields.SetField("Zip", dto.Zip);
        //pdfFormFields.SetField("LoanDeniedReason", reasons);
        //pdfFormFields.SetField("DocumentName", dto.FooterInfo);
        //pdfFormFields.SetField("DocumentName2", dto.FooterInfo);
        //pdfFormFields.SetField("MiddleName", dto.MiddleName);
        //pdfFormFields.SetField("SSN", FormattingHelpers.GetMaskedNumber(dto.SSN));
        //pdfFormFields.SetField("ShareID", dto.ShareId);
        //pdfFormFields.SetField("FundingAccountType", dto.FundingAccountType);
        //pdfStamper.FormFlattening = true;
        //pdfStamper.Close();
        //file = stream.ToArray();
        //return file;
    }

    /// <summary>
    /// Whether to display the languages.
    /// </summary>
    /// <param name="step">The step.</param>
    /// <param name="loanApplication">The loan application.</param>
    /// <returns></returns>
    public bool DisplayLanguages(LoanApplicationStep step, Data.Models.LoanApplication loanApplication)
    {
        var langSteps = new List<LoanApplicationStep>
        {
            LoanApplicationStep.EConsentDisclosure,
            LoanApplicationStep.HaveLoanApplicationInProgress,
            LoanApplicationStep.HaveLoanApplicationSignaturePending,
            LoanApplicationStep.ProductExclusionByState,
            LoanApplicationStep.ProductExclusionByPersonalLoan,
            LoanApplicationStep.AccountExclusionByType,
            LoanApplicationStep.HaveMaximumDeniedLoans,
            LoanApplicationStep.ExclusionByBankruptcyStatus,
            LoanApplicationStep.ExclusionByJointAccount,
            LoanApplicationStep.ExclusionByFraudControlStatus,
            LoanApplicationStep.ExclusionByMissingEmailAndPhone,
            LoanApplicationStep.ExclusionByAge,
            LoanApplicationStep.ExclusionByBadEmail,
            LoanApplicationStep.ExclusionByBadAddress,
            LoanApplicationStep.ExclusionByTroubledDebt,
            LoanApplicationStep.ExclusionByNoEligibleAccounts,
            LoanApplicationStep.ExclusionByContactInfoChanged,
            LoanApplicationStep.ExclusionByBlocklist,
        };

        return step == LoanApplicationStep.LoanHub || (langSteps.Contains(step) || (step == LoanApplicationStep.Application && !loanApplication.IsEConsentAccepted()));
    }

    /// <summary>
    /// Updates the loan application status.
    /// </summary>
    /// <param name="loanApplication">The loan application.</param>
    /// <param name="step">The step.</param>
    public async Task<Data.Models.LoanApplication> UpdateLoanApplicationStatusAsync(Data.Models.LoanApplication loanApplication, LoanApplicationStep step)
    {
        var loanStatusTypeLookup = await qCashContext.LoanStatusTypes.AsNoTracking().ToListAsync();
        var logs = new List<LoanApplicationLogDetail>();

        switch (step)
        {
            case LoanApplicationStep.EConsentDisclosure:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTIE"), LogSortOrder.LoanStatusIsEConsent);
                break;

            case LoanApplicationStep.MilitaryNoProduct:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTMNP"), LogSortOrder.LoanStatusMilitaryNoProduct, LogDetailItemReportGroupEnum.None);
                break;

            case LoanApplicationStep.Application:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTLA"), LogSortOrder.LoanStatusLoanApplication);
                break;

            case LoanApplicationStep.TILA:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTDT"), LogSortOrder.LoanStatusDisplayTILA);
                break;

            case LoanApplicationStep.Awareness:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTDA"), LogSortOrder.LoanStatusDisplayAwareness);
                break;

            case LoanApplicationStep.FraudControl:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTFC"), LogSortOrder.LoanStatusFraudControl);
                break;

            case LoanApplicationStep.FraudControlCodeExpired:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTFCE"), LogSortOrder.LoanStatusFraudControlCodeExpired);
                break;

            case LoanApplicationStep.LoanHub:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTLH"), LogSortOrder.LoanStatusLoanHub);
                break;

            case LoanApplicationStep.LoanLanding:
            case LoanApplicationStep.LoanLandingInitial:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTDL"), LogSortOrder.LoanStatusDisplayLoanLanding);
                break;

            case LoanApplicationStep.ExclusionByBankruptcyStatus:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTBEX"), LogSortOrder.IsBankrupt);
                break;

            case LoanApplicationStep.ExclusionByFraudControlStatus:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTFCE"), LogSortOrder.IsFraudControlExcluded);
                break;

            case LoanApplicationStep.ExclusionByMissingEmailAndPhone:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSEMEP"), LogSortOrder.IsEmailAndPhoneExcluded);
                break;

            case LoanApplicationStep.ExclusionByTroubledDebt:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTTDE"), LogSortOrder.IsTroubledDebtExcluded);
                break;

            case LoanApplicationStep.ExclusionByNoEligibleAccounts:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTEAE"), LogSortOrder.NoEligbleAccountsExcluded);
                break;

            case LoanApplicationStep.ExclusionByContactInfoChanged:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTCICE"), LogSortOrder.ContactInfoChangedExcluded);
                break;

            case LoanApplicationStep.ExclusionByAge:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTAGE"), LogSortOrder.IsAgeExcluded);
                break;

            case LoanApplicationStep.ExclusionByBadEmail:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTBEE"), LogSortOrder.IsBadEmailExcluded);
                break;

            case LoanApplicationStep.ExclusionByBadAddress:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTBAE"), LogSortOrder.IsBadAddressExcluded);
                break;

            case LoanApplicationStep.ExclusionByBlocklist:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTBLE"), LogSortOrder.BlocklistExcluded);
                break;

            case LoanApplicationStep.Maintenance:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTM"), LogSortOrder.None);
                break;

            case LoanApplicationStep.QCFAudit:
                loanApplication = loanApplicationService.SetLoanApplicationStatus(logs, loanApplication, loanStatusTypeLookup.Single(o => o.Abrv == "LSTQCFA"), LogSortOrder.QCFAudit);
                break;

            case LoanApplicationStep.None:
                logger.LogTrace("UpdateLoanApplicationStatus() None");
                break;
        }

        await SaveLoanApplicationLogMessagesAsync(loanApplication, logs);
        await qCashContext.SaveChangesAsync();

        return loanApplication;
    }

    /// <summary>
    /// Returns list of modal steps.
    /// </summary>
    /// <returns>Modal steps.</returns>
    public ImmutableHashSet<LoanApplicationStep> GetModals() =>
        ImmutableHashSet.Create(
            LoanApplicationStep.PayoffConfirmation,
            LoanApplicationStep.TILAEmail,
            LoanApplicationStep.AANEmail,
            LoanApplicationStep.FundingComplete,
            LoanApplicationStep.FundingPending
        );

    /// <summary>
    /// Gets the loan landing interface texts with language.
    /// </summary>
    /// <param name="token">The token.</param>
    /// <param name="languageId">The language identifier.</param>
    /// <param name="preApproved">Pre approved </param>
    public async Task<MaskWaitApiModelGet> GetLoanLandingInterfaceTextsWithLanguageAsync(string token, Guid? languageId, bool preApproved)
    {
        var data = Convert.FromBase64String(token);
        var tokenValue = Encoding.UTF8.GetString(data);

        string languageCode;
        var maskAwaitInterfaceTexts = new MaskWaitApiModelGet
        {
            Languages = [],
            PreApprovedLaProcess = preApproved,
        };

        var languageIdFilter = languageId;
        var languages = await qCashContext.Languages
            .Where(l => l.LanguageSupportStatuses.Any(ls => ls.IsActive)
                || l.Id == languageIdFilter).ToListAsync();
        if (languageId != null)
        {
            var language = languages.SingleOrDefault(l => l.Id == languageId)
                           ?? languages.SingleOrDefault(l => l.Name == LanguageEnum.English.Name);
            language.ThrowIfNull($"Unable to find languageId: {languageId} or English");
            languageCode = language.LanguageCode;
            foreach (var lang in languages.Where(lang => languageId != lang.Id))
            {
                maskAwaitInterfaceTexts.Languages.Add(new AvailableLanguageApiModel { Key = lang.Id.ToString(), Value = lang.Name });
            }
        }
        else
        {
            var loanApplicationToken = await tokenService.GetLoanApplicationTokenAsync(tokenValue);
            languageCode = loanApplicationToken?.LanguageCode ?? string.Empty;
            languageId = loanApplicationToken?.Id;
            if (languages.Count == 1)
            {
                maskAwaitInterfaceTexts.SelectedLanguageId = languages[0].Id.ToString();
            }
            else
            {
                foreach (var lang in languages)
                {
                    if (languageCode != lang.LanguageCode)
                    {
                        maskAwaitInterfaceTexts.Languages.Add(new AvailableLanguageApiModel { Key = lang.Id.ToString(), Value = lang.Name });
                    }
                    else
                    {
                        maskAwaitInterfaceTexts.SelectedLanguageId = lang.Id.ToString();
                    }
                }
            }
        }

        var loanLandingTexts = memberProcessInterfaceService.GetLoanLandingInterfaceTexts();
        var formattedTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(languageId ?? Guid.Empty, null, loanLandingTexts.ToList());

        maskAwaitInterfaceTexts.SelectedLanguageCode = languageCode;

        var displayTexts = formattedTexts.Select(ft => new DisplayTextApiModel { Key = ft.Key, Value = ft.Value }).ToList();

        maskAwaitInterfaceTexts.DisplayTexts = displayTexts;

        return maskAwaitInterfaceTexts;
    }

    public async Task<LoanHubApiModelGet> GetLoanHubDataAsync(string? languageCode, string preApproved)
    {
        var loanHubData = new LoanHubApiModelGet
        {
            LanguageValues = new List<AvailableLanguageApiModel>(),
            PreApprovedLaProcess = GetFlagFromSsoCredentials(preApproved),
        };

        var languages = await qCashContext.Languages
            .Where(l => l.LanguageSupportStatuses.Any(s => s.IsActive))
            .ToListAsync();
        languageCode ??= string.Empty;
        var language = languages.FirstOrDefault(x => x.LanguageCode == languageCode);
        language ??= languages.FirstOrDefault(x => x.Name == LanguageEnum.English.Name);
        language.ThrowIfNull($"Language not found: {languageCode}");
        var languageId = language.Id;

        foreach (Language lang in languages)
        {
            if (languageId != lang.Id)
            {
                loanHubData.LanguageValues.Add(new AvailableLanguageApiModel { Key = lang.Id.ToString(), Value = lang.Name });
            }
        }

        var loanHubTexts = memberProcessInterfaceService.GetLoanHubInterfaceTexts();
        var formattedTexts = await memberInterfaceHelper.GetFormattedInterfaceTextsAsync(languageId, null, loanHubTexts.ToList());

        var displayTexts = formattedTexts.Select(ft => new DisplayTextApiModel { Key = ft.Key, Value = ft.Value }).ToList();

        loanHubData.DisplayValues = displayTexts;

        return loanHubData;
    }

    public async Task<LoanApplicationCampaignCode?> GetPersonalCampaignCodeForLoanSummaryDisclosureAsync(Data.Models.LoanApplication loanApplication, Data.Models.Product selectedProduct)
    {
        var campaignCode = await qCashContext.CampaignCodes.FirstOrDefaultAsync(p => p.Code == loanApplication.MarketingCampaignCode);

        var loanTypeFb = await qCashContext.LoanTypes.SingleOrDefaultAsync(p => p.Abrv == nameof(LoanTypeAbbreviation.FB));
        var isFeeBased = selectedProduct.LoanTypeId == loanTypeFb?.Id;
        var settings = await qCashContext.Settings.FirstAsync();

        if (campaignCode == null)
        {
            if (!settings.UseManualCampaignCodeInput)
            {
                var personalCampaignCodes = await loanApplicationService.GetValidPersonalCampaignCodesForMemberAsync(await loanApplicationService.GetMemberIdentifierAsync(loanApplication));

                if (personalCampaignCodes.Any(pcc => pcc.ProductId == selectedProduct.Id))
                {
                    campaignCode = personalCampaignCodes.First(pcc => pcc.ProductId == selectedProduct.Id);
                }
            }
        }

        if (campaignCode == null)
        {
            return null;
        }

        if (selectedProduct.Id != campaignCode.ProductId)
        {
            return null;
        }

        var (loanApplicationCampaignCode, logmessage) = await CalculateCampaignCodeReductionsAsync(
            loanApplication, selectedProduct, campaignCode, isFeeBased);

        return loanApplicationCampaignCode;
    }

    public async Task<TILAResponse> GetLoanDisclosureInformationAsync(Data.Models.LoanApplication loanApplication, Data.Models.Product selectedProduct, LoanApplicationMemberSelection laMemberSelection)
    {
        var productPaymentOptions = await paymentOptionService.GetPaymentOptionsForProductAsync(selectedProduct.Id, selectedProduct.LoanTypeId);
        var paymentOptionTypeOT = await qCashContext.PaymentOptionTypes.SingleAsync(o => o.Abrv == "OT");
        var paymentOptionTypeNone = await qCashContext.PaymentOptionTypes.SingleAsync(o => o.Abrv == "NONE");
        if (laMemberSelection.PaymentOptionTypeId != paymentOptionTypeOT.Id && laMemberSelection.PaymentOptionTypeId != paymentOptionTypeNone.Id)
        {
            var paymentOption = productPaymentOptions.First(p => p.PaymentOptionTypeId == laMemberSelection.PaymentOptionTypeId);
            var scoreTypeLookupIBLPP = await qCashContext.ScoreTypes.SingleOrDefaultAsync(o => o.Abrv == "IntrestBaseLoanPaybackPeriod");   //yes it's Intrest as of 5/2025
            var scores = await GetScoresForPaymentOptionAsync(paymentOption.Id, scoreTypeLookupIBLPP?.Id);
            var sl = loanApplicationHelper.ConvertScoresToScoreList(scores);

            var months = sl.GetScoreValue(Convert.ToDouble(loanApplication.AmountBorrowed));

            laMemberSelection.LoanTermInMonths = Convert.ToInt32(months);
        }

        var tilaRequest = await CreateTILARequestWithSelectionInfoAsync(loanApplication, selectedProduct, laMemberSelection, string.Empty);
        var tilaResponse = ReturnTILA(tilaRequest);

        return tilaResponse;
    }

    /// <inheritdoc />
    public async Task<LoanHubApiModelGet> GetLoanHubStepAsync(Guid financialInstitutionMemberId)
    {
        var loanApplication = await GetLoanApplicationForWizardAsync(financialInstitutionMemberId);
        var language = await languageService.GetLanguageByIdAsync(loanApplication.SelectedLanguageId);

        var formattedInterfaceTexts = await memberProcessInterfaceService
            .GetFormattedInterfaceTextsForStepAsync(loanApplication, language.Id, LoanApplicationStep.LoanHub);

        return new LoanHubApiModelGet
        {
            LanguageValues = [new ()
            {
                Key = language.Id.ToString(),
                Value = language.Name,
            },],
            Step = nameof(LoanApplicationStep.LoanHub),
            SelectedLanguageCode = language.LanguageCode,
            DisplayValues = [..formattedInterfaceTexts.Select(text => new DisplayTextApiModel
            {
                Key = text.Key,
                Value = text.Value,
            }),],
            LoanApplicationId = loanApplication.Id,
        };
    }

    /// <inheritdoc />
    public async Task<Response> ProcessLoanHubStepAsync(Guid financialInstitutionMemberId, LoanHubApiModelPost model)
    {
        var loanApplication = await GetLoanApplicationForWizardAsync(financialInstitutionMemberId);
        var logs = new List<LoanApplicationLogDetail>();

        try
        {
            // Process the loan hub step using the existing ExecuteWizardStepAsync method
            var nextStep = await ExecuteWizardStepAsync(loanApplication, model, false, logs);

            return new Response { Step = nextStep.ToString() };
        }
        catch (Exception ex)
        {
            // Log the error and return a response indicating failure
            loanApplicationService.AddLogMessage(logs, $"Loan Hub Processing Error: {ex.Message}", DateTime.UtcNow);
            await SaveLoanApplicationLogMessagesAsync(loanApplication, logs);

            return new Response
            {
                Step = nameof(LoanApplicationStep.LoanHub),
                Errors = [ex.Message],
            };
        }
    }
}
