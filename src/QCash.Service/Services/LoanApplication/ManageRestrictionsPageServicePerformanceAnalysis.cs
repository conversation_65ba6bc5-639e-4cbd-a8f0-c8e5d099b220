using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using QCash.Data.Context;
using QCash.Service.Models.LoanApplication;
using QCash.Service.Services.Interfaces;

namespace QCash.Service.Services.LoanApplication;

/// <summary>
/// Performance analysis helper for ManageRestrictionsPageService
/// This class is for investigation purposes only and should be removed after analysis
/// </summary>
public class ManageRestrictionsPageServicePerformanceAnalysis
{
    private readonly QCashContext context;
    private readonly IProductQueries productQueries;
    private readonly ILogger<ManageRestrictionsPageServicePerformanceAnalysis> logger;

    public ManageRestrictionsPageServicePerformanceAnalysis(
        QCashContext context,
        IProductQueries productQueries,
        ILogger<ManageRestrictionsPageServicePerformanceAnalysis> logger)
    {
        this.context = context;
        this.productQueries = productQueries;
        this.logger = logger;
    }

    public async Task<PerformanceAnalysisResult> AnalyzeGetAsyncPerformanceAsync(bool includeInactiveProducts)
    {
        var result = new PerformanceAnalysisResult();
        var totalStopwatch = Stopwatch.StartNew();

        try
        {
            // Step 1: Analyze product query performance
            var productStopwatch = Stopwatch.StartNew();
            var products = productQueries.GetProducts().AsNoTracking();
            products = products.Where(a => !a.IsArchived);
            if (!includeInactiveProducts)
            {
                products = products.Where(a => a.IsActive);
            }

            var productList = products
                .Select(p => new ManageRestrictionProductDto() { Id = p.Id, Name = p.Name, })
                .ToList();
            productStopwatch.Stop();

            result.ProductQueryTime = productStopwatch.ElapsedMilliseconds;
            result.ProductCount = productList.Count;

            // Step 2: Analyze main query performance
            var mainQueryStopwatch = Stopwatch.StartNew();
            var productIds = productList.Select(a => a.Id).ToList();

            // Count records for analysis
            var globalExclusionCount = await context.GlobalExclusionStates
                .Where(a => productIds.Contains(a.ProductId))
                .CountAsync();

            var restrictedRatesCount = await context.StateRestrictedInterestRates
                .Where(a => productIds.Contains(a.ProductId) && !string.IsNullOrWhiteSpace(a.State))
                .CountAsync();

            result.GlobalExclusionStatesCount = globalExclusionCount;
            result.StateRestrictedInterestRatesCount = restrictedRatesCount;

            // Execute the main query
            var data = await context.Settings
                .Select(s => new ManageRestrictionsPageDto()
                {
                    IncludeInactiveProducts = includeInactiveProducts,
                    FiSlug = s.FinancialInstitution.Slug,
                    MilitaryAnnualPercentageRate = s.MilitaryAnnualPercentageRate,
                    SettingId = s.Id,
                    SettingTimeStamp = s.TimeStamp,
                    Products = productList,
                    ExcludedStates = context.GlobalExclusionStates
                        .Where(a => productIds.Contains(a.ProductId))
                        .Select(st => new ManageRestrictionStateDto()
                        {
                            Id = st.Id,
                            State = st.State,
                            ProductId = st.ProductId,
                            TimeStamp = st.TimeStamp,
                            DeleteRequested = false,
                            CreateRequested = false,
                        }).ToList(),
                    RestrictedRates = context.StateRestrictedInterestRates
                        .Where(a => productIds.Contains(a.ProductId)
                            && !string.IsNullOrWhiteSpace(a.State))
                        .Select(r => new ManageRestrictionRateDto()
                        {
                            Id = r.Id,
                            ProductId = r.ProductId,
                            State = r.State,
                            Rate = r.Rate,
                            TimeStamp = r.TimeStamp,
                        }).ToList(),
                })
                .FirstOrDefaultAsync();

            mainQueryStopwatch.Stop();
            result.MainQueryTime = mainQueryStopwatch.ElapsedMilliseconds;

            totalStopwatch.Stop();
            result.TotalTime = totalStopwatch.ElapsedMilliseconds;

            if (data != null)
            {
                result.ExcludedStatesReturned = data.ExcludedStates.Count;
                result.RestrictedRatesReturned = data.RestrictedRates.Count;
            }

            logger.LogInformation("ManageRestrictionsPageService Performance Analysis: " +
                "Total: {TotalTime}ms, Products: {ProductTime}ms ({ProductCount} products), " +
                "Main Query: {MainQueryTime}ms, GlobalExclusions: {GlobalExclusionCount}, " +
                "RestrictedRates: {RestrictedRatesCount}",
                result.TotalTime, result.ProductQueryTime, result.ProductCount,
                result.MainQueryTime, result.GlobalExclusionStatesCount, result.StateRestrictedInterestRatesCount);

            return result;
        }
        catch (Exception ex)
        {
            totalStopwatch.Stop();
            result.TotalTime = totalStopwatch.ElapsedMilliseconds;
            result.Error = ex.Message;
            logger.LogError(ex, "Error during performance analysis");
            return result;
        }
    }

    /// <summary>
    /// Analyze the complexity growth as data increases
    /// </summary>
    public async Task<ComplexityAnalysisResult> AnalyzeComplexityGrowthAsync()
    {
        var result = new ComplexityAnalysisResult();

        try
        {
            // Get total counts
            result.TotalProducts = await context.Products.CountAsync();
            result.TotalActiveProducts = await context.Products.Where(p => p.IsActive && !p.IsArchived).CountAsync();
            result.TotalGlobalExclusionStates = await context.GlobalExclusionStates.CountAsync();
            result.TotalStateRestrictedInterestRates = await context.StateRestrictedInterestRates.CountAsync();

            // Analyze distribution
            var productExclusionCounts = await context.GlobalExclusionStates
                .GroupBy(g => g.ProductId)
                .Select(g => new { ProductId = g.Key, Count = g.Count() })
                .ToListAsync();

            var productRateCounts = await context.StateRestrictedInterestRates
                .GroupBy(r => r.ProductId)
                .Select(g => new { ProductId = g.Key, Count = g.Count() })
                .ToListAsync();

            result.MaxExclusionsPerProduct = productExclusionCounts.Any() ? productExclusionCounts.Max(p => p.Count) : 0;
            result.MaxRatesPerProduct = productRateCounts.Any() ? productRateCounts.Max(p => p.Count) : 0;
            result.AverageExclusionsPerProduct = productExclusionCounts.Any() ? productExclusionCounts.Average(p => p.Count) : 0;
            result.AverageRatesPerProduct = productRateCounts.Any() ? productRateCounts.Average(p => p.Count) : 0;

            logger.LogInformation("Complexity Analysis: " +
                "Total Products: {TotalProducts}, Active: {ActiveProducts}, " +
                "Global Exclusions: {GlobalExclusions}, Restricted Rates: {RestrictedRates}, " +
                "Max Exclusions/Product: {MaxExclusions}, Max Rates/Product: {MaxRates}",
                result.TotalProducts, result.TotalActiveProducts,
                result.TotalGlobalExclusionStates, result.TotalStateRestrictedInterestRates,
                result.MaxExclusionsPerProduct, result.MaxRatesPerProduct);

            return result;
        }
        catch (Exception ex)
        {
            result.Error = ex.Message;
            logger.LogError(ex, "Error during complexity analysis");
            return result;
        }
    }
}

public class PerformanceAnalysisResult
{
    public long TotalTime { get; set; }
    public long ProductQueryTime { get; set; }
    public long MainQueryTime { get; set; }
    public int ProductCount { get; set; }
    public int GlobalExclusionStatesCount { get; set; }
    public int StateRestrictedInterestRatesCount { get; set; }
    public int ExcludedStatesReturned { get; set; }
    public int RestrictedRatesReturned { get; set; }
    public string? Error { get; set; }
}

public class ComplexityAnalysisResult
{
    public int TotalProducts { get; set; }
    public int TotalActiveProducts { get; set; }
    public int TotalGlobalExclusionStates { get; set; }
    public int TotalStateRestrictedInterestRates { get; set; }
    public int MaxExclusionsPerProduct { get; set; }
    public int MaxRatesPerProduct { get; set; }
    public double AverageExclusionsPerProduct { get; set; }
    public double AverageRatesPerProduct { get; set; }
    public string? Error { get; set; }
}
