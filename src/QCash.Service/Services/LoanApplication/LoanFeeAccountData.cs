using QCash.LoanApplication;

namespace QCash.Service.Services.LoanApplication;

public class LoanFeeAccountData
{
    /// <summary>
    /// Initializes a new instance of the <see cref="LoanFeeAccountData"/> class.
    /// </summary>
    public LoanFeeAccountData()
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="LoanFeeAccountData"/> class.
    /// </summary>
    /// <param name="account">The account.</param>
    /// <param name="transferAccountType">Type of the transfer account.</param>
    /// <param name="accountId">The account identifier.</param>
    /// <param name="memberAccountId">The member account identifier.</param>
    public LoanFeeAccountData(Account account, Enums.TransferAccountType transferAccountType, string accountId, Guid? memberAccountId)
    {
        Account = account;
        TransferAccountType = transferAccountType;
        AccountId = accountId;
        MemberAccountId = memberAccountId;
    }

    public Account? Account { get; set; }
    public Enums.TransferAccountType TransferAccountType { get; set; }
    public Guid? MemberAccountId { get; set; }
    public string? AccountId { get; set; }
}