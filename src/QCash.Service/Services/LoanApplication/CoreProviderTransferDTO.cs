using QCash.Data.Models;
using QCash.LoanApplication;
using static QCash.Service.Models.LoanApplication.Enums;
using static QCash.Service.Services.LoanApplication.Enums;

namespace QCash.Service.Services.LoanApplication;

public class CoreProviderTransferDTO
{
    public OriginApp OriginApp { get; set; }
    public required IList<LoanApplicationLogDetail> Logs { get; set; }
    public LogSortOrder FeeSortOrder { get; set; }
    public TransferType TransferType { get; set; }
    public LoanApplicationFeeType LoanApplicationFeeType { get; set; }

    public LoanFeeAccountData? FromAccountData { get; set; }
    public LoanFeeAccountData? ToAccountData { get; set; }
    public HoldFundsParameters? HoldFundsParameters { get; set; }
    public ExtraInfo? ExtraInfo { get; set; }

    public Guid LoanApplicationId { get; set; }
    public Guid CorrelationId { get; set; }
    public string? AppId { get; set; }
    public string TransferDescription => LoanApplicationFeeType.ToString();
    public string? FeeLog { get; set; }
    public string? FeeDescription { get; set; }
    public string? ExceptionMessage { get; set; }
    public bool? CoreProviderException { get; set; }
    public decimal Amount { get; set; }
}