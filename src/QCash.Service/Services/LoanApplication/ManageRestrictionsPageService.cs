using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.General;
using QCash.Service.Models.LoanApplication;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.LoanApplication.Interfaces;
using QCash.Utils.Core;

namespace QCash.Service.Services.LoanApplication;

public class ManageRestrictionsPageService(
    QCashContext context,
    ISystemClockService systemClockService,
    IGuidExtensionService guidExtensionService,
    IProductQueries productQueries,
    IEfPocoService efPocoService
        ) : IManageRestrictionsPageService
{
    public string RecordName { get; } = "Manage Restrictions Settings";

    public async Task<ManageRestrictionsPageDto?> GetAsync(bool includeInactiveProducts)
    {
        var products = productQueries.GetProducts().AsNoTracking();
        products = products.Where(a => !a.IsArchived);
        if (!includeInactiveProducts)
        {
            products = products.Where(a => a.IsActive);
        }

        var productList = products
            .Select(p => new ManageRestrictionProductDto() { Id = p.Id, Name = p.Name, })
            .ToList();
        var productIds = productList.Select(a => a.Id).ToList();
        var data = await context.Settings
            .Select(s => new ManageRestrictionsPageDto()
            {
                IncludeInactiveProducts = includeInactiveProducts,
                FiSlug = s.FinancialInstitution.Slug,
                MilitaryAnnualPercentageRate = s.MilitaryAnnualPercentageRate,
                SettingId = s.Id,
                SettingTimeStamp = s.TimeStamp,
                Products = productList,
                ExcludedStates = context.GlobalExclusionStates
                    .Where(a => productIds.Contains(a.ProductId))
                    .Select(st => new ManageRestrictionStateDto()
                    {
                        Id = st.Id,
                        State = st.State,
                        ProductId = st.ProductId,
                        TimeStamp = st.TimeStamp,
                        DeleteRequested = false,
                        CreateRequested = false,
                    }).ToList(),
                RestrictedRates = context.StateRestrictedInterestRates
                    .Where(a => productIds.Contains(a.ProductId)
                        && !string.IsNullOrWhiteSpace(a.State))
                    .Select(r => new ManageRestrictionRateDto()
                    {
                        Id = r.Id,
                        ProductId = r.ProductId,
                        State = r.State,
                        Rate = r.Rate,
                        TimeStamp = r.TimeStamp,
                    }).ToList(),
            })
            .FirstOrDefaultAsync();
        return data;
    }

    public async Task<GenericActionResult> SaveAsync(ManageRestrictionsPageDto dto)
    {
        var settingSaveResult = await efPocoService.CreateOrUpdateAsync(dto.SettingId, null, dto.SettingTimeStamp, new PerformCreateOrUpdateOptions<Setting>()
        {
            ExecuteRecordChangesFunc = (result) =>
            {
                result.Record.MilitaryAnnualPercentageRate = dto.MilitaryAnnualPercentageRate;
            },
        });
        if (!settingSaveResult.IsSuccessful)
        {
            return settingSaveResult;
        }

        var now = systemClockService.GetSystemTimeUtc();
        await SaveStateExclusionsAsync(dto, now);
        var saveRateResult = await SaveStateRateRestrictionsAsync(dto, now);
        return saveRateResult.IsSuccessful
            ? new GenericActionResult() { IsSuccessful = true }
            : saveRateResult;
    }

    private async Task SaveStateExclusionsAsync(ManageRestrictionsPageDto dto, DateTime now)
    {
        var deleteRequests = dto.ExcludedStates
            .Where(a => a.DeleteRequested && a.Id != Guid.Empty)
            .Select(a => a.Id).ToList();
        var dbItemsToDelete = context.GlobalExclusionStates.Where(a => deleteRequests.Contains(a.Id));
        context.GlobalExclusionStates.RemoveRange(dbItemsToDelete);

        var newDbRecords = dto.ExcludedStates
            .Where(a => a.CreateRequested)
            .Select(a => new GlobalExclusionState()
            {
                Id = guidExtensionService.NewSequentialGuid(),
                ProductId = a.ProductId,
                State = a.State,
                DateCreatedUtc = now,
                DateUpdatedUtc = now,
                TimeStamp = [],  // value automatically set by SqlServer
            }).ToList();
        await context.GlobalExclusionStates.AddRangeAsync(newDbRecords);
    }

    private async Task<GenericActionResult> SaveStateRateRestrictionsAsync(ManageRestrictionsPageDto dto, DateTime now)
    {
        var deleteRequests = dto.RestrictedRates
            .Where(a => a.DeleteRequested && a.Id != Guid.Empty)
            .Select(a => a.Id).ToList();
        var dbItemsToDelete = await context.StateRestrictedInterestRates
            .Where(a => deleteRequests.Contains(a.Id)).ToListAsync();
        foreach (var record in dbItemsToDelete)
        {
            var deleteResult = await efPocoService.DeleteItemAsync<StateRestrictedInterestRate>(new DeleteItemDto() { DeleteItemId = record.Id, TimeStamp = record.TimeStamp });
            if (!deleteResult.IsSuccessful)
            {
                return deleteResult;
            }
        }

        var newDbRecords = dto.RestrictedRates
            .Where(a => a.CreateRequested)
            .Select(a => new StateRestrictedInterestRate()
            {
                Id = guidExtensionService.NewSequentialGuid(),
                ProductId = a.ProductId,
                Rate = a.Rate,
                State = a.State,
            }).ToList();
        efPocoService.AddRange(newDbRecords);

        var idsThatNeedChanging = dto.RestrictedRates
            .Where(a => a.UpdateRequested)
            .Select(a => a.Id).ToList();

        var recordsThatNeedChanging = await context.StateRestrictedInterestRates
            .AsTracking()
            .Where(a => idsThatNeedChanging.Contains(a.Id))
            .ToListAsync();
        foreach (var record in recordsThatNeedChanging)
        {
            var editedInfo = dto.RestrictedRates.Single(a => a.Id.Equals(record.Id));
            record.Rate = editedInfo.Rate;
            if (efPocoService.PrepRecordForSaveAfterChangesMade(record, editedInfo.TimeStamp) is { IsSuccessful: false } failedPrepResult)
            {
                return failedPrepResult;  // Failed timestamp check.  abort.
            }
        }

        return new GenericActionResult() { IsSuccessful = true, };
    }
}
