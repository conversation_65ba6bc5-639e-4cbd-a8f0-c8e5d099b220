using Microsoft.AspNetCore.Http;
using QCash.Data.Extensions;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Utilities.Extensions;

namespace QCash.Service.Services;

public class SitemapService : ISitemapService
{
    private readonly IHttpContextAccessor? _httpContextAccessor;
    private readonly IDecisionEngineSettingsService _decisionEngineSettingsService;
    private readonly string _urlPrefix;
    private readonly SitemapNode _rootNode;

    public SitemapService(IHttpContextAccessor? httpContextAccessor, IDecisionEngineSettingsService decisionEngineSettingsService)
    {
        _httpContextAccessor = httpContextAccessor;
        _decisionEngineSettingsService = decisionEngineSettingsService;
        var tenantSlug = GetCurrentTenantSlug();
        _urlPrefix = $"/{tenantSlug}";
        _rootNode = BuildSitemapAndReturnRootNode();
    }

    public async Task<List<SitemapNode>> GetBreadcrumbTrailAsync(string? controller, string? action, string? productId)
    {
        if (!string.IsNullOrEmpty(productId))
        {
            UpdateModelDashboardUrlWithProductId(productId);
        }

        var breadcrumbs = new List<SitemapNode>();
        var node = await FindNodeByControllerNameAndActionAsync(controller, action);

        while (node != null)
        {
            breadcrumbs.Insert(0, node);
            node = node.Parent;
        }

        return breadcrumbs;
    }

    private async Task<SitemapNode?> FindNodeByControllerNameAndActionAsync(string? controllerName, string? actionName) =>
        await FindNodeByControllerNameAndActionRecursiveAsync(_rootNode, controllerName, actionName);

    private static async Task<SitemapNode?> FindNodeByControllerNameAndActionRecursiveAsync(
        SitemapNode node,
        string? controllerName,
        string? action)
    {
        if (MatchesControllerAndAction(node, controllerName, action))
        {
            return node;
        }

        foreach (var child in node.Children)
        {
            var result = await FindNodeByControllerNameAndActionRecursiveAsync(child, controllerName, action);
            if (result != null)
            {
                return result;
            }
        }

        return null;
    }

    private static bool MatchesControllerAndAction(SitemapNode node, string? controllerName, string? action) =>
        !string.IsNullOrEmpty(controllerName) &&
        controllerName.Equals(node.Controller, StringComparison.OrdinalIgnoreCase) &&
        !string.IsNullOrEmpty(action) &&
        action.Equals(node.Action, StringComparison.OrdinalIgnoreCase);

    private string GetCurrentTenantSlug()
    {
        if (_httpContextAccessor == null)
        {
            return string.Empty;
        }

        var httpContext = _httpContextAccessor.HttpContext;
        return httpContext != null ? httpContext.GetTenant().Slug : string.Empty;
    }

    private static SitemapNode AddNode(
        SitemapNode parent,
        string title,
        string? controller = null,
        string? action = null,
        string? url = null)
    {
        var section = new SitemapNode
        {
            Title = title,
            Controller = controller,
            Action = action,
            Url = url,
            Parent = parent,
        };
        parent.Children.Add(section);

        return section;
    }

    private static string GetFormattedTitle(string controllerName) =>
        controllerName switch
        {
            _ when controllerName.Equals(Constants.Controllers.FiConfiguration, StringComparison.OrdinalIgnoreCase) => "FI Configuration",
            _ when controllerName.Equals(Constants.Controllers.Products, StringComparison.OrdinalIgnoreCase) => "Products",
            _ when controllerName.Equals(Constants.Controllers.SsoTestSetup, StringComparison.OrdinalIgnoreCase) => "QA Test Suite",
            _ => string.Concat(controllerName.Select(c => char.IsUpper(c) ? " " + c : c.ToString())).Trim(),
        };

    private SitemapNode BuildSitemapAndReturnRootNode()
    {
        var rootNode = CreateRootNode();

        BuildSection(rootNode,
            GetFormattedTitle(Constants.Controllers.UserManagement),
            Constants.Controllers.UserManagement,
            $"{_urlPrefix}/{Constants.Controllers.FiConfiguration}",
            Constants.Actions.List);

        BuildSection(rootNode,
            GetFormattedTitle(Constants.Controllers.FiConfiguration),
            Constants.Controllers.FiConfiguration,
            _urlPrefix);

        BuildSection(rootNode,
            GetFormattedTitle(Constants.Controllers.DecisionEngine),
            Constants.Controllers.DecisionEngine,
            _urlPrefix,
            Constants.Actions.Index);

        BuildSection(rootNode,
            GetFormattedTitle(Constants.Controllers.Products),
            Constants.Controllers.Products,
            _urlPrefix,
            Constants.Actions.List);

        BuildSection(rootNode,
            GetFormattedTitle(Constants.Controllers.ManageRestrictions),
            Constants.Controllers.ManageRestrictions,
            _urlPrefix,
            Constants.Actions.Index);

        BuildSection(rootNode,
            GetFormattedTitle(Constants.Controllers.MemberInterface),
            Constants.Controllers.MemberInterface,
            _urlPrefix);

        BuildSection(rootNode,
            GetFormattedTitle(Constants.Controllers.SsoTestSetup),
            Constants.Controllers.SsoTestSetup,
            _urlPrefix,
            Constants.Actions.Edit);

        BuildSection(rootNode,
            GetFormattedTitle(Constants.Controllers.Reports),
            Constants.Controllers.Reports,
            _urlPrefix);

        BuildSection(rootNode,
            GetFormattedTitle(Constants.Controllers.ModelDashboard),
            Constants.Controllers.ModelDashboard,
            $"{_urlPrefix}/{Constants.Controllers.ModelManager}",
            Constants.Actions.List);

        BuildSection(rootNode,
            "Change Password",
            "Account",
            _urlPrefix,
            "ChangePassword");

        return rootNode;
    }

    private SitemapNode CreateRootNode() =>
        new()
        {
            IsRoot = true,
            Title = GetFormattedTitle(Constants.Controllers.Home),
            Controller = Constants.Controllers.Home,
            Action = Constants.Actions.Index,
            Url = _urlPrefix,
        };

    private void BuildSection(SitemapNode rootNode, string title, string controllerName, string urlPrefix, string? mainAction = null)
    {
        var controllerUrl = $"{urlPrefix}/{controllerName}";
        var url = GetSectionUrl(controllerUrl, mainAction);
        var section = AddNode(rootNode, title, controllerName, mainAction, url: url);

        if (controllerName.Equals(Constants.Controllers.FiConfiguration, StringComparison.OrdinalIgnoreCase))
        {
            AddChildrenToFiConfigurationSection(section, controllerUrl);
            return;
        }
        if (controllerName.Equals(Constants.Controllers.DecisionEngine, StringComparison.OrdinalIgnoreCase))
        {
            AddChildrenToDecisionEngineSection(section, urlPrefix);
            return;
        }
        if (controllerName.Equals(Constants.Controllers.MemberInterface, StringComparison.OrdinalIgnoreCase))
        {
            AddChildrenToMemberInterfaceSection(section);
            return;
        }
        if (controllerName.Equals(Constants.Controllers.Reports, StringComparison.OrdinalIgnoreCase))
        {
            AddChildrenToReportsSection(section, controllerUrl);
            return;
        }
        if (controllerName.Equals(Constants.Controllers.Products, StringComparison.OrdinalIgnoreCase))
        {
            AddChildrenToProductsSection(section);
            // Product section needs to Create and Edit Actions Below
        }

        // Add the Create and Edit Actions if not stopped by specialized builds above
        AddNode(section, Constants.Actions.Create, controllerName, Constants.Actions.Create);
        AddNode(section, Constants.Actions.Edit, controllerName, Constants.Actions.Edit);
    }

    private static string? GetSectionUrl(string controllerUrl, string? mainAction) => mainAction switch
    {
        null => null,
        Constants.Actions.Index => controllerUrl,
        _ => $"{controllerUrl}/{mainAction}",
    };

    private void AddChildrenToFiConfigurationSection(SitemapNode sectionNode, string urlPrefix)
    {
        AddNode(sectionNode, "FI Info", "FiInfo", Constants.Actions.Edit);
        AddNode(sectionNode, "Templates", "Templates", Constants.Actions.Edit);

        var fiConfigurationLoanApplicationSection =
            AddNode(sectionNode,
                "Loan Application",
                "", //Don't need to specify a controller - No page, but want to route to LoanApplicationGeneralSettings
                Constants.Actions.Edit,
            $"{urlPrefix}/LoanApplicationGeneralSettings/{Constants.Actions.Edit}");

        AddLoanApplicationNodes(fiConfigurationLoanApplicationSection, urlPrefix);

        var fiConfigurationInvoiceSection =
            AddNode(sectionNode,
                "Invoice",
                "", //Don't need to specify a controller - No page, but want to route to InvoiceGeneralSettings
                Constants.Actions.Edit,
            $"{urlPrefix}/Invoice/InvoiceGeneralSettings/{Constants.Actions.Edit}");

        AddNode(fiConfigurationInvoiceSection,
            "General",
            "InvoiceGeneralSettings",
            Constants.Actions.Edit);

        var fiConfigurationGeneralSection =
            AddNode(sectionNode,
                "General",
                "General",
                Constants.Actions.Edit,
            $"{urlPrefix}/General/{Constants.Actions.Edit}");

        AddNode(fiConfigurationGeneralSection,
            "Application Info",
            "ApplicationKey",
            Constants.Actions.Edit);

        AddNode(fiConfigurationGeneralSection,
            "Generate Secondary Keys",
            "ApplicationKey",
            "GenerateSecondaryKeys");

        var whiteListIp =
            AddNode(fiConfigurationGeneralSection,
                "Allowed IPs",
                "WhiteListIp",
                Constants.Actions.List,
            $"{urlPrefix}/WhiteListIp/AdminUI/{Constants.Actions.List}");

        AddNode(whiteListIp,
            "IP Address Item",
            "WhiteListIp",
            Constants.Actions.Create);
    }

    private void AddLoanApplicationNodes(SitemapNode parentNode, string urlPrefix)
    {
        const string loanApplicationGeneralSettingsController = "LoanApplicationGeneralSettings";
        var generalSection =
            AddNode(parentNode,
                "General",
                loanApplicationGeneralSettingsController,
                Constants.Actions.Edit,
                $"{urlPrefix}/{loanApplicationGeneralSettingsController}/{Constants.Actions.Edit}");

        // Weekday page needs global _urlPrefix since it doesn't live under FI Configuration Area - it lives in DE Lookup
        const string weekdayLookupController = "Lookup/Weekday";
        var weekdayList =
            AddNode(generalSection,
                "Weekday",
                weekdayLookupController
                , Constants.Actions.List,
                $"{_urlPrefix}/{weekdayLookupController}/{Constants.Actions.List}");
        AddNode(weekdayList, "Weekday Item", weekdayLookupController, Constants.Actions.Edit);

        AddNode(parentNode, "Financial Coaching", "LoanApplicationFinancialCoaching", Constants.Actions.Edit);
        AddNode(parentNode, "Loan Exclusion", "LoanApplicationLoanExclusion", Constants.Actions.Edit);
        AddNode(parentNode, "Calc Engine", "LoanApplicationCalcEngine", Constants.Actions.Edit);
        AddNode(parentNode, "NSF", "LoanApplicationNSFSettings", Constants.Actions.Edit);
        AddNode(parentNode, "Fraud Control", "LoanApplicationFraudControl", Constants.Actions.Edit);
        AddNode(parentNode, "Unsecured Loan Limit", "LoanApplicationUnsecuredLoanLimit", Constants.Actions.Edit);
        AddNode(parentNode, "QCash Connect", "LoanApplicationQCashConnect", Constants.Actions.Edit);
        AddNode(parentNode, "Insurance Products", "LoanApplicationInsuranceProducts", Constants.Actions.Edit);
    }

    private void AddChildrenToDecisionEngineSection(SitemapNode sectionNode, string urlPrefix)
    {
        var lookups = _decisionEngineSettingsService.GetLookups();

        // Skip Weekday Lookup as it is handled under FI Configuration -> Loan Application -> General -> Weekday
        foreach (var lookup in lookups.Where(lookup => lookup.LookupType != Enums.DecisionEngineLookupTypeEnum.Weekday))
        {
            if (lookup.HasIndividualListUi)
            {
                var isDvLookup = _decisionEngineSettingsService.IsDvLookup(lookup.LookupType);

                if (isDvLookup)
                {
                    const string dvLookupControllerName = "DvLookup";
                    var dvLookupNode =
                        AddNode(sectionNode,
                        lookup.LookupType.GetDisplayName(),
                        $"{dvLookupControllerName}/{lookup.LookupType}",
                        Constants.Actions.List,
                        $"{urlPrefix}/DvLookup/{lookup.LookupType}/{Constants.Actions.List}");

                    AddNode(dvLookupNode,
                        $"{lookup.LookupType.GetDisplayName()} Item",
                        $"{dvLookupControllerName}/{lookup.LookupType}",
                        Constants.Actions.Create);
                    AddNode(dvLookupNode,
                        $"{lookup.LookupType.GetDisplayName()} Item",
                        $"{dvLookupControllerName}/{lookup.LookupType}",
                        Constants.Actions.Edit);
                    continue;
                }

                var scoreType = _decisionEngineSettingsService.GetScoreTypeFromLookupType(lookup.LookupType);
                if (scoreType != null)
                {
                    const string scoreLookupControllerName = "DecisionEngineSettings";
                    var scoreLookupNode =
                        AddNode(sectionNode,
                        lookup.LookupType.GetDisplayName(),
                        $"{scoreLookupControllerName}/{scoreType.ToDescriptionString()}",
                        "ListScoreTypes",
                        $"{urlPrefix}/{scoreLookupControllerName}/{scoreType.ToDescriptionString()}/{Constants.Actions.List}");

                    AddNode(scoreLookupNode,
                        $"{lookup.LookupType.GetDisplayName()} Item",
                        $"{scoreLookupControllerName}/{scoreType.ToDescriptionString()}",
                        "CreateBusinessRule");
                    AddNode(scoreLookupNode,
                        $"Edit {lookup.LookupType.GetDisplayName()}",
                        $"{scoreLookupControllerName}/{scoreType.ToDescriptionString()}",
                        Constants.Actions.Edit);
                    continue;
                }
            }

            const string lookupControllerName = "Lookup";
            var lookupNode = AddNode(sectionNode,
                lookup.LookupType.GetDisplayName(),
                $"{lookupControllerName}/{lookup.LookupType}",
                Constants.Actions.List,
                $"{urlPrefix}/{lookupControllerName}/{lookup.LookupType}/{Constants.Actions.List}");

            AddNode(lookupNode,
                $"{lookup.LookupType.GetDisplayName()} Item",
                $"{lookupControllerName}/{lookup.LookupType}",
                lookup.LookupType == Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping
                    ? "EditTrackingRecordDataMapping"
                    : Constants.Actions.Edit);
        }
    }

    private static void AddChildrenToProductsSection(SitemapNode sectionNode)
    {
        AddNode(sectionNode, Constants.Actions.Edit, "ProductFeesAndInterestRate", Constants.Actions.Edit);
        AddNode(sectionNode, Constants.Actions.Edit, "ProductDecisionEngineSettings", Constants.Actions.Edit);
    }

    private static void AddChildrenToMemberInterfaceSection(SitemapNode sectionNode)
    {
        AddNode(sectionNode, "Language", "Language", Constants.Actions.Index);
        AddNode(sectionNode, "Presentation", "Presentation", Constants.Actions.Edit);
        AddNode(sectionNode, "Member Interface Text", "TextList", Constants.Actions.List);
    }

    private static void AddChildrenToReportsSection(SitemapNode sectionNode, string urlPrefix)
    {
        const string applicationLogsControllerName = "ApplicationLogs";
        const string reportDownloadsControllerName = "ReportDownloads";
        const string reportSchedulerControllerName = "ReportScheduler";

        var applicationLogs =
            AddNode(
                sectionNode,
                GetFormattedTitle(applicationLogsControllerName),
                applicationLogsControllerName,
                Constants.Actions.Index,
                $"{urlPrefix}/{applicationLogsControllerName}");

        AddNode(applicationLogs,
            "Application Log Details",
            applicationLogsControllerName,
            "Details");

        AddNode(sectionNode,
            "User Logs",
            "AuditLogs",
            Constants.Actions.Index);

        AddNode(sectionNode,
            GetFormattedTitle(reportDownloadsControllerName),
            reportDownloadsControllerName,
            Constants.Actions.Index);

        AddNode(sectionNode,
            GetFormattedTitle(reportDownloadsControllerName),
            reportDownloadsControllerName,
            "DownloadReport");

        AddNode(sectionNode,
            GetFormattedTitle(reportSchedulerControllerName),
            reportSchedulerControllerName,
            Constants.Actions.Index);
    }

    private void UpdateModelDashboardUrlWithProductId(string productId)
    {
        // Find the existing Model Dashboard node
        var existingNode = _rootNode.Children
            .FirstOrDefault(n => n is { Controller: Constants.Controllers.ModelDashboard, Action: Constants.Actions.List });

        if (existingNode != null)
        {
            // Update the Breadcrumb URL to include the productId
            existingNode.Url =
                $"{_urlPrefix}" +
                $"/{Constants.Controllers.ModelManager}" +
                $"/{Constants.Controllers.ModelDashboard}" +
                $"/{Constants.Actions.List}?ProductID={productId}";
        }
    }

    private static class Constants
    {
        public static class Actions
        {
            public const string Index = "Index";
            public const string List = "List";
            public const string Create = "Create";
            public const string Edit = "Edit";
        }

        public static class Controllers
        {
            public const string Home = "Home";
            public const string UserManagement = "UserManagement";
            public const string FiConfiguration = "FIConfiguration";
            public const string DecisionEngine = "DecisionEngine";
            public const string ModelManager = "ModelManager";
            public const string ModelDashboard = "ModelDashboard";
            public const string Products = "ProductGeneralProductInfo";
            public const string ManageRestrictions = "ManageRestrictions";
            public const string MemberInterface = "MemberInterface";
            public const string SsoTestSetup = "SsoTestSetup";
            public const string Reports = "Reports";
        }
    }
}
