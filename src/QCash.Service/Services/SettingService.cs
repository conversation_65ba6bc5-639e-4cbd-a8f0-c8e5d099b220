using System.Text.RegularExpressions;
using Ganss.Xss;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using QCash.Common.Enums;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Classes.DecisionManager;
using QCash.Service.Models;
using QCash.Service.Models.Core;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Utilities.Extensions;

namespace QCash.Service.Services;

public class SettingService(QCashContext qCashContext, IGuidExtensionService guidExtensionService) : ISettingService
{
    private static readonly string[] ProductExclusionsByName = [ "InvoiceId", "InvoiceDescription", "LoanType", "TimeStamp", "ProductScore", "CalcResult", "DecisionModel", "FinancialInstitution",
        "LoanApplication", "LoanCategoryProduct", "LoanType", "ModelSelector", "ModelTestRunResult", "ProductDecisionModel", "InvoiceDetail", "InvoiceStatusSelection", "InvoicePlan", "CampaignCode",
        "CustomLoanDataMapping", "ScoreType", "ModelTransformationScore"];

    #region Language Import-Export
    /// <summary>
    /// Imports the language items.
    /// </summary>
    /// <param name="languageCode">The language code.</param>
    /// <param name="values">The values.</param>
    /// <returns>Returns a summary of items not found or not updated.</returns>
    public async Task<string> ImportLanguageItemsAsync(string languageCode, IList<ImportExportTextDto> values)
    {
        var sanitizer = new HtmlSanitizer();

        var notFound = new List<string>();
        var notUpdated = new List<string>();

        try
        {
            var settings = await qCashContext.Settings.AsNoTracking().FirstAsync();
            //These may all be modified, so we don't use AsNoTracking here
            var memberInterfaceTexts = await qCashContext.InterfaceDefaultTexts.Where(o => o.Language.LanguageCode == languageCode).Include(o => o.InterfaceOverrideTexts).ToListAsync();
            var paymentOptionTitles = await qCashContext.PaymentOptionTitles.Include(o => o.TranslationPaymentOptionTitles).ToListAsync();
            var weekdays = await qCashContext.Weekdays.Include(o => o.TranslationWeekdays).Where(o => !o.IsDeleted).ToListAsync();
            var purposeOfLoanTypes = await qCashContext.PurposeOfLoanTypes.Include(o => o.TranslationPurposeOfLoanTypes).Where(o => !o.IsDeleted).ToListAsync();
            var aanReasons = await qCashContext.AanReasonDefaultTexts.Where(o => o.Language.LanguageCode == languageCode).Include(o => o.AANReasonOverrideTexts).ToListAsync();
            var paymentDates = await qCashContext.PaymentDates.Include(o => o.TranslationPaymentDates).OrderBy(p => p.Value).ToListAsync();
            //var englishLanguageId = (await qCashContext.Languages.SingleAsync(a => a.LanguageCode == "")).Id;

            foreach (var item in values)
            {
                switch (item.Type)
                {
                    case Enums.ImportExportInterfaceTextType.InterfaceText:
                        var miTxt = memberInterfaceTexts.FirstOrDefault(p => p.Abrv == item.Abrv);
                        if (miTxt != null)
                        {
                            var newValue = sanitizer.Sanitize(item.FieldValue);
                            if (newValue == miTxt.FieldValue)
                            {
                                //if an override exists, remove it - the value has been set back to the default
                                if (miTxt.InterfaceOverrideTexts.Count > 0)
                                {
                                    qCashContext.InterfaceOverrideTexts.Remove(miTxt.InterfaceOverrideTexts.First());
                                }
                                else
                                {
                                    notUpdated.Add($"[{item.Abrv}]");
                                }
                            }
                            else if (miTxt.InterfaceOverrideTexts.Count > 0 && miTxt.InterfaceOverrideTexts.First().FieldValue == newValue)
                            {
                                //there is an override, and the fieldvalues already match
                                notUpdated.Add($"[{item.Abrv}]");
                            }
                            else if (miTxt.InterfaceOverrideTexts.Count > 0 && miTxt.InterfaceOverrideTexts.First().FieldValue != newValue)
                            {
                                //there is an override, and the fieldvalues do not match
                                miTxt.InterfaceOverrideTexts.First().FieldValue = newValue;
                            }
                            else if (miTxt.InterfaceOverrideTexts.Count == 0)
                            {
                                //add a new Override
                                InterfaceOverrideText newOverride = new InterfaceOverrideText
                                {
                                    Id = guidExtensionService.NewSequentialGuid(),
                                    InterfaceDefaultTextId = miTxt.Id,
                                    FinancialInstitutionId = settings.FinancialInstitutionId,
                                    FieldValue = newValue,
                                    DateCreatedUtc = DateTime.UtcNow,
                                    DateUpdatedUtc = DateTime.UtcNow,
                                    TimeStamp = []
                                };
                                miTxt.InterfaceOverrideTexts.Add(newOverride);
                            }
                        }
                        else
                        {
                            notFound.Add($"[{item.Abrv}]");
                        }

                        break;

                    case Enums.ImportExportInterfaceTextType.Weekday:
                        var weekdayTxt = weekdays.FirstOrDefault(p => p.Abrv == item.Abrv);
                        if (weekdayTxt != null)
                        {
                            if (string.IsNullOrEmpty(languageCode))
                            {
                                if (item.FieldValue == weekdayTxt.Description)
                                {
                                    //They still match
                                    notUpdated.Add($"[{item.Abrv}]");
                                }
                                else
                                {
                                    //update the default (english) FieldValue
                                    weekdayTxt.Description = item.FieldValue;
                                }
                            }
                            else
                            {
                                //not English
                                var translation = weekdayTxt.TranslationWeekdays.FirstOrDefault(p => p.LanguageCode == languageCode);
                                if (translation != null)
                                {
                                    if (item.FieldValue == translation.Description)
                                    {
                                        //They still match
                                        notUpdated.Add($"[{item.Abrv}]");
                                    }
                                    else
                                    {
                                        //update the non-english FieldValue
                                        translation.Description = item.FieldValue;
                                    }
                                }
                                else
                                {
                                    // Need to create this translation.
                                    var newTranslationText = new TranslationWeekday()
                                    {
                                        Id = guidExtensionService.NewSequentialGuid(),
                                        LanguageCode = languageCode,
                                        Name = item.Name,
                                        Description = item.FieldValue,
                                        WeekdayId = weekdayTxt.Id,
                                        DateCreatedUtc = DateTime.UtcNow,
                                        DateUpdatedUtc = DateTime.UtcNow,
                                        TimeStamp = [],
                                    };
                                    weekdayTxt.TranslationWeekdays.Add(newTranslationText);
                                }
                            }
                        }
                        else
                        {
                            notFound.Add($"[{item.Abrv}]");
                        }

                        break;

                    case Enums.ImportExportInterfaceTextType.PaymentOptionTitle:
                        var potText = paymentOptionTitles.FirstOrDefault(p => p.Abrv == item.Abrv);
                        if (potText != null)
                        {
                            if (string.IsNullOrEmpty(languageCode))
                            {
                                if (item.FieldValue == potText.FieldValue)
                                {
                                    //They still match
                                    notUpdated.Add($"[{item.Abrv}]");
                                }
                                else
                                {
                                    //update the default (english) FieldValue
                                    potText.FieldValue = item.FieldValue;
                                }
                            }
                            else
                            {
                                var translation = potText.TranslationPaymentOptionTitles.FirstOrDefault(p => p.LanguageCode == languageCode);

                                if (translation != null)
                                {
                                    if (item.FieldValue == translation.FieldValue)
                                    {
                                        //They still match
                                        notUpdated.Add($"[{item.Abrv}]");
                                    }
                                    else
                                    {
                                        //update the non-english FieldValue
                                        translation.FieldValue = item.FieldValue;
                                    }
                                }
                                else
                                {
                                    // Need to create this translation.
                                    var newTranslationText = new TranslationPaymentOptionTitle()
                                    {
                                        Id = guidExtensionService.NewSequentialGuid(),
                                        LanguageCode = languageCode,
                                        FieldValue = item.FieldValue,
                                        PaymentOptionTitleId = potText.Id,
                                        DateCreatedUtc = DateTime.UtcNow,
                                        DateUpdatedUtc = DateTime.UtcNow,
                                        TimeStamp = [],
                                    };
                                    potText.TranslationPaymentOptionTitles.Add(newTranslationText);
                                }
                            }
                        }
                        else
                        {
                            notFound.Add($"[{item.Abrv}]");
                        }

                        break;

                    case Enums.ImportExportInterfaceTextType.PurposeOfLoanType:
                        // In PurposeOfLoanType user can only change 'Name' and 'Description' for eng, other lang just 'Name'. 
                        var lptTxt = purposeOfLoanTypes.FirstOrDefault(p => p.Abrv == item.Abrv);
                        if (lptTxt != null)
                        {
                            var newFieldValue = item.FieldValue.Length <= 500 ? item.FieldValue : item.FieldValue.Substring(0, 500);
                            var newNameValue = item.Name.Length <= 50 ? item.Name : item.Name.Substring(0, 50);

                            if (string.IsNullOrEmpty(languageCode))
                            {
                                if (newFieldValue == lptTxt.Description && newNameValue == lptTxt.Name)
                                {
                                    //They still match
                                    notUpdated.Add($"[{item.Abrv}]");
                                }
                                else
                                {
                                    //update the default (english) Description and Name
                                    lptTxt.Description = newFieldValue;
                                    lptTxt.Name = newNameValue;
                                }
                            }
                            else
                            {
                                var translation = lptTxt.TranslationPurposeOfLoanTypes.FirstOrDefault(p => p.LanguageCode == languageCode);
                                if (translation != null)
                                {
                                    if (newNameValue == translation.Name)
                                    {
                                        //They still match
                                        notUpdated.Add($"[{item.Abrv}]");
                                    }
                                    else
                                    {
                                        //update the non-english FieldValue
                                        translation.Name = newNameValue;
                                    }
                                }
                                else
                                {
                                    // Need to create this translation.
                                    var newTranslationText = new TranslationPurposeOfLoanType()
                                    {
                                        Id = guidExtensionService.NewSequentialGuid(),
                                        LanguageCode = languageCode,
                                        Name = newNameValue,
                                        PurposeOfLoanTypeId = lptTxt.Id,
                                        DateCreatedUtc = DateTime.UtcNow,
                                        DateUpdatedUtc = DateTime.UtcNow,
                                        TimeStamp = [],
                                    };
                                    lptTxt.TranslationPurposeOfLoanTypes.Add(newTranslationText);
                                }
                            }
                        }
                        else
                        {
                            notFound.Add($"[{item.Abrv}]");
                        }

                        break;

                    case Enums.ImportExportInterfaceTextType.AanReason:
                        var aanTxt = aanReasons.FirstOrDefault(p => p.Slug == item.Abrv);
                        //Note: only Description can change now, not Name
                        if (aanTxt != null)
                        {
                            var newValue = sanitizer.Sanitize(item.FieldValue);
                            newValue = newValue.Length <= 500 ? newValue : newValue.Substring(0, 500);

                            if (newValue == aanTxt.Description)
                            {
                                //if an override exists, remove it - the value has been set back to the default
                                if (aanTxt.AANReasonOverrideTexts.Count > 0)
                                {
                                    qCashContext.AANReasonOverrideTexts.Remove(aanTxt.AANReasonOverrideTexts.First());
                                }
                                else
                                {
                                    notUpdated.Add($"[{item.Abrv}]");
                                }
                            }
                            else if (aanTxt.AANReasonOverrideTexts.Count > 0 && aanTxt.AANReasonOverrideTexts.First().Description == newValue)
                            {
                                //there is an override, and the Descriptions already match
                                notUpdated.Add($"[{item.Abrv}]");
                            }
                            else if (aanTxt.AANReasonOverrideTexts.Count > 0 && aanTxt.AANReasonOverrideTexts.First().Description != newValue)
                            {
                                //there is an override, and the Descriptions do not match
                                aanTxt.AANReasonOverrideTexts.First().Description = newValue;
                            }
                            else if (aanTxt.AANReasonOverrideTexts.Count == 0)
                            {
                                //add a new Override
                                AANReasonOverrideText newOverride = new AANReasonOverrideText
                                {
                                    Id = guidExtensionService.NewSequentialGuid(),
                                    AanReasonDefaultTextId = aanTxt.Id,
                                    FinancialInstitutionId = settings.FinancialInstitutionId,
                                    Description = newValue,
                                    DateCreatedUtc = DateTime.UtcNow,
                                    DateUpdatedUtc = DateTime.UtcNow,
                                    TimeStamp = []
                                };
                                aanTxt.AANReasonOverrideTexts.Add(newOverride);
                            }
                        }
                        else
                        {
                            notFound.Add($"[{item.Abrv}]");
                        }

                        break;

                    case Enums.ImportExportInterfaceTextType.PaymentDate:
                        //Matches on Name=Value
                        var pdateTxt = paymentDates.FirstOrDefault(p => p.Value.ToString() == item.Name);
                        if (pdateTxt != null)
                        {
                            var newFieldValue = item.FieldValue.Length <= 500 ? item.FieldValue : item.FieldValue.Substring(0, 500);

                            if (string.IsNullOrEmpty(languageCode))
                            {
                                if (newFieldValue == pdateTxt.Description)
                                {
                                    //They still match
                                    notUpdated.Add($"[{item.Abrv}]");
                                }
                                else
                                {
                                    //update the default (english) Description
                                    pdateTxt.Description = newFieldValue;
                                }
                            }
                            else
                            {
                                var translation = pdateTxt.TranslationPaymentDates.FirstOrDefault(p => p.LanguageCode == languageCode);
                                if (translation != null)
                                {
                                    if (newFieldValue == translation.Description)
                                    {
                                        //They still match
                                        notUpdated.Add($"[{item.Abrv}]");
                                    }
                                    else
                                    {
                                        //update the non-english FieldValue
                                        translation.Description = newFieldValue;
                                    }
                                }
                                else
                                {
                                    // Need to create this translation.
                                    var newTranslationText = new TranslationPaymentDate()
                                    {
                                        Id = guidExtensionService.NewSequentialGuid(),
                                        LanguageCode = languageCode,
                                        Value = int.Parse(item.Name),
                                        Description = newFieldValue,
                                        PaymentDateId = pdateTxt.Id,
                                        DateCreatedUtc = DateTime.UtcNow,
                                        DateUpdatedUtc = DateTime.UtcNow,
                                        TimeStamp = [],
                                    };
                                    pdateTxt.TranslationPaymentDates.Add(newTranslationText);
                                }
                            }
                        }
                        else
                        {
                            notFound.Add($"[{item.Abrv}]");
                        }

                        break;
                }
            }

            await qCashContext.SaveChangesAsync();

        }
        catch (Exception ex)
        {
            return ex.GetDetailedErrorMessage(Environment.NewLine);
        }

        var notUpdatedStr = string.Empty;

        if (notFound.Count > 0)
        {
            notUpdatedStr = $"These Interface Texts were not found in the database: {string.Join("; ", notFound)}";
        }

        if (notUpdated.Count > 0)
        {
            notUpdatedStr += $"These Interface Texts were not updated: {string.Join("; ", notUpdated)}";
        }

        return notUpdatedStr;
    }

    /// <summary>
    /// Gets all language items. Used for exporting the language.
    /// </summary>
    /// <param name="languageCode">The language code.</param>
    /// <returns>All language items.</returns>
    public async Task<IList<ImportExportTextDto>> GetAllLanguageItemsAsync(string? languageCode)
    {
        var memberInterfaceTexts = await qCashContext.InterfaceDefaultTexts.AsNoTracking().Where(o => o.Language.LanguageCode == languageCode).Include(o => o.InterfaceOverrideTexts).ToListAsync();
        var paymentOptionTitles = await qCashContext.PaymentOptionTitles.AsNoTracking().Include(o => o.TranslationPaymentOptionTitles).ToListAsync();
        var weekdays = await qCashContext.Weekdays.AsNoTracking().Include(o => o.TranslationWeekdays).Where(o => !o.IsDeleted).ToListAsync();
        var purposeOfLoanTypes = await qCashContext.PurposeOfLoanTypes.AsNoTracking().Include(o => o.TranslationPurposeOfLoanTypes).Where(o => !o.IsDeleted).ToListAsync();
        var aanReasons = await qCashContext.AanReasonDefaultTexts.AsNoTracking().Where(o => o.Language.LanguageCode == languageCode).Include(o => o.AANReasonOverrideTexts).ToListAsync();
        var paymentDates = await qCashContext.PaymentDates.AsNoTracking().Include(o => o.TranslationPaymentDates).OrderBy(p => p.Value).ToListAsync();

        var texts = new List<ImportExportTextDto>();
        foreach (var item in memberInterfaceTexts)
        {
            //already filtered by language in EF query
            if (item.InterfaceOverrideTexts.Count > 0)
            {
                var overrideText = item.InterfaceOverrideTexts.First();
                texts.Add(new ImportExportTextDto(Enums.ImportExportInterfaceTextType.InterfaceText, item.Abrv, item.Name ?? string.Empty, overrideText.FieldValue ?? string.Empty));
            }
            else
            {
                texts.Add(new ImportExportTextDto(Enums.ImportExportInterfaceTextType.InterfaceText, item.Abrv, item.Name ?? string.Empty, item.FieldValue ?? string.Empty));
            }
        }

        foreach (var item in weekdays)
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                texts.Add(new ImportExportTextDto(Enums.ImportExportInterfaceTextType.Weekday, item.Abrv, item.Name, item.Description));
            }
            else
            {
                var translation = item.TranslationWeekdays.FirstOrDefault(p => p.LanguageCode == languageCode);
                if (translation != null)
                {
                    texts.Add(new ImportExportTextDto(Enums.ImportExportInterfaceTextType.Weekday, item.Abrv, translation.Name, translation.Description));
                }
            }
        }

        foreach (var item in paymentOptionTitles)
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                texts.Add(new ImportExportTextDto(Enums.ImportExportInterfaceTextType.PaymentOptionTitle, item.Abrv, item.Name, item.FieldValue ?? string.Empty));
            }
            else
            {
                var translation = item.TranslationPaymentOptionTitles.FirstOrDefault(p => p.LanguageCode == languageCode);
                if (translation != null)
                {
                    texts.Add(new ImportExportTextDto(Enums.ImportExportInterfaceTextType.PaymentOptionTitle, item.Abrv, item.Name, translation.FieldValue ?? string.Empty));
                }
            }
        }

        foreach (var item in purposeOfLoanTypes)
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                texts.Add(new ImportExportTextDto(Enums.ImportExportInterfaceTextType.PurposeOfLoanType, item.Abrv, item.Name, item.Description));
            }
            else
            {
                var translation = item.TranslationPurposeOfLoanTypes.FirstOrDefault(p => p.LanguageCode == languageCode);
                if (translation != null)
                {
                    texts.Add(new ImportExportTextDto(Enums.ImportExportInterfaceTextType.PurposeOfLoanType, item.Abrv, translation.Name, string.Empty));
                }
            }
        }

        foreach (var item in aanReasons)
        {
            //already filtered by language in EF query
            if (item.AANReasonOverrideTexts.Count > 0)
            {
                var overrideText = item.AANReasonOverrideTexts.First();
                texts.Add(new ImportExportTextDto(Enums.ImportExportInterfaceTextType.AanReason, item.Slug, item.Name, overrideText.Description));
            }
            else
            {
                texts.Add(new ImportExportTextDto(Enums.ImportExportInterfaceTextType.AanReason, item.Slug, item.Name, item.Description));
            }
        }

        foreach (var item in paymentDates)
        {
            if (string.IsNullOrEmpty(languageCode))
            {
                texts.Add(new ImportExportTextDto(Enums.ImportExportInterfaceTextType.PaymentDate, "PaymentDate", item.Value.ToString(), item.Description ?? string.Empty));
            }
            else
            {
                var translation = item.TranslationPaymentDates.FirstOrDefault(p => p.LanguageCode == languageCode);
                if (translation != null)
                {
                    texts.Add(new ImportExportTextDto(Enums.ImportExportInterfaceTextType.PaymentDate, "PaymentDate", translation.Value.ToString() ?? string.Empty, translation.Description ?? string.Empty));
                }
            }
        }

        return texts;
    }
    #endregion

    //Below are other methods migrated from QC6 that were not just trivial repo calls and were actively
    //used in QC6.
    //As these are integrated into other logic, move it out of this service so the service itself can eventually
    //be removed or turned into a Language Import/Export service.

    public async Task<IList<Data.Models.Product>> GetAllProductsAsync(bool includePreproduct = false)
    {
        var products = await qCashContext.Products.ToListAsync();
        if (includePreproduct)
        {
            return products;
        }
        return products.Where(p => !guidExtensionService.IsNullOrEmpty(p.Id)).ToList();
    }

    /// <summary>
    /// Gets all products.
    /// </summary>
    /// <param name="loanCategoryId">The loan category identifier.</param>
    /// <param name="loanTypeId">The loan type identifier.</param>
    /// <param name="includePreProduct">Include pre product flag.</param>
    /// <returns>List of products.</returns>
    public async Task<IList<Data.Models.Product>> GetAllProductAsync(Guid? loanCategoryId, Guid? loanTypeId, bool includePreProduct = false)
    {
        var query = qCashContext.Products.AsQueryable();

        if (!includePreProduct)
        {
            query = query.Where(p => p.Id != Guid.Empty);
        }
        if (loanCategoryId.HasValue)
        {
            query = query.Where(o => o.LoanCategoryId == loanCategoryId.Value);
        }
        if (loanTypeId.HasValue)
        {
            query = query.Where(p => p.LoanTypeId == loanTypeId.Value);
        }

        return await query.ToListAsync();
    }

    public async Task<bool> IsOldPasswordAsync(string username, string encryptedPassword) =>
        await qCashContext.OldPasswords.Where(p => p.UserName == username).OrderByDescending(p => p.DateCreatedUtc).Distinct().Take(6)
            .AnyAsync(p => p.Password == encryptedPassword);

    /// <summary>
    /// Gets the documentfilename by language Id, TILADocumentType, product Id, and groupId.
    /// </summary>
    public async Task<DocumentFileName?> GetTilaDocumentFileNameAsync(Guid languageId, TilaDocumentTemplateType tilaDocumentType, Guid productId, Guid? groupId = null) =>
        await qCashContext.DocumentFileNames
            .Where(d => d.LanguageId == languageId && d.TilaDocumentType == tilaDocumentType.ToString() && d.ProductId == productId && d.TemplateGroupId == groupId)
            .Include(d => d.DocumentType)
            .FirstOrDefaultAsync(d => d.DocumentType.Abrv == "DTTILA");

    public async Task<List<string>> GetStatesByGroupIdAsync(Guid groupId)
    {
        var resultStates = qCashContext.TemplateGroupRegions
            .Where(g => g.TemplateGroupId == groupId && !string.IsNullOrEmpty(g.State.Name))
            .Select(s => s.State.Name);

        return await resultStates.OrderBy(x => x).ToListAsync();
    }

    public async Task ValidateLoanFeeAsync(LoanFee entity)
    {
        if (entity.FromQuantity >= entity.ToQuantity)
        {
            throw new Exception("'From' value must be lower than 'To' value.");
        }

        var loanFees = await qCashContext.LoanFees.AsNoTracking()
            .Where(p => p.ProductId == entity.ProductId && p.InvoicePlanId == entity.InvoicePlanId && p.Id != entity.Id)
            .ToListAsync();

        var lowerScore = loanFees.LastOrDefault(s => s.FromQuantity <= entity.FromQuantity);
        var higherScore = loanFees.FirstOrDefault(s => s.FromQuantity >= entity.FromQuantity);

        if (lowerScore != null && entity.FromQuantity <= lowerScore.ToQuantity)
        {
            throw new Exception($"'From' value must be higher than {lowerScore.ToQuantity} because it overlaps with an existing score item.");
        }

        if (higherScore != null && entity.ToQuantity >= higherScore.FromQuantity)
        {
            throw new Exception($"'To' value must be lower than {higherScore.FromQuantity} because it overlaps with an existing score item.");
        }
    }

    /// <summary>
    /// Validate the product score.
    /// </summary>
    /// <param name="entity">The entity.</param>
    /// <exception cref="System.Exception">Can't insert score - it overlaps with an existing score items.</exception>
    public async Task ValidateProductScoreAsync(ProductScore entity)
    {
        var scores = await qCashContext.ProductScores.AsNoTracking().Include(o => o.Score)
            .Where(p => p.ProductId.Equals(entity.ProductId) && p.Score.ScoreTypeId.Equals(entity.Score.ScoreTypeId) && p.Id != entity.Id)
            .Select(p => p.Score)
            .ToListAsync();
        var valid = ScoreValidator.Validate(entity.Score, scores);
        if (!valid)
        {
            throw new Exception("Can't insert score - it overlaps with an existing score item.");
        }
    }

    /// <summary>
    /// Gets the active products for display json.
    /// </summary>
    public async Task<string> GetActiveProductsForDisplayJsonAsync()
    {
        var intrestBaseLoanPaybackPeriodScoreTypeId = await qCashContext.ScoreTypes.Where(o => o.Abrv == "IntrestBaseLoanPaybackPeriod").Select(o => o.Id).SingleOrDefaultAsync();
        var variableLoanOriginationFeeScoreTypeId = await qCashContext.ScoreTypes.Where(o => o.Abrv == "LoanOriginationFee").Select(o => o.Id).SingleOrDefaultAsync();

        var jsonSerializer = new JsonSerializer()
        {
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore
        };

        // Get all active products with included properties.
        var products = await qCashContext.Products
            .AsNoTracking()
            .Include(o => o.GlobalExclusionStates)
            .Include(o => o.LoanAmountQualifiers)
            .Include(o => o.LoanCategory)
            .Include(o => o.StateRestrictedInterestRates)
            .Include(o => o.ProductScores)
            .ThenInclude(o => o.Score)
            .Include(o => o.InterfaceDefaultTexts)
            .ThenInclude(o => o.InterfaceOverrideTexts)
            .Include(o => o.LoanFees)
            .Where(p => p.IsActive)
            .ToListAsync();

        //var products = Repository.GetProductsByIsActive(true, nameof(IProduct.GlobalExclusionState),
        //    nameof(IProduct.LoanAmountQualifier),
        //    nameof(IProduct.LoanCategoryProduct),
        //    nameof(IProduct.StateRestrictedInterestRate),
        //    nameof(IProduct.ProductScore),
        //    nameof(IProduct.MemberInterfaceText),
        //    nameof(IProduct.MobileMemberInterfaceText),
        //    nameof(IProduct.LoanFee),
        //    nameof(IProduct.LoanCategoryProduct),
        //    string.Format($"{nameof(IProduct.ProductScore)}.{nameof(IProductScore.Score)}"));

        var objects = new List<object>();
        foreach (var product in products)
        {
            var productJson = JObject.FromObject(product, jsonSerializer);

            // Add IntrestBaseLoanPaybackPeriod and VariableLoanOriginationFee scores
            var productScores = product.ProductScores.Select(p => p.Score).ToList();
            var intrestBaseLoanPaybackPeriodScores = productScores.Where(p => p.ScoreTypeId == intrestBaseLoanPaybackPeriodScoreTypeId).ToArray();
            var variableLoanOriginationFeeScores = productScores.Where(p => p.ScoreTypeId == variableLoanOriginationFeeScoreTypeId).ToArray();

            productJson.Add("IntrestBaseLoanPaybackPeriodScore", JToken.FromObject(intrestBaseLoanPaybackPeriodScores, jsonSerializer));
            productJson.Add("VariableLoanOriginationFeeScore", JToken.FromObject(variableLoanOriginationFeeScores, jsonSerializer));

            var properties = productJson.Properties().ToArray();
            for (var i = 0; i < properties.Count(); i++)
            {
                ExcludeProductProperty(properties[i]);
            }

            // Add Loan Category.
            //var loanCategory = product.LoanCategory;
            //productJson.Add("LoanCategory", loanCategory.Name);
            objects.Add(productJson.ToObject<object>()!);
        }

        var json = JsonConvert.SerializeObject(objects, new JsonSerializerSettings() { ReferenceLoopHandling = ReferenceLoopHandling.Ignore });
        return Regex.Unescape(json);
    }

    private void ExcludeProductProperty(JProperty jProperty)
    {
        // Exclusion by Guid type.
        if (jProperty.Value.Type == JTokenType.Guid || (jProperty.Value.Type == JTokenType.String && Guid.TryParse(jProperty.Value.ToString(), out Guid result)))
        {
            jProperty.Remove();
            return;
        }

        // Exclusion by name. 
        if (ProductExclusionsByName.Contains(jProperty.Name))
        {
            jProperty.Remove();
            return;
        }

        // Exclude on relations.
        if (jProperty.Value.Type == JTokenType.Array)
        {
            // Get array of properties (This should not be more than 1).
            var jChildren = jProperty.Children().ToArray();
            for (var i = 0; i < jChildren.Count(); i++)
            {
                // Get array of objects.
                var objects = jChildren.Children().ToArray();
                for (var o = 0; o < objects.Count(); o++)
                {
                    var jChild = objects[o];
                    if (jChild != null)
                    {
                        // Parse JToken to JObject to extract the array of properties.
                        var jObject = (JObject)jChild;
                        var properties = jObject.Properties().ToArray();
                        for (var j = 0; j < properties.Count(); j++)
                        {
                            // Do exclusion on each property. 
                            ExcludeProductProperty(properties[j]);
                        }
                    }
                }
            }
        }
    }

    //TODO - in QC6 these were special MemberInterfaceTexts added as part of inserting a product.
    //  Once PBI #10443 is fully defined, the defaults here will probably just be InterfaceDefaultText entries. However, since
    //  some of that logic has yet to be determined, the old method (slightly cleaned up) is included below as a reference.

    //public Data.Models.Product InsertProduct(Data.Models.Product entity)
    //{
    //    if (entity != null)
    //    {
    //        var loanDisclosureTemplate = "<p><strong>Congratulations!</strong><br />" +
    //                                     "Funding for your {Product.Name} loan is dependent upon your reading, understanding and acceptance of the Truth-in-Lending Act Disclosures (TILA) and Loan Agreement. <br /> <br />" +
    //                                     "<strong>NOTE: The APR is the cost of your loan expressed as a yearly rate.</strong></p>";

    //        var automaticPaymentTemplate = "<p><strong>Automatic Payment</strong><br />" +
    //                                       "I understand that by default my Credit Union will setup an automatic payment transfer from my {FundingAccountType} for the scheduled payment.<br /> " +
    //                                       "By unchecking this box will decline consent for automatic payment.</p> ";

    //        var loanPreviewFeeBasedTemplate = "<p><strong>Loan Preview:</strong></p> " +
    //                                          "<ul>" +
    //                                          "<li>Amount Financed: ${LA.AmountFinanced(#,##0.00)}</li>" +
    //                                          "<li>APR: {LA.AnnualPercentageRate(#,##0.00)}%</li>" +
    //                                          "<li>Finance Charge: $ {LA.FinanceCharge(#,##0.00)}</li>" +
    //                                          "<li>Total of Payments: ${LA.TotalOfPayments(#,##0.00)}</li>" +
    //                                          "<li>Maturity Date: {finalPaymentDate}</li>" +
    //                                          "</ul>";

    //        var loanPreviewInterestBasedTemplate = "<p><strong>Loan Preview:</strong></p>" +
    //                                               "<ul>" +
    //                                               "<li>Amount Financed: ${LA.AmountFinanced(#,##0.00)}</li>" +
    //                                               "<li>APR: {AdjustedAPR}%</li>" +
    //                                               "<li>Finance Charge: ${LA.FinanceCharge(#,##0.00)}</li>" +
    //                                               "<li>Total of Payments: ${LA.TotalOfPayments(#,##0.00)}</li>" +
    //                                               "<li>Total Count of Payments: {totalNumberOfPayments}</li>" +
    //                                               "<li>First Payment Date: {firstPaymentDate}</li>" +
    //                                               "<li>First Payment Amount: ${firstPaymentAmount}</li>" +
    //                                               "</ul>";

    //        var productDescriptionFeeBasedTemplate =
    //            "<p style='background: rgb(0, 55, 100); border: 1px solid rgb(204, 204, 204); color: rgb(255, 255, 255); padding: 5px 10px'><strong>QCash</strong></p>" +
    //            "<ul>" +
    //            "<li>No credit report required</li>" +
    //            "<li>Short term loan</li>" +
    //            "<li>Borrow between $50 to $700</li>" +
    //            "<li>Maximum of 2 loans at a time</li>" +
    //            "<li>Maximum amount not to exceed $700</li>" +
    //            "<li>Must be credit union member for at least 1 month</li>" +
    //            "<li>Eligibility for Primary Account holder only</li>" +
    //            "</ul> ";

    //        var productDescriptionInterestBasedTemplate =
    //            "<p style='background: rgb(0, 55, 100); border: 1px solid rgb(204, 204, 204); color: rgb(255, 255, 255); padding: 5px 10px'><strong>QCash Plus</strong></p>" +
    //            "<ul>" +
    //            "<li>No credit report required.</li>" +
    //            "<li>Borrow between $701 and $4,000</li>" +
    //            "<li>Repayment period up to 36 months</li>" +
    //            "<li>$25 loan origination fee</li>" +
    //            "<li>Maximum of 2 loans at a time</li>" +
    //            "<li>Maximum amount not to exceed $4,000</li>" +
    //            "<li>Must be credit union member for at least 6 months</li>" +
    //            "<li>Eligibility for Primary Account holder only</li>" +
    //            "</ul> ";

    //        var shortDescriptionTemplate = "<p>QCash. A small dollar loan solution for Credit Unions.</p>";

    //        var fundingCompleteTemplate = "<p>Your loan funds have been deposited into your {FundingAccountType} account / {shareID} and are available immediately! " +
    //                                      "Thank you for choosing {clientFullName}.</p>";

    //        var fundingPendingTemplate = "<p><span style='line - height: 1.6em'>Your transaction is complete; " +
    //                                     "however, the system is temporarily down for maintenance and we are unable to deposit the funds to your account at this time. " +
    //                                     "We will deposit the funds once the system is available. Thank you for your patience.</span></p>";

    //        // Insert product interface text.
    //        int sortOrder = memberInterfaceTextLookup.GetAllItems().OrderBy(p => p.SortOrder).First().SortOrder;

    //        var productDescription = lookupService.CreateMemberInterfaceText();
    //        productDescription.ProductId = entity.Id;
    //        productDescription.ApplicationId = entity.FinancialInstitutionId;
    //        productDescription.IsDisclosure = false;
    //        productDescription.Abrv = $"{entity.Slug}-ProductDescription";
    //        productDescription.Name = $"{entity.Slug} Product Description";
    //        productDescription.SortOrder = ++sortOrder;

    //        var shortDescription = lookupService.CreateMemberInterfaceText();
    //        shortDescription.ApplicationId = entity.FinancialInstitutionId;
    //        shortDescription.ProductId = entity.Id;
    //        shortDescription.Abrv = $"{entity.Slug}-ShortDescription";
    //        shortDescription.Name = $"{entity.Slug} Short Description";
    //        shortDescription.IsDisclosure = false;
    //        shortDescription.FieldValue = shortDescriptionTemplate;
    //        shortDescription.SortOrder = ++sortOrder;

    //        var loanDisclosureText = lookupService.CreateMemberInterfaceText();
    //        loanDisclosureText.ApplicationId = entity.FinancialInstitutionId;
    //        loanDisclosureText.ProductId = entity.Id;
    //        loanDisclosureText.Abrv = $"{entity.Slug}-LoanDisclosure";
    //        loanDisclosureText.Name = $"Loan Disclosure";
    //        loanDisclosureText.FieldValue = loanDisclosureTemplate;
    //        loanDisclosureText.IsDisclosure = true;
    //        loanDisclosureText.SortOrder = ++sortOrder;

    //        var autoPayment = lookupService.CreateMemberInterfaceText();
    //        autoPayment.ProductId = entity.Id;
    //        autoPayment.ApplicationId = entity.FinancialInstitutionId;
    //        autoPayment.IsDisclosure = false;
    //        autoPayment.Abrv = memberInterfaceHelper.GetAutoPaymentAbrvForProduct(entity.Slug);
    //        autoPayment.Name = InterfaceTextConstants.AUTO_PAYMENT_NAME;
    //        autoPayment.SortOrder = ++sortOrder;
    //        autoPayment.FieldValue = automaticPaymentTemplate;

    //        var fundingCompleteText = lookupService.CreateMemberInterfaceText();
    //        fundingCompleteText.ApplicationId = entity.FinancialInstitutionId;
    //        fundingCompleteText.ProductId = entity.Id;
    //        fundingCompleteText.Abrv = $"{entity.Slug}-FundingComplete";
    //        fundingCompleteText.Name = $"{entity.Slug} FundingComplete";
    //        fundingCompleteText.FieldValue = fundingCompleteTemplate;
    //        fundingCompleteText.IsDisclosure = false;
    //        fundingCompleteText.SortOrder = ++sortOrder;

    //        var fundingPendingText = lookupService.CreateMemberInterfaceText();
    //        fundingPendingText.ApplicationId = entity.FinancialInstitutionId;
    //        fundingPendingText.ProductId = entity.Id;
    //        fundingPendingText.Abrv = $"{entity.Slug}-FundingPending";
    //        fundingPendingText.Name = $"{entity.Slug} FundingPending";
    //        fundingPendingText.FieldValue = fundingPendingTemplate;
    //        fundingPendingText.IsDisclosure = false;
    //        fundingPendingText.SortOrder = ++sortOrder;

    //        var sb = new StringBuilder();
    //        sb.Append($"TilaPreview-{entity.Abrv}-{entity.Slug}");
    //        if (sb.Length > 46)
    //            sb.Remove(46, (sb.Length - 46));

    //        if (loanTypeLookup.FB.Id == entity.LoanTypeId)
    //            sb.Append("-FB");
    //        else
    //            sb.Append("-IB");

    //        // Insert product interface text.
    //        var loanPreview = lookupService.CreateMemberInterfaceText();
    //        loanPreview.ProductId = entity.Id;
    //        loanPreview.ApplicationId = entity.FinancialInstitutionId;
    //        loanPreview.IsDisclosure = false;
    //        loanPreview.Abrv = sb.ToString();
    //        loanPreview.Name = InterfaceTextConstants.LOAN_PREVIEW_NAME;
    //        loanPreview.FieldValue = string.Empty;
    //        loanPreview.SortOrder = ++sortOrder;

    //        var uow = Repository.CreateUnitOfWork();
    //        base.InsertProduct(uow, entity);

    //        if (loanTypeLookup.IB.Id == entity.LoanTypeId)
    //        {
    //            productDescription.FieldValue = productDescriptionInterestBasedTemplate;
    //            loanPreview.FieldValue = loanPreviewInterestBasedTemplate;
    //        }
    //        else if (loanTypeLookup.FB.Id == entity.LoanTypeId)
    //        {
    //            productDescription.FieldValue = productDescriptionFeeBasedTemplate;
    //            loanPreview.FieldValue = loanPreviewFeeBasedTemplate;
    //        }
    //        else
    //        {
    //            productDescription.FieldValue = string.Empty;
    //            loanPreview.FieldValue = string.Empty;
    //        }
    //    }
    //}

}
