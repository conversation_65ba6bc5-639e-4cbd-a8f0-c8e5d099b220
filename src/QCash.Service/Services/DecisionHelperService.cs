using System.Globalization;
using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.LoanApplication;
using QCash.Service.Classes;
using QCash.Service.Classes.DecisionManager;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Utilities.Extensions;
using static QCash.Service.Services.LoanApplication.Enums;

namespace QCash.Service.Services;

//functionality moved out of DecisionEngineService
public partial class DecisionHelperService(
    QCashContext context,
    IGuidExtensionService guidExtensionService)
    : IDecisionHelperService
{
    /// <summary>
    /// Maps the configuration to settings.
    /// </summary>
    /// <param name="productAvailableResult">The product available result.</param>
    /// <param name="nSFSetting">The NSFSetting.</param>
    /// <param name="decisionModel">The decision model.</param>
    /// <param name="financialInstitutionMemberId">The financial institution member identifier.</param>
    /// <param name="transformationSettings">The settings template.</param>
    public void MapConfigurationToSettings(ProductAvailableResult productAvailableResult, NsfSetting? nSFSetting, DecisionModel decisionModel, Guid financialInstitutionMemberId, Dictionary<string, string> transformationSettings, IList<LoanApplicationLogDetail> logs)
    {
        MapProductSettingsToTransformation(productAvailableResult.Product, transformationSettings, financialInstitutionMemberId);
        MapNSFSettingsToTransformation(nSFSetting, transformationSettings);
        MapUnsecuredLoanLimitSettingsToTransformation(transformationSettings);
        MapDecisionModelSettingsToTransformation(decisionModel, transformationSettings);
        MapDecisionEngineSettingsToTransformation(transformationSettings);
        MapLocalSettingsToTransformation(productAvailableResult, financialInstitutionMemberId, transformationSettings, logs);
    }

    private void MapProductSettingsToTransformation(Data.Models.Product? product, Dictionary<string, string> transformationSettings, Guid financialInstitutionMemberId)
    {
        if (product != null && transformationSettings.Any(p => p.Key.StartsWith(TransformationConstants.PLACEHOLDER_PRODUCT, StringComparison.CurrentCultureIgnoreCase)))
        {
            foreach (var tSetting in transformationSettings.Where(p => p.Key.StartsWith(TransformationConstants.PLACEHOLDER_PRODUCT, StringComparison.CurrentCultureIgnoreCase)).ToList())
            {
                var pname = tSetting.Key.Split('.')[1];
                if (pname == "Interest")
                {
                    transformationSettings[tSetting.Key] = product.AnnualPercentage.ToString(CultureInfo.CurrentCulture);
                }
                else if (pname == "LengthOfRelationshipOverride")
                {
                    transformationSettings[tSetting.Key] = (product.LengthOfRelationshipOverride ?? 0).ToString();
                }
                else if (pname == "BalanceBase")
                {
                    transformationSettings[tSetting.Key] = (product.BalanceBase ?? 0).ToString(CultureInfo.CurrentCulture);
                }
                else if (pname == "AvailableBalanceCheck")
                {
                    transformationSettings[tSetting.Key] = (product.AvailableBalanceCheck ? "1" : "0");
                }
                else if (pname == "MaximumLoanAmount" && financialInstitutionMemberId != Guid.Empty)
                {
                    var fiMember = context.FinancialInstitutionMembers.Where(p => p.Id == financialInstitutionMemberId)
                        .Include(p => p.MemberBaseAccounts)
                        .OrderByDescending(s => s.DateCreatedUtc).ThenByDescending(r => r.Id).First();
                    transformationSettings[tSetting.Key] = (fiMember.IsNewBorrower.GetValueOrDefault() && (product.NewBorrowerMaximumLoanAmount.HasValue && product.NewBorrowerMaximumLoanAmount > 0) ? product.NewBorrowerMaximumLoanAmount! : product.MaximumLoanAmount).ToString() ?? string.Empty;
                }
                else
                {
                    MapGenericSettingsToTransformation(transformationSettings, product, pname, tSetting.Key);
                }
            }
        }
    }

    private void MapNSFSettingsToTransformation(NsfSetting? nsfSetting, Dictionary<string, string> transformationSettings)
    {
        if (nsfSetting != null && transformationSettings.Any(p => p.Key.StartsWith(TransformationConstants.PLACEHOLDER_NSF, StringComparison.CurrentCultureIgnoreCase)))
        {
            foreach (var tSetting in transformationSettings.Where(p => p.Key.StartsWith(TransformationConstants.PLACEHOLDER_NSF, StringComparison.CurrentCultureIgnoreCase)).ToList())
            {
                var pname = tSetting.Key.Split('.')[1];
                MapGenericSettingsToTransformation(transformationSettings, nsfSetting, pname, tSetting.Key);
            }
        }
    }

    private void MapUnsecuredLoanLimitSettingsToTransformation(Dictionary<string, string> transformationSettings)
    {
        //var unsecuredLoanLimit = SettingService.GetAllSettings().FirstOrDefault().UnsecuredFILoanLimit;
        var unsecuredLoanLimit = context.Settings.FirstOrDefault()?.UnsecuredFiLoanLimit ?? 0m;

        if (transformationSettings.Any(p => p.Key.StartsWith(TransformationConstants.PLACEHOLDER_LOANLIMIT, StringComparison.CurrentCultureIgnoreCase)))
        {
            foreach (var tSetting in transformationSettings.Where(p => p.Key.StartsWith(TransformationConstants.PLACEHOLDER_LOANLIMIT, StringComparison.CurrentCultureIgnoreCase)).ToList())
            {
                var pname = tSetting.Key.Split('.')[1];
                if (pname == "UnsecuredLimit")
                {
                    transformationSettings[tSetting.Key] = unsecuredLoanLimit.ToString(CultureInfo.CurrentCulture);
                }
            }
        }
    }

    private void MapDecisionModelSettingsToTransformation(DecisionModel? decisionModel, Dictionary<string, string> transformationSettings)
    {
        if (decisionModel != null && transformationSettings.Any(p => p.Key.StartsWith(TransformationConstants.PLACEHOLDER_MODEL, StringComparison.CurrentCultureIgnoreCase)))
        {
            var decisionModelTypes = context.DecisionModelTypes.Where(o => !o.IsDeleted).ToList();
            foreach (var tSetting in transformationSettings.Where(p => p.Key.StartsWith(TransformationConstants.PLACEHOLDER_MODEL, StringComparison.CurrentCultureIgnoreCase)).ToList())
            {
                var pname = tSetting.Key.Split('.')[1];
                if (pname == "Type")
                {
                    var type = decisionModelTypes.First(p => p.Id == decisionModel.ModelTypeId).Name;
                    transformationSettings[tSetting.Key] = type;
                }
                else
                {
                    MapGenericSettingsToTransformation(transformationSettings, decisionModel, pname, tSetting.Key);
                }
            }
        }
    }

    private void MapDecisionEngineSettingsToTransformation(Dictionary<string, string> transformationSettings)
    {
        if (transformationSettings.Any(p => p.Key.StartsWith(TransformationConstants.PLACEHOLDER_DECISION_ENGINE, StringComparison.CurrentCultureIgnoreCase)))
        {
            var exclusionLoanTypeFphcs = context.ExclusionLoanTypeFphcs.Where(o => !o.IsDeleted).ToList();
            var codes = exclusionLoanTypeFphcs.Select(p => p.Name).ToList();
            foreach (var tSetting in transformationSettings.Where(p => p.Key.StartsWith(TransformationConstants.PLACEHOLDER_DECISION_ENGINE, StringComparison.CurrentCultureIgnoreCase)).ToList())
            {
                var pname = tSetting.Key.Split('.')[1];
                if (pname == TransformationConstants.CUSTOM_ExclusionLoanTypeFPHC)
                {
                    if (codes != null && codes.Count > 0)
                    {
                        transformationSettings[tSetting.Key] = codes.Aggregate((i, j) => i + ", " + j);
                    }
                    else
                    {
                        transformationSettings[tSetting.Key] = "0";
                    }
                }
            }
        }
    }

    private void MapLocalSettingsToTransformation(ProductAvailableResult productAvailableResult, Guid financialInstitutionMemberId, Dictionary<string, string> transformationSettings, IList<LoanApplicationLogDetail> logs)
    {
        if (transformationSettings.Any(p => p.Key.StartsWith(TransformationConstants.PLACEHOLDER_LOCAL, StringComparison.CurrentCultureIgnoreCase)))
        {
            foreach (var tSetting in transformationSettings.Where(p => p.Key.StartsWith(TransformationConstants.PLACEHOLDER_LOCAL, StringComparison.CurrentCultureIgnoreCase)).ToList())
            {
                var personalCampaign = productAvailableResult.CampaignCodeAccount?.PersonalCampaigns?.FirstOrDefault();
                if (productAvailableResult.Product != null && productAvailableResult.Product.PersonalLoanCampaign && tSetting.Key == TransformationConstants.CUSTOM_Campaign_QualifiedAmount && personalCampaign != null)
                {
                    transformationSettings[tSetting.Key] = productAvailableResult.CampaignCodeAccount?.PersonalCampaigns?.First().Amount.ToString(CultureInfo.CurrentCulture) ?? string.Empty;
                }
                else
                {
                    var pname = tSetting.Key.Split('.')[1];
                    if (pname == TransformationConstants.CUSTOM_HasMemberWarningCodes)
                    {
                        transformationSettings[tSetting.Key] = Convert.ToDouble(HasMemberWarningCodes(financialInstitutionMemberId, logs)).ToString(CultureInfo.CurrentCulture);
                    }
                    else if (pname == TransformationConstants.CUSTOM_HasAccountWarningCodes)
                    {
                        transformationSettings[tSetting.Key] = Convert.ToDouble(HasAccountWarningCodes(financialInstitutionMemberId, logs)).ToString(CultureInfo.CurrentCulture);
                    }
                }
            }
        }
    }

    private void MapGenericSettingsToTransformation(Dictionary<string, string> transformationSettings, object item, string propertyName, string settingKey)
    {
        try
        {
            var val = item.GetPropertyValue(propertyName);
            if (val == null)
            {
                transformationSettings[settingKey] = 0D.ToString(CultureInfo.CurrentCulture);
                return;
            }

            if (decimal.TryParse(val.ToString(), out decimal decValue))
            {
                transformationSettings[settingKey] = decValue.ToString(CultureInfo.CurrentCulture);
            }
            else
            {
                transformationSettings[settingKey] = val.ToString() ?? string.Empty;
            }
        }
        catch
        {
            transformationSettings[settingKey] = 0D.ToString(CultureInfo.CurrentCulture);
        }
    }

    public bool HasMemberWarningCodes(Guid financialInstitutionMemberId, IList<LoanApplicationLogDetail> logs)
    {
        var warningCodes = GetMemberWarningCodes(financialInstitutionMemberId).ToList();
        if (warningCodes.Any())
        {
            string logString = string.Join(", ", warningCodes);
            logs.Add(new LoanApplicationLogDetail()
            {
                ActionDateTimeUtc = DateTime.UtcNow,
                IsTemp = true,
                ActionDescription = $"Member warning codes: {logString}",
                LogType = (int)LogType.Clean,
                SortOrder = (int)LogSortOrder.AANReasons
            });
        }
        return warningCodes.Any();
    }

    public IEnumerable<string> GetMemberWarningCodes(Guid financialInstitutionMemberId)
    {
        var membersWarningCodes = context.MemberWarningCodes.Where(p => p.FinancialInstitutionMemberId.Equals(financialInstitutionMemberId) && !p.IsExpired)
            .OrderBy(p => p.Id)
            .Select(p => p.Code)
            .ToList();

        if (membersWarningCodes.Count == 0)
        {
            return new List<string>();
        }

        var memberWarningCodesSetting = context.MemberWarningCodeTypes.Where(p => !p.IsDeleted).Select(mwc => mwc.Name).ToHashSet();

        return membersWarningCodes.Where(memberWarningCodesSetting.Contains).Distinct();
    }

    public bool HasAccountWarningCodes(Guid financialInstitutionMemberId, IList<LoanApplicationLogDetail> logs)
    {
        //var entities = Repository.Get<MemberBaseAccount>(p => p.FinancialInstitutionMemberId == value && (p.CloseDateUTC == null || p.CloseDateUTC > DateTime.UtcNow), null, includeProperties).ToList();
        //include MemberBaseAccountWarningCode, MemberAccount,MemberAccountWarningCode
        var baseAccounts = context.MemberBaseAccounts
            .Include(o => o.MemberBaseAccountWarningCodes)
            .Include(o => o.MemberAccounts)
            .ThenInclude(o => o.MemberAccountWarningCodes)
            .Where(p => p.FinancialInstitutionMemberId == financialInstitutionMemberId &&
                        (p.CloseDateUtc == null || p.CloseDateUtc > DateTime.UtcNow));

        if (!baseAccounts.Any())
        {
            return false;
        }

        var primaryBaseAccounts = baseAccounts
            .Where(p => p.OwnerShipType == OwnerShipType.Primary.ToString())
            .ToList();

        var baseAccountsWarningCodes = GetBaseAccountsWarningCodes(primaryBaseAccounts);
        var accountsWarningCodes = GetAccountsWarningCodes(primaryBaseAccounts);
        AddWarningCodesLog("Base Accounts warning codes", baseAccountsWarningCodes, logs);
        AddWarningCodesLog("Sub Accounts warning codes", accountsWarningCodes, logs);

        return baseAccountsWarningCodes.Any() || accountsWarningCodes.Any();
    }


    public void AddWarningCodesLog(string message, Dictionary<Tuple<string, string>, List<string>> warningCodes, IList<LoanApplicationLogDetail> logs)
    {
        if (warningCodes.Any())
        {
            string logString = string.Join("; ", warningCodes
                .Select(kv => $"BaseAccountId: {GetMaskedNumber(kv.Key.Item1)}, AccountId: {kv.Key.Item2}, WarningCodes: {string.Join(", ", kv.Value)}"));

            logs.Add(new LoanApplicationLogDetail()
            {
                ActionDateTimeUtc = DateTime.UtcNow,
                IsTemp = true,
                ActionDescription = $"{message}: {logString}",
                LogType = (int)LogType.Clean,
                SortOrder = (int)LogSortOrder.AANReasons
            });
        }
    }

    public void AddWarningCodesLog(string message, IDictionary<string, List<string>> warningCodes, IList<LoanApplicationLogDetail> logs)
    {
        if (warningCodes.Any())
        {
            string logString = string.Join("; ", warningCodes
                .Select(kv => $"BaseAccountId: {GetMaskedNumber(kv.Key)}, WarningCodes: {string.Join(", ", kv.Value)}"));

            logs.Add(new LoanApplicationLogDetail()
            {
                ActionDateTimeUtc = DateTime.UtcNow,
                IsTemp = true,
                ActionDescription = $"{message}: {logString}",
                LogType = (int)LogType.Clean,
                SortOrder = (int)LogSortOrder.AANReasons
            });
        }
    }

    private static string GetMaskedNumber(string? number, int cleanCharsTotal = 4, char maskedChar = 'X')
    {
        if (string.IsNullOrEmpty(number))
        {
            return string.Empty;
        }

        var diff = number.Length - cleanCharsTotal;
        var zeros = diff < 0 ? Math.Abs(diff) : 0;
        var clean = diff < 0 ? number : number.Substring(diff);
        var maskedNum = new string(maskedChar, 10 - cleanCharsTotal) + new string('0', zeros) + clean;

        return maskedNum;
    }

    public IDictionary<string, List<string>> GetBaseAccountsWarningCodes(IList<MemberBaseAccount> primaryBaseAccounts)
    {
        var baseAccountWarningCodesSetting = context.AccountWarningCodes
            .Where(p => !p.IsDeleted).OrderBy(p => p.Id)
            .Select(q => q.Name)
            .ToHashSet();

        return primaryBaseAccounts
            .SelectMany(baseAccount => baseAccount.MemberBaseAccountWarningCodes, (baseAccount, warningCode) => new
            {
                BaseAccountId = baseAccount.AccountId,
                WarningCode = warningCode
            })
            .Where(entry => entry.WarningCode.ExpirationDateUtc == null || entry.WarningCode.ExpirationDateUtc > DateTime.UtcNow)
            .Where(entry => baseAccountWarningCodesSetting.Contains(entry.WarningCode.Code))
            .GroupBy(entry => entry.BaseAccountId)
            .ToDictionary(
                group => group.Key,
                group => group.Select(entry => entry.WarningCode.Code).Distinct().ToList()
            );
    }


    public Dictionary<Tuple<string, string>, List<string>> GetAccountsWarningCodes(IList<MemberBaseAccount> primaryBaseAccounts)
    {
        var accountWarningCodesSetting = context.AccountWarningCodes
            .Where(p => !p.IsDeleted).OrderBy(p => p.Id)
            .Select(awc => (awc.Name.IndexOf(" - ") > 0) ? awc.Name.Substring(0, awc.Name.IndexOf(" - ") - 1) : awc.Name)
            .ToHashSet();

        return primaryBaseAccounts
            .SelectMany(baseAccount => baseAccount.MemberAccounts, (baseAccount, account) => new
            {
                BaseAccountId = baseAccount.AccountId,
                AccountId = account.AccountId,
                Account = account
            })
            .Where(account => !account.Account.IsClosed)
            .SelectMany(accountEntry => accountEntry.Account.MemberAccountWarningCodes, (accountEntry, warningCode) => new
            {
                accountEntry.BaseAccountId,
                accountEntry.AccountId,
                WarningCode = warningCode
            })
            .Where(entry => entry.WarningCode.ExpirationDateUtc == null || entry.WarningCode.ExpirationDateUtc > DateTime.UtcNow)
            .Where(entry => accountWarningCodesSetting.Contains(entry.WarningCode.Code))
            .GroupBy(entry => Tuple.Create(entry.BaseAccountId, entry.AccountId))
            .ToDictionary(
                group => group.Key,
                group => group.Select(entry => entry.WarningCode.Code).Distinct().ToList()
            );
    }

    /// <summary>
    /// Validates the decision engine.
    /// </summary>
    /// <param name="productAvailableResult">The product available result.</param>
    /// <param name="transformation">The transformation.</param>
    /// <param name="skipLogs">if set to <c>true</c> [skip logs].</param>
    /// <param name="logs">The logs.</param>
    /// <param name="tSettings">The transformations settings.</param>
    /// <returns><see cref="DecisionEngineResult"/>.</returns>
    public DecisionEngineResult ValidateDecisionEngine(ProductAvailableResult productAvailableResult, Transformation transformation, bool skipLogs, IList<LoanApplicationLogDetail>? logs, Dictionary<string, string> tSettings, bool isNewBorrower, bool isActiveDuty)
    {
        using (var calculator = new DecisionEngineCalculator(transformation, tSettings, guidExtensionService))
        {
            calculator.ExecuteTransformation();
        }

        var tResults = transformation.GetTransformationsResultRecursive();
        var result = new DecisionEngineResult()
        {
            QualifiedAmount = Convert.ToDecimal(GetResultValue(tResults, TransformationConstants.NAME_QUALIFIED_AMOUNT).Value),
            MinimumAmount = productAvailableResult.Product?.MinimumLoanAmount ?? 0m,
            MaximumAmount = (isNewBorrower
                             && productAvailableResult.Product?.NewBorrowerMaximumLoanAmount > 0 ? productAvailableResult.Product.NewBorrowerMaximumLoanAmount.GetValueOrDefault() :
                productAvailableResult.Product?.MaximumLoanAmount) ?? 0m
        };

        var rewardRateValue = GetResultValue(tResults, TransformationConstants.NAME_REWARD_RATE);

        if (rewardRateValue.Value.HasValue)
        {
            result.InterestRate = Math.Max(0, Convert.ToDecimal(rewardRateValue.Value));
        }
        else if (isActiveDuty && productAvailableResult.Product != null && productAvailableResult.Product.MaprInterestRate.HasValue)
        {
            result.InterestRate = productAvailableResult.Product.MaprInterestRate;
        }
        else
        {
            result.InterestRate = productAvailableResult.Product?.AnnualPercentage;
        }

        //Scores
        result.ScorePoints = tResults.Where(s => s.Value != null && s.Value.Score != null).Select(p => p.Value!.Score!).ToList();

        result.IsEligible = Convert.ToBoolean(GetResultValue(tResults, TransformationConstants.NAME_DECISION_ENGINE).Value);

        //Adverse action notice reasons
        var rAAN = transformation.GetAANReasonsResultRecursiveWithTransform();
        var aanIds = rAAN.Select(a => a.Value).ToList();
        var aans = context.AanReasonDefaultTexts.Where(a => aanIds.Contains(a.Id)).ToList();

        var targetAanReasons = rAAN.Where(r => !guidExtensionService.IsNullOrEmpty(r.Value))
            .Select(p => aans.FirstOrDefault(s => s.Id == p.Value))
            .ToList();

        result.AANReasons.AddRange(targetAanReasons.Where(o => o != null).Select(o => o!)
            .Select(a => new AanReasonForCalcDto()
            {
                Id = a.Id,
                Description = a.Description,
            })
        );

        if (!skipLogs)
        {
            result.Logs = CreateTransformationLogs(transformation.Logs, tResults, tSettings, logs);
        }

        return result;
    }

    public IList<LoanApplicationLogDetail> CreateTransformationLogs(string? tLogs, Dictionary<string, TransformationResult?> transformationResults, Dictionary<string, string> tSettings, IList<LoanApplicationLogDetail>? logs)
    {
        var logMessages = new List<LoanApplicationLogDetail>();
        if (string.IsNullOrEmpty(tLogs) || logs == null)
        {
            return logMessages;
        }

        // Get the next sort order.
        int sortOrder = logs.Last().SortOrder + 1;
        var logsArray = Regex.Unescape(tLogs).Split('|');

        var rxPlaceholder = PlaceholderRegex();
        var rxFormat = CreateRegexForFormatPlaceholder();

        foreach (var log in logsArray)
        {
            logs.Add(ResolveLogMessage(rxPlaceholder, rxFormat, log, sortOrder, transformationResults, tSettings));
        }

        return logMessages;
    }

    private static Regex CreateRegexForFormatPlaceholder()
    {
        return new Regex($@"\{InterfaceTextConstants.PLACEHOLDER_FORMAT_START}.*?\{InterfaceTextConstants.PLACEHOLDER_FORMAT_END}", RegexOptions.None, TimeSpan.FromMilliseconds(1000));
    }

    /// <summary>
    /// Gets the format from the placeholder text.
    /// </summary>
    /// <param name="rx">Regex for finding the format placeholder.</param>
    /// <param name="placeholder">Placeholder text.</param>
    private static string GetFormatFromPlaceholder(Regex rx, string placeholder)
    {
        return rx.Match(placeholder).Value;
    }


    /// <summary>
    /// Gets the formatted string from provided text placeholder.
    /// </summary>
    /// <param name="format">Format.</param>
    /// <param name="value">Placeholder text.</param>
    private static string GetFormattedStringFromPlaceholderWithFormat(string format, string value)
    {
        if (string.IsNullOrEmpty(format))
        {
            return value;
        }

        // Remove parentheses.
        value = value.Replace(format, string.Empty);
        format = format
            .Trim(InterfaceTextConstants.PLACEHOLDER_FORMAT_START)
            .Trim(InterfaceTextConstants.PLACEHOLDER_FORMAT_END);

        // Decimal format.
        if (decimal.TryParse(value, out decimal decValue))
        {
            value = decValue.ToString(format);
        }

        // DateTime format.
        else if (DateTime.TryParse(value, out var dateValue))
        {
            value = dateValue.ToString(format);
        }

        return value;
    }

    public LoanApplicationLogDetail ResolveLogMessage(Regex rxPlaceholder, Regex rxFormat, string log, int sortOrder, Dictionary<string, TransformationResult?> transformationResults, Dictionary<string, string> tSettings)
    {
        string description = log;

        var placeholders = rxPlaceholder.Matches(log);
        foreach (var tMatch in placeholders)
        {
            var placeholder = tMatch.ToString() ?? string.Empty;
            var isBool = placeholder.Contains(":bool");
            var format = GetFormatFromPlaceholder(rxFormat, placeholder);

            string transformationName = placeholder
                .Replace(":bool", string.Empty)
                .Trim(InterfaceTextConstants.PLACEHOLDER_START)
                .Trim(InterfaceTextConstants.PLACEHOLDER_END);

            if (!string.IsNullOrEmpty(format))
            {
                transformationName = transformationName.Replace(format, string.Empty);
            }

            if (!string.IsNullOrEmpty(placeholder))
            {
                string? value = string.Empty;

                if (transformationResults.ContainsKey(transformationName))
                {
                    value = transformationResults.First(p => p.Key.Equals(transformationName)).Value?.Value.ToString();
                }

                else if (tSettings.ContainsKey(transformationName))
                {
                    value = tSettings.First(p => p.Key.Equals(transformationName)).Value;
                }

                if (string.IsNullOrEmpty(value))
                {
                    description = description.Replace(placeholder, "No value");
                }
                else
                {
                    if (!isBool)
                    {
                        description = description.Replace(placeholder, GetFormattedStringFromPlaceholderWithFormat(format, value));
                    }

                    else if (int.TryParse(value, out int intValue))
                    {
                        description = intValue > 0 ? description.Replace(placeholder, "True") : description.Replace(placeholder, "False");
                    }
                }
            }
        }

        return new LoanApplicationLogDetail()
        {
            ActionDateTimeUtc = DateTime.UtcNow,
            IsTemp = true,
            ActionDescription = description,
            LogType = (int)LogType.Clean,
            SortOrder = sortOrder
        };
    }

    private TransformationResult GetResultValue(Dictionary<string, TransformationResult?>? tResult, string key)
    {
        var result = (tResult != null && tResult.ContainsKey(key)) ? tResult[key] : new TransformationResult();
        return result ?? new TransformationResult();
    }

    [GeneratedRegex("{.*?}", RegexOptions.None, matchTimeoutMilliseconds: 1000)]
    private static partial Regex PlaceholderRegex();
}
