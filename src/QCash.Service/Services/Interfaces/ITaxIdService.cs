namespace QCash.Service.Services.Interfaces;

public interface ITaxIdService
{
    /// <summary>
    /// Get tax ID from cached TempMemberDTO.  Note that this can only return a value if the data has previously been cached
    /// via TokenService.StoreTempMemberDataAsync.
    /// </summary>
    /// <param name="financialInstitutionMemberId"></param>
    Task<string> GetTaxIdByFinancialInstitutionMemberIdAsync(Guid financialInstitutionMemberId);

    /// <summary>
    /// Generate hash of tax ID from cached TempMemberDTO.  Note that this can only return a value if the data has previously been cached
    /// via TokenService.StoreTempMemberDataAsync.
    /// </summary>
    /// <param name="financialInstitutionMemberId"></param>
    Task<string> GetTaxIdHashByFinancialInstitutionMemberIdAsync(Guid financialInstitutionMemberId);
}
