using QCash.Data.Models;
using QCash.Service.Models;

namespace QCash.Service.Services.Interfaces;

public interface ILookupService
{
    Task<List<QListItem<Guid>>> GetLoanTypesForDropdownAsync(string fiSlug);
    Task<List<QListItem<Guid>>> GetLoanTypesForDropdownAsync(IQueryable<LoanType> loanTypes);
    LoanType? GetDefaultLoanType(List<LoanType> loanTypes);
    Task<List<QListItem<Guid>>> GetLoanCategoriesForDropdownAsync(string fiSlug, Guid modelLoanTypeId);
    List<QListItem<TEnum>> GetItemsFromEnum<TEnum>() where TEnum : Enum;
    public List<QListItem<TEnum>> GetItemsFromEnumWithEnumString<TEnum>(Func<IEnumerable<TEnum>, IEnumerable<TEnum>>? filter = null)
        where TEnum : Enum;
    Task<List<QListItem<string>>> GetStatesAsync();
    Task<List<QListItem<Guid>>> GetProductDropdownChoicesAsync();
    List<QListItem<string>> GetTimeZoneChoices();
    public Task<List<QListItem<Guid>>> GetDecisionModelTypesAsync();
    public Task<List<QListItem<Guid>>> GetDecisionModelStatusChoicesAsync();
    public List<QListItem<string>> GetFontChoices();
}
