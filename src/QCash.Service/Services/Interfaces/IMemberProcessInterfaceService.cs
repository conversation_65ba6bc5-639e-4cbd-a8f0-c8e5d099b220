using QCash.Data.Models;
using static QCash.Service.Models.Core.Enums;
using static QCash.Service.Models.LoanApplication.Enums;

namespace QCash.Service.Services.Interfaces;

public interface IMemberProcessInterfaceService
{
    /// <summary>
    /// Gets the Formatted interface texts for <see cref="LoanApplicationStep" />.
    /// </summary>
    /// <param name="la">The loan application.</param>
    /// <param name="languageId">The language.</param>
    /// <param name="step">The step.</param>
    /// <returns>
    /// Formatted interface texts in Dictionary{string, string} />
    /// </returns>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForStepAsync(Data.Models.LoanApplication? la, Guid languageId, LoanApplicationStep step);

    /// <summary>
    /// Gets the Formatted interface texts for available products.
    /// </summary>
    /// <param name="la">The la.</param>
    /// <param name="availableProducts">The available products.</param>
    /// <param name="languageId">The language.</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForAvailableProductsAsync(Data.Models.LoanApplication la, IList<Data.Models.Product> availableProducts, Guid languageId);

    /// <summary>
    /// Gets the Formatted product short description interface texts.
    /// </summary>
    /// <param name="la">The loan application.</param>
    /// <param name="availableProducts">The available products.</param>
    /// <param name="languageId">The language.</param>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForAvailableProductsShortDescriptionAsync(Data.Models.LoanApplication la, IList<Data.Models.Product> availableProducts, Guid languageId);

    /// <summary>
    /// Gets the Formatted interface description texts for product.
    /// </summary>
    /// <param name="la">The loan application.</param>
    /// <param name="languageId">The language.</param>
    /// <param name="product">The product.</param>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForProductDescriptionAsync(Data.Models.LoanApplication la, Guid languageId, Data.Models.Product product);

    /// <summary>
    /// Gets the Formatted product short description interface texts.
    /// </summary>
    /// <param name="la"></param>
    /// <param name="languageId"></param>
    /// <param name="product"></param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForProductShortDescriptionAsync(Data.Models.LoanApplication la, Guid languageId, Data.Models.Product product);

    /// <summary>
    /// Gets the Formatted interface texts for tila preview.
    /// </summary>
    /// <param name="la">The la.</param>
    /// <param name="languageId">The language.</param>
    /// <returns></returns>
    Task<KeyValuePair<string, string>> GetFormattedInterfaceTextForTilaPreviewAsync(Data.Models.LoanApplication la, Guid languageId);

    /// <summary>
    /// Gets the Formatted interface texts for tila preview.
    /// </summary>
    /// <param name="la">The la.</param>
    /// <param name="languageId">The language.</param>
    /// <returns></returns>
    Task<KeyValuePair<string, string>> GetFormattedInterfaceTextForDisclosureTextAsync(Data.Models.LoanApplication la, Guid languageId);

    /// <summary>
    /// Gets the Formatted interface texts for email debt counselor.
    /// </summary>
    /// <param name="la">The loan application.</param>
    /// <param name="languageId">The language.</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForEmailDebtCounselorAsync(Data.Models.LoanApplication la, Guid languageId);

    /// <summary>
    /// Gets the Formatted interface texts for emails.
    /// </summary>
    /// <param name="la">The la.</param>
    /// <param name="languageId">The language.</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForEmailsAsync(Data.Models.LoanApplication la, Guid languageId);

    /// <summary>
    /// Gets the Formatted interface texts for fee refund.
    /// </summary>
    /// <param name="la">The la.</param>
    /// <param name="languageId">The language.</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForFeeRefundAsync(Data.Models.LoanApplication la, Guid languageId);

    /// <summary>
    /// Gets the Formatted interface texts for credit card.
    /// </summary>
    /// <param name="la">The la.</param>
    /// <param name="languageId">The language.</param>
    /// <param name="isSuccess">if set to <c>true</c> [is success].</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForCreditCardAsync(Data.Models.LoanApplication la, Guid languageId, bool isSuccess);

    /// <summary>
    /// Gets the Formatted interface texts for campaign fee refund.
    /// </summary>
    /// <param name="la">The la.</param>
    /// <param name="languageId">The language.</param>
    /// <param name="isSuccess">if set to <c>true</c> [is success].</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForCampaignFeeRefundAsync(Data.Models.LoanApplication la, Guid languageId, bool isSuccess);

    /// <summary>
    /// Gets the Formatted interface texts for pending signature emails.
    /// </summary>
    /// <param name="la">The la.</param>
    /// <param name="languageId">The language.</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForPendingSignatureEmailsAsync(Data.Models.LoanApplication la, Guid languageId);
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForFraudControlNotificationAsync(Data.Models.LoanApplication la, Guid languageId);

    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForLoginFraudControlNotificationAsync(FinancialInstitutionMember financialInstitutionMember, OriginApp originApp, Guid languageId);

    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForLoginFraudControlAsync(FinancialInstitutionMember financialInstitutionMember, OriginApp originApp, Guid languageId);

    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForLoginAsync(Guid languageId);

    /// <summary>
    /// Gets the Formatted interface texts for payment options.
    /// </summary>
    /// <param name="paymentOptions">The payment options.</param>
    /// <param name="la">The la.</param>
    /// <param name="languageId">The language.</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForPaymentOptionsAsync(IEnumerable<Guid> paymentOptions, Data.Models.LoanApplication la, Guid languageId);

    /// <summary>
    /// Gets the Formatted day of week by date.
    /// </summary>
    /// <param name="date">The date.</param>
    /// <param name="languageCode">The language.</param>
    /// <returns></returns>
    string GetFormattedDayOfWeekByDate(DateTime date, string languageCode);

    /// <summary>
    /// Gets the Formatted interface texts for loan landing debt counselor.
    /// </summary>
    /// <param name="la">The la.</param>
    /// <param name="languageId">The language.</param>
    /// <param name="includeDebtCounselorDescription">if set to <c>true</c> [include debt counselor description].</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForLoanLandingPartialAsync(Data.Models.LoanApplication la, Guid languageId, bool includeDebtCounselorDescription);

    /// <summary>
    /// Gets the Formatted interface texts for automatic payment.
    /// </summary>
    /// <param name="la">The la.</param>
    /// <param name="availableProducts">The available products.</param>
    /// <param name="languageId">The language.</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForAutoPaymentAsync(Data.Models.LoanApplication la, IList<Data.Models.Product> availableProducts, Guid languageId);

    /// <summary>
    /// Gets the Formatted interface texts for initiate error.
    /// </summary>
    /// <param name="languageId">The language.</param>
    Task<Dictionary<string, string>> GetFormattedInterfaceTextsForInitiateErrorAsync(Guid languageId);

    Task<Dictionary<string, string>> GetPaymentOptionTitleAsync(Guid paymentOptionId, Guid languageId);

    Task<Dictionary<string, string>> GetErrorSessionInterfaceTextAsync(Guid languageId, OriginApp originApp);

    Task<Dictionary<string, string>> GetGeneralErrorInterfaceTextAsync(Guid languageId, OriginApp originApp);

    Task<Dictionary<string, string>> GetFirebirdWizardDefaultTextsAsync(Guid languageId);

    Task<Dictionary<string, string>> GetFiGenericErrorTextAsync(OriginApp originApp, Guid languageId);
    IEnumerable<string> GetLoanLandingInterfaceTexts();
    IEnumerable<string> GetLoanHubInterfaceTexts();
}
