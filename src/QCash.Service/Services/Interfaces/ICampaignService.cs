using QCash.Data.Models;
using QCash.Service.Classes;

namespace QCash.Service.Services.Interfaces;

public interface ICampaignService
{
    /// <summary>
    /// Gets the campaign code accounts by campaign code identifier.
    /// </summary>
    /// <param name="campaignCodeId">The campaign code identifier.</param>
    /// <param name="personalCampaign">Personal Campaign Flag</param>
    /// <param name="search">The account number search criteria.</param>
    /// <returns>The campaign code accounts list.</returns>
    Task<IList<CampaignCodeAccount>> GetCampaignCodeAccountsByCampaignCodeIdAsync(
        Guid campaignCodeId,
        bool personalCampaign,
        string search);

    /// <summary>
    /// Inserts the campaign code accounts.
    /// </summary>
    Task InsertCampaignCodeAccountsAsync(IEnumerable<CampaignCodeAccount> campaignCodeAccounts, Guid campaignCodeId);

    /// <summary>
    /// Inserts the personal campaign on both instances.
    /// </summary>
    /// <param name="personalCampaigns">The entities.</param>
    Task InsertPersonalCampaignsAsync(IEnumerable<PersonalCampaign> personalCampaigns);

    /// <summary>
    /// Gets CampaignCodeAccounts by CampaignCodeId and Account status.
    /// </summary>
    /// <remarks>Note that this method also marks existing unused entries for deletion. Also, this method does NOT call SaveChanges.</remarks>
    /// <param name="campaignCodeId">Account campaign code id identifier</param>
    /// <param name="status">Campaign code account status identifier</param>
    /// <returns>Returns a list of accounts by campaign code id and campaign code account status</returns>
    Task<IEnumerable<CampaignCodeAccount>> GetCampaignCodeAccountsByCampaignCodeIdAndAccountStatusAsync(Guid campaignCodeId, string status);

    /// <summary>
    /// Gets the personal campaign codes for account.
    /// </summary>
    /// <param name="memberIdentifier">Member identifier</param>
    /// <param name="productIds">The product identifier.</param>
    Task<IList<CampaignCode>> GetCampaignCodesForAccountAndProductsAsync(
        MemberIdentifier memberIdentifier,
        Guid[] productIds);

    /// <summary>
    /// Converts the dates and insert campaign code.
    /// </summary>
    /// <param name="entity">The campaign code entity.</param>
    /// <returns>The instance of <see cref="CampaignCode"/>.</returns>
    Task<CampaignCode> ConvertDatesAndInsertCampaignCodeAsync(CampaignCode entity);

    /// <summary>
    /// Converts the dates and update campaign code.
    /// </summary>
    /// <param name="entity">The entity.</param>
    /// <returns></returns>
    Task<bool> ConvertDatesAndUpdateCampaignCodeAsync(CampaignCode entity);

    /// <summary>
    /// Gets the campaign code accounts by account number.
    /// Either accountNumber or memberId will be used, based on the campaign code account data
    /// </summary>
    /// <param name="memberIdentifier">Member identifier</param>
    Task<IList<CampaignCodeAccount>> GetCampaignCodeAccountsByMemberIdentifierAsync(MemberIdentifier memberIdentifier);

    /// <summary>
    /// Gets whether to show the campaign code.
    /// </summary>
    /// <param name="memberIdentifier">Member identifier</param>
    /// <param name="activeMilitary">if set to <c>true</c> [active military].</param>
    /// <param name="availableProductIds">The available product ids.</param>
    Task<bool> ShowCampaignCodeAsync(
        MemberIdentifier memberIdentifier,
        bool? activeMilitary,
        IEnumerable<Guid> availableProductIds);

    /// <summary>
    /// Gets the data extract CSV.
    /// </summary>
    Task<byte[]?> GetCampaignCodeAccountCsvAsync();

    /// <summary>
    /// Gets the data extract CSV.
    /// </summary>
    /// <param name="campaignCodeId">The campaign code identifier.</param>
    Task<byte[]?> GetDataExtractCsvAsync(string campaignCodeId);
}
