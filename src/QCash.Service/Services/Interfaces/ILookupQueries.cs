using QCash.Data.Models;

namespace QCash.Service.Services.Interfaces;

public interface ILookupQueries
{
    IQueryable<LoanType> GetLoanTypes(string fiSlug);
    IQueryable<LoanCategory> GetLoanCategories(Guid applicationId);
    Task<IQueryable<LoanCategory>> GetLoanCategoriesQAsync(string fiSlug, Guid loanTypeId);
    IQueryable<AspNetRole> GetRolesQueryable();
    IQueryable<State> GetStatesQueryable();
}
