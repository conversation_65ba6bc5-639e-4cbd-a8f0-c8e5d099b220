using QCash.Common.Enums;
using QCash.Data.Models;
using QCash.Service.Models;

namespace QCash.Service.Services.Interfaces;

public interface ISettingService
{
    /// <summary>
    /// Imports the language items.
    /// </summary>
    /// <param name="languageCode">The language code.</param>
    /// <param name="values">The values.</param>
    /// <returns>Returns a summary of items not found or not updated.</returns>
    Task<string> ImportLanguageItemsAsync(string languageCode, IList<ImportExportTextDto> values);

    /// <summary>
    /// Gets all language items. Used for exporting the language.
    /// </summary>
    /// <param name="languageCode">The language code.</param>
    /// <returns>All language items.</returns>
    Task<IList<ImportExportTextDto>> GetAllLanguageItemsAsync(string? languageCode);

    Task<IList<Data.Models.Product>> GetAllProductsAsync(bool includePreproduct = false);

    /// <summary>
    /// Gets all products.
    /// </summary>
    /// <param name="loanCategoryId">The loan category identifier.</param>
    /// <param name="loanTypeId">The loan type identifier.</param>
    /// <param name="includePreProduct">Include pre product flag.</param>
    /// <returns>List of products.</returns>
    Task<IList<Data.Models.Product>> GetAllProductAsync(Guid? loanCategoryId, Guid? loanTypeId, bool includePreProduct = false);

    Task<bool> IsOldPasswordAsync(string username, string encryptedPassword);

    /// <summary>
    /// Gets the documentfilename by language Id, TILADocumentType, product Id, and groupId.
    /// </summary>
    Task<DocumentFileName?> GetTilaDocumentFileNameAsync(Guid languageId, TilaDocumentTemplateType tilaDocumentType, Guid productId, Guid? groupId = null);

    Task<List<string>> GetStatesByGroupIdAsync(Guid groupId);
    Task ValidateLoanFeeAsync(LoanFee entity);

    /// <summary>
    /// Validate the product score.
    /// </summary>
    /// <param name="entity">The entity.</param>
    /// <exception cref="Exception">Can't insert score - it overlaps with an existing score items.</exception>
    Task ValidateProductScoreAsync(ProductScore entity);

    /// <summary>
    /// Gets the active products for display json.
    /// </summary>
    Task<string> GetActiveProductsForDisplayJsonAsync();
}
