using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using QCash.Data.Context;
using QCash.Service.Services.General;
using QCash.Service.Services.Helpers;
using QCash.Service.Services.Interfaces;
using QCash.Service.Utilities.Extensions;
using static QCash.Service.Models.Core.Enums;
using static QCash.Service.Services.LoanApplication.Enums;

namespace QCash.Service.Services;

public class FilenameTemplateService(QCashContext qCashContext, ITaxIdService taxIdService, ILogger<FilenameTemplateService> logger) : IFilenameTemplateService
{
    private readonly Dictionary<string, LoanApplicationDocumentType[]> _allowedFilenamePlaceholder = new()
{
    {
        FilenameTemplatePlaceholders.Account, [
            LoanApplicationDocumentType.AdverseActionNotice,
            LoanApplicationDocumentType.TILA,
            LoanApplicationDocumentType.EConsentDisclosure,
            LoanApplicationDocumentType.AppLogs,
        ]
    },
    {
        FilenameTemplatePlaceholders.Product, [
            LoanApplicationDocumentType.AdverseActionNotice,
            LoanApplicationDocumentType.TILA,
            LoanApplicationDocumentType.AppLogs,
        ]
    },
    {
        FilenameTemplatePlaceholders.AppId, [
            LoanApplicationDocumentType.AdverseActionNotice,
            LoanApplicationDocumentType.TILA,
            LoanApplicationDocumentType.EConsentDisclosure,
            LoanApplicationDocumentType.AppLogs,
        ]
    },
    {
        FilenameTemplatePlaceholders.LoanOrigination, [
            LoanApplicationDocumentType.AdverseActionNotice,
            LoanApplicationDocumentType.TILA,
            LoanApplicationDocumentType.AppLogs,
        ]
    },
    {
        FilenameTemplatePlaceholders.Maturity, [
            LoanApplicationDocumentType.AdverseActionNotice,
            LoanApplicationDocumentType.TILA,
            LoanApplicationDocumentType.EConsentDisclosure,
            LoanApplicationDocumentType.AppLogs,
        ]
    },
    {
        FilenameTemplatePlaceholders.PlainSsn, [
            LoanApplicationDocumentType.AdverseActionNotice,
            LoanApplicationDocumentType.TILA,
        ]
    },
    {
        FilenameTemplatePlaceholders.Ssn, [
            LoanApplicationDocumentType.AdverseActionNotice,
            LoanApplicationDocumentType.TILA,
            LoanApplicationDocumentType.EConsentDisclosure,
            LoanApplicationDocumentType.AppLogs,
        ]
    },
    {
        FilenameTemplatePlaceholders.FirstName, [
            LoanApplicationDocumentType.AdverseActionNotice,
            LoanApplicationDocumentType.TILA,
            LoanApplicationDocumentType.EConsentDisclosure,
            LoanApplicationDocumentType.AppLogs,
        ]
    },
    {
        FilenameTemplatePlaceholders.MiddleName, [
            LoanApplicationDocumentType.AdverseActionNotice,
            LoanApplicationDocumentType.TILA,
            LoanApplicationDocumentType.EConsentDisclosure,
            LoanApplicationDocumentType.AppLogs,
        ]
    },
    {
        FilenameTemplatePlaceholders.LastName, [
            LoanApplicationDocumentType.AdverseActionNotice,
            LoanApplicationDocumentType.TILA,
            LoanApplicationDocumentType.EConsentDisclosure,
            LoanApplicationDocumentType.AppLogs,
        ]
    },
    {
        FilenameTemplatePlaceholders.StateProvince, [
            LoanApplicationDocumentType.AdverseActionNotice,
            LoanApplicationDocumentType.TILA,
            LoanApplicationDocumentType.AppLogs,
        ]
    },
    {
        FilenameTemplatePlaceholders.DocCreatedDate, [
            LoanApplicationDocumentType.AdverseActionNotice,
            LoanApplicationDocumentType.TILA,
            LoanApplicationDocumentType.EConsentDisclosure,
            LoanApplicationDocumentType.AppLogs,
        ]
    },
};

    public bool ParseTemplate(
        string? template,
        LoanApplicationDocumentType documentType,
        out List<string> allowedVariables,
        out List<string> notAllowedVariables)
    {
        allowedVariables = [];
        notAllowedVariables = [];
        const string pattern = @"^(?:[^{}]*\{([^\{\}]+)\})*[^{}]*$";
        var match = Regex.Match(template ?? "", pattern, RegexOptions.ExplicitCapture, TimeSpan.FromSeconds(RegexValidation.RegexTimeoutSeconds));
        if (!match.Success)
        {
            return false;
        }

        foreach (Capture capture in match.Groups[1].Captures)
        {
            var variable = $"{{{capture.Value}}}";
            if (!_allowedFilenamePlaceholder.ContainsKey(variable) ||
                !_allowedFilenamePlaceholder[variable].Contains(documentType))
            {
                notAllowedVariables.Add(variable);
                continue;
            }

            allowedVariables.Add(variable);
        }

        return true;
    }

    public async Task<string> GetDocumentNameAsync(
            LoanApplicationDocumentType docType,
            Data.Models.LoanApplication la,
            int? cleanCharsTotal = 4,
            bool isSigned = false,
            bool redactInformation = false)
    {
        var selectedProduct = await qCashContext.Products.AsNoTracking().FirstAsync(o => o.Id == la.SelectedProductId.GetValueOrDefault());
        //var selectedProduct = _settingService.GetProduct(la.SelectedProductId.GetValueOrDefault());
        var settings = await qCashContext.Settings.AsNoTracking().FirstAsync();

        string filenameTemplate;
        switch (docType)
        {
            case LoanApplicationDocumentType.AdverseActionNotice:
                filenameTemplate = settings.FilenameAan;
                break;
            case LoanApplicationDocumentType.TILA:
                filenameTemplate = selectedProduct.FilenameTilaTemplate ?? string.Empty;
                break;
            case LoanApplicationDocumentType.EConsentDisclosure:
                filenameTemplate = settings.FilenameEConsent;
                break;
            default:
                filenameTemplate = string.Empty;
                break;
        }

        var accountId = la.AccountId ?? string.Empty;
        var account = cleanCharsTotal.HasValue
            ? GetMaskedNumber(accountId, cleanCharsTotal.Value)
            : accountId.TrimStart('0');

        if (string.IsNullOrWhiteSpace(account))
        {
            logger.LogWarning("Failed to get account for {FilenameTemplate}", filenameTemplate);
        }

        var clientTimeZone = qCashContext.Settings.FirstOrDefault()?.TimeZone ?? string.Empty;
        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(clientTimeZone);
        var maturityDate = la.MatureDateUtc.FromUTC(timeZone).ToString("MMddyyyy");

        var signedSuffix = isSigned ? "_Signed" : string.Empty;
        if (string.IsNullOrEmpty(filenameTemplate))
        {
            var fnDocType = docType.ToString();
            return $"{account}_{fnDocType}_{la.AppId}_{maturityDate}{signedSuffix}.PDF";
        }

        if (filenameTemplate.Contains(FilenameTemplatePlaceholders.Account) &&
            _allowedFilenamePlaceholder[FilenameTemplatePlaceholders.Account].Contains(docType))
        {
            filenameTemplate = filenameTemplate.Replace(FilenameTemplatePlaceholders.Account,
                redactInformation ? "acct-redacted" : account);
        }

        if (filenameTemplate.Contains(FilenameTemplatePlaceholders.Product) &&
            _allowedFilenamePlaceholder[FilenameTemplatePlaceholders.Product].Contains(docType))
        {
            filenameTemplate = filenameTemplate.Replace(
                FilenameTemplatePlaceholders.Product,
                selectedProduct.Name.Replace(" ", string.Empty));
        }

        if (filenameTemplate.Contains(FilenameTemplatePlaceholders.AppId) &&
            _allowedFilenamePlaceholder[FilenameTemplatePlaceholders.AppId].Contains(docType))
        {
            //var appID = la.IsSyncedEntry ? la.LoanApplicationSync.BackupAppId : la.AppId;
            var appID = la.AppId;
            filenameTemplate = filenameTemplate.Replace(FilenameTemplatePlaceholders.AppId, appID.ToString());
        }

        if (filenameTemplate.Contains(FilenameTemplatePlaceholders.LoanOrigination) &&
            _allowedFilenamePlaceholder[FilenameTemplatePlaceholders.LoanOrigination].Contains(docType))
        {
            filenameTemplate = filenameTemplate.Replace(FilenameTemplatePlaceholders.LoanOrigination,
                la.DateCreatedUtc.FromUTC(timeZone).ToString("MMddyyyy"));
        }

        if (filenameTemplate.Contains(FilenameTemplatePlaceholders.Maturity) &&
            _allowedFilenamePlaceholder[FilenameTemplatePlaceholders.Maturity].Contains(docType))
        {
            filenameTemplate = filenameTemplate.Replace(FilenameTemplatePlaceholders.Maturity, maturityDate);
        }

        if (filenameTemplate.Contains(FilenameTemplatePlaceholders.PlainSsn) &&
            _allowedFilenamePlaceholder[FilenameTemplatePlaceholders.PlainSsn].Contains(docType))
        {
            if (redactInformation)
            {
                filenameTemplate = filenameTemplate.Replace(FilenameTemplatePlaceholders.PlainSsn, "ssn-redacted");
            }
            else
            {
                var ssn = await taxIdService.GetTaxIdByFinancialInstitutionMemberIdAsync(la.FinancialInstitutionMemberId);
                filenameTemplate = filenameTemplate.Replace(FilenameTemplatePlaceholders.PlainSsn, ssn);
            }
        }

        if (filenameTemplate.Contains(FilenameTemplatePlaceholders.Ssn) &&
            _allowedFilenamePlaceholder[FilenameTemplatePlaceholders.Ssn].Contains(docType))
        {
            if (redactInformation)
            {
                filenameTemplate = filenameTemplate.Replace(FilenameTemplatePlaceholders.Ssn, "ssn-redacted");
            }
            else
            {
                var ssn = await taxIdService.GetTaxIdByFinancialInstitutionMemberIdAsync(la.FinancialInstitutionMemberId);
                var maskedSSN = GetMaskedNumber(ssn, 4, '0');
                filenameTemplate = filenameTemplate.Replace(FilenameTemplatePlaceholders.Ssn, maskedSSN);
            }
        }

        if (filenameTemplate.Contains(FilenameTemplatePlaceholders.FirstName) &&
            _allowedFilenamePlaceholder[FilenameTemplatePlaceholders.FirstName].Contains(docType))
        {
            filenameTemplate = filenameTemplate.Replace(FilenameTemplatePlaceholders.FirstName,
                la.FinancialInstitutionMember.FirstName ?? "");
        }

        if (filenameTemplate.Contains(FilenameTemplatePlaceholders.LastName) &&
            _allowedFilenamePlaceholder[FilenameTemplatePlaceholders.LastName].Contains(docType))
        {
            filenameTemplate = string.IsNullOrWhiteSpace(la.FinancialInstitutionMember.Suffix)
                ? filenameTemplate.Replace(FilenameTemplatePlaceholders.LastName,
                    la.FinancialInstitutionMember.LastName ?? "")
                : filenameTemplate.Replace(FilenameTemplatePlaceholders.LastName,
                    $"{la.FinancialInstitutionMember.LastName} {la.FinancialInstitutionMember.Suffix}");
        }

        if (filenameTemplate.Contains(FilenameTemplatePlaceholders.MiddleName) &&
            _allowedFilenamePlaceholder[FilenameTemplatePlaceholders.MiddleName].Contains(docType))
        {
            filenameTemplate = filenameTemplate.Replace(FilenameTemplatePlaceholders.MiddleName,
                la.FinancialInstitutionMember.MiddleName ?? "-");
        }

        if (filenameTemplate.Contains(FilenameTemplatePlaceholders.StateProvince) &&
            _allowedFilenamePlaceholder[FilenameTemplatePlaceholders.StateProvince].Contains(docType))
        {
            filenameTemplate = filenameTemplate.Replace(FilenameTemplatePlaceholders.StateProvince,
                la.FinancialInstitutionMember.State ?? "-");
        }

        if (filenameTemplate.Contains(FilenameTemplatePlaceholders.DocCreatedDate) &&
            _allowedFilenamePlaceholder[FilenameTemplatePlaceholders.DocCreatedDate].Contains(docType))
        {
            Guid? documentId;
            switch (docType)
            {
                case LoanApplicationDocumentType.AdverseActionNotice:
                    documentId = la.AdverseActionNoticeDocumentId.GetValueOrDefault();
                    break;
                case LoanApplicationDocumentType.EConsentDisclosure:
                    documentId = la.MemberConsentEConsentOrPriorEConsent()?.DocumentId.GetValueOrDefault();
                    break;
                case LoanApplicationDocumentType.TILA:
                    documentId = GetTilaDocumentId(la.Id);
                    break;
                default:
                    documentId = null;
                    break;
            }

            var docCreatedDate = DateTime.UtcNow;
            if (documentId.HasValue)
            {
                var document = qCashContext.Documents.Single(o => o.Id == documentId.Value);
                docCreatedDate = document?.DateCreatedUtc ?? DateTime.UtcNow;
            }

            var docCreatedDateStr = docCreatedDate.FromUTC(timeZone).ToString("MMddyyyy");
            filenameTemplate = filenameTemplate.Replace(FilenameTemplatePlaceholders.DocCreatedDate, docCreatedDateStr);
        }

        filenameTemplate += signedSuffix;
        return filenameTemplate + ".PDF";
    }

    private Guid? GetTilaDocumentId(Guid loanAppId)
    {
        var memberConsent = qCashContext.MemberConsents.Where(p => p.LoanApplicationId.Equals(loanAppId)).OrderBy(p => p.Id)
            .FirstOrDefault(x => x.AcceptanceType == MemberConsentAcceptanceType.LoanDisclosure.ToString());
        return memberConsent?.DocumentId.GetValueOrDefault();
    }
    private string GetMaskedNumber(string? number, int cleanCharsTotal = 4, char maskedChar = 'X')
    {
        //already exists in LoanApplicationService, but duplicating here so as not to need that service
        if (string.IsNullOrEmpty(number))
        {
            return string.Empty;
        }

        var diff = number.Length - cleanCharsTotal;
        var zeros = diff < 0 ? Math.Abs(diff) : 0;
        var clean = diff < 0 ? number : number.Substring(diff);
        var maskedNum = new string(maskedChar, 10 - cleanCharsTotal) + new string('0', zeros) + clean;

        return maskedNum;
    }
}
