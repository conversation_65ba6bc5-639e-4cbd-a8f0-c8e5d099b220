using Kendo.Mvc.UI;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;

namespace QCash.Service.Services.User.Interfaces;

public interface IUserService
{
    public string RecordName { get; }
    Task<UserDetailsPageDTO?> GetUserPageDataAsync(string userName);
    Task<UserDetailsPageDTO> GetBlankDtoToCreateUserAsync();
    void PopulateMetadata(UserDetailsPageDTO dto, List<QListItem<Guid>> roles);
    Task<GenericActionResult> SaveAsync(Guid financialInstitutionId, string userName, UserDetailsPageDTO dto);
    Task<GenericActionResult> UnlockUserAsync(string userName);
    Task<List<QListItem<Guid>>> GetRoleChoicesAsync();
    Task<DataSourceResult> GetUsersQueryableAsync(string fiSlug, DataSourceRequest request);
    GenericActionResult DeleteItem(DeleteItemDto dto);
    Task<AspNetUser?> FindByEmailWithRolesAsync(string email);
    Task<AspNetUser?> FindByEmailAsync(string email);
    AspNetUser CreateUserForTenant(Guid tenantId);
    Task<AspNetUser?> FindByUserIdWithRolesAsync(Guid userId);
    void UpdateUser(AspNetUser user);
    GenericActionResult ChangePassword(AspNetUser user, string oldPassword, string newPassword);
}
