using CommunityToolkit.Diagnostics;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.FIConfiguration;
using QCash.Service.Models.General;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.User.Interfaces;
using QCash.Service.Utilities.Extensions;
using QCash.Utils.Core;
using static QCash.Service.Models.FIConfiguration.UserManagement.Constants;

namespace QCash.Service.Services.User;

public class UserService(
    ILogger<IUserService> logger,
    QCashContext context,
    ISystemClockService systemClockService,
    ILookupQueries lookupQueries,
    IRoleService roleService,
    IAuthUserService authUserService,
    IPasswordHasher<AspNetUser> passwordHasher,
    IEfPocoService efPocoService
    ) : IUserService
{
    // these permission flags refer to the currently logged-in user.
    private bool? IsSuperUser { get; set; }     // null indicates the value has not been retrieved from the database yet.
    private bool? IsSystemAdmin { get; set; }     // null indicates the value has not been retrieved from the database yet.
    private bool? IsFIManager { get; set; }     // null indicates the value has not been retrieved from the database yet.
    public string RecordName { get; } = "User";

    private async Task<UserDetailsPageDTO?> GetDTOFromUserQueryableAsync(IQueryable<AspNetUser>? userQuery)
    {
        UserDetailsPageDTO? dto;
        if (userQuery == null)
        {
            dto = new UserDetailsPageDTO { IsApproved = true, TimeStamp = [], UserName = "Create", };
        }
        else
        {
            dto = await userQuery.Select(u => new UserDetailsPageDTO
            {
                Id = u.Id,
                FirstName = u.FirstName ?? string.Empty,
                LastName = u.LastName ?? string.Empty,
                Email = u.Email ?? string.Empty,
                UserName = u.UserName ?? "",
                PhoneNumber = u.PhoneNumber ?? "",
                IsApproved = u.IsApproved,
                IsLockedOut = u.LockoutEnd.HasValue && u.LockoutEnd.Value.UtcDateTime > systemClockService.GetSystemTimeUtc(),
                DateUpdatedUtc = u.DateUpdatedUtc,
                DateCreatedUtc = u.DateCreatedUtc,
                SelectedRoles = u.Roles.Select(ur => new QListItem<Guid>
                {
                    Value = ur.Id,
                    Text = ur.Name ?? "",
                }).ToList(),
                TimeStamp = u.TimeStamp,
            })
            .SingleOrDefaultAsync();
        }

        if (dto == null)
        {
            return dto;
        }

        // TODO: we probably need to associate a user with a FI to get their time zone?
        // or get logged-in user's time zone?
        var timeZone = SystemClockService.DefaultTimeZoneNameId;
        if (dto.DateCreatedUtc.HasValue)
        {
            dto.DateCreatedLocal = systemClockService.ConvertUTCTimeToLocal(dto.DateCreatedUtc.Value, timeZone);
        }
        if (dto.DateUpdatedUtc.HasValue)
        {
            dto.DateUpdatedLocal = systemClockService.ConvertUTCTimeToLocal(dto.DateUpdatedUtc.Value, timeZone);
        }
        return dto;
    }


    public async Task<UserDetailsPageDTO?> GetUserPageDataAsync(string userName)
    {
        var userQuery = context.AspNetUsers
            .AsNoTracking()
            .Where(u => u.UserName == userName);
        var dto = await GetDTOFromUserQueryableAsync(userQuery);
        return dto;
    }

    public async Task<UserDetailsPageDTO> GetBlankDtoToCreateUserAsync()
    {
        var dto = await GetDTOFromUserQueryableAsync(null);
        dto.ThrowIfNull();
        dto.UserName = "Create";
        return dto;
    }

    private bool CanFiManagerEditInputs(List<QListItem<Guid>> userRoles)
    {
        var roles = userRoles.ToList();
        var canUpdate = !(roleService.IsSuperUser(roles)
                          || roleService.IsSystemAdmin(roles)
                          || roleService.IsDeManager(roles));

        return canUpdate;
    }

    internal GenericActionResult GetUserCanUpdateRole(List<QListItem<Guid>> userRoles, string userNameBeingEdited, string loggedInUserRoles, string userNameLoggedIn)
    {
        // If the user is editing their own record.
        var canUpdateSelf = roleService.CanEditOwnRoles(userNameBeingEdited,
            userNameLoggedIn, loggedInUserRoles);
        if (!canUpdateSelf)
        {
            return new GenericActionResult
            {
                IsSuccessful = false,
                ErrorMessage = "User does not have permissions to edit their own record.",
            };
        }

        // Role Admin validation.
        if (loggedInUserRoles.Contains(RoleAdminAbrv))
        {
            var success = !(roleService.IsSuperUser(userRoles)
                       || roleService.IsFIManager(userRoles)
                       || roleService.IsSystemAdmin(userRoles));
            return new GenericActionResult
            {
                IsSuccessful = success,
                ErrorMessage = success ? null : "Role Admins cannot edit super users / FI Managers / system admins.",
            };
        }

        //FiAdmin validation
        if (loggedInUserRoles.Contains(FiManagerAbrv))
        {
            var success = !(roleService.IsSuperUser(userRoles)
                            || roleService.IsSystemAdmin(userRoles)
                            || roleService.IsDeManager(userRoles));
            return new GenericActionResult
            {
                IsSuccessful = success,
                ErrorMessage = success ? null : "FI Managers cannot edit super users / system admins / DE Managers.",
            };
        }

        // Role System Admin validation
        if (loggedInUserRoles.Contains(SystemAdminAbrv))
        {
            var success = !(roleService.IsSuperUser(userRoles) || roleService.IsSystemAdmin(userRoles));
            return new GenericActionResult
            {
                IsSuccessful = success,
                ErrorMessage = success ? null : "System admins cannot edit super users / other system admins.",
            };
        }

        // Role Super User validation.
        if (loggedInUserRoles.Contains(SuperUserAbrv))
        {
            return new GenericActionResult
            {
                IsSuccessful = true,
                ErrorMessage = null,
            };
        }

        return new GenericActionResult
        {
            IsSuccessful = false,
            ErrorMessage = "Insufficient permissions to edit roles",
        };
    }

    public async Task<List<QListItem<Guid>>> GetRoleChoicesAsync()
    {
        var roles = lookupQueries.GetRolesQueryable();
        var models = await roles
            .AsNoTracking()
            .Select(c =>
                new QListItem<Guid>
                {
                    Value = c.Id,
                    Text = c.Name ?? "",
                })
            .ToListAsync();
        return models;
    }

    public void PopulateMetadata(UserDetailsPageDTO dto, List<QListItem<Guid>> roles)
    {
        SetRoleTypeId(dto);
        dto.RoleTypeOptions = GetRoleTypeOptions(roles);
        dto.CanUpdateRoles = GetUserCanUpdateRole(dto.SelectedRoles, dto.UserName, authUserService.Roles, authUserService.Username);
        logger.LogDebug("Editing/creating user:[{UserName}]. LoggedInUser:[{LoggedInUser}].  CanUpdateRoles={CanUpdateRoles}  [{Explanation}]", dto.UserName, authUserService.Username, dto.CanUpdateRoles.IsSuccessful, dto.CanUpdateRoles.ErrorMessage);
        dto.EditingSuperUser = roleService.IsSuperUser(dto.SelectedRoles);
        dto.CanFIMngrEditInputs = CanFiManagerEditInputs(dto.SelectedRoles);
    }

    private void SetRoleTypeId(UserDetailsPageDTO dto)
    {
        var role = dto.SelectedRoles
            .FirstOrDefault(r => r.Text == SuperUserAbrv || r.Text == SystemAdminAbrv);
        if (role != null)
        {
            dto.RoleTypeId = role.Value;
            return;
        }

        role = dto.SelectedRoles
            .FirstOrDefault(r => r.Text == FiManagerAbrv || r.Text == DeManagerAbrv);
        if (role != null)
        {
            dto.RoleTypeId = ManagerRoleTypeGuid;
            return;
        }

        dto.RoleTypeId = OtherRoleTypeGuid;
    }

    private List<QListItem<Guid>> GetRoleTypeOptions(List<QListItem<Guid>> roleChoices)
    {
        if (!roleChoices.Any())
        {
            return [];
        }
        var result = new List<QListItem<Guid>>
        {
            new() { Text = "Other", Value = OtherRoleTypeGuid, },
            new() { Text = "Manager", Value = ManagerRoleTypeGuid, },
        };

        result.AddRange(roleChoices
            .Where(r => string.Equals(r.Text, SystemAdminAbrv, StringComparison.Ordinal)
                        || string.Equals(r.Text, SuperUserAbrv, StringComparison.Ordinal)));

        return result;
    }

    private async Task<bool> EmailExistsAsync(Guid currentUserId, string emailAddress)
    {
        var normalize = Normalize(emailAddress);
        var result = await context.AspNetUsers
            .Where(u => u.NormalizedEmail == normalize)
            .AnyAsync(u => u.Id != currentUserId);
        return result;
    }

    public async Task<GenericActionResult> SaveAsync(Guid financialInstitutionId, string userName, UserDetailsPageDTO dto)
    {
        if (dto.UserName != userName)
        {
            throw new Exception($"User posted ({dto.UserName}) does not match url ({userName}) ");
        }

        if (await EmailExistsAsync(dto.Id, dto.Email))
        {
            return new GenericActionResult
            {
                IsSuccessful = false,
                ErrorMessage = "Email address is already in use",
            };
        }

        GenericActionResult<int>? saveRoleResult = null;
        var userResult = await efPocoService.CreateOrUpdateAsync(dto.Id, financialInstitutionId, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<AspNetUser>
            {
                ExecuteRecordChangesFunc = performUpdateParam =>
                {
                    if (performUpdateParam.CreatingNewRecord)
                    {
                        performUpdateParam.Record.PasswordChangeDate = performUpdateParam.Record.DateCreatedUtc;
                        performUpdateParam.Record.EmailConfirmed = true;
                        performUpdateParam.Record.PasswordHash = passwordHasher.HashPassword(performUpdateParam.Record, dto.Password ?? "");
                        performUpdateParam.Record.SecurityStamp = Guid.NewGuid().ToString();
                    }

                    performUpdateParam.Record.UserName = string.Concat(dto.FirstName.Trim().AsSpan(0, 1), dto.LastName.Trim());
                    performUpdateParam.Record.NormalizedUserName = Normalize(performUpdateParam.Record.UserName);
                    performUpdateParam.Record.FirstName = dto.FirstName;
                    performUpdateParam.Record.LastName = dto.LastName;
                    performUpdateParam.Record.Email = dto.Email;
                    performUpdateParam.Record.NormalizedEmail = Normalize(dto.Email);
                    performUpdateParam.Record.PhoneNumber = dto.PhoneNumber;
                    performUpdateParam.Record.IsApproved = dto.IsApproved;

                    saveRoleResult = SaveRoles(dto.UserName, dto, performUpdateParam.Record);
                    var roleChangeCount = saveRoleResult.IsSuccessful ? saveRoleResult.Value : 0;
                    var entry = context.Entry(performUpdateParam.Record);
                    if (roleChangeCount > 0 && entry.State == EntityState.Unchanged)
                    {   // changes to roles should be timestamp-checked together with the user.  So if the user didn't change, trigger it to be changed.
                        entry.State = EntityState.Modified;
                    }
                },
                QueryableCustomization = q =>
                {
                    return q
                        .Include(a => a.Roles);
                },
            });
        if (saveRoleResult != null && !saveRoleResult.IsSuccessful)
        {
            return new GenericActionResult { ErrorMessage = saveRoleResult.ErrorMessage, IsSuccessful = false, };
        }
        if (!userResult.IsSuccessful)
        {
            return new GenericActionResult { ErrorMessage = userResult.ErrorMessage, IsSuccessful = false, };
        }
        dto.UserName = userResult.Record?.UserName ?? "";
        userResult.Record.ThrowIfNull();
        return new GenericActionResult { IsSuccessful = true, };
    }

    /// <summary>
    /// Compare the changes from the browser with the existing roles in the database for this user.
    /// Insert / Delete the appropriate records.
    /// </summary>
    /// <param name="userName"></param>
    /// <param name="dto"></param>
    /// <param name="dbUser"></param>
    /// <returns>the number of records to be changed in AspNetUserRole</returns>
    private GenericActionResult<int> SaveRoles(string userName, UserDetailsPageDTO dto, AspNetUser dbUser)
    {
        var userCanUpdateRoles = GetUserCanUpdateRole(dto.SelectedRoles, userName, authUserService.Roles, authUserService.Username);
        if (!userCanUpdateRoles.IsSuccessful)
        {
            // Clear out the previously selected role if the current user does not have sufficient permission
            // Keeping previously Selected Role caused downstream impact when re-evaluating if the Role Type section should be Enabled/Disabled
            dto.SelectedRoles.Clear();

            logger.LogWarning("User edit attempted to save edited roles without permission.  UI should have prevented this.  User={UserName}", userName);
            return new GenericActionResult<int>
            {
                IsSuccessful = false,
                ErrorMessage = "Current user does not have sufficient permissions to set this user's role - " + userCanUpdateRoles.ErrorMessage,
            };
        }

        var selectedRoleIds = dto.SelectedRoles.Select(a => a.Value).ToList();
        var currentRoles = dbUser.Roles.Select(a => a.Id).ToList();
        var newPendingRoleIds = selectedRoleIds.Except(currentRoles).ToList();
        dbUser.Roles.AddRange(
            context.AspNetRoles.Where(a => newPendingRoleIds.Contains(a.Id))
            );
        var removingRoleIds = currentRoles.Except(selectedRoleIds).ToList();
        foreach (var removingRoleId in removingRoleIds)
        {
            dbUser.Roles.Remove(dbUser.Roles.Single(a => a.Id == removingRoleId));
        }
        var changeCount = newPendingRoleIds.Count + removingRoleIds.Count;
        return new GenericActionResult<int> { IsSuccessful = true, Value = changeCount, };
    }

    public async Task<GenericActionResult> UnlockUserAsync(string userName)
    {
        var recordCount = await context.AspNetUsers
            .Where(a => a.UserName == userName)
            .ExecuteUpdateAsync(a =>
                a.SetProperty(b => b.LockoutEnd, DateTimeOffset.UtcNow.AddMinutes(-1)));
        if (recordCount == 1)
        {
            return new GenericActionResult { IsSuccessful = true };
        }

        if (recordCount == 0)
        {
            return new GenericActionResult
            {
                IsSuccessful = false,
                ErrorMessage = $"No such user {userName} was found.",
            };
        }

        throw new Exception($"Error in UnlockUserAsync updating {userName}.  Updated {recordCount} records.");
    }

    private IQueryable<UserListPageItemDTO> GetUsersQueryable() =>
        context.AspNetUsers.Select(u => new UserListPageItemDTO
        {
            Id = u.Id,
            EmailAddress = u.Email ?? string.Empty,
            FirstName = u.FirstName ?? string.Empty,
            LastName = u.LastName ?? string.Empty,
            UserName = u.UserName!,
            ActiveStatus = u.IsApproved,
            CanDelete = false,  // value defaults to false.  real value is resolved by caller.
            IsSuperUser = u.Roles.Any(ur => ur.Name == SuperUserAbrv),
            IsSystemAdmin = u.Roles.Any(ur => ur.Name == SystemAdminAbrv),
            TimeStamp = u.TimeStamp,
            Roles = string.Join(", ", u.Roles.Select(ur => ur.Name).OrderBy(a => a).ToList()),
        });

    public async Task<DataSourceResult> GetUsersQueryableAsync(string fiSlug, DataSourceRequest request)
    {
        var q = GetUsersQueryable();
        var result = await q.ToDataSourceResultAsync(request);
        foreach (var item in result.Data.Cast<UserListPageItemDTO>())
        {
            item.CanDelete = CanDelete(item);
        }
        return result;
    }

    private bool CanDelete(UserListPageItemDTO user)
    {
        IsSuperUser ??= authUserService.IsSuperUser();
        IsSystemAdmin ??= authUserService.IsSystemAdmin();
        IsFIManager ??= authUserService.IsFiManager();
        return IsSuperUser == null || IsSystemAdmin == null || IsFIManager == null
            ? throw new Exception("Error in CanDeleteAsync.  IsSuperUser, IsSystemAdmin, or IsFIManager cannot be determined.")
            : (IsSuperUser == true)
                || ((IsSystemAdmin == true) && !user.IsSuperUser)
                || ((IsFIManager == true) && !user.IsSuperUser && !user.IsSystemAdmin);
    }

    public GenericActionResult DeleteItem(DeleteItemDto dto)
    {
        var userRecord = GetUsersQueryable()
            .FirstOrDefault(a => a.Id == dto.DeleteItemId);
        if (userRecord == null)
        {
            return new GenericActionResult { IsSuccessful = true, };
        }
        var canDelete = CanDelete(userRecord);
        if (!canDelete)
        {
            logger.LogInformation("User attempted to delete a user without permission.  UI prevented this.  User={UserName}", userRecord.UserName);
            return new GenericActionResult
            {
                IsSuccessful = false,
                ErrorMessage = "You do not have sufficient permissions to delete this user",
            };
        }

        context.Remove(new AspNetUser { Id = dto.DeleteItemId });
        return new GenericActionResult { IsSuccessful = true, };
    }

    public GenericActionResult ChangePassword(AspNetUser user, string oldPassword, string newPassword)
    {
        var verifyOldPasswordVerificationResult = passwordHasher.VerifyHashedPassword(user, user.PasswordHash!, oldPassword);
        if (verifyOldPasswordVerificationResult == PasswordVerificationResult.Failed)
        {
            return new GenericActionResult
            {
                IsSuccessful = false,
                ErrorMessage = "Old Password is incorrect.",
            };
        }

        if (oldPassword == newPassword)
        {
            return new GenericActionResult
            {
                IsSuccessful = false,
                ErrorMessage = "New password must be different from the current password.",
            };
        }

        user.PasswordHash = passwordHasher.HashPassword(user, newPassword);
        user.PasswordChangeDate = DateTime.UtcNow;
        user.SecurityStamp = Guid.NewGuid().ToString();

        context.Update(user);

        return new GenericActionResult { IsSuccessful = true };
    }

    public async Task<AspNetUser?> FindByEmailAsync(string email)
    {
        var normalizedEmail = Normalize(email);
        return await context.AspNetUsers.FirstOrDefaultAsync(u => u.NormalizedEmail == normalizedEmail);
    }

    public AspNetUser CreateUserForTenant(Guid tenantId) =>
        new()
        {
            FinancialInstitutionId = tenantId,
            SecurityStamp = Guid.NewGuid().ToString(),
            TwoFactorEnabled = false,
            LockoutEnabled = false,
            LockoutEnd = null,
            AccessFailedCount = 0,
            PhoneNumberConfirmed = false,
            PhoneNumber = null,
            PasswordHash = null,
            RecoveryCode = null,
            RecoveryCodeTime = null,
            PasswordChangeDate = DateTime.UtcNow,
            DeleteDate = null,
            IsDeleted = false,
        };

    public async Task<AspNetUser?> FindByUserIdWithRolesAsync(Guid userId)
    {
        Guard.IsNotDefault(userId);

        return await context.AspNetUsers
            .Include(x => x.Roles)
            .FirstOrDefaultAsync(x => x.Id == userId);
    }

    public async Task<AspNetUser?> FindByEmailWithRolesAsync(string email)
    {
        var normalized = Normalize(email);
        return await context.AspNetUsers
            .Include(u => u.Roles)
            .FirstOrDefaultAsync(u => u.NormalizedEmail == normalized);
    }

    public void UpdateUser(AspNetUser user)
    {
        user.NormalizedUserName = Normalize(user.UserName ?? string.Empty);
        user.NormalizedEmail = Normalize(user.Email ?? string.Empty);
        context.Update(user);
    }

    private static string Normalize(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return string.Empty;
        }

        var normalized = input.Trim().Normalize(System.Text.NormalizationForm.FormD);
        var sb = new System.Text.StringBuilder();
        foreach (var c in normalized)
        {
            var unicodeCategory = System.Globalization.CharUnicodeInfo.GetUnicodeCategory(c);
            if (unicodeCategory != System.Globalization.UnicodeCategory.NonSpacingMark)
            {
                sb.Append(c);
            }
        }
        return sb.ToString().Normalize(System.Text.NormalizationForm.FormC);
    }
}
