using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Models;
using QCash.Models.Api;
using QCash.Service.FileSystem;
using QCash.Service.Models;
using QCash.Service.Models.General;
using QCash.Service.Models.MemberInterface;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.MemberInterface.Interfaces;

namespace QCash.Service.Services.MemberInterface;

public class MemberInterfacePresentationService(
    IEfPocoService efPocoService,
    IFileSystemProvider fileSystemProvider) : IMemberInterfacePresentationService
{
    public string RecordName { get; } = "Member Interface Presentation Settings";

    /// <summary>
    /// There are two records for each FI in the MemberInterfacePresentation table.
    /// Abrv = QCash is the default record, and it should not ever be changed.  That's the source if we want to revert to defaults.
    /// Abrv = QCashTemplate is the editable record with current data per FI.
    /// </summary>
    /// <returns>The instance of <see cref="PresentationPageViewDto"/>.</returns>
    public async Task<PresentationPageViewDto?> GetPageDataAsync()
    {
        var dto = await efPocoService
            .GetQuery<MemberInterfacePresentation>(q => q.Where(mip => mip.Abrv == "QCashTemplate"))
            .AsNoTracking()
            .Select(presentation => new PresentationPageViewDto
            {
                MemberInterfacePresentationId = presentation.Id,
                MemberInterfacePresentationTimeStamp = presentation.TimeStamp,
                LogoLink = presentation.LogoLink,
                BodyFont = presentation.BodyFont,
                ShouldClearLogo = false,
                FirebirdPrimaryColor = presentation.FirebirdPrimaryColor,
                FirebirdSecondaryColor = presentation.FirebirdSecondaryColor,
            })
            .SingleOrDefaultAsync();

        if (dto != null)
        {
            await fileSystemProvider.SetupAsync(ContainerTypeEnum.Public);
            if (string.IsNullOrWhiteSpace(dto.LogoLink))
            {
                // No logo found in the database?  Show the default blank logo.
                dto.LogoLink = Constants.DefaultLogoFullPath;
            }
            else
            {
                var logoFileName = Constants.MainLogoName + Path.GetExtension(dto.LogoLink);
                dto.LogoLink = fileSystemProvider.GetFileUrl(logoFileName);
            }

        }
        return dto;
    }

    /// <summary>
    /// Gets member interface presentation DTO object.
    /// </summary>
    /// <returns>The instance of <see cref="WizardMemberInterfacePresentation"/>.</returns>
    public async Task<WizardMemberInterfacePresentation?> GetMemberInterfacePresentationAsync()
    {
        var dto = await efPocoService
            .GetQuery<MemberInterfacePresentation>(q => q.Where(mip => mip.Abrv == "QCashTemplate"))
            .AsNoTracking()
            .Select(presentation => new WizardMemberInterfacePresentation
            {
                Id = presentation.Id,
                CssFilePath = presentation.CssPath ?? string.Empty,
                CssIE8FilePath = presentation.CssIe8Path ?? string.Empty,
                LogoFilePath = presentation.LogoLink,
                PrimaryColor = presentation.FirebirdPrimaryColor,
                SecondaryColor = presentation.FirebirdSecondaryColor,
            })
            .SingleOrDefaultAsync();

        return dto;
    }

    public async Task<GenericActionResult> SaveAsync(Guid financialInstitutionId, PresentationPageViewDto dto,
        List<IFormFile>? logoFiles)
    {
        var saveTemplateCssResult = await SaveTemplateCssAsync(dto);
        if (!saveTemplateCssResult.IsSuccessful)
        {
            return saveTemplateCssResult;
        }

        if (dto.ShouldClearLogo)
        {
            dto.LogoLink = "";
        }
        else
        {
            var saveFileResult = await SaveFileAsync(logoFiles, dto);
            if (!saveFileResult.IsSuccessful)
            {
                return saveFileResult;
            }
        }

        var logoFileName = logoFiles?.FirstOrDefault()?.FileName;
        var saveResult = await efPocoService.CreateOrUpdateAsync(dto.MemberInterfacePresentationId, financialInstitutionId,
            dto.MemberInterfacePresentationTimeStamp ?? [],
            new PerformCreateOrUpdateOptions<MemberInterfacePresentation>()
            {
                ExecuteRecordChangesFunc = (o) =>
                {
                    if (o.CreatingNewRecord)
                    {
                        throw new Exception("'MemberInterfacePresentation' record was not found, and cannot be created from this page.");
                    }

                    if (!string.IsNullOrWhiteSpace(logoFileName))
                    {
                        o.Record.LogoLink = $"~/Content/themes/qcash/images/{logoFileName}";
                    }
                    o.Record.BodyFont = dto.BodyFont;
                    o.Record.FirebirdPrimaryColor = dto.FirebirdPrimaryColor;
                    o.Record.FirebirdSecondaryColor = dto.FirebirdSecondaryColor;
                    o.Record.LogoLink = dto.LogoLink;
                },
            });
        return saveResult;
    }

    private async Task<GenericActionResult> SaveTemplateCssAsync(PresentationPageViewDto dto)
    {
        var presentationTemplate = await efPocoService.GetItemAsync<MemberInterfacePresentationTemplate>(id: null);
        if (presentationTemplate == null)
        {
            return new GenericActionResult() { IsSuccessful = false, ErrorMessage = "Error: MemberPresentationTemplateLookup record does not exist", };
        }
        return new GenericActionResult() { IsSuccessful = true, };
    }

    private async Task<GenericActionResult> SaveFileAsync(List<IFormFile>? logoFiles, PresentationPageViewDto dto)
    {
        if (logoFiles == null || logoFiles.Count != 1)
        {
            return new GenericActionResult() { IsSuccessful = true, };
        }
        var file = logoFiles.Single();
        await fileSystemProvider.SetupAsync(ContainerTypeEnum.Public);
        var origFn = file.FileName;
        var fn = origFn.Replace(Path.GetFileName(origFn), Constants.MainLogoName + Path.GetExtension(origFn));

        var fileStoreResult = await fileSystemProvider.StoreFileAsync(file.OpenReadStream(), file.ContentType, fn);

        dto.LogoLink = file.FileName;
        return new GenericActionResult() { IsSuccessful = fileStoreResult, };
    }
}
