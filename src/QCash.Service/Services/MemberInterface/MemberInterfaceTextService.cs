using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using QCash.Data.Models;
using QCash.Data.Models.Enums;
using QCash.Service.Models.MemberInterface;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.MemberInterface.Interfaces;

namespace QCash.Service.Services.MemberInterface;

public class MemberInterfaceTextService(
    IEfPocoService efPocoService
) : IMemberInterfaceTextService
{
    public async Task<DataSourceResult> GetAllAsync(DataSourceRequest request)
    {
        var query = efPocoService.GetQuery<InterfaceDefaultText>()
            .Where(idt => idt.ProductId == null
                && idt.Language.Name == LanguageEnum.English.Name)
            .Select(idt => new TextLandingPageListItemDto
            {
                Id = idt.Id,
                FieldName = idt.Name ?? "",
                Value = (idt.InterfaceOverrideTexts.FirstOrDefault() == null
                    ? idt.FieldValue
                    : idt.InterfaceOverrideTexts.FirstOrDefault()!.FieldValue) ?? "",
            });
        var result = await query.ToDataSourceResultAsync(request);
        return result;
    }
}
