using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.General;
using QCash.Service.Models.MemberInterface;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.MemberInterface.Interfaces;

namespace QCash.Service.Services.MemberInterface;

public class MemberInterfaceLanguageService(ILanguageService languageService, IEfPocoService efPocoService) : IMemberInterfaceLanguageService
{
    public string RecordName { get; } = "Language Setting";

    public async Task<GenericActionResult> UpdateLanguageSupportStatusAsync(
        Guid financialInstitutionId,
        LanguageSupportStatusDto languageSupportStatusDto)
    {
        var languageDto = await languageService.GetLanguageByIdAsync(languageSupportStatusDto.LanguageId);

        if (languageDto == null)
        {
            return new GenericActionResult
            {
                IsSuccessful = false,
                ErrorMessage = "Language record not found",
            };
        }

        if (string.IsNullOrWhiteSpace(languageDto.LanguageCode))
        {
            return new GenericActionResult
            {
                IsSuccessful = false,
                ErrorMessage = "Updating English is not allowed - English is always supported by default",
            };
        }

        return await efPocoService.CreateOrUpdateAsync(languageSupportStatusDto.Id, financialInstitutionId,
            languageSupportStatusDto.TimeStamp,
            new PerformCreateOrUpdateOptions<LanguageSupportStatus>()
            {
                ExecuteRecordChangesFunc = (performUpdateParam) =>
                {
                    if (performUpdateParam.CreatingNewRecord)
                    {
                        performUpdateParam.Record.FinancialInstitutionId = financialInstitutionId;
                        performUpdateParam.Record.LanguageId = languageSupportStatusDto.LanguageId;
                    }

                    performUpdateParam.Record.IsActive = languageSupportStatusDto.IsActive;
                },
            });
    }
}
