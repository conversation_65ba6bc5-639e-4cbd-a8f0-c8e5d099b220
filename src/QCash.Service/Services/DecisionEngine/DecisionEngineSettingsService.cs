using System.Reflection;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Data.Models.Interfaces;
using QCash.LoanApplication;
using QCash.LoanApplication.Interfaces;
using QCash.Service.Models;
using QCash.Service.Models.Common;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Utilities.Extensions;
#pragma warning disable CS8524 // The switch expression does not handle some values of its input type (it is not exhaustive) involving an unnamed enum value.

namespace QCash.Service.Services.DecisionEngine;

// ReSharper disable once ClassWithVirtualMembersNeverInherited.Global
public class DecisionEngineSettingsService(
    QCashContext context,
    IEfPocoService efPocoService,
    IScoreTypeService scoreTypeService,
    IDecisionEngineParametersProvider decisionEngineParametersProvider
) : IDecisionEngineSettingsService
{
    public virtual Enums.DecisionEngineLookupTypeEnum GetLookupTypeEnum(string lookupTypeName)
    {
        var enumValues = Enum.GetValues(typeof(Enums.DecisionEngineLookupTypeEnum)).Cast<Enums.DecisionEngineLookupTypeEnum>();
        var lookup = enumValues.SingleOrDefault(a => $"{a}" == lookupTypeName);
        return lookup;
    }

    public virtual Type GetLookupDbObjectType(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum)
    {
        // A few lookup types are stored in the BusinessRule table, linked to a score type.
        var scoreType = GetScoreTypeFromLookupType(lookupTypeEnum);
        if (scoreType != null && scoreType != Enums.ScoreTypesEnum.Unknown)
        {
            return typeof(BusinessRule);
        }

        // Most lookup types are stored in their own table.
        var assembly = Assembly.GetAssembly(typeof(QCashContext));
        assembly.ThrowIfNull();
        Type? resultType = assembly.ExportedTypes.FirstOrDefault(a => a.Name == lookupTypeEnum.ToString());
        resultType.ThrowIfNull($"Cannot identify the object type for lookup {lookupTypeEnum}");
        return resultType;
    }

    public bool GetSupportsForeignLanguageStatus(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum)
    {
        if (lookupTypeEnum == Enums.DecisionEngineLookupTypeEnum.Unknown)
        {
            throw new Exception($"Cannot determine GetSupportsLanguageStatus for {lookupTypeEnum} ");
        }

        if (lookupTypeEnum == Enums.DecisionEngineLookupTypeEnum.Weekday
            || lookupTypeEnum == Enums.DecisionEngineLookupTypeEnum.PurposeOfLoanType)
        {
            return true;
        }

        return false;
    }

    private async Task<IQueryable<IEFPoco>> GetQueryAsync(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum)
    {
        var q = await ActOnTypedLookupAsync(lookupTypeEnum, TypedActionEnum.GetQuery) as IQueryable<IEFPoco>;
        q.ThrowIfNull();
        return q;
    }

    public async Task<IQueryable<IEFPocoLookup>> GetQueryForLookupAsync(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum) =>
        (await GetQueryAsync(lookupTypeEnum))
            .Cast<IEFPocoLookup>();

    public string GetNameHeader(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum) =>
        lookupTypeEnum switch
        {
            Enums.DecisionEngineLookupTypeEnum.ExclusionLoan
                or Enums.DecisionEngineLookupTypeEnum.AccountWarningCode
                or Enums.DecisionEngineLookupTypeEnum.MemberWarningCodeType
                or Enums.DecisionEngineLookupTypeEnum.ExclusionBaseAccountTypeLoanInitiate
                or Enums.DecisionEngineLookupTypeEnum.ExclusionBaseAccountTypeDecisionEngine
                or Enums.DecisionEngineLookupTypeEnum.ExclusionSubAccountTypeLoanInitiate
                or Enums.DecisionEngineLookupTypeEnum.ExclusionSubAccountTypeDecisionEngine
                or Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeLoanInitiate
                or Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeRelationship
                or Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeDecisionEngine
                or Enums.DecisionEngineLookupTypeEnum.MemberControlCode
                or Enums.DecisionEngineLookupTypeEnum.DirectDepositActionCode
                or Enums.DecisionEngineLookupTypeEnum.LoanRestrictiveCodeLoanInitiate
                or Enums.DecisionEngineLookupTypeEnum.LoanRestrictiveCodeDecisionEngine
                or Enums.DecisionEngineLookupTypeEnum.ShareRestrictiveCodeLoanInitiate
                or Enums.DecisionEngineLookupTypeEnum.ShareRestrictiveCodeDecisionEngine
                or Enums.DecisionEngineLookupTypeEnum.DirectDepositSourceType
                or Enums.DecisionEngineLookupTypeEnum.ExclusionLoanTypeFphc
                or Enums.DecisionEngineLookupTypeEnum.ClosedAccountCode
                or Enums.DecisionEngineLookupTypeEnum.InactiveAccountCode
                or Enums.DecisionEngineLookupTypeEnum.LoanCode
                or Enums.DecisionEngineLookupTypeEnum.ExcludedAccountCode
                or Enums.DecisionEngineLookupTypeEnum.LoanCodeForJointOwner
                or Enums.DecisionEngineLookupTypeEnum.ExclusionLoanPurposeCode => "Code",
            Enums.DecisionEngineLookupTypeEnum.ExclusionTransactionDescription
                or Enums.DecisionEngineLookupTypeEnum.GeneralCodeMapping
                or Enums.DecisionEngineLookupTypeEnum.AccountDescriptionMapping
                or Enums.DecisionEngineLookupTypeEnum.TransferCodeFromMapping
                or Enums.DecisionEngineLookupTypeEnum.TransferCodeToMapping => "Value",
            _ => "Name",
        };

    public List<DecisionEngineLookupListType> GetLookups() // LookupLinkViewModel in QCash6
    {
        var links = new List<DecisionEngineLookupListType>
        {
            // ---------------  'Eligibility Attributes' section
            new()
            {
                LinkText = "Range Types",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ScoreType,
                CssClass = "score-type",
                Title = "Range Type lookup list",
                CreateTitle = "Range Type",
                ShowInUi = false,
            },
            new()
            {
                LinkText = "Loan Status Types",
                LookupType = Enums.DecisionEngineLookupTypeEnum.LoanStatusType,
                CssClass = "loan-status-type",
                Title = "Loan Status Type lookup list",
                CreateTitle = "Loan Status Type",
                ShowInUi = false,
            },
            new()
            {
                LinkText = "Document Types",
                LookupType = Enums.DecisionEngineLookupTypeEnum.DocumentType,
                CssClass = "document-type",
                Title = "Document Type lookup list",
                CreateTitle = "Document Type",
                ShowInUi = false,
            },
            new()
            {
                LinkText = "Account Warning Codes",
                LookupType = Enums.DecisionEngineLookupTypeEnum.AccountWarningCode,
                CssClass = "account-warning-code",
                Title = "Account Warning Code lookup list",
                CreateTitle = "Account Warning Code",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.EligibilityAttributes,
                UIOrder = 4,
            },
            new()
            {
                LinkText = "Purpose of Loan Type",
                LookupType = Enums.DecisionEngineLookupTypeEnum.PurposeOfLoanType,
                CssClass = "purpose-of-loan-type",
                Title = "Purpose of Loan Type lookup list",
                CreateTitle = "Purpose of Loan Type",
                ShowInUi = false,
            },
            new()
            {
                LinkText = "Exclusion Loan Purpose Codes",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ExclusionLoanPurposeCode,
                CssClass = "exclusion-loan-purpose-code",
                Title = "Exclusion Loan Purpose Code lookup list",
                CreateTitle = "Exclusion Loan Purpose Code",
                ListHelpTextId = HelpTextIdentifier.ExclusionLoanPurposeCodeLookupListHelp,
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.EligibilityAttributes,
                UIOrder = 2,
            },
            new()
            {
                LinkText = "Exclusion Base Account Types Loan Initiate",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ExclusionBaseAccountTypeLoanInitiate,
                CssClass = "exclusion-base-account-type-loan-initiate",
                Title = "Exclusion Base Account Type Loan Initiate",
                CreateTitle = "Exclusion Base Account Type Loan Initiate",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.EligibilityAttributes,
                UIOrder = 5,
            },
            new()
            {
                LinkText = "Exclusion Base Account Types Decision Engine",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ExclusionBaseAccountTypeDecisionEngine,
                CssClass = "exclusion-base-account-type-decision-engine",
                Title = "Exclusion Base Account Type Decision Engine",
                CreateTitle = "Exclusion Base Account Type Decision Engine",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.EligibilityAttributes,
                UIOrder = 7,
            },
            new()
            {
                LinkText = "Exclusion Sub Account Types Loan Initiate",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ExclusionSubAccountTypeLoanInitiate,
                CssClass = "exclusion-sub-account-type-loan-initiate",
                Title = "Exclusion Sub Account Type Loan Initiate",
                CreateTitle = "Exclusion Sub Account Type Loan Initiate",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.EligibilityAttributes,
                UIOrder = 6,
            },
            new()
            {
                LinkText = "Exclusion Sub Account Types Decision Engine",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ExclusionSubAccountTypeDecisionEngine,
                CssClass = "exclusion-sub-account-type-decision-engine",
                Title = "Exclusion Sub Account Type Decision Engine",
                CreateTitle = "Exclusion Sub Account Type Decision Engine",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.EligibilityAttributes,
                UIOrder = 8,
            },
            // ---------------  'Spectrum Common Lookups' section
            new()
            {
                LinkText = "Member Restriction Codes Loan Initiate",
                LookupType = Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeLoanInitiate,
                CssClass = "member-restriction-code-loan-initiate",
                Title = "Member Restriction Code Loan Initiate",
                CreateTitle = "Member Restriction Code Loan Initiate",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.SpectrumCommonLookups,
                UIOrder = 1,
            },
            new()
            {
                LinkText = "Member Restriction Codes Decision Engine",
                LookupType = Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeDecisionEngine,
                CssClass = "member-restriction-code-decision-engine",
                Title = "Member Restriction Code Decision Engine",
                CreateTitle = "Member Restriction Code Decision Engine",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.SpectrumCommonLookups,
                UIOrder = 2,
            },
            new()
            {
                LinkText = "Deposit Action Codes",
                LookupType = Enums.DecisionEngineLookupTypeEnum.DirectDepositActionCode,
                CssClass = "direct-deposit-action-code",
                Title = "Deposit Action Code Lookup List",
                CreateTitle = "Deposit Action Code",
                ListHelpTextId = HelpTextIdentifier.DepositActionCodeLookupListHelp,
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.AccountsAndTransactions,
                UIOrder = 0,
            },
            new()
            {
                LinkText = "Checking Account Types",
                LookupType = Enums.DecisionEngineLookupTypeEnum.CheckingAccountType,
                CssClass = "",
                Title = "Checking Account Types",
                CreateTitle = "Checking Account Type",
                ListHelpTextId = Guid.Empty,
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.AccountsAndTransactions,
                UIOrder = 2,
                HasIndividualEditUi = true,
                HasIndividualListUi = true,
            },
            new()
            {
                LinkText = "Saving Account Types",
                LookupType = Enums.DecisionEngineLookupTypeEnum.SavingAccountType,
                CssClass = "",
                Title = "Saving Account Types",
                CreateTitle = "Saving Account Type",
                ListHelpTextId = Guid.Empty,
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.AccountsAndTransactions,
                UIOrder = 3,
                HasIndividualEditUi = true,
                HasIndividualListUi = true,
            },
            new()
            {
                LinkText = "NSF Transaction Codes",
                LookupType = Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode,
                CssClass = "",
                Title = "NSF Transaction Code List",
                CreateTitle = "NSF Transaction Code",
                ListHelpTextId = Guid.Empty,
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.AccountsAndTransactions,
                UIOrder = 4,
                HasIndividualEditUi = true,
                HasIndividualListUi = true,
            },
            new()
            {
                LinkText = "Member Control Codes",
                LookupType = Enums.DecisionEngineLookupTypeEnum.MemberControlCode,
                CssClass = "member-control-codes",
                Title = "Member Control Codes",
                CreateTitle = "Member Control Codes",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.SpectrumCommonLookups,
                UIOrder = 3,
            },
            new()
            {
                LinkText = "Loan Restriction Codes Loan Initiate",
                LookupType = Enums.DecisionEngineLookupTypeEnum.LoanRestrictiveCodeLoanInitiate,
                CssClass = "loan-restrictive-code-loan-initiate",
                Title = "Loan Restriction Codes Loan Initiate",
                CreateTitle = "Loan Restriction Code Loan Initiate",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.SpectrumCommonLookups,
                UIOrder = 4,
                UIGroupDescription = "Loan Restriction Codes Loan Initiate",
            },
            new()
            {
                LinkText = "Loan Restriction Codes Decision Engine",
                LookupType = Enums.DecisionEngineLookupTypeEnum.LoanRestrictiveCodeDecisionEngine,
                CssClass = "loan-restrictive-code-decision-engine",
                Title = "Loan Restriction Codes Decision Engine",
                CreateTitle = "Loan Restriction Code Decision Engine",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.SpectrumCommonLookups,
                UIOrder = 5,
            },
            new()
            {
                LinkText = "Share Restriction Codes Loan Initiate",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ShareRestrictiveCodeLoanInitiate,
                CssClass = "share-restrictive-code-loan-initiate",
                Title = "Share Restriction Codes Loan Initiate",
                CreateTitle = "Share Restriction Code Loan Initiate",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.SpectrumCommonLookups,
                UIOrder = 6,
            },
            new()
            {
                LinkText = "Share Restriction Codes Decision Engine",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ShareRestrictiveCodeDecisionEngine,
                CssClass = "share-restrictive-code-decision-engine",
                Title = "Share Restriction Codes Decision Engine",
                CreateTitle = "Share Restriction Code Decision Engine",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.SpectrumCommonLookups,
                UIOrder = 7,
            },
            // ---------------  'Accounts and Transactions' section

            // Is this an unnecessary duplicate?
            // new()
            // {
            //     LinkText = "Deposit Action Code",
            //     LookupType = Enums.DecisionEngineLookupTypeEnum.DirectDepositActionCode,
            //     CssClass = "direct-deposit-action-code",
            //     Title = "Deposit Action Code Lookup List",
            //     CreateTitle = "Deposit Action Code",
            //     NameHeader = "Code",
            //     ShowInUi = true,
            //     UIGroupEnum = Enums.DecisionEngineUIGroupEnum.AccountsAndTransactions,
            //     UIOrder = 1,
            // },
            new()
            {
                LinkText = "Electronic Deposit Source Types",
                LookupType = Enums.DecisionEngineLookupTypeEnum.DirectDepositSourceType,
                CssClass = "direct-deposit-source-type",
                Title = "Electronic Deposit Source Type lookup list",
                CreateTitle = "Deposit Source Type",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.AccountsAndTransactions,
                UIOrder = 1,
            },
            new()
            {
                LinkText = "Exclusion Loans",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ExclusionLoan,
                CssClass = "exclusion-loans",
                Title = "Exclusion Loans lookup list",
                CreateTitle = "Exclusion Loans",
                ListHelpTextId = HelpTextIdentifier.ExclusionLoansLookupListHelp,
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.EligibilityAttributes,
                UIOrder = 1,
            },
            new()
            {
                LinkText = "Member Warning Codes",
                LookupType = Enums.DecisionEngineLookupTypeEnum.MemberWarningCodeType,
                CssClass = "member-warning-code",
                Title = "Member Warning Code lookup list",
                CreateTitle = "Member Warning Code",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.EligibilityAttributes,
                UIOrder = 3,
            },
            new()
            {
                LinkText = "Exclusion Loan Type For Payment History Calculation",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ExclusionLoanTypeFphc,
                CssClass = "exclusion-loan-type-fphc",
                Title = "Exclusion Loan Type For Payment History Calculation lookup list",
                CreateTitle = "Exclusion Loan Type For Payment History Calculation",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.EligibilityAttributes,
                UIOrder = 9,
            },
            new()
            {
                LinkText = "Exclusion Transaction Descriptions",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ExclusionTransactionDescription,
                CssClass = "exclusion-transaction-descriptions",
                Title = "Exclusion Transaction Descriptions lookup list",
                CreateTitle = "Exclusion Transaction Descriptions",
                NameHeader = "Value",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.EligibilityAttributes,
                UIOrder = 10,
            },
            new()
            {
                LinkText = "DE Tracking Record Data Mappings",
                LookupType = Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping,
                CssClass = "",
                Title = "DE Tracking Record Data Mappings",
                CreateTitle = "Create DE Tracking Record Data Mapping",
                NameHeader = "Value",
                ShowInUi = true,
                HasIndividualListUi = true,
                HasIndividualEditUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.EligibilityAttributes,
                UIOrder = 11,
            },
            // ---------------  'Other' section
            new()
            {
                LinkText = "Included No. Of Electronic Transactions Teller Ids",
                LookupType = Enums.DecisionEngineLookupTypeEnum.IncludedNumberOfElectronicTransactionsTellerId,
                CssClass = "",
                Title = "Included No. Of Electronic Transactions Teller Ids",
                CreateTitle = "Included No. Of Electronic Transactions Teller Id",
                ListHelpTextId = Guid.Empty,
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.Other,
                UIOrder = 1,
                HasIndividualEditUi = true,
                HasIndividualListUi = true,
            },
            new()
            {
                LinkText = "Deposits to be excluded for Aggregate Deposit Total",
                LookupType = Enums.DecisionEngineLookupTypeEnum.DepositsToBeExcludedForAggregateDepositTotal,
                CssClass = "",
                Title = "Deposits to be excluded for Aggregate Deposit Total",
                CreateTitle = "Deposits to be excluded for Aggregate Deposit Total",
                ListHelpTextId = Guid.Empty,
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.Other,
                UIOrder = 2,
                HasIndividualEditUi = true,
                HasIndividualListUi = true,
            },
            new()
            {
                LinkText = "Deposits to be included for Aggregate Deposit Total",
                LookupType = Enums.DecisionEngineLookupTypeEnum.DepositsToBeIncludedForAggregateDepositTotal,
                CssClass = "",
                Title = "Deposits to be included for Aggregate Deposit Total",
                CreateTitle = "Deposits to be included for Aggregate Deposit Total",
                ListHelpTextId = Guid.Empty,
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.Other,
                UIOrder = 3,
                HasIndividualEditUi = true,
                HasIndividualListUi = true,
            },
            new()
            {
                LinkText = "Voided Transactions",
                LookupType = Enums.DecisionEngineLookupTypeEnum.VoidedTransactions,
                CssClass = "",
                Title = "Voided Transactions",
                CreateTitle = "Voided Transactions",
                ListHelpTextId = Guid.Empty,
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.Other,
                UIOrder = 4,
                HasIndividualEditUi = true,
                HasIndividualListUi = true,
            },
            // ---------------  'CoreApiLookups' section
            new()
            {
                LinkText = "Closed Account Codes",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ClosedAccountCode,
                CssClass = "closed-account-code",
                Title = "Closed Account Codes lookup list",
                CreateTitle = "Closed Account Code",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.CoreApiLookups,
                UIOrder = 1,
            },
            new()
            {
                LinkText = "Inactive Account Codes",
                LookupType = Enums.DecisionEngineLookupTypeEnum.InactiveAccountCode,
                CssClass = "inactive-account-code",
                Title = "Inactive Account Codes lookup list",
                CreateTitle = "Inactive Account Code",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.CoreApiLookups,
                UIOrder = 2,
            },
            new()
            {
                LinkText = "Loan Codes",
                LookupType = Enums.DecisionEngineLookupTypeEnum.LoanCode,
                CssClass = "loan-code",
                Title = "Loan Codes lookup list",
                CreateTitle = "Loan Code",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.CoreApiLookups,
                UIOrder = 3,
            },
            new()
            {
                LinkText = "Excluded Account Codes",
                LookupType = Enums.DecisionEngineLookupTypeEnum.ExcludedAccountCode,
                CssClass = "excluded-account-code",
                Title = "Excluded Account Codes lookup list",
                CreateTitle = "Excluded Account Code",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.CoreApiLookups,
                UIOrder = 4,
            },
            new()
            {
                LinkText = "Loan Codes For Joint Owners",
                LookupType = Enums.DecisionEngineLookupTypeEnum.LoanCodeForJointOwner,
                CssClass = "loan-code-for-joint-owner",
                Title = "Loan Codes For Joint Owners lookup list",
                CreateTitle = "Loan Codes For Joint Owners",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.CoreApiLookups,
                UIOrder = 5,
            },
            new()
            {
                LinkText = "General Code Mappings",
                LookupType = Enums.DecisionEngineLookupTypeEnum.GeneralCodeMapping,
                CssClass = "general-code-mapping",
                Title = "General Code Mappings lookup list",
                CreateTitle = "General Code Mapping",
                NameHeader = "Value",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.CoreApiLookups,
                UIOrder = 6,
            },
            new()
            {
                LinkText = "Account Description Mappings",
                LookupType = Enums.DecisionEngineLookupTypeEnum.AccountDescriptionMapping,
                CssClass = "account-description-mapping",
                Title = "Account Description Mapping lookup list",
                CreateTitle = "Account Description Mapping",
                NameHeader = "Value",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.CoreApiLookups,
                UIOrder = 7,
            },
            new()
            {
                LinkText = "Transfer Code From Mappings",
                LookupType = Enums.DecisionEngineLookupTypeEnum.TransferCodeFromMapping,
                CssClass = "transfer-code-from-mapping",
                Title = "Transfer Code From Mapping lookup list",
                CreateTitle = "Transfer Code From Mapping",
                NameHeader = "Value",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.CoreApiLookups,
                UIOrder = 8,
            },
            new()
            {
                LinkText = "Transfer Code To Mappings",
                LookupType = Enums.DecisionEngineLookupTypeEnum.TransferCodeToMapping,
                CssClass = "transfer-code-to-mapping",
                Title = "Transfer Code To Mapping lookup list",
                CreateTitle = "Transfer Code To Mapping",
                NameHeader = "Value",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.CoreApiLookups,
                UIOrder = 9,
            },
            new()
            {
                LinkText = "Weekdays",
                LookupType = Enums.DecisionEngineLookupTypeEnum.Weekday,
                CssClass = "weekday",
                Title = "Weekday lookup list",
                CreateTitle = "Weekdays",
                ShowInUi = false,
            },
            // ---------------  'CUFX/MISER Lookups' section
            new()
            {
                LinkText = "Member Restriction Codes - Relationship Id",
                LookupType = Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeRelationship,
                CssClass = "member-restriction-code-relationship",
                Title = "Member Restriction Code Relationship",
                CreateTitle = "Member Restriction Code Relationship Id",
                NameHeader = "Code",
                ShowInUi = true,
                UIGroupEnum = Enums.DecisionEngineUIGroupEnum.CufxMiserLookups,
                UIOrder = 1,
            },
        };

        return links;
    }

    public DecisionEngineLookupListType GetLookup(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum)
    {
        var lookupTypes = GetLookups();
        var lookupType = lookupTypes.FirstOrDefault(a => a.LookupType == lookupTypeEnum);
        lookupType.ThrowIfNull();
        return lookupType;
    }

    public Enums.DecisionEngineLookupTypeEnum GetLookupTypeEnumFromScoreType(Enums.ScoreTypesEnum scoreTypeEnum) =>
        scoreTypeEnum switch
        {
            Enums.ScoreTypesEnum.Unknown =>
                Enums.DecisionEngineLookupTypeEnum.Unknown,
            Enums.ScoreTypesEnum.NSFTransactionCode =>
                Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode,
            Enums.ScoreTypesEnum.DepositsExcludedForAggregateDepositTotal =>
                Enums.DecisionEngineLookupTypeEnum.DepositsToBeExcludedForAggregateDepositTotal,
            Enums.ScoreTypesEnum.DepositsIncludedForAggregateDepositTotal =>
                Enums.DecisionEngineLookupTypeEnum.DepositsToBeIncludedForAggregateDepositTotal,
            Enums.ScoreTypesEnum.VoidedTransaction =>
                Enums.DecisionEngineLookupTypeEnum.VoidedTransactions,
        };

    public Enums.ScoreTypesEnum? GetScoreTypeFromLookupType(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum) =>
        lookupTypeEnum switch
        {
            Enums.DecisionEngineLookupTypeEnum.Unknown =>
                Enums.ScoreTypesEnum.Unknown,
            Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode =>
                Enums.ScoreTypesEnum.NSFTransactionCode,
            Enums.DecisionEngineLookupTypeEnum.DepositsToBeExcludedForAggregateDepositTotal =>
                Enums.ScoreTypesEnum.DepositsExcludedForAggregateDepositTotal,
            Enums.DecisionEngineLookupTypeEnum.DepositsToBeIncludedForAggregateDepositTotal =>
                Enums.ScoreTypesEnum.DepositsIncludedForAggregateDepositTotal,
            Enums.DecisionEngineLookupTypeEnum.VoidedTransactions =>
                Enums.ScoreTypesEnum.VoidedTransaction,
            _ => null,
        };

    public async Task<GenericActionResult> SaveAsync(Guid applicationId, BusinessRuleDto dto)
    {
        //var id = context.FinancialInstitutions.FirstOrDefault(o => o.Slug == dto.FiSlug)?.Id ?? Guid.Empty;

        var thisFi = context.FinancialInstitutions
            .Include(o => o.FinancialInstitutionConfigurations)
            .FirstOrDefault(o => o.Slug == dto.FiSlug);
        var id = thisFi?.FinancialInstitutionConfigurations.FirstOrDefault()?.DefaultProviderId ?? Guid.Empty;

        try
        {
            var filter = await decisionEngineParametersProvider.ParseProductTypeFilterExpressionAsync(id, dto.Detail, dto.ScoreTypeSlug);
            if (!filter.IsValid)
            {
                // This check should already have been done during UI Validation, but this secondary check is here just in case.
                // Review later if this is too expensive to do twice.
                return new GenericActionResult()
                {
                    IsSuccessful = false,
                    ErrorMessage = "Rule Detail did not pass validation. Please re-check your input.",
                };
            }
        }
        catch (ApiCoreProviderException ex)
        {
            return new GenericActionResult()
            {
                IsSuccessful = false,
                ErrorMessage = $"Unable to validate against Core/Middleware provider. Error returned: {ex.Response?.Fault?.ErrorMessage ?? ex.Message}",
            };
        }
        catch (Exception ex)
        {
            return new GenericActionResult()
            {
                IsSuccessful = false,
                ErrorMessage = $"Unable to validate against Core/Middleware provider. Error returned: {ex.Message}",
            };
        }

        var scoreTypeRecord = scoreTypeService.GetScoreTypeRecord(dto.ScoreTypeSlug);
        if (scoreTypeRecord == null)
        {
            return new GenericActionResult() { IsSuccessful = false, ErrorMessage = $"No such score type found: {dto.ScoreTypeSlug}", };
        }

        return await efPocoService.CreateOrUpdateAsync(dto.Id, applicationId, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<BusinessRule>()
            {
                ExecuteRecordChangesFunc = (performUpdateParam) =>
                {
                    performUpdateParam.Record.Name = dto.Name;
                    performUpdateParam.Record.Detail = dto.Detail;
                    performUpdateParam.Record.Score = dto.Score;
                    performUpdateParam.Record.ScoreTypeId = scoreTypeRecord.Id;
                },
            });
    }

    public bool ShouldHideScore(Enums.ScoreTypesEnum scoreType) =>
        scoreType is Enums.ScoreTypesEnum.DepositsExcludedForAggregateDepositTotal
            or Enums.ScoreTypesEnum.DepositsIncludedForAggregateDepositTotal
            or Enums.ScoreTypesEnum.VoidedTransaction
            or Enums.ScoreTypesEnum.NSFTransactionCode;

    private enum TypedActionEnum
    {
        // ReSharper disable once UnusedMember.Local
        Unknown = 0,
        GetQuery = 1,
        Delete = 3,
    }

    private async Task<object> ActOnTypedLookupTAsync<T>(TypedActionEnum actionEnum, params object[] args) where T : class, IEFPoco
    {
        switch (actionEnum)
        {
            case TypedActionEnum.Delete:
            {
                var deleteItemDto = args[0] as DeleteItemDto;
                deleteItemDto.ThrowIfNull();
                return await efPocoService.DeleteItemAsync<T>(deleteItemDto);
            }
            case TypedActionEnum.GetQuery:
            {
                var q = context.Set<T>();
                return await Task.FromResult(q);
            }
            case TypedActionEnum.Unknown:
            default:
                throw new Exception($"ActOnTypedItemTAsync called with an invalid action type: {actionEnum} ");
        }
    }

    private async Task<object> ActOnTypedLookupAsync(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum,
        TypedActionEnum actionEnum, params object[] args) =>
        lookupTypeEnum switch
        {
            Enums.DecisionEngineLookupTypeEnum.Unknown =>
                throw new Exception($"Unable to identify a lookup type for ActOnTypedLookupAsync: {lookupTypeEnum} "),
            Enums.DecisionEngineLookupTypeEnum.Weekday =>
                await ActOnTypedLookupTAsync<Weekday>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.DocumentType =>
                await ActOnTypedLookupTAsync<DocumentType>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ExclusionLoan =>
                await ActOnTypedLookupTAsync<ExclusionLoan>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.InvoicePlan =>
                await ActOnTypedLookupTAsync<InvoicePlan>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.LoanCode =>
                await ActOnTypedLookupTAsync<LoanCode>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ScoreType =>
                await ActOnTypedLookupTAsync<ScoreType>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.AccountDescriptionMapping =>
                await ActOnTypedLookupTAsync<AccountDescriptionMapping>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.AccountWarningCode =>
                await ActOnTypedLookupTAsync<AccountWarningCode>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.CheckingAccountType =>
                await ActOnTypedLookupTAsync<CheckingAccountType>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ClosedAccountCode =>
                await ActOnTypedLookupTAsync<ClosedAccountCode>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ExcludedAccountCode =>
                await ActOnTypedLookupTAsync<ExcludedAccountCode>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ExclusionTransactionDescription =>
                await ActOnTypedLookupTAsync<ExclusionTransactionDescription>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.GeneralCodeMapping =>
                await ActOnTypedLookupTAsync<GeneralCodeMapping>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.InactiveAccountCode =>
                await ActOnTypedLookupTAsync<InactiveAccountCode>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.LoanStatusType =>
                await ActOnTypedLookupTAsync<LoanStatusType>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.MemberControlCode =>
                await ActOnTypedLookupTAsync<MemberControlCode>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.SavingAccountType =>
                await ActOnTypedLookupTAsync<SavingAccountType>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.DirectDepositActionCode =>
                await ActOnTypedLookupTAsync<DirectDepositActionCode>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.DirectDepositSourceType =>
                await ActOnTypedLookupTAsync<DirectDepositSourceType>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ExclusionLoanPurposeCode =>
                await ActOnTypedLookupTAsync<ExclusionLoanPurposeCode>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ExclusionLoanTypeFphc =>
                await ActOnTypedLookupTAsync<ExclusionLoanTypeFphc>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeRelationship =>
                await ActOnTypedLookupTAsync<MemberRestrictionCodeRelationship>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.MemberWarningCodeType =>
                await ActOnTypedLookupTAsync<MemberWarningCodeType>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.PurposeOfLoanType =>
                await ActOnTypedLookupTAsync<PurposeOfLoanType>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.TransferCodeFromMapping =>
                await ActOnTypedLookupTAsync<TransferCodeFromMapping>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.TransferCodeToMapping =>
                await ActOnTypedLookupTAsync<TransferCodeToMapping>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.LoanCodeForJointOwner =>
                await ActOnTypedLookupTAsync<LoanCodeForJointOwner>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.LoanRestrictiveCodeDecisionEngine =>
                await ActOnTypedLookupTAsync<LoanRestrictiveCodeDecisionEngine>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.LoanRestrictiveCodeLoanInitiate =>
                await ActOnTypedLookupTAsync<LoanRestrictiveCodeLoanInitiate>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeDecisionEngine =>
                await ActOnTypedLookupTAsync<MemberRestrictionCodeDecisionEngine>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode =>
                await ActOnTypedLookupTAsync<BusinessRule>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ShareRestrictiveCodeDecisionEngine =>
                await ActOnTypedLookupTAsync<ShareRestrictiveCodeDecisionEngine>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ShareRestrictiveCodeLoanInitiate =>
                await ActOnTypedLookupTAsync<ShareRestrictiveCodeLoanInitiate>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping =>
                await ActOnTypedLookupTAsync<DecisionEngineTrackingRecordDataMapping>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ExclusionBaseAccountTypeDecisionEngine =>
                await ActOnTypedLookupTAsync<ExclusionBaseAccountTypeDecisionEngine>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ExclusionBaseAccountTypeLoanInitiate =>
                await ActOnTypedLookupTAsync<ExclusionBaseAccountTypeLoanInitiate>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ExclusionSubAccountTypeDecisionEngine =>
                await ActOnTypedLookupTAsync<ExclusionSubAccountTypeDecisionEngine>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.ExclusionSubAccountTypeLoanInitiate =>
                await ActOnTypedLookupTAsync<ExclusionSubAccountTypeLoanInitiate>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.IncludedNumberOfElectronicTransactionsTellerId =>
                await ActOnTypedLookupTAsync<IncludedNumberOfElectronicTransactionsTellerId>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.DepositsToBeExcludedForAggregateDepositTotal =>
                await ActOnTypedLookupTAsync<BusinessRule>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.DepositsToBeIncludedForAggregateDepositTotal =>
                await ActOnTypedLookupTAsync<BusinessRule>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.VoidedTransactions =>
                await ActOnTypedLookupTAsync<BusinessRule>(actionEnum, args),
            Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeLoanInitiate =>
                await ActOnTypedLookupTAsync<MemberRestrictionCodeLoanInitiate>(actionEnum, args),
        };

    public async Task<GenericActionResult> DeleteItemAsync(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum, DeleteItemDto dto) =>
        (GenericActionResult)(await ActOnTypedLookupAsync(lookupTypeEnum, TypedActionEnum.Delete, dto));

    public async Task ValidateModelAsync(BusinessRuleDto dto, ModelStateDictionary modelState)
    {
        var thisFi = context.FinancialInstitutions
            .Include(o => o.FinancialInstitutionConfigurations)
            .FirstOrDefault(o => o.Slug == dto.FiSlug);
        var id = thisFi?.FinancialInstitutionConfigurations.FirstOrDefault()?.DefaultProviderId ?? Guid.Empty;

        try
        {
            var filter = await decisionEngineParametersProvider.ParseProductTypeFilterExpressionAsync(id, dto.Detail, dto.ScoreTypeSlug);
            if (!filter.IsValid)
            {
                modelState.AddModelError(
                    nameof(BusinessRuleDto.Detail), "Rule Detail did not pass validation. Please re-check your input.");
            }
        }
        catch (ApiCoreProviderException ex)
        {
            modelState.AddModelError(
                nameof(BusinessRuleDto.Detail), $"Unable to validate against Core/Middleware provider. Error returned: {ex.Response?.Fault?.ErrorMessage ?? ex.Message}");
        }
        catch (Exception ex)
        {
            modelState.AddModelError(
                nameof(BusinessRuleDto.Detail), $"Unable to validate against Core/Middleware provider. Error returned: {ex.Message}");
        }

    }

    public async Task<BusinessRuleDto?> GetBusinessRuleAsync(Enums.ScoreTypesEnum scoreTypeEnum, Guid id)
    {
        var scoreTypeSlug = scoreTypeEnum.ToDescriptionString();
        var scoreTypeRecord = scoreTypeService.GetScoreTypeRecord(scoreTypeSlug);
        if (scoreTypeRecord == null)
        {
            return null;
        }

        var q = efPocoService.GetQuery<BusinessRule>(q =>
        {
            return q.Where(a => a.Id == id && a.ScoreTypeId == scoreTypeRecord.Id)
                    .AsNoTracking();
        });
        var data = await q
            .Select(a => new BusinessRuleDto
            {
                FiSlug = a.ScoreType.FinancialInstitution.Slug,
                Id = a.Id,
                Name = a.Name,
                Detail = a.Detail,
                Score = a.Score,
                ScoreTypeSlug = scoreTypeRecord.Slug,
                TimeStamp = a.TimeStamp,
            })
            .FirstOrDefaultAsync();
        return data;
    }

    /// <summary>
    /// DvLookup is a category of decision engine settings whose content is a Description and a Value property.
    /// </summary>
    /// <param name="lookupType"></param>
    /// <returns></returns>
    public bool IsDvLookup(Enums.DecisionEngineLookupTypeEnum lookupType) =>
        lookupType is Enums.DecisionEngineLookupTypeEnum.CheckingAccountType
            or Enums.DecisionEngineLookupTypeEnum.SavingAccountType
            or Enums.DecisionEngineLookupTypeEnum.IncludedNumberOfElectronicTransactionsTellerId;

    public string GetPageTitle(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum, bool creatingRecord)
    {
        var lookup = GetLookup(lookupTypeEnum);
        if (creatingRecord)
        {
            return $"Create a new {lookup.CreateTitle} lookup item";
        }
        return $"Edit {lookup.Title} lookup item";
    }

    public int? GetNameFieldMaxLength(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum) =>
        lookupTypeEnum switch
        {
            Enums.DecisionEngineLookupTypeEnum.Unknown or Enums.DecisionEngineLookupTypeEnum.ScoreType =>
                throw new Exception($"Unable to determine {nameof(GetNameFieldMaxLength)} for {lookupTypeEnum} lookup type"),
            Enums.DecisionEngineLookupTypeEnum.AccountDescriptionMapping =>
                efPocoService.GetFieldMaxLength<AccountDescriptionMapping>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.AccountWarningCode =>
                efPocoService.GetFieldMaxLength<AccountWarningCode>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.CheckingAccountType =>
                efPocoService.GetFieldMaxLength<CheckingAccountType>(x => x.Value),
            Enums.DecisionEngineLookupTypeEnum.ClosedAccountCode =>
                efPocoService.GetFieldMaxLength<ClosedAccountCode>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping =>
                efPocoService.GetFieldMaxLength<DecisionEngineTrackingRecordDataMapping>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.DepositsToBeExcludedForAggregateDepositTotal =>
                efPocoService.GetFieldMaxLength<BusinessRule>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.DepositsToBeIncludedForAggregateDepositTotal =>
                efPocoService.GetFieldMaxLength<BusinessRule>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.DirectDepositActionCode =>
                efPocoService.GetFieldMaxLength<DirectDepositActionCode>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.DirectDepositSourceType =>
                efPocoService.GetFieldMaxLength<DirectDepositSourceType>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.DocumentType =>
                efPocoService.GetFieldMaxLength<DocumentType>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.ExcludedAccountCode =>
                efPocoService.GetFieldMaxLength<ExcludedAccountCode>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.ExclusionBaseAccountTypeDecisionEngine =>
                efPocoService.GetFieldMaxLength<ExclusionBaseAccountTypeDecisionEngine>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.ExclusionBaseAccountTypeLoanInitiate =>
                efPocoService.GetFieldMaxLength<ExclusionBaseAccountTypeLoanInitiate>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.ExclusionLoan =>
                efPocoService.GetFieldMaxLength<ExclusionLoan>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.ExclusionLoanPurposeCode =>
                efPocoService.GetFieldMaxLength<ExclusionLoanPurposeCode>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.ExclusionLoanTypeFphc =>
                efPocoService.GetFieldMaxLength<ExclusionLoanTypeFphc>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.ExclusionSubAccountTypeDecisionEngine =>
                efPocoService.GetFieldMaxLength<ExclusionSubAccountTypeDecisionEngine>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.ExclusionSubAccountTypeLoanInitiate =>
                efPocoService.GetFieldMaxLength<ExclusionSubAccountTypeLoanInitiate>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.ExclusionTransactionDescription =>
                efPocoService.GetFieldMaxLength<ExclusionTransactionDescription>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.GeneralCodeMapping =>
                efPocoService.GetFieldMaxLength<GeneralCodeMapping>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.InactiveAccountCode =>
                efPocoService.GetFieldMaxLength<InactiveAccountCode>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.IncludedNumberOfElectronicTransactionsTellerId =>
                efPocoService.GetFieldMaxLength<IncludedNumberOfElectronicTransactionsTellerId>(x => x.Value),
            Enums.DecisionEngineLookupTypeEnum.InvoicePlan =>
                efPocoService.GetFieldMaxLength<InvoicePlan>(x => x.ResellerName),
            Enums.DecisionEngineLookupTypeEnum.LoanCode =>
                efPocoService.GetFieldMaxLength<LoanCode>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.LoanCodeForJointOwner =>
                efPocoService.GetFieldMaxLength<LoanCodeForJointOwner>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.LoanRestrictiveCodeDecisionEngine =>
                efPocoService.GetFieldMaxLength<LoanRestrictiveCodeDecisionEngine>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.LoanRestrictiveCodeLoanInitiate =>
                efPocoService.GetFieldMaxLength<LoanRestrictiveCodeLoanInitiate>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.LoanStatusType =>
                efPocoService.GetFieldMaxLength<LoanStatusType>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.MemberControlCode =>
                efPocoService.GetFieldMaxLength<MemberControlCode>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeDecisionEngine =>
                efPocoService.GetFieldMaxLength<MemberRestrictionCodeDecisionEngine>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeLoanInitiate =>
                efPocoService.GetFieldMaxLength<MemberRestrictionCodeLoanInitiate>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.MemberRestrictionCodeRelationship =>
                efPocoService.GetFieldMaxLength<MemberRestrictionCodeLoanInitiate>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.MemberWarningCodeType =>
                efPocoService.GetFieldMaxLength<MemberWarningCodeType>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.NSFTransactionCode =>
                efPocoService.GetFieldMaxLength<BusinessRule>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.PurposeOfLoanType =>
                efPocoService.GetFieldMaxLength<PurposeOfLoanType>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.SavingAccountType =>
                efPocoService.GetFieldMaxLength<SavingAccountType>(x => x.Value),
            Enums.DecisionEngineLookupTypeEnum.ShareRestrictiveCodeDecisionEngine =>
                efPocoService.GetFieldMaxLength<ShareRestrictiveCodeDecisionEngine>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.ShareRestrictiveCodeLoanInitiate =>
                efPocoService.GetFieldMaxLength<ShareRestrictiveCodeLoanInitiate>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.TransferCodeFromMapping =>
                efPocoService.GetFieldMaxLength<TransferCodeFromMapping>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.TransferCodeToMapping =>
                efPocoService.GetFieldMaxLength<TransferCodeToMapping>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.VoidedTransactions =>
                efPocoService.GetFieldMaxLength<BusinessRule>(x => x.Name),
            Enums.DecisionEngineLookupTypeEnum.Weekday =>
                efPocoService.GetFieldMaxLength<Weekday>(x => x.Name),
        };
}
