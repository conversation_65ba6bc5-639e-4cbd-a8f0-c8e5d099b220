using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;

namespace QCash.Service.Services.DecisionEngine;

public class CheckingAccountService(
    IEfPocoService efPocoService
    ) : ICheckingAccountService
{
    public async Task<GenericActionResult> SaveAsync(Guid financialInstitutionId, GenericDescriptionValueDto dto) =>
        await efPocoService.CreateOrUpdateAsync(dto.Id, financialInstitutionId, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<CheckingAccountType>()
            {
                ExecuteRecordChangesFunc = result =>
                {
                    result.Record.Value = dto.Value;
                    result.Record.Description = dto.Description ?? "";
                },
            });

    public async Task<GenericDescriptionValueDto?> GetItemAsync(Guid id)
    {
        var record = await efPocoService.GetItemAsync<CheckingAccountType>(id);
        if (record == null)
        {
            return null;
        }
        return new GenericDescriptionValueDto()
        {
            Id = record.Id,
            Description = record.Description,
            Value = record.Value,
            TimeStamp = record.TimeStamp,
            LookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.CheckingAccountType),
        };
    }

    public async Task<GenericActionResult> DeleteItemAsync(DeleteItemDto dto) =>
        await efPocoService.DeleteItemAsync<CheckingAccountType>(dto);
}
