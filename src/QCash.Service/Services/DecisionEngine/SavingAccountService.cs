using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;

namespace QCash.Service.Services.DecisionEngine;

public class SavingAccountService(
    IEfPocoService efPocoService
    ) : ISavingAccountService
{
    public async Task<GenericActionResult> SaveAsync(Guid financialInstitutionId, GenericDescriptionValueDto dto) =>
        await efPocoService.CreateOrUpdateAsync(dto.Id, financialInstitutionId, dto.TimeStamp,
            new PerformCreateOrUpdateOptions<SavingAccountType>()
            {
                ExecuteRecordChangesFunc = (performUpdateParam) =>
                {
                    performUpdateParam.Record.Value = dto.Value;
                    performUpdateParam.Record.Description = dto.Description ?? "";
                },
            });

    public async Task<GenericDescriptionValueDto?> GetItemAsync(Guid id)
    {
        var record = await efPocoService.GetItemAsync<SavingAccountType>(id);
        if (record == null)
        {
            return null;
        }
        return new GenericDescriptionValueDto()
        {
            Id = record.Id,
            Description = record.Description,
            Value = record.Value,
            TimeStamp = record.TimeStamp,
            LookupTypeName = Enums.DecisionEngineLookupTypeEnum.SavingAccountType.ToString(),
        };
    }

    public async Task<GenericActionResult> DeleteItemAsync(DeleteItemDto dto) =>
        await efPocoService.DeleteItemAsync<SavingAccountType>(dto);
}
