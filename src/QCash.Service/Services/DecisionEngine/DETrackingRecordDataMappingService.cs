using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Models;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Utilities;

namespace QCash.Service.Services.DecisionEngine;

public class DETrackingRecordDataMappingService(
    IEfPocoService efPocoService,
    IGuidExtensionService guidExtensionService,
    QCashContext context,
    ILookupItemService lookupItemService
    ) : IDETrackingRecordDataMappingService
{
    public string RecordName { get; } = "Decision Engine Tracking Record Data Mapping";
    private Enums.DecisionEngineLookupTypeEnum LookupTypeEnum { get; set; } = Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping;

    private async Task SetInitialDataAsync(DETrackingRecordDataMappingDto dto, DecisionEngineTrackingRecordDataMapping dbRecord, Guid financialInstitutionId)
    {
        await lookupItemService.SetInitialDataAsync(dto, dbRecord, LookupTypeEnum, financialInstitutionId);
        efPocoService.SetInitialDataForNewRecord(dbRecord);
        dbRecord.FinancialInstitutionId = financialInstitutionId;
        dbRecord.Slug = guidExtensionService.NewSequentialGuid().ToString();
    }

    public async Task<GenericActionResult<DecisionEngineTrackingRecordDataMapping>> SaveAsync(Guid financialInstitutionId, DETrackingRecordDataMappingDto dto)
    {
        DecisionEngineTrackingRecordDataMapping dbRecord;
        if (dto.Id == Guid.Empty && dto.Slug == ItemConstants.CreateItemTag)
        {
            // Creating a new record.
            dbRecord = new DecisionEngineTrackingRecordDataMapping();
            context.DecisionEngineTrackingRecordDataMappings.Add(dbRecord);

            await SetInitialDataAsync(dto, dbRecord, financialInstitutionId);
        }
        else
        {
            // Saving an existing record
            var dbRecordOrNull = await context.DecisionEngineTrackingRecordDataMappings.SingleOrDefaultAsync(a =>
                a.Id == dto.Id);
            if (dbRecordOrNull == null)
            {
                return new GenericActionResult<DecisionEngineTrackingRecordDataMapping>() { IsSuccessful = false, ErrorMessage = $"No such item found: {dto.Id}", Value = null, };
            }
            dbRecord = dbRecordOrNull;

            if (!dbRecord.TimeStamp.SequenceEqual(dto.TimeStamp))
            {
                return new GenericActionResult<DecisionEngineTrackingRecordDataMapping>() { IsSuccessful = false, ErrorMessage = ItemConstants.ConcurrencyErrorMessage, Value = dbRecord, };
            }
        }

        ApplyContentFromDtoToRecord(dbRecord, dto);

        return new GenericActionResult<DecisionEngineTrackingRecordDataMapping>() { IsSuccessful = true, Value = dbRecord, };
    }

    private void ApplyContentFromDtoToRecord(DecisionEngineTrackingRecordDataMapping dbRecord, DETrackingRecordDataMappingDto dto)
    {
        dbRecord.Name = dto.Name;
        dbRecord.Description = dto.Description;
        dbRecord.TrackingRecordLevel = dto.TrackingRecordLevel;
        dbRecord.TrackingRecordType = dto.TrackingRecordType;
        dbRecord.TrackingRecordField = dto.TrackingRecordField;
        dbRecord.TrackingRecordPullOption = dto.TrackingRecordPullOption;
    }

    public async Task<DETrackingRecordDataMappingDto?> GetItemAsync(Guid financialInstitutionId, string lookupItemSlug)
    {
        var data = await context.DecisionEngineTrackingRecordDataMappings
            .Where(a => a.Slug == lookupItemSlug)
            .Where(a => a.FinancialInstitutionId == financialInstitutionId)
            .Select(a => new DETrackingRecordDataMappingDto()
            {
                Id = a.Id,
                Name = a.Name,
                Description = a.Description,
                FinancialInstitutionId = a.FinancialInstitutionId,
                TrackingRecordPullOption = a.TrackingRecordPullOption,
                TrackingRecordLevel = a.TrackingRecordLevel,
                Slug = a.Slug,
                Abrv = a.Abrv,
                AppAbrv = a.AppAbrv,
                TimeStamp = a.TimeStamp,
                TrackingRecordField = a.TrackingRecordField,
                TrackingRecordType = a.TrackingRecordType,
                DateUpdatedUtc = a.DateUpdatedUtc,
                IsDeleted = a.IsDeleted,
                DateCreatedUtc = a.DateCreatedUtc,
                TranslationDescription = null,
                TranslationName = null,
                Code = "",
                FiSlug = a.FinancialInstitution.Slug,
                LookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping),
            })
        .FirstOrDefaultAsync();
        return data;
    }

    public async Task<GenericActionResult<DecisionEngineTrackingRecordDataMapping>> ResurrectAndOverwriteDeletedRecordAsync(
        DETrackingRecordDataMappingDto dto, DecisionEngineTrackingRecordDataMapping? editingRecord)
    {
        var record = await efPocoService.GetItemAsync<DecisionEngineTrackingRecordDataMapping>(id: null,
        q => q.Where(a => a.Name == dto.Name)
        );
        if (record == null || !record.IsDeleted)
        {
            // this does not satisfy the rules to resurrect.  abort.
            return new GenericActionResult<DecisionEngineTrackingRecordDataMapping>() { IsSuccessful = false, Value = null, };
        }

        record.IsDeleted = false;
        ApplyContentFromDtoToRecord(record, dto);

        if (editingRecord != null)
        {
            // Now that we are resurrecting the record with the matching Name, we need to delete the record that the user was looking at.
            // The result needs to be one non-deleted record with the requested name and data.
            // Note that this is only necessary if the previous record existed in the database.
            //   If the user was creating a new record, meaning it didn't exist in the database, then this step is not needed.
            editingRecord.IsDeleted = true;
        }

        return new GenericActionResult<DecisionEngineTrackingRecordDataMapping>() { IsSuccessful = true, Value = record, };
    }
}
