using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Extensions;
using QCash.Data.Models.Interfaces;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;
using PropertyInfoExtensions = QCash.Utils.Extensions.PropertyInfoExtensions;

namespace QCash.Service.Services.General;

public class LookupItemService(
    IDecisionEngineSettingsService decisionEngineSettingsService,
    QCashContext context,
    IGuidExtensionService guidExtensionService,
    IEfPocoService efPocoService
    ) : ILookupItemService
{
    public string RecordName { get; } = "Lookup item";
    private async Task<IEFPocoLookup?> GetDbRecordBySlugAsync(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum, string slug)
    {
        var lookupQuery = (await decisionEngineSettingsService
            .GetQueryForLookupAsync(lookupTypeEnum))
            .AsNoTracking();
        var dbRecord = await lookupQuery.SingleOrDefaultAsync(a =>
            a.Slug == slug);
        return dbRecord;
    }

    private async Task<IEFPocoLookup?> GetDbRecordByAbrvAsync(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum, string abrv)
    {
        var lookupQueryBase = (await decisionEngineSettingsService
            .GetQueryForLookupAsync(lookupTypeEnum))
            .AsNoTracking();
        var dbRecord = await lookupQueryBase
            .SingleOrDefaultAsync(a => a.Abrv == abrv);
        return dbRecord;
    }

    public async Task<EFPocoLookupDto?> GetItemAsync(string fiSlug, string lookupTypeName, string lookupItemSlug)
    {
        var lookupTypeEnum = decisionEngineSettingsService.GetLookupTypeEnum(lookupTypeName);
        var dbRecord = await GetDbRecordBySlugAsync(lookupTypeEnum, lookupItemSlug);
        if (dbRecord == null)
        {
            return null;
        }
        var dto = EFPocoLookupDto.FromDbRecord(dbRecord, lookupTypeName);
        PopulateForeignLanguageData(dto, lookupTypeEnum);
        dto.FiSlug = fiSlug;

        return dto;
    }

    public virtual EFPocoLookupDto CreateBlankItem(string fiSlug, string lookupTypeName, string lookupItemSlug)
    {
        var dto = new EFPocoLookupDto()
        {
            Id = Guid.Empty,
            Slug = lookupItemSlug,
            FiSlug = fiSlug,
            Name = string.Empty,
            Description = string.Empty,
            Code = string.Empty,
            AppAbrv = "",
            TimeStamp = [],
            LookupTypeName = lookupTypeName,
        };
        return dto;
    }

    private IQueryable<IEFPoco>? GetForeignLanguageRecordQuery(EFPocoLookupDto pocoDto, Enums.DecisionEngineLookupTypeEnum lookupTypeEnum)
    {
        if (lookupTypeEnum == Enums.DecisionEngineLookupTypeEnum.PurposeOfLoanType)
        {
            var q = context.TranslationPurposeOfLoanTypes
                .Where(a =>
                    a.PurposeOfLoanTypeId == pocoDto.Id);
            return q;
        }
        if (lookupTypeEnum == Enums.DecisionEngineLookupTypeEnum.Weekday)
        {
            var q = context.TranslationWeekdays
                .Where(a =>
                    a.WeekdayId == pocoDto.Id);
            return q;
        }
        return null;
    }

    private void PopulateForeignLanguageData(EFPocoLookupDto pocoDto, Enums.DecisionEngineLookupTypeEnum lookupTypeEnum)
    {
        var q = GetForeignLanguageRecordQuery(pocoDto, lookupTypeEnum);
        var record = q?.AsNoTracking()
            .FirstOrDefault();
        if (record != null)
        {
            pocoDto.TranslationName = (record as IEFPocoWithName)?.Name;
            pocoDto.TranslationDescription = (record as IEFPocoWithDescription)?.Description;
        }
    }

    private void SaveForeignLanguageData(EFPocoLookupDto pocoDto, Enums.DecisionEngineLookupTypeEnum lookupTypeEnum)
    {
        var q = GetForeignLanguageRecordQuery(pocoDto, lookupTypeEnum);
        var record = q?.AsTracking()
            .FirstOrDefault();
        if (record is IEFPocoWithName nameRecord)
        {
            nameRecord.Name = pocoDto.TranslationName ?? string.Empty;
        }
        if (record is IEFPocoWithDescription descRecord)
        {
            descRecord.Description = pocoDto.TranslationDescription ?? string.Empty;
        }
    }

    public async Task<GetOrCreateRecordResult> SaveAsync(Guid applicationId, string lookupTypeName, EFPocoLookupDto dto)
    {
        var lookupTypeEnum = decisionEngineSettingsService.GetLookupTypeEnum(lookupTypeName);
        var dbObjType = decisionEngineSettingsService.GetLookupDbObjectType(lookupTypeEnum);
        return await efPocoService.CreateOrUpdateAsync(dbObjType, dto.Id, applicationId, dto.TimeStamp,
            new PerformCreateOrUpdateOptionsAsync()
            {
                ExecuteRecordChangesFunc = async getResult =>
            {
                var lookupRecord = (getResult.Record as IEFPocoLookup)!;
                if (getResult.CreatingNewRecord)
                {
                    await SetInitialDataAsync(dto, lookupRecord, lookupTypeEnum, applicationId);
                }

                ApplyContentFromDtoToRecord(lookupRecord, dto);
                SaveForeignLanguageData(dto, lookupTypeEnum);
            },
            });
    }

    public async Task<GenericActionResult<IEFPocoLookup>> ResurrectAndOverwriteDeletedRecordAsync(EFPocoLookupDto dto,
        IEFPocoLookup? editingRecord)
    {
        var lookupTypeEnum = decisionEngineSettingsService.GetLookupTypeEnum(dto.LookupTypeName);
        var dbObjType = decisionEngineSettingsService.GetLookupDbObjectType(lookupTypeEnum);
        var record = await efPocoService.GetItemAsync(dbObjType, (Guid?)null,
            q => q.Where(a => ((IEFPocoLookup)a).Name == dto.Name)
            ) as IEFPocoLookup;
        if (record == null || !record.IsDeleted)
        {
            // this does not satisfy the rules to resurrect.  abort.
            return new GenericActionResult<IEFPocoLookup>() { IsSuccessful = false, Value = null, };
        }

        record.IsDeleted = false;
        ApplyContentFromDtoToRecord(record, dto);

        if (editingRecord != null)
        {
            // Now that we are resurrecting the record with the matching Name, we need to delete the record that the user was looking at.
            // The result needs to be one non-deleted record with the requested name and data.
            // Note that this is only necessary if the previous record existed in the database.
            //   If the user was creating a new record, meaning it didn't exist in the database, then this step is not needed.
            editingRecord.IsDeleted = true;
        }

        return new GenericActionResult<IEFPocoLookup>() { IsSuccessful = true, Value = record, };
    }

    private void ApplyContentFromDtoToRecord(IEFPocoLookup record, EFPocoLookupDto dto)
    {
        record.Name = dto.Name;
        if (record is IEFPocoWithDescription dbRecordWDesc)
        {
            dbRecordWDesc.Description = dto.Description;
        }
    }

    public async Task SetInitialDataAsync(EFPocoLookupDto dto, IEFPocoLookup? dbRecord, Enums.DecisionEngineLookupTypeEnum lookupTypeEnum, Guid applicationId)
    {
        if (dbRecord == null)
        {
            return;
        }
        dbRecord.Slug = guidExtensionService.NewSequentialGuid().ToString();
        var abrv = await GetInitialAbrvAsync(dbRecord, lookupTypeEnum);
        dbRecord.Abrv = abrv;
        PropertyInfoExtensions.SetPropertyValue(dbRecord, "AppAbrv", abrv);
    }

    private async Task<string> GetInitialAbrvAsync(IEFPocoLookup dbRecord, Enums.DecisionEngineLookupTypeEnum lookupTypeEnum)
    {
        var nameLetters = Regex.Replace(dbRecord.Name ?? "", RegexStrings.LettersOnly, string.Empty, RegexOptions.Compiled,
            new TimeSpan(0, 0, 1)).ToUpper();
        var nameNumbers = Regex.Replace(dbRecord.Name ?? "", RegexStrings.NumbersOnly, string.Empty, RegexOptions.Compiled,
            new TimeSpan(0, 0, 1));

        // If updatedModel.Name contains no letters, use the lookup type and any numbers from updatedModel.Name
        // to create slug and abrv
        var lookupTypeName = lookupTypeEnum.ToString();
        string abrvBase;
        if (nameLetters == string.Empty)
        {
            // Only uppercase letters
            var lookupNameAbrv = Regex.Replace(lookupTypeName, RegexStrings.CapitalLettersOnly, string.Empty, RegexOptions.Compiled,
                new TimeSpan(0, 0, 1)).ToUpper();
            abrvBase = $"{lookupNameAbrv}{nameNumbers}";
        }
        else
        {
            // Create abrv based on the first letters of words in updatedModel.Name
            abrvBase = string.Join(string.Empty, nameLetters.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries).Select(s => s[0]));
        }
        var attemptNumber = 1;
        string abrvCurrentAttempt;
        IEFPocoLookup? testRecord;
        do
        {
            var attemptSuffix = (attemptNumber <= 1) ? "" : attemptNumber.ToString();
            abrvCurrentAttempt = $"{abrvBase}{attemptSuffix}";
            testRecord = await GetDbRecordByAbrvAsync(lookupTypeEnum, abrvCurrentAttempt);
            attemptNumber++;
        } while (testRecord != null);

        return abrvCurrentAttempt;
    }

    public bool ShouldShowTranslationName(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum, LanguageDto? modelForeignLanguage)
    {
        if (modelForeignLanguage == null)
        {
            return false;
        }

        if (lookupTypeEnum == Enums.DecisionEngineLookupTypeEnum.PurposeOfLoanType
            || lookupTypeEnum == Enums.DecisionEngineLookupTypeEnum.Weekday)
        {
            return true;
        }

        return false;
    }

    public bool ShouldShowTranslationDescription(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum, LanguageDto? modelForeignLanguage)
    {
        if (modelForeignLanguage == null)
        {
            return false;
        }

        if (lookupTypeEnum == Enums.DecisionEngineLookupTypeEnum.Weekday)
        {
            return true;
        }

        return false;
    }

}
