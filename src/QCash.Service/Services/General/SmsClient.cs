using QCash.Service.Services.General.Interfaces;
using Twilio.Rest.Api.V2010.Account;

namespace QCash.Service.Services.General;

public class SmsClient : ISmsClient
{
    /// <summary>
    /// Initiates the SMSSender.
    /// </summary>
    /// <param name="accountSId">The accountSId.</param>
    /// <param name="token">The token.</param>
    public void Init(string accountSId, string token)
    {
        if (string.IsNullOrEmpty(accountSId) || string.IsNullOrEmpty(token))
        {
            return;
        }

        Twilio.TwilioClient.Init(accountSId, token);
    }

    /// <inheritdoc cref="TrySendSms"/>
    public bool TrySendSms(string from, string to, string body, out string errorMessage)
    {
        var message = MessageResource.Create(
            body: body,
            from: new Twilio.Types.PhoneNumber(from),
            to: new Twilio.Types.PhoneNumber(to));

        errorMessage = message.ErrorMessage;

        return !message.ErrorCode.HasValue;
    }
}