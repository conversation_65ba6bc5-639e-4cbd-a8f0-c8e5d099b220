namespace QCash.Service.Services.General.Interfaces;

//was ITwilioClient in QCash6
public interface ISmsClient
{
    /// <summary>
    /// Initiates the SMSSender.
    /// </summary>
    /// <param name="accountSId">The accountSId.</param>
    /// <param name="token">The token.</param>
    void Init(string accountSId, string token);

    /// <summary>
    /// Send the SMS message.
    /// </summary>
    /// <param name="from">The from number.</param>
    /// <param name="to">The to number.</param>
    /// <param name="body">The message body.</param>
    /// <param name="errorMessage">Contains error description in case of failing.</param>
    /// <returns>True if message sent.</returns>
    bool TrySendSms(string from, string to, string body, out string errorMessage);
}