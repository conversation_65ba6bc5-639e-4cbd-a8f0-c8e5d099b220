using System.Linq.Expressions;
using QCash.Data.Models.Interfaces;
using QCash.Service.Models;
using QCash.Service.Models.General;

namespace QCash.Service.Services.General.Interfaces;

public interface IEfPocoService
{
    public void SetInitialDataForNewRecord(IEFPoco data, Guid? financialInstitutionId = null);
    public PrepRecordResult PrepRecordForSaveAfterChangesMade(IEFPoco record, byte[] editingTimestamp);
    public T Add<T>(T obj, Guid? financialInstitutionId = null) where T : class, IEFPoco;
    public IQueryable<T> GetQuery<T>(Func<IQueryable<T>, IQueryable<T>>? queryableCustomization = null) where T : class, IEFPoco;
    public Task<T?> GetItemAsync<T>(Func<IQueryable<T>, IQueryable<T>>? queryableCustomization = null) where T : class, IEFPoco;
    public Task<IEFPoco?> GetItemAsync(Type type, Guid? id, Func<IQueryable<IEFPoco>, IQueryable<IEFPoco>>? queryableCustomization = null);
    public Task<T?> GetItemAsync<T>(Guid? id, Func<IQueryable<T>, IQueryable<T>>? queryableCustomization = null) where T : class, IEFPoco;
    public Task<GetOrCreateRecordResult<T>> GetOrCreateRecordAsync<T>(Guid? id, Guid? financialInstitutionId) where T : class, IEFPoco;
    public Task<GetOrCreateRecordResult<T>> GetOrCreateRecordAsync<T>(Guid? id, Guid? financialInstitutionId,
        PerformCreateOrUpdateOptionsAsync<T> options) where T : class, IEFPoco;
    public Task<GetOrCreateRecordResult<T>> GetOrCreateRecordAsync<T>(Guid? id, Guid? financialInstitutionId,
        PerformCreateOrUpdateOptions<T> options) where T : class, IEFPoco;
    public Task<GenericActionResult> DeleteItemAsync<T>(DeleteItemDto dto) where T : class, IEFPoco;
    public Task<GenericActionResult> DeleteItemAsync(Type dbObjectType, DeleteItemDto dto);
    public Task<GetOrCreateRecordResult<T>> CreateOrUpdateAsync<T>(Guid id, Guid? financialInstitutionId, byte[] timeStamp,
        PerformCreateOrUpdateOptions<T>? options) where T : class, IEFPoco;
    public Task<GetOrCreateRecordResult<T>> CreateOrUpdateAsync<T>(Guid id, Guid? financialInstitutionId, byte[] timeStamp,
        PerformCreateOrUpdateOptionsAsync<T>? options) where T : class, IEFPoco;
    public Task<GetOrCreateRecordResult> CreateOrUpdateAsync(Type type, Guid id, Guid? financialInstitutionId, byte[] timeStamp,
        PerformCreateOrUpdateOptions? options);
    public Task<GetOrCreateRecordResult> CreateOrUpdateAsync(Type type, Guid id, Guid? financialInstitutionId, byte[] timeStamp,
        PerformCreateOrUpdateOptionsAsync? options);
    public GetOrCreateRecordResult<T> CreateOrUpdate<T>(T? dbRecord, Guid? financialInstitutionId, byte[] timeStamp, PerformCreateOrUpdateOptions<T>? options)
        where T : class, IEFPoco;
    GetOrCreateRecordResult AddRange<T>(IEnumerable<T>? newDbRecords) where T : class, IEFPoco;
    int? GetFieldMaxLength<TEntity>(Expression<Func<TEntity, object?>> propertyExpression) where TEntity : class;
}
