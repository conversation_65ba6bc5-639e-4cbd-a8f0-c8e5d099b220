using QCash.Data.Models.Interfaces;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;

namespace QCash.Service.Services.General.Interfaces;

public interface ILookupItemService
{
    public string RecordName { get; }
    public Task<EFPocoLookupDto?> GetItemAsync(string fiSlug, string lookupTypeName, string lookupItemSlug);
    public Task<GetOrCreateRecordResult> SaveAsync(Guid applicationId, string lookupTypeName, EFPocoLookupDto dto);
    public bool ShouldShowTranslationName(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum, LanguageDto? modelForeignLanguage);
    public bool ShouldShowTranslationDescription(Enums.DecisionEngineLookupTypeEnum lookupTypeEnum, LanguageDto? modelForeignLanguage);
    public Task SetInitialDataAsync(EFPocoLookupDto dto, IEFPocoLookup dbRecord, Enums.DecisionEngineLookupTypeEnum lookupTypeEnum, Guid applicationId);
    public EFPocoLookupDto CreateBlankItem(string fiSlug, string lookupTypeName, string lookupItemSlug);
    public Task<GenericActionResult<IEFPocoLookup>> ResurrectAndOverwriteDeletedRecordAsync(EFPocoLookupDto dto, IEFPocoLookup? record);
}
