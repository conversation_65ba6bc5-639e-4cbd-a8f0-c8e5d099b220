using System.Net;
using QCash.Service.Services.General.Interfaces;

namespace QCash.Service.Services.General;

public class IPAddressService : IIPAddressService
{
    public bool IsIPAddressOrRange(string? input) => ParseIP(input) != string.Empty;

    public string ParseIP(string? ip)
    {
        var result = string.Empty;
        ip = (ip ?? "").Replace(" ", string.Empty);
        if (ip.Contains('-'))
        {
            var ips = ip.Split('-');
            if (ips.Length == 2
                && IPAddress.TryParse(ips[0], out var ip1)
                && IPAddress.TryParse(ips[1], out var ip2))
            {
                result = $"{ip1}-{ip2}";
            }
        }
        else if (IPAddress.TryParse(ip, out var ip0))
        {
            result = ip0.ToString();
        }
        return result;
    }
}
