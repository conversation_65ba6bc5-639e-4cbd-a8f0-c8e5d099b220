using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Extensions;
using QCash.Data.Models.Interfaces;
using QCash.Service.Models;
using QCash.Service.Models.General;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Utilities;
using QCash.Service.Utilities.Extensions;
using QCash.Utils.Core;

namespace QCash.Service.Services.General;

public class EfPocoService(
    IGuidExtensionService guidExtensionService,
    ISystemClockService systemClockService,
    QCashContext context
    ) : IEfPocoService
{
    private T CreateRecord<T>(Guid? financialInstitutionId) where T : class, IEFPoco
    {
        var record = Activator.CreateInstance<T>();
        record.ThrowIfNull();
        return Add(record, financialInstitutionId);
    }

    private IEFPoco CreateRecord(Type type, Guid? financialInstitutionId)
    {
        var record = Activator.CreateInstance(type);
        record.ThrowIfNull();
        return Add((IEFPoco)record, financialInstitutionId);
    }

    public T Add<T>(T obj, Guid? financialInstitutionId = null) where T : class, IEFPoco
    {
        context.Add(obj);
        SetInitialDataForNewRecord(obj, financialInstitutionId);
        return obj;
    }

    public virtual GetOrCreateRecordResult AddRange<T>(IEnumerable<T>? newDbRecords) where T : class, IEFPoco
    {
        foreach (var item in newDbRecords ?? [])
        {
            Add(item);
        }
        return new GetOrCreateRecordResult() { IsSuccessful = true, Record = null, CreatingNewRecord = false, EditingExistingRecord = false, FoundExistingRecord = false, };
    }

    public IQueryable<IEFPoco> GetQuery(Type type, Func<IQueryable<IEFPoco>, IQueryable<IEFPoco>>? queryableCustomization = null)
    {
        var q = context.Set(type);
        q.ThrowIfNull();
        if (queryableCustomization != null)
        {
            q = queryableCustomization(q);
        }

        return q;
    }

    public IQueryable<T> GetQuery<T>(Func<IQueryable<T>, IQueryable<T>>? queryableCustomization = null) where T : class, IEFPoco
    {
        var q = context.Set<T>().AsQueryable();
        if (queryableCustomization != null)
        {
            q = queryableCustomization(q);
        }

        return q;
    }

    public async Task<T?> GetItemAsync<T>(Func<IQueryable<T>, IQueryable<T>>? queryableCustomization = null) where T : class, IEFPoco
    {
        var q = GetQuery(queryableCustomization);
        var dbRecord = await q.FirstOrDefaultAsync();
        return dbRecord;
    }

    public async Task<T?> GetItemAsync<T>(Guid? id, Func<IQueryable<T>, IQueryable<T>>? queryableCustomization = null) where T : class, IEFPoco
    {
        var q = GetQuery(queryableCustomization);
        if (id != null)
        {
            q = q.Where(a => a.Id == id);
        }
        var dbRecord = await q.SingleOrDefaultAsync();
        return dbRecord;
    }

    public async Task<IEFPoco?> GetItemAsync(Type type, Guid? id, Func<IQueryable<IEFPoco>, IQueryable<IEFPoco>>? queryableCustomization = null)
    {
        var q = GetQuery(type, queryableCustomization);
        if (id.HasValue)
        {
            q = q.Where(a => a.Id == id);
        }
        var dbRecord = await q.SingleOrDefaultAsync();
        return dbRecord;
    }

    /// <summary>
    /// This method will first check to see if it can find the requested record in the database based on type and id.
    /// If so, it will return that record.
    /// If not, it will create a new record, and return that.
    /// </summary>
    /// <param name="dbObjectType"></param>
    /// <param name="id">Primary key of the database table.  </param>
    /// <param name="financialInstitutionId">Optional parameter used to tell a newly created record what FI it belongs to.</param>
    /// <param name="queryableCustomization">Func to allow the caller to change the query used to find the record.
    /// For example, this can be used to Include() other tables, or to add additional filters.</param>
    /// <returns></returns>
    private async Task<GetOrCreateRecordResult> GetOrCreateRecordAsync(Type dbObjectType, Guid id, Guid? financialInstitutionId,
        Func<IQueryable<IEFPoco>, IQueryable<IEFPoco>>? queryableCustomization = null)
    {
        if (id == Guid.Empty)
        {
            // Creating a new record.
            var dbRecord = CreateRecord(dbObjectType, financialInstitutionId);
            return new GetOrCreateRecordResult { IsSuccessful = true, Record = dbRecord, CreatingNewRecord = true, EditingExistingRecord = false, FoundExistingRecord = false, };
        }

        // Find an existing record
        if (await GetItemAsync(dbObjectType, id, queryableCustomization) is not { } dbRecordIfExists)
        {
            // Failed to find the record.  return the error.
            return new GetOrCreateRecordResult { IsSuccessful = false, ErrorMessage = $"No such item found: {id}", CreatingNewRecord = false, Record = null, EditingExistingRecord = false, FoundExistingRecord = false, };
        }

        // Successfully found the existing record.
        return new GetOrCreateRecordResult { IsSuccessful = true, Record = dbRecordIfExists, CreatingNewRecord = false, EditingExistingRecord = false, FoundExistingRecord = true, };
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="id">
    /// - Value of Guid.Empty means we know we need to create a record.
    /// - Value of null means we do not know the ID, but we should be able to look it up based on relationships/possibly only a single record
    /// - Value other than null/Guid.Empty means we search for that key in the id field</param>
    /// <param name="financialInstitutionId"></param>
    /// <param name="options"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<GetOrCreateRecordResult<T>> GetOrCreateRecordAsync<T>(Guid? id, Guid? financialInstitutionId,
        PerformCreateOrUpdateOptionsAsync<T> options) where T : class, IEFPoco
    {
        if (id == Guid.Empty)
        {
            // Creating a new record.
            var dbRecord = CreateRecord<T>(financialInstitutionId);
            return new GetOrCreateRecordResult<T> { IsSuccessful = true, Record = dbRecord, CreatingNewRecord = true, EditingExistingRecord = false, FoundExistingRecord = false, };
        }

        // Find an existing record
        if (await GetItemAsync(id, options.QueryableCustomization) is not { } dbRecordIfExists)
        {
            // Failed to find the record. create or return error as appropriate
            if (id == null)
            {
                // Creating a new record.
                var dbRecord = CreateRecord<T>(financialInstitutionId);
                return new GetOrCreateRecordResult<T> { IsSuccessful = true, Record = dbRecord, CreatingNewRecord = true, EditingExistingRecord = false, FoundExistingRecord = false, };
            }
            return new GetOrCreateRecordResult<T> { IsSuccessful = false, ErrorMessage = $"No such item found: {id}", CreatingNewRecord = false, Record = null, EditingExistingRecord = false, FoundExistingRecord = false, };
        }

        // Successfully found the existing record.
        return new GetOrCreateRecordResult<T> { IsSuccessful = true, Record = dbRecordIfExists, CreatingNewRecord = false, EditingExistingRecord = true, FoundExistingRecord = true, };
    }

    public async Task<GetOrCreateRecordResult<T>> GetOrCreateRecordAsync<T>(Guid? id, Guid? financialInstitutionId) where T : class, IEFPoco
    {
        var param = new PerformCreateOrUpdateOptionsAsync<T>() { ExecuteRecordChangesFunc = null, QueryableCustomization = null, };
        return await GetOrCreateRecordAsync(id, financialInstitutionId, param);
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="id">
    /// - Value of Guid.Empty means we know we need to create a record.
    /// - Value of null means we do not know the ID, but we should be able to look it up based on relationships/possibly only a single record
    /// - Value other than null/Guid.Empty means we search for that key in the id field</param>
    /// <param name="financialInstitutionId"></param>
    /// <param name="options"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<GetOrCreateRecordResult<T>> GetOrCreateRecordAsync<T>(Guid? id, Guid? financialInstitutionId,
        PerformCreateOrUpdateOptions<T>? options) where T : class, IEFPoco
    {
        if (id == Guid.Empty)
        {
            // Creating a new record.
            var dbRecord = CreateRecord<T>(financialInstitutionId);
            options?.ExecuteRecordChangesFunc?.Invoke(new PerformUpdateParam<T>() { Record = dbRecord, CreatingNewRecord = true, IsSuccessful = true, });
            return new GetOrCreateRecordResult<T>
            {
                IsSuccessful = true,
                Record = dbRecord,
                CreatingNewRecord = true,
                EditingExistingRecord = false,
                FoundExistingRecord = false,
            };
        }

        // Find an existing record
        if (await GetItemAsync(id, options?.QueryableCustomization) is not { } dbRecordIfExists)
        {
            // Failed to find the record. create or return error as appropriate
            if (id == null)
            {
                // Creating a new record.
                var dbRecord = CreateRecord<T>(financialInstitutionId);
                options?.ExecuteRecordChangesFunc?.Invoke(new PerformUpdateParam<T>()
                {
                    Record = dbRecord,
                    CreatingNewRecord = true,
                    IsSuccessful = true,
                });
                return new GetOrCreateRecordResult<T>
                {
                    IsSuccessful = true,
                    Record = dbRecord,
                    CreatingNewRecord = true,
                    EditingExistingRecord = false,
                    FoundExistingRecord = false,
                };
            }
            return new GetOrCreateRecordResult<T>
            {
                IsSuccessful = false,
                ErrorMessage = $"No such item found: {id}",
                CreatingNewRecord = false,
                Record = null,
                EditingExistingRecord = false,
                FoundExistingRecord = false,
            };
        }

        // Successfully found the existing record.
        return new GetOrCreateRecordResult<T>
        {
            IsSuccessful = true,
            Record = dbRecordIfExists,
            CreatingNewRecord = false,
            EditingExistingRecord = true,
            FoundExistingRecord = true,
        };
    }

    /// <summary>
    ///
    /// </summary>
    /// <param name="id"></param>
    /// <param name="financialInstitutionId"></param>
    /// <param name="timeStamp">Used to check if other changes were made to this record while it was being edited.
    /// https://learn.microsoft.com/en-us/sql/t-sql/data-types/rowversion-transact-sql?view=sql-server-ver16</param>
    /// <param name="options"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public async Task<GetOrCreateRecordResult<T>> CreateOrUpdateAsync<T>(Guid id, Guid? financialInstitutionId, byte[] timeStamp,
        PerformCreateOrUpdateOptions<T>? options) where T : class, IEFPoco
    {
        var getOrCreateRecordResult = await GetOrCreateRecordAsync<T>(id, financialInstitutionId, options);
        if (!getOrCreateRecordResult.IsSuccessful || getOrCreateRecordResult.Record == null)
        {
            return getOrCreateRecordResult;
        }

        options?.ExecuteRecordChangesFunc?.Invoke(getOrCreateRecordResult.ToPerformUpdateParam());
        if (PrepRecordForSaveAfterChangesMade(getOrCreateRecordResult.Record, timeStamp) is { IsSuccessful: false } failedPrepResult)
        {
            return failedPrepResult.ToGetOrCreateRecordResult<T>(getOrCreateRecordResult.FoundExistingRecord, getOrCreateRecordResult.CreatingNewRecord, getOrCreateRecordResult.EditingExistingRecord);
        }
        return getOrCreateRecordResult;
    }

    public GetOrCreateRecordResult<T> CreateOrUpdate<T>(T? dbRecord, Guid? financialInstitutionId, byte[] timeStamp, PerformCreateOrUpdateOptions<T>? options) where T : class, IEFPoco
    {
        var foundExistingRecord = dbRecord != null;
        var creatingNewRecord = false;
        if (dbRecord == null)
        {
            // Creating a new record.
            creatingNewRecord = true;
            dbRecord = CreateRecord<T>(financialInstitutionId);
        }

        options?.ExecuteRecordChangesFunc?.Invoke(new PerformUpdateParam<T>()
        {
            Record = dbRecord,
            CreatingNewRecord = creatingNewRecord,
            IsSuccessful = true,
        });
        if (PrepRecordForSaveAfterChangesMade(dbRecord, dbRecord.TimeStamp) is { IsSuccessful: false } failedPrepResult)
        {
            return failedPrepResult.ToGetOrCreateRecordResult<T>(foundExistingRecord, creatingNewRecord, foundExistingRecord);
        }
        return new GetOrCreateRecordResult<T> { IsSuccessful = true, Record = dbRecord, CreatingNewRecord = creatingNewRecord, EditingExistingRecord = foundExistingRecord, FoundExistingRecord = foundExistingRecord, };
    }

    public async Task<GetOrCreateRecordResult<T>> CreateOrUpdateAsync<T>(Guid id, Guid? financialInstitutionId, byte[] timeStamp,
        PerformCreateOrUpdateOptionsAsync<T>? options) where T : class, IEFPoco
    {
        var getOrCreateRecordResult = await GetOrCreateRecordAsync(id, financialInstitutionId, options ?? new PerformCreateOrUpdateOptionsAsync<T>());
        if (!getOrCreateRecordResult.IsSuccessful || getOrCreateRecordResult.Record == null)
        {
            return getOrCreateRecordResult;
        }

        if (options?.ExecuteRecordChangesFunc != null)
        {
            await options.ExecuteRecordChangesFunc(getOrCreateRecordResult.ToPerformUpdateParam());
        }
        if (PrepRecordForSaveAfterChangesMade(getOrCreateRecordResult.Record, timeStamp) is { IsSuccessful: false } failedPrepResult)
        {
            return failedPrepResult.ToGetOrCreateRecordResult<T>(getOrCreateRecordResult.FoundExistingRecord, getOrCreateRecordResult.CreatingNewRecord, getOrCreateRecordResult.EditingExistingRecord);
        }
        return getOrCreateRecordResult;
    }

    public async Task<GetOrCreateRecordResult> CreateOrUpdateAsync(Type type, Guid id, Guid? financialInstitutionId, byte[] timeStamp,
        PerformCreateOrUpdateOptions? options)
    {
        var getOrCreateRecordResult = await GetOrCreateRecordAsync(type, id, financialInstitutionId, options?.QueryableCustomization);
        if (!getOrCreateRecordResult.IsSuccessful || getOrCreateRecordResult.Record == null)
        {
            return getOrCreateRecordResult;
        }

        options?.ExecuteRecordChangesFunc?.Invoke(getOrCreateRecordResult.ToPerformUpdateParam());
        if (PrepRecordForSaveAfterChangesMade(getOrCreateRecordResult.Record, timeStamp) is { IsSuccessful: false } failedPrepResult)
        {
            return failedPrepResult.ToGetOrCreateRecordResult(getOrCreateRecordResult.FoundExistingRecord, getOrCreateRecordResult.CreatingNewRecord, getOrCreateRecordResult.EditingExistingRecord);
        }
        return getOrCreateRecordResult;
    }

    public async Task<GetOrCreateRecordResult> CreateOrUpdateAsync(Type type, Guid id, Guid? financialInstitutionId, byte[] timeStamp,
        PerformCreateOrUpdateOptionsAsync? options)
    {
        var getOrCreateRecordResult = await GetOrCreateRecordAsync(type, id, financialInstitutionId, options?.QueryableCustomization);
        if (!getOrCreateRecordResult.IsSuccessful || getOrCreateRecordResult.Record == null)
        {
            return getOrCreateRecordResult;
        }

        if (options?.ExecuteRecordChangesFunc != null)
        {
            await options.ExecuteRecordChangesFunc(getOrCreateRecordResult.ToPerformUpdateParam());
        }

        var entry = context.Entry(getOrCreateRecordResult.Record);
        getOrCreateRecordResult.EditingExistingRecord = entry.State == EntityState.Modified;

        if (PrepRecordForSaveAfterChangesMade(getOrCreateRecordResult.Record, timeStamp) is { IsSuccessful: false } failedPrepResult)
        {
            return failedPrepResult.ToGetOrCreateRecordResult(getOrCreateRecordResult.FoundExistingRecord, getOrCreateRecordResult.CreatingNewRecord, getOrCreateRecordResult.EditingExistingRecord);
        }
        return getOrCreateRecordResult;
    }

    public void SetInitialDataForNewRecord(IEFPoco data, Guid? financialInstitutionId = null)
    {
        data.Id = guidExtensionService.NewSequentialGuid();
        data.DateCreatedUtc = systemClockService.GetSystemTimeUtc();
        data.DateUpdatedUtc = systemClockService.GetSystemTimeUtc();
        if (data is IFinancialInstitutionId dbRecordWFinancialInstitutionId && financialInstitutionId.HasValue)
        {
            dbRecordWFinancialInstitutionId.FinancialInstitutionId = financialInstitutionId.Value;
        }
        data.TimeStamp = [];
    }

    /// <summary>
    /// After changes have been made to an entity framework model object, this method performs two tasks:
    /// 1: It can verify that the changes pass a db consistency check.
    ///     When possible, this will compare the database timestamp to the timestamp of the data the user was editing.
    ///     If they do not match, then some other user has saved changes while this user was reviewing the data.
    ///     We will usually refuse to make this update and inform the user when this happens.
    ///
    /// 2: If appropriate, it will set the value for 'DateUpdatedUtc'
    /// </summary>
    /// <param name="record"></param>
    /// <param name="editingTimestamp"></param>
    /// <returns>This method returns a PrepRecordResult that indicates if the check was successful, and a message if not.</returns>
    public PrepRecordResult PrepRecordForSaveAfterChangesMade(IEFPoco record, byte[] editingTimestamp)
    {
        if (context.Entry(record).State == EntityState.Unchanged)
        {
            // no changes made?  Nothing to do here.  we do not need to bother checking the timestamp or set the date changed.
            return new PrepRecordResult() { IsSuccessful = true, Record = record, };
        }

        if (context.Entry(record).State == EntityState.Added)
        {
            // timestamp doesn't matter for a new record.
        }
        else if (!record.TimeStamp.SequenceEqual(editingTimestamp))
        {
            // timestamp doesn't match.  Another user may have modified the record we are attempting to modify.
            return new PrepRecordResult() { IsSuccessful = false, ErrorMessage = ItemConstants.ConcurrencyErrorMessage, Record = record, };
        }

        if (context.Entry(record).State != EntityState.Deleted)
        {
            record.DateUpdatedUtc = systemClockService.GetSystemTimeUtc();
        }

        return new PrepRecordResult() { IsSuccessful = true, Record = record, };
    }

    private GenericActionResult? SetItemDeleted(IEFPoco dbRecord)
    {
        if (dbRecord is IEFPocoLookup lookupRecord)
        {
            if (lookupRecord.IsDeleted)
            {
                // definition of success is weird here.  doing nothing because the record was already deleted, but the intention was accomplished
                return new GenericActionResult() { IsSuccessful = true, ErrorMessage = "Record was already deleted.", };
            }
            lookupRecord.IsDeleted = true;
        }
        else
        {
            // This record does not have a flag for a soft-delete, so we need to perform a real delete.
            context.Entry(dbRecord).State = EntityState.Deleted;
        }
        return null;
    }

    public virtual async Task<GenericActionResult> DeleteItemAsync<T>(DeleteItemDto dto) where T : class, IEFPoco
    {
        if (await GetItemAsync<T>(dto.DeleteItemId) is not { } dbRecord)
        {
            return new GenericActionResult { IsSuccessful = false, ErrorMessage = "No such item found." };
        }

        var deleteResult = SetItemDeleted(dbRecord);
        if (deleteResult != null)
        {
            return deleteResult;
        }

        if (PrepRecordForSaveAfterChangesMade(dbRecord, dto.TimeStamp) is { IsSuccessful: false } failedPrepResult)
        {
            return GetOrCreateRecordResult.FromBase(failedPrepResult, dbRecord);
        }

        return new GenericActionResult { IsSuccessful = true, };
    }

    public async Task<GenericActionResult> DeleteItemAsync(Type dbObjectType, DeleteItemDto dto)
    {
        if (await GetItemAsync(dbObjectType, dto.DeleteItemId) is not { } dbRecord)
        {
            return new GenericActionResult { IsSuccessful = false, ErrorMessage = "No such item found." };
        }

        var deleteResult = SetItemDeleted(dbRecord);
        if (deleteResult != null)
        {
            return deleteResult;
        }

        if (PrepRecordForSaveAfterChangesMade(dbRecord, dto.TimeStamp) is { IsSuccessful: false } failedPrepResult)
        {
            return GetOrCreateRecordResult.FromBase(failedPrepResult, dbRecord);
        }

        return new GenericActionResult { IsSuccessful = true, };
    }

    public int? GetFieldMaxLength<TEntity>(Expression<Func<TEntity, object?>> propertyExpression) where TEntity : class
    {
        var entityType = context.Model.FindEntityType(typeof(TEntity));
        if (entityType == null)
        {
            return null;
        }
        var member = propertyExpression.Body is UnaryExpression unary
            ? ((MemberExpression)unary.Operand).Member
            : ((MemberExpression)propertyExpression.Body).Member;
        var property = entityType.FindProperty(member.Name);
        return property?.GetMaxLength();
    }
}
