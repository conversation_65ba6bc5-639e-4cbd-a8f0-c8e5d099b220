using Kendo.Mvc;
using Kendo.Mvc.Extensions;
using Kendo.Mvc.UI;
using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Extensions;
using QCash.Data.Models.Interfaces;
using QCash.Service.Models;
using QCash.Service.Models.DecisionEngine;
using QCash.Service.Models.General;
using QCash.Service.Services.DecisionEngine.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Utilities.Extensions;

namespace QCash.Service.Services.General;

public class LookupListService(
    IDecisionEngineSettingsService decisionEngineSettingsService,
    QCashContext context,
    IEfPocoService efPocoService
    ) : ILookupListService
{

    public async Task<DataSourceResult> GetAllLookupDataAsync(Guid financialInstitutionId, string lookupTypeName,
        DataSourceRequest request)
    {
        var lookupTypeEnum = decisionEngineSettingsService.GetLookupTypeEnum(lookupTypeName);
        var lookupQuery = (await decisionEngineSettingsService.GetQueryForLookupAsync(lookupTypeEnum))
            .AsNoTracking()
            .Where(a => !a.IsDeleted);
        var sourceTypeNullable = decisionEngineSettingsService.GetLookupDbObjectType(lookupTypeEnum);
        var sortFieldName = request.Sorts?.First()?.Member;
        if (sortFieldName == nameof(IEFPocoWithCode.Code))
        {
            if (typeof(IEFPocoWithCode).IsAssignableFrom(sourceTypeNullable))
            {
                // For the lookup types that have a code, we need to use a type that has a code
                // so that Kendo can sort on it.
                var codeTypedQ = lookupQuery
                    .Cast<IEFPocoWithCode>();
                var resultWCode = await codeTypedQ.ToDataSourceResultAsync(request);
                return resultWCode;
            }
            // User somehow asked to sort on 'Code', but there is no 'Code' field... just remove the sort.
            request.Sorts = new List<SortDescriptor>();
        }
        else if (sortFieldName == nameof(IEFPocoWithDescription.Description))
        {
            if (typeof(IEFPocoWithDescription).IsAssignableFrom(sourceTypeNullable))
            {
                var codeTypedQ = lookupQuery
                    .Cast<IEFPocoWithDescription>();
                var resultWCode = await codeTypedQ.ToDataSourceResultAsync(request);
                return resultWCode;
            }
            request.Sorts = new List<SortDescriptor>();
        }

        var result = await lookupQuery.ToDataSourceResultAsync(request);
        return result;
    }

    public async Task<GenericActionResult> DeleteItemAsync(Guid applicationId, Enums.DecisionEngineLookupTypeEnum lookupTypeEnum,
        DeleteItemDto dto)
    {
        var dbObjType = decisionEngineSettingsService.GetLookupDbObjectType(lookupTypeEnum);
        return await efPocoService.DeleteItemAsync(dbObjType, dto);
    }

    public bool CanDelete(Enums.DecisionEngineLookupTypeEnum modelLookupTypeEnum)
    {
        if (modelLookupTypeEnum <= Enums.DecisionEngineLookupTypeEnum.Unknown)
        {
            return false;
        }

        // InvoicePlan should not be deletable. However, it doesn't seem to exist.
        if (modelLookupTypeEnum == Enums.DecisionEngineLookupTypeEnum.InvoicePlan
            //|| modelLookupTypeEnum == Enums.DecisionEngineLookupTypeEnum.AanReasonDefaultText
            || modelLookupTypeEnum == Enums.DecisionEngineLookupTypeEnum.Weekday
           )
        {
            return false;
        }

        return true;
    }

    public async Task<DataSourceResult> GetDETrackingRecordsAsync(DataSourceRequest request)
    {
        var data = context.DecisionEngineTrackingRecordDataMappings
            .Where(a => !a.IsDeleted)
            .Select(a => new DETrackingRecordDataMappingDto()
            {
                Id = a.Id,
                Name = a.Name,
                Slug = a.Slug,
                Code = string.Empty,
                Abrv = string.Empty,
                AppAbrv = string.Empty,
                Description = a.Description,
                TrackingRecordType = a.TrackingRecordType,
                TrackingRecordField = a.TrackingRecordField,
                TrackingRecordLevel = a.TrackingRecordLevel,
                TrackingRecordPullOption = a.TrackingRecordPullOption,
                IsDeleted = a.IsDeleted,
                TimeStamp = a.TimeStamp,
                FiSlug = a.Slug,
                LookupTypeName = nameof(Enums.DecisionEngineLookupTypeEnum.DecisionEngineTrackingRecordDataMapping),
            });
        var result = await data.ToDataSourceResultAsync(request);
        return result;
    }

    public async Task<DataSourceResult> GetBusinessRulesAsync(DataSourceRequest request, Enums.ScoreTypesEnum scoreTypesEnum)
    {
        var data = context.BusinessRules
            .AsNoTracking()
            .Where(s => s.ScoreType.Slug == scoreTypesEnum.ToDescriptionString())
            .Select(s => new ScoreListItemTypeDto()
            {
                Id = s.Id,
                Name = s.Name,
                TimeStamp = s.TimeStamp,
            });
        var result = await data.ToDataSourceResultAsync(request);
        return result;
    }

    public async Task<DataSourceResult> GetNameDescLookupAsync<T>(DataSourceRequest request) where T : class, IDescriptionValueLookup
    {
        var data = context.Set<T>()
            .Select(a => new GenericDescriptionListItemDto()
            {
                Id = a.Id,
                Description = a.Description,
                TimeStamp = a.TimeStamp,
            });
        var result = await data.ToDataSourceResultAsync(request);
        return result;
    }

    public async Task<DataSourceResult> GetDescriptionValueLookupAsync(Type type, DataSourceRequest request)
    {
        var q = context.Set(type);
        if (q == null)
        {
            throw new Exception($"Cannot find database table for type {type}");
        }
        var data = q.Cast<IDescriptionValueLookup>()
            .Select(a => new GenericDescriptionListItemDto()
            {
                Id = a.Id,
                Description = a.Description,
                Value = a.Value,
                TimeStamp = a.TimeStamp,
            });
        var result = await data.ToDataSourceResultAsync(request);
        return result;
    }
}
