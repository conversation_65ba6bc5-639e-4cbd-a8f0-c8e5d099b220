using Microsoft.EntityFrameworkCore;
using QCash.Data.Context;
using QCash.Data.Extensions;
using QCash.Data.Models;
using QCash.Service.Services.Interfaces;

namespace QCash.Service.Services;

public class LookupQueries(QCashContext context) : ILookupQueries
{
    public IQueryable<LoanType> GetLoanTypes(string fiSlug) =>
        context.LoanTypes
            .Where(lt => lt.FinancialInstitution.Slug == fiSlug);

    public IQueryable<LoanCategory> GetLoanCategories(Guid financialInstitutionId) =>
        context.LoanCategories
            .Where(a => a.FinancialInstitutionId == financialInstitutionId)
            .Where(lc => !lc.IsDeleted);

    public async Task<IQueryable<LoanCategory>> GetLoanCategoriesQAsync(string fiSlug, Guid loanTypeId)
    {
        var feeBasedLoanTypeId = await GetLoanTypes(fiSlug)
            .Where(lt => lt.Abrv == LookupService.LoanTypeFeeBasedAbbreviation)
            .Select(lt => lt.Id)
            .FirstOrDefaultAsync();

        var q = context.LoanCategories
            .Where(lc => lc.FinancialInstitution.Slug == fiSlug)
            .Where(lc => !lc.IsDeleted)
            .WhereIf(feeBasedLoanTypeId != default && loanTypeId == feeBasedLoanTypeId,
                lc => lc.Abrv != LookupService.LoanCategoryUnsecuredOpenAbbreviation)
            .OrderBy(lc => lc.Id);

        return q;
    }

    public IQueryable<AspNetRole> GetRolesQueryable() =>
        context.AspNetRoles.AsQueryable();

    public IQueryable<State> GetStatesQueryable() =>
        context.States.AsQueryable();
}

