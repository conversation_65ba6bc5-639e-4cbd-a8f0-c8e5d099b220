using System.Text;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using QCash.Data.Models;
using QCash.LoanApplication;
using QCash.Service.FileSystem;

namespace QCash.Service;

public class ErrorHelper(
    ILogger<ErrorHelper> logger,
    IFileSystemProvider fileSystemProvider
    )
{
    //Original QCash6 implementation used IFinancialInstitutionRoute as input here.  Since we are using FinancialInstitutionConfiguration directly now
    //as a return object from FinancialInstitutionConfigurationService, the ErrorHelper implementation needs to accept that as an input.

    //Also note this used to be a static class, now a singleton
    public async Task StoreErrorAsync(Exception ex, FinancialInstitutionConfiguration financialInstitutionConfiguration, Guid correlationId = default)
    {
        var routeInformation = new RouteInfo
        {
            Slug = financialInstitutionConfiguration.FinancialInstitution.Slug,
            StorageName = financialInstitutionConfiguration.StorageName,
            StorageKey = financialInstitutionConfiguration.StorageKey,
            StorageBlobEndpoint = financialInstitutionConfiguration.StorageBlobEndpoint
        };
        await StoreErrorAsync(ex, routeInformation, correlationId);
    }

    public async Task StoreErrorAsync(Exception ex, RouteInfo routeInformation, Guid correlationId = default)
    {
        //TODO - determine how we're storing the errors here. AzureFileSystemProvider? How does this change for QCash7?

        //QCash7 implementation replaces NewRelic with ILogger and comments out Azure pieces for now. Needs analysis of current/desired state.

        //IFileSystemProvider fs = new FileSystemProvider();

        //Original implementation

        //if (ApplicationSettings.AzureEnvironment == false)
        //       fs = new FileSystemProvider();
        //   else
        //   {
        //       fs = new AzureFileSystemProvider();

        //       if (routeInformation != null
        //           && !string.IsNullOrEmpty(routeInformation.StorageName)
        //           && !string.IsNullOrEmpty(routeInformation.StorageKey))
        //       {
        //           fs.AccountKey = TokenManager.Decrypt(routeInformation.StorageKey);
        //           fs.AccountName = routeInformation.StorageName;
        //           fs.BlobEndpoint = routeInformation.StorageBlobEndpoint;
        //       }
        //       else
        //           return;
        //   }


        var fiSlug = routeInformation.Slug;
        if (string.IsNullOrEmpty(fiSlug))
        {
            fiSlug = Constants.QCashFi.ToLower();
        }

        //Original implementation
        //if (ApplicationSettings.AzureEnvironment)
        //{
        //    NewRelicLogger.LogException(ex, ("fiSlug", fiSlug));
        //}
        logger.LogError(ex, "{fiSlug}", fiSlug);

        //((FileSystemProviderBase)fileSystemProvider).ContainerName = fiSlug;
        await fileSystemProvider.SetupAsync(ContainerTypeEnum.Errors);

        var filename = $"{DateTime.UtcNow:yyyyMMdd_HH}_Log.txt";
        var file = string.Empty;
        var old = await fileSystemProvider.GetFileAsync(filename);
        if (old is { Length: < 5000000 })
        {
            file = Encoding.UTF8.GetString(old);
        }

        var em = string.Empty;

        var serializerSettings = new JsonSerializerSettings
        {
            NullValueHandling = NullValueHandling.Ignore,
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore
        };
        try
        {
            if (ex is CoreProviderException coreProviderException)
            {
                var message = JsonConvert.SerializeObject(coreProviderException.CoreProviderFault, serializerSettings);
                em += $@"{DateTime.UtcNow.ToLongTimeString()} {message}{Environment.NewLine}";
            }

            em += $@"{DateTime.UtcNow.ToLongTimeString()} {ex}";
        }
        catch
        {
            em = ex.Message;
        }
        var correlationIdPart = correlationId == default ? string.Empty : $"CorrelationId: {correlationId}, ";
        file = $"{file}{Environment.NewLine}{correlationIdPart}{em}";
        fileSystemProvider.StoreFile(Encoding.UTF8.GetBytes(file), "text/plain", filename);
    }

    public class RouteInfo
    {
        public string? Slug { get; set; }
        public string? StorageName { get; set; }
        public string? StorageKey { get; set; }
        public string? StorageBlobEndpoint { get; set; }
    }
}
