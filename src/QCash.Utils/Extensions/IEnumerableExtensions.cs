namespace QCash.Utils.Extensions;

// ReSharper disable once InconsistentNaming -- This is an extension on an interface
public static class IEnumerableExtensions
{
    public static (List<U> inserts, List<Tuple<T, U>> updates, List<T> deletes) Diff<T, U, TKey>(this IEnumerable<T> src, IEnumerable<U> dst, Func<T, TKey> srcKeySelector, Func<U, TKey> dstKeySelector)
        where TK<PERSON> : IEquatable<TKey>
    {
        var srcDict = src.ToDictionary(srcKeySelector, v => v);
        var inserts = new List<U>();
        var updates = new List<Tuple<T, U>>();

        foreach (var d in dst)
        {
            var dstKey = dstKeySelector(d);

            if (srcDict.Remove(dstKey, out var s))
            {
                updates.Add(Tuple.Create(s, d));
            }
            else
            {
                inserts.Add(d);
            }
        }

        var deletes = srcDict.Values.ToList();

        return (inserts, updates, deletes);
    }
}
