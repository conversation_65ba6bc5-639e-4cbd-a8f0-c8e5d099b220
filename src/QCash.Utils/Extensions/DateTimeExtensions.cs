namespace QCash.Utils.Extensions;

public static class DateTimeExtensions
{
    public static DateTime GetFirstDayOfMonth(this DateTime dateTime)
    {
        return new DateTime(dateTime.Year, dateTime.Month, 1);
    }

    public static DateTime GetLastDayOfMonth(this DateTime dateTime)
    {
        return new DateTime(dateTime.Year, dateTime.Month, DateTime.DaysInMonth(dateTime.Year, dateTime.Month));
    }

    public static bool IsLastDayOfMonth(this DateTime dateTime)
    {
        return dateTime.Date == GetLastDayOfMonth(dateTime);
    }

    public static bool IsFirstDayOfMonth(this DateTime dateTime)
    {
        return dateTime.Day == 1;
    }

    public static DateTime WithEndOfDayTime(this DateTime dateTime)
    {
        return dateTime.Date.AddDays(1).AddSeconds(-1);
    }

    public static IEnumerable<DateTime> AllDatesInMonth(this DateTime dateTime)
    {
        return Enumerable
            .Range(1, DateTime.DaysInMonth(dateTime.Year, dateTime.Month))
            .Select(d => new DateTime(dateTime.Year, dateTime.Month, d));
    }

    public static bool IsBeforeCutoff(this DateTime dateTime, DateTime cutoff, int offset = 0)
    {
        dateTime = dateTime.AddHours(offset);
        return TimeOnly.FromDateTime(dateTime) < TimeOnly.FromDateTime(cutoff);
    }

    public static bool IsAfterCutoff(this DateTime dateTime, DateTime cutoff, int offset = 0)
        => IsBeforeCutoff(TimeOnly.FromDateTime(dateTime), TimeOnly.FromDateTime(cutoff), offset);

    public static bool IsBetween(this DateTime dateTime, DateTime startTime, DateTime endTime, int offset = 0)
        => IsBetween(TimeOnly.FromDateTime(dateTime), TimeOnly.FromDateTime(startTime), TimeOnly.FromDateTime(endTime), offset);

    public static bool IsBetween(this DateTime dateTime, long startTicks, long endTicks, int offset = 0)
        => IsBetween(TimeOnly.FromDateTime(dateTime), new TimeOnly(startTicks), new TimeOnly(endTicks), offset);

    public static bool IsBeforeCutoff(this TimeOnly timeOnly, TimeOnly cutoff, int offset = 0)
    {
        timeOnly = timeOnly.AddHours(offset);
        return timeOnly > cutoff;
    }

    public static bool IsAfterCutoff(this TimeOnly timeOnly, TimeOnly cutoff, int offset = 0)
    {
        timeOnly = timeOnly.AddHours(offset);
        return timeOnly < cutoff;
    }

    public static bool IsBetween(this TimeOnly timeOnly, TimeOnly startTime, TimeOnly endTime, int offset)
    {
        timeOnly = timeOnly.AddHours(offset);
        return timeOnly >= startTime && timeOnly <= endTime;
    }
}