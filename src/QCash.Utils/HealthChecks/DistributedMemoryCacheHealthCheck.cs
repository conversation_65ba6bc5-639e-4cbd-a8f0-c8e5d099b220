using System.Globalization;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using QCash.Utils.Core;

namespace QCash.Utils.HealthChecks;

public class DistributedMemoryCacheHealthCheck(IDistributedCache distributedCache, ISystemClockService systemClockService) : IHealthCheck
{
    internal string TestKey = $"health_check_key_{Environment.ProcessId}";
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken token = new CancellationToken())
    {
        try
        {
            var utcNow = systemClockService.GetSystemTimeUtc();
            var testValue = utcNow.ToString(CultureInfo.InvariantCulture);
            await distributedCache.SetStringAsync(TestKey, testValue, token);
            var cachedTimeString = await distributedCache.GetStringAsync(TestKey, token);
            if (testValue == cachedTimeString)
            {
                return HealthCheckResult.Healthy();
            }

            return HealthCheckResult.Degraded($"Expected {testValue} but got {cachedTimeString}");
        }
        catch (Exception exception)
        {
            return HealthCheckResult.Unhealthy(exception.Message, exception);
        }
    }
}
