using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.DependencyInjection;

namespace QCash.Utils.HealthChecks;

public static class DistributedMemoryCacheExtensions
{
    public static IHealthChecksBuilder AddDistributedMemoryCacheHealthCheck(
        this IHealthChecksBuilder builder,
        string? name = null,
        HealthStatus? failureStatus = null,
        IEnumerable<string>? tags = null,
        TimeSpan? timeout = null
        )
    {
        return builder.AddCheck<DistributedMemoryCacheHealthCheck>(
            name ?? "DistributedMemoryCache",
            failureStatus ?? HealthStatus.Degraded,
            tags ?? [],
            timeout);
    }
}
