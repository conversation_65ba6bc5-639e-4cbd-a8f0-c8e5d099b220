<Project Sdk="Microsoft.NET.Sdk">
    <ItemGroup>
      <PackageReference Include="log4net" Version="3.1.0" />
      <PackageReference Include="Meziantou.Analyzer" Version="2.0.201">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
      <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
      <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="9.0.0">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.0" />
      <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
      <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
      <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.16" />
      <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions" Version="8.0.16" />
      <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.1" />
      <PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="8.0.0" />
      <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
      <PackageReference Include="Microsoft.Web.Xdt" Version="3.1.0" />
      <PackageReference Include="Phoenix.Analyzer" Version="1.6.1">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="System.Security.Cryptography.Xml" Version="8.0.0" />
    </ItemGroup>

  <ItemGroup>
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleToAttribute">
      <_Parameter1>QCash.Utils.UnitTest</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>
</Project>
