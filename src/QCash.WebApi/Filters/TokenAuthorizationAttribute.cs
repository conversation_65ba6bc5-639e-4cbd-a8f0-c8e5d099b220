using System.Diagnostics.CodeAnalysis;
using System.Text;
using Microsoft.AspNetCore.Mvc.Filters;
using QCash.Service.Services.Interfaces;

namespace QCash.WebApi.Filters;

/// <summary>
/// LoanApplication authorization.
/// </summary>
[ExcludeFromCodeCoverage]
[AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
public class TokenAuthorizationAttribute : ActionFilterAttribute, IAsyncAuthorizationFilter
{
    /// <summary>
    /// Redirect to NoPermissions if AuthorizeCoreAsync == false on authorization.
    /// </summary>
    async Task IAsyncAuthorizationFilter.OnAuthorizationAsync(AuthorizationFilterContext actionContext)
    {
        var tokenService = actionContext.HttpContext.RequestServices.GetRequiredService<ITokenService>();

        _ = actionContext.HttpContext.Request.Headers.TryGetValue("Token", out var tokenHeaderValues);
        var securityToken = tokenHeaderValues.FirstOrDefault();

        var message = await AuthorizeTokenAsync(tokenService, securityToken);

        if (!string.IsNullOrEmpty(message))
            throw new ArgumentNullException($"Invalid authorization token; error: {message}");
    }

    /// <summary>
    /// Decodes (base64) and authorizes securityToken.
    /// </summary>
    /// <param name="tokenService">The token service.</param>
    /// <param name="securityToken">Security token.</param>
    /// <returns>
    /// The error message if authorization fails, otherwise an empty string.
    /// </returns>
    private static async Task<string> AuthorizeTokenAsync(ITokenService tokenService, string? securityToken)
    {
        if (string.IsNullOrWhiteSpace(securityToken))
            return $"{nameof(securityToken)} should not be null or whitespace but is '{securityToken}'";

        var tokenData = Convert.FromBase64String(securityToken);
        var decodedSecurityToken = Encoding.UTF8.GetString(tokenData);

        if (string.IsNullOrWhiteSpace(decodedSecurityToken))
            return $"{nameof(decodedSecurityToken)} should not be whitespace but is '{decodedSecurityToken}'";

        var currentToken = await tokenService.GetLoanApplicationTokenAsync(decodedSecurityToken);
        if (currentToken == null)
            return $"{nameof(currentToken)} is not available in the system";
        if (string.IsNullOrWhiteSpace(currentToken.Token))
            return
                $"{nameof(currentToken)}.{nameof(currentToken.Token)} should not be null or whitespace but is '{currentToken.Token}'";
        if (!currentToken.IsSet)
            return $"{nameof(currentToken)}.{nameof(currentToken.IsSet)} should be true";
        if (!currentToken.IsWebApiToken.GetValueOrDefault())
            return
                $"{nameof(currentToken)}.{nameof(currentToken.IsWebApiToken)} should be true but is {currentToken.IsWebApiToken}";
        if (currentToken.FinancialInstitutionMemberId == null)
            return $"{nameof(currentToken)}.{nameof(currentToken.FinancialInstitutionMemberId)} should not be null";

        var latestToken = await tokenService.GetLatestWebApiTokenAsync(currentToken.FinancialInstitutionMemberId.Value);
        if (latestToken == null)
            return $"{nameof(latestToken)} is not available in the system";
        if (string.IsNullOrWhiteSpace(latestToken.Token))
            return
                $"{nameof(latestToken)}.{nameof(latestToken.Token)} should not be null or whitespace but is '{latestToken.Token}'";
        if (!latestToken.IsSet)
            return $"{nameof(latestToken)}.{nameof(latestToken.IsSet)} should be true";
        if (!latestToken.IsWebApiToken.GetValueOrDefault())
            return
                $"{nameof(latestToken)}.{nameof(latestToken.IsWebApiToken)} should be true but is {latestToken.IsWebApiToken}";

        if (currentToken.Token != latestToken.Token)
            return
                $"{nameof(currentToken)}.{nameof(currentToken.Token)} should equal to {nameof(latestToken)}.{nameof(latestToken.Token)}; actual values: '{currentToken.Token}', '{latestToken.Token}'";
        if (DateTime.UtcNow >= currentToken.ExpirationUtc)
            return $"token expired at ${currentToken.ExpirationUtc} but now is {DateTime.UtcNow}";

        return string.Empty;
    }
}