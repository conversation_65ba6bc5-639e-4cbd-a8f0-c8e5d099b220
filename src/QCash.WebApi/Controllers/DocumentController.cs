using Microsoft.AspNetCore.Mvc;
using QCash.Data.Extensions;
using QCash.Data.Models;
using QCash.Service.FileSystem;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.LoanApplication.Interfaces;
using QCash.WebApi.Extensions;
using QCash.WebApi.Filters;
using ServiceEnums = QCash.Service.Models.Core.Enums;
using LoanApplicationEnums = QCash.Service.Services.LoanApplication.Enums;

namespace QCash.WebApi.Controllers;

[ApiController]
[Route("{fiSlug}/api/document")]
public class DocumentController(
    ITokenService tokenService,
    ILoanApplicationService loanApplicationService,
    ILoanApplicationStepsService loanApplicationStepsService,
    IFilenameTemplateService filenameTemplateService,
    IFileSystemProvider fileSystemProvider,
    ILogger<DocumentController> logger) : ControllerBase
{
    private const string LoanApplicationNotFoundMessage = "Loan application not found";
    private const string DocumentNotFoundMessage = "Document not found";
    private const string GenericErrorMessage = "An error occurred while retrieving the document";

    private record DocumentRequestContext(
        string DocumentType,
        bool PrintAction,
        Guid CorrelationId,
        ServiceEnums.LoanApplicationDocumentType DocumentTypeEnum,
        Func<Data.Models.LoanApplication, Document?> GetDocumentFunc,
        Func<List<LoanApplicationLogDetail>, Data.Models.LoanApplication, Task> AddLogMessagesFunc
    );

    [HttpGet("adverse-action-notice")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<IActionResult> GetAdverseActionNotice([FromQuery] bool printAction = false)
    {
        var context = new DocumentRequestContext(
            "Adverse Action Notice",
            printAction,
            Request.HttpContext.GetOrCreateCorrelationId(),
            ServiceEnums.LoanApplicationDocumentType.AdverseActionNotice,
            loanApplicationService.GetAdverseActionDocument,
            (logs, _) =>
            {
                loanApplicationService.AddLogMessage(logs, "AdverseActionNotice retrieved.", DateTime.UtcNow);
                return Task.CompletedTask;
            }
        );

        return await ProcessDocumentRequestAsync(context);
    }

    [HttpGet("econsent-disclosure")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<IActionResult> GetEConsentDisclosure([FromQuery] bool printAction = false)
    {
        var context = new DocumentRequestContext(
            "E-Consent Disclosure",
            printAction,
            Request.HttpContext.GetOrCreateCorrelationId(),
            ServiceEnums.LoanApplicationDocumentType.EConsentDisclosure,
            loanApplicationService.GetEConsentDocument,
            (logs, _) =>
            {
                loanApplicationService.AddLogMessage(logs, "EConsentDisclosure retrieved.", DateTime.UtcNow);
                return Task.CompletedTask;
            }
        );

        return await ProcessDocumentRequestAsync(context);
    }

    [HttpGet("tila")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<IActionResult> GetTILA([FromQuery] bool printAction = false)
    {
        var context = new DocumentRequestContext(
            "TILA",
            printAction,
            Request.HttpContext.GetOrCreateCorrelationId(),
            ServiceEnums.LoanApplicationDocumentType.TILA,
            loanApplicationService.GetTilaDocument,
            async (logs, loanApp) =>
            {
                var actionDesc = printAction ? "Action: Print TILA" : "Action: View TILA";
                loanApplicationService.AddLogMessage(logs, actionDesc, DateTime.UtcNow, LoanApplicationEnums.LogType.Clean, LoanApplicationEnums.LogSortOrder.TILAAction);

                var fileName = await filenameTemplateService.GetDocumentNameAsync(
                    ServiceEnums.LoanApplicationDocumentType.TILA,
                    loanApp,
                    redactInformation: true);
                var fileNameLog = "TILA Disclosure Retrieved: " + fileName;
                loanApplicationService.AddLogMessage(logs, fileNameLog, DateTime.UtcNow, LoanApplicationEnums.LogType.Admin, LoanApplicationEnums.LogSortOrder.TILAAction);
            }
        );

        return await ProcessDocumentRequestAsync(context);
    }

    [HttpGet("payment-guard")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<IActionResult> GetPaymentGuard([FromQuery] bool printAction = false)
    {
        var correlationId = Request.HttpContext.GetOrCreateCorrelationId();
        try
        {
            logger.LogInformation(
                "Payment Guard request initiated. PrintAction: {PrintAction}, CorrelationId: {CorrelationId}",
                printAction, correlationId);

            var currentToken = await Request.GetLoanApplicationTokenAsync(tokenService);
            var loanApplications = await loanApplicationService
                .GetLoanApplicationsWithChildsByFinancialInstitutionMemberIdAsync(currentToken.FinancialInstitutionMemberId);
            var currentLoanApplication = loanApplications.FirstOrDefault();

            if (currentLoanApplication == null)
            {
                return HandleLoanApplicationNotFound(currentToken.FinancialInstitutionMemberId, correlationId);
            }

            var logs = new List<LoanApplicationLogDetail>();
            var (paymentGuardFileName, fileContent) = await loanApplicationService.GetPaymentGuardDocumentAsync();
            var log = "TruStage Payment Guard Disclosure Retrieved: " + paymentGuardFileName;
            loanApplicationService.AddLogMessage(logs, log, DateTime.UtcNow, LoanApplicationEnums.LogType.Admin, LoanApplicationEnums.LogSortOrder.PaymentGuard);

            await loanApplicationStepsService.SaveLoanApplicationLogMessagesAsync(currentLoanApplication, logs);

            logger.LogInformation(
                "Payment Guard retrieved successfully. Size: {Size} bytes, CorrelationId: {CorrelationId}",
                fileContent.Length,
                correlationId);

            return new FileContentResult(fileContent, "application/pdf")
            {
                FileDownloadName = paymentGuardFileName
            };
        }
        catch (Exception ex)
        {
            return HandleException(ex, "Payment Guard", printAction, correlationId);
        }
    }

    private async Task<IActionResult> ProcessDocumentRequestAsync(DocumentRequestContext context)
    {
        try
        {
            logger.LogInformation(
                "{DocumentType} request initiated. PrintAction: {PrintAction}, CorrelationId: {CorrelationId}",
                context.DocumentType, context.PrintAction, context.CorrelationId);

            var currentToken = await Request.GetLoanApplicationTokenAsync(tokenService);
            var loanApplications = await loanApplicationService
                .GetLoanApplicationsWithChildsByFinancialInstitutionMemberIdAsync(
                    currentToken.FinancialInstitutionMemberId);
            var currentLoanApplication = loanApplications.FirstOrDefault();

            if (currentLoanApplication == null)
            {
                return HandleLoanApplicationNotFound(currentToken.FinancialInstitutionMemberId, context.CorrelationId);
            }

            var logs = new List<LoanApplicationLogDetail>();
            var document = context.GetDocumentFunc(currentLoanApplication);
            await context.AddLogMessagesFunc(logs, currentLoanApplication);

            var fileName = await filenameTemplateService.GetDocumentNameAsync(
                context.DocumentTypeEnum,
                currentLoanApplication,
                redactInformation: true);

            await loanApplicationStepsService.SaveLoanApplicationLogMessagesAsync(currentLoanApplication, logs);

            if (document == null)
            {
                logger.LogWarning(
                    "{DocumentType} document not found. LoanApplicationId: {LoanApplicationId}, CorrelationId: {CorrelationId}",
                    context.DocumentType, currentLoanApplication.Id, context.CorrelationId);
                return NotFound(DocumentNotFoundMessage);
            }

            return await GetDocumentFileResult(document, fileName, context.CorrelationId);
        }
        catch (Exception ex)
        {
            return HandleException(ex, context.DocumentType, context.PrintAction, context.CorrelationId);
        }
    }

    private NotFoundObjectResult HandleLoanApplicationNotFound(Guid? financialInstitutionMemberId, Guid correlationId)
    {
        logger.LogWarning(
            "Loan application not found. FinancialInstitutionMemberId: {FinancialInstitutionMemberId}, CorrelationId: {CorrelationId}",
            financialInstitutionMemberId, correlationId);
        return NotFound(LoanApplicationNotFoundMessage);
    }

    private ObjectResult HandleException(Exception ex, string documentType, bool printAction, Guid correlationId)
    {
        logger.LogError(ex,
            "Error retrieving {DocumentType}. PrintAction: {PrintAction}, CorrelationId: {CorrelationId}",
            documentType, printAction, correlationId);
        return StatusCode(500, GenericErrorMessage);
    }

    private async Task<IActionResult> GetDocumentFileResult(Document document, string fileName, Guid correlationId)
    {
        await fileSystemProvider.SetupAsync(ContainerTypeEnum.DocumentsPrivate);
        var documentData = await fileSystemProvider.GetFileAsync(document.Filename);

        if (documentData is not { Length: > 0 })
        {
            logger.LogWarning(
                "Document file not found or empty. Filename: {Filename}, CorrelationId: {CorrelationId}",
                document.Filename, correlationId);
            return NotFound("Document file not found");
        }

        logger.LogInformation(
            "Document retrieved successfully. Size: {Size} bytes, CorrelationId: {CorrelationId}",
            documentData.Length, correlationId);

        return new FileContentResult(documentData, "application/pdf")
        {
            FileDownloadName = fileName
        };
    }
}