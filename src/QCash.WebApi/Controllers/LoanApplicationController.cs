using System.Net;
using System.Text;
using System.Text.Json;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using QCash.Common.Enums;
using QCash.Data.Extensions;
using QCash.Data.Models;
using QCash.Models.Api;
using QCash.Models.Api.Get;
using QCash.Models.Api.Post;
using QCash.Service.Core;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.LoanApplication.Interfaces;
using QCash.WebApi.Extensions;
using QCash.WebApi.Filters;

// ReSharper disable RouteTemplates.MethodMissingRouteParameters

namespace QCash.WebApi.Controllers;

[ApiController]
[Route("{fiSlug}/api/loan-application")]
public class LoanApplicationController(
    ITokenService tokenService,
    ILoanApplicationService loanApplicationService,
    ILoanApplicationStepsService loanApplicationStepsService,
    ILoanApplicationProcessService loanApplicationProcessService,
    ILoanApplicationHelper loanApplicationHelper,
    IUnitOfWork unitOfWork,
    IFiSettingService settingService,
    IQCashConnectService qcashConnectService,
    ILogger<LoanApplicationController> logger)
    : ControllerBase
{
    [HttpGet("error-state")]
    public Task<ActionResult<LoanApplicationApiModelBaseGet>> GetErrorState()
    {
        // var languageCode = Request.Headers["LanguageId"].ToString();
        // TODO: more details will be implemented in the future on actual error handling after the step processing
        return Task.FromResult<ActionResult<LoanApplicationApiModelBaseGet>>(
            Ok(new LoanApplicationApiModelBaseGet { Step = nameof(LoanApplicationStep.Application) }));
    }

    [HttpPut("language")]
    [TokenAuthorization]
    public async Task<IActionResult> ChangeLanguage(LanguageApiModelPost language)
    {
        var loanApplication = await Request.GetLoanApplicationTokenAsync(tokenService);

        if (loanApplication.FinancialInstitutionMemberId == null)
        {
            return NotFound("Loan application not found");
        }

        await loanApplicationProcessService.ChangeLanguageAsync(language, loanApplication.FinancialInstitutionMemberId.Value);
        await unitOfWork.CommitAsync();

        return Ok();
    }

    [HttpPost("initiate-token")]
    [SsoAuthorization]
    public async Task<IActionResult> InitiateToken()
    {
        try
        {
            // Get correlation ID from headers or generate a new one
            var correlationId = Request.HttpContext.GetOrCreateCorrelationId();

            using var reader = new StreamReader(Request.Body, Encoding.UTF8);
            var requestContent = await reader.ReadToEndAsync();

            var model = JsonSerializer.Deserialize<SsoInitiateApiModel>(requestContent);

            if (model == null)
                return BadRequest("Invalid request data");

            var memberId = model.MemberId ?? string.Empty;
            var baseAccount = model.BaseAccount ?? string.Empty;

            var token = await GetLoanApplicationToken(null, memberId, baseAccount);

            if (string.IsNullOrEmpty(token))
            {
                return StatusCode((int)HttpStatusCode.InternalServerError, "Failed to generate token");
            }

            var response = new ResponseWithTokenAndAuthorization
            {
                Token = token,
                Step = nameof(LoanApplicationStep.Application),
                CorrelationId = correlationId,
                Status = nameof(HttpStatusCode.OK),
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error initiating token");

            return StatusCode((int)HttpStatusCode.InternalServerError, "An error occurred during token initiation");
        }
    }

    [HttpPost("initiate")]
    [SsoAuthorization]
    public async Task<IActionResult> Initiate()
    {
        try
        {
            // Get correlation ID from headers or generate a new one
            var correlationId = Request.HttpContext.GetOrCreateCorrelationId();

            // Read Authorization header for SSO validation
            var authHeader = Request.Headers.Authorization.FirstOrDefault();

            // Read additional headers that may be needed for core provider integration
            var additionalHeaders = GetAdditionalHeaders();

            // Read IP address for logging and audit
            var ipAddress = Request.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";

            logger.LogInformation(
                "Initiate loan application started. CorrelationId: {CorrelationId}, IP: {IpAddress}", correlationId, ipAddress);

            using var reader = new StreamReader(Request.Body, Encoding.UTF8);
            var requestContent = await reader.ReadToEndAsync();

            var ssoModel = JsonSerializer.Deserialize<SsoInitiateApiModel>(requestContent);

            if (ssoModel == null)
            {
                logger.LogWarning("Invalid SSO initiate request data. CorrelationId: {CorrelationId}", correlationId);
                return BadRequest("Invalid request data");
            }

            // Log the request (with sensitive data masked)
            logger.LogInformation("SSO Initiate request received. MemberId: {MemberId}, CorrelationId: {CorrelationId}",
                loanApplicationService.GetMaskedNumber(ssoModel.MemberId),
                correlationId);

            ssoModel.AdditionalHeaders = additionalHeaders;
            ssoModel.Token = Request.Headers["Token"].FirstOrDefault()
                             ?? throw new ArgumentNullException("Token header is missing");

            // Start the loan application process using the step service
            var (loanApplication, errorDescription) = await loanApplicationStepsService.StartLoanApplicationAsync(
                ssoModel,
                correlationId);

            // Check for errors
            if (!string.IsNullOrEmpty(errorDescription))
            {
                logger.LogWarning(
                    "Loan application initiation failed. Error: {Error}, CorrelationId: {CorrelationId}",
                    errorDescription,
                    correlationId);
                return BadRequest(errorDescription);
            }

            var setting = await settingService.GetSettingsResponseAsync();

            string? token = null;
            if (setting?.MaskInitiate == true)
            {
                var data = Convert.FromBase64String(ssoModel.Token);
                var tokenValue = Encoding.UTF8.GetString(data);
                await tokenService.UpdateWebApiTokenAsync(loanApplication.FinancialInstitutionMemberId, tokenValue);
            }
            else
            {
                token = await GetLoanApplicationToken(
                    loanApplication.FinancialInstitutionMemberId,
                    ssoModel.MemberId ?? string.Empty,
                    ssoModel.BaseAccount ?? string.Empty);
            }

            // Based on the result, return the appropriate response
            var response = new ResponseWithTokenAndAuthorization
            {
                Token = token,
                Step = ((LoanApplicationStep)loanApplication.NextStep).ToString(),
                CorrelationId = correlationId,
                Status = nameof(HttpStatusCode.OK),
                AppId = loanApplication.AppId,
                LoanApplicationId = loanApplication.Id,
                Authorization = authHeader
            };

            logger.LogInformation(
                "Initiate loan application completed successfully. Step: {Step}, CorrelationId: {CorrelationId}",
                response.Step, correlationId);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            logger.LogWarning(ex, "Invalid argument in initiate request. CorrelationId: {CorrelationId}",
                Request.HttpContext.GetOrCreateCorrelationId());
            return BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException ex)
        {
            logger.LogWarning(ex, "Unauthorized access in initiate request. CorrelationId: {CorrelationId}",
                Request.HttpContext.GetOrCreateCorrelationId());
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            var correlationId = Request.HttpContext.GetOrCreateCorrelationId();
            logger.LogError(ex, "Error initiating loan application. CorrelationId: {CorrelationId}", correlationId);

            return StatusCode((int)HttpStatusCode.InternalServerError, "An error occurred during loan application initiation");
        }
    }

    [HttpPost("save-initiate-log")]
    [AllowAnonymous]
    public async Task<IActionResult> SaveInitiateLog(InitiateLogsDto log)
    {
        loanApplicationProcessService.SaveInitiateLog(log);
        await unitOfWork.CommitAsync();
        return Ok();
    }

    [HttpPost("log-message/{appId:int}")]
    [AllowAnonymous]
    public async Task AddLogMessage(int appId, [FromBody] string message)
    {
        var la = await loanApplicationHelper.GetLoanApplicationByAppIdAsync(appId);
        if (la == null) return;

        var logs = new List<LoanApplicationLogDetail>();
        loanApplicationService.AddLogMessage(logs, message, DateTime.UtcNow);
        await loanApplicationStepsService.SaveLoanApplicationLogMessagesAsync(la, logs);
    }

    [HttpGet("fraud-control")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<ActionResult<FraudControlApiModelGet>> GetFraudControl()
    {
        var correlationId = Request.HttpContext.GetOrCreateCorrelationId();

        try
        {
            var loanApplicationToken = await Request.GetLoanApplicationTokenAsync(tokenService);

            if (loanApplicationToken.FinancialInstitutionMemberId == null || loanApplicationToken.LanguageCode == null)
                return NotFound("Loan application not found");

            var fraudControlModel = await loanApplicationStepsService.GetFraudControlStepAsync(
                loanApplicationToken.FinancialInstitutionMemberId.Value, loanApplicationToken.LanguageCode);

            logger.LogInformation("Fraud control data retrieved successfully. CorrelationId: {CorrelationId}", correlationId);

            return Ok(fraudControlModel);
        }
        catch (ArgumentException ex)
        {
            logger.LogWarning(ex, "Invalid request for fraud control. {CorrelationId}", correlationId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving fraud control data. CorrelationId: {CorrelationId}", correlationId);
            return StatusCode(500, "An error occurred while retrieving fraud control data");
        }
    }

    [HttpPost("fraud-control")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<ActionResult<Response>> ProcessFraudControl(FraudControlApiModelPost model) => await ProcessLoanApplicationStepAsync(
        model,
        loanApplicationStepsService.ProcessFraudControlStepAsync,
        nameof(ProcessFraudControl));

    [HttpPost("cancel-application")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<ActionResult<Response>> CancelApplication(ApplicationApiModelPost model) => await ProcessLoanApplicationStepAsync(
        model,
        loanApplicationStepsService.ProcessCancelApplicationAsync,
        nameof(CancelApplication));

    [HttpGet("loan-landing")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<ActionResult<LoanApplicationApiModelBaseGet>> GetLoanLanding()
    {
        var correlationId = Request.HttpContext.GetOrCreateCorrelationId();

        try
        {
            var loanApplicationToken = await Request.GetLoanApplicationTokenAsync(tokenService);

            if (loanApplicationToken.FinancialInstitutionMemberId == null || loanApplicationToken.LanguageCode == null)
                return NotFound("Loan application not found");

            var apiModel = await loanApplicationStepsService.GetLoanLandingStepAsync(
                loanApplicationToken.FinancialInstitutionMemberId.Value);

            logger.LogInformation("Loan landing data retrieved successfully. CorrelationId: {CorrelationId}", correlationId);

            return Ok(apiModel);
        }
        catch (ArgumentException ex)
        {
            logger.LogWarning(ex, "Invalid request for loan landing. CorrelationId: {CorrelationId}", correlationId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving loan landing data. CorrelationId: {CorrelationId}", correlationId);
            return StatusCode(500, "An error occurred while retrieving loan landing data");
        }
    }

    [HttpPost("loan-landing")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<ActionResult<Response>> ProcessLoanLanding(LoanLandingApiModelPost model) => await ProcessLoanApplicationStepAsync(
        model, loanApplicationStepsService.ProcessLoanLandingStepAsync, nameof(ProcessLoanLanding));

    [HttpGet("econsent-disclosure")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<ActionResult<EConsentDisclosureApiModelGet>> GetEConsentDisclosure()
    {
        var correlationId = Request.HttpContext.GetOrCreateCorrelationId();

        try
        {
            var loanApplicationToken = await Request.GetLoanApplicationTokenAsync(tokenService);

            if (loanApplicationToken.FinancialInstitutionMemberId == null || loanApplicationToken.LanguageCode == null)
            {
                const string errorMessage = "Invalid loan application token or missing language code";
                logger.LogError("{ErrorMessage} for correlation ID {CorrelationId}", errorMessage, correlationId);
                return BadRequest(errorMessage);
            }

            var result = await loanApplicationStepsService.GetEConsentDisclosureStepAsync(
                loanApplicationToken.FinancialInstitutionMemberId.Value,
                loanApplicationToken.LanguageCode);

            return Ok(result);
        }
        catch (ArgumentException ex)
        {
            logger.LogError(ex, "Invalid argument in GetEConsentDisclosure for correlation ID {CorrelationId}", correlationId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in GetEConsentDisclosure for correlation ID {CorrelationId}", correlationId);
            return StatusCode(500, "An error occurred while processing the request");
        }
    }

    [HttpPost("econsent-disclosure")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<ActionResult<Response>> ProcessEConsentDisclosure(StatementAcceptedApiModelPost model) =>
        await ProcessLoanApplicationStepAsync(
            model,
            loanApplicationStepsService.ProcessEConsentDisclosureStepAsync,
            nameof(ProcessEConsentDisclosure));

    [HttpGet("loan-initiate-status")]
    [AllowAnonymous]
    public async Task<ActionResult<LoanInitiateStatusResponse>> GetLoanStartStatus()
    {
        var correlationId = Request.HttpContext.GetOrCreateCorrelationId();
        try
        {
            var loanApplicationToken = await Request.GetLoanApplicationTokenAsync(tokenService);

            var isInitiated = loanApplicationToken.FinancialInstitutionMemberId != null
                             && loanApplicationToken.FinancialInstitutionMemberId != Guid.Empty;

            return Ok(new LoanInitiateStatusResponse { IsLoanInitiated = isInitiated });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error checking loan application status. CorrelationId: {CorrelationId}", correlationId);
            return StatusCode(500, "An error occurred while checking loan application status");
        }
    }

    [HttpPost("loan-landing-status")]
    [TokenAuthorization]
    public async Task<ActionResult<MaskWaitResponse>> UpdateLoanLandingStatus()
    {
        var correlationId = Request.HttpContext.GetOrCreateCorrelationId();
        try
        {
            var loanApplicationToken = await Request.GetLoanApplicationTokenAsync(tokenService);

            if (loanApplicationToken.FinancialInstitutionMemberId == null)
                return NotFound("Loan application not found");

            logger.LogInformation("Updating loan landing status. CorrelationId: {CorrelationId}", correlationId);

            var response = await loanApplicationStepsService.UpdateLoanLandingStatusAsync(
                loanApplicationToken.FinancialInstitutionMemberId.Value);

            logger.LogInformation("Loan landing status updated successfully. CorrelationId: {CorrelationId}", correlationId);

            return Ok(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error updating loan landing status. CorrelationId: {CorrelationId}", correlationId);
            return StatusCode(500, "An error occurred while updating loan landing status");
        }
    }

    [HttpPost("login-qcash-connect")]
    [AllowAnonymous]
    public async Task<ActionResult<LoginResponse>> LoginQCashConnect(LoginApiModelPost model)
    {
        var correlationId = Request.HttpContext.GetOrCreateCorrelationId();
        var result = await qcashConnectService.LoginQCashConnectAsync(model, correlationId);
        return result;
    }

    [HttpPost("login-fraud-control")]
    [AllowAnonymous]
    public async Task<LoginFraudControlResponse> LoginFraudControl(LoginFraudControlApiModelPost model)
    {
        var result = await qcashConnectService.LoginFraudControlAsync(model);
        return result;
    }

    [HttpGet("loan-hub")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<ActionResult<LoanHubApiModelGet>> GetLoanHub()
    {
        var correlationId = Request.HttpContext.GetOrCreateCorrelationId();

        try
        {
            var loanApplicationToken = await Request.GetLoanApplicationTokenAsync(tokenService);

            if (loanApplicationToken.FinancialInstitutionMemberId == null || loanApplicationToken.LanguageCode == null)
                return NotFound("Loan application not found");

            var loanHubModel = await loanApplicationStepsService.GetLoanHubStepAsync(
                loanApplicationToken.FinancialInstitutionMemberId.Value);

            logger.LogInformation("Loan hub data retrieved successfully. CorrelationId: {CorrelationId}", correlationId);

            return Ok(loanHubModel);
        }
        catch (ArgumentException ex)
        {
            logger.LogWarning(ex, "Invalid request for loan hub");
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving loan hub data. CorrelationId: {CorrelationId}", correlationId);
            return StatusCode(500, "An error occurred while retrieving loan hub data");
        }
    }

    [HttpPost("loan-hub")]
    [SsoAuthorization]
    [TokenAuthorization]
    public async Task<ActionResult<Response>> ProcessLoanHub(LoanHubApiModelPost model) => await ProcessLoanApplicationStepAsync(
        model,
        loanApplicationStepsService.ProcessLoanHubStepAsync,
        nameof(ProcessLoanHub));

    private async Task<ActionResult<Response>> ProcessLoanApplicationStepAsync<TModel>(
        TModel model,
        Func<Guid, TModel, Task<Response>> processStep,
        string methodName)
    {
        var correlationId = Request.HttpContext.GetOrCreateCorrelationId();
        try
        {
            var loanApplicationToken = await Request.GetLoanApplicationTokenAsync(tokenService);

            if (loanApplicationToken.FinancialInstitutionMemberId == null)
                return NotFound("Loan application not found");

            logger.LogInformation("Processing {MethodName} submission. CorrelationId: {CorrelationId}", methodName, correlationId);

            var response = await processStep(
                loanApplicationToken.FinancialInstitutionMemberId.Value, model);

            logger.LogInformation(
                "{MethodName} processed successfully. CorrelationId: {CorrelationId}, Status: {Status}",
                methodName,
                correlationId,
                response.Status);

            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            logger.LogWarning(ex, "Invalid request for {MethodName}. {CorrelationId}", methodName, correlationId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing {MethodName}. CorrelationId: {CorrelationId}", methodName, correlationId);

            return StatusCode(500, new Response
            {
                Status = "Error",
                Description = $"An error occurred while processing {methodName}",
                Errors = [ex.Message],
            });
        }
    }

    private string GetAdditionalHeaders()
    {
        var headers = new StringBuilder();

        // Collect relevant headers for core provider integration
        foreach (var header in Request.Headers)
        {
            if ((header.Key.StartsWith("X-", StringComparison.OrdinalIgnoreCase) ||
                header.Key.Equals("User-Agent", StringComparison.OrdinalIgnoreCase) ||
                header.Key.Equals("Accept-Language", StringComparison.OrdinalIgnoreCase)) && !StringValues.IsNullOrEmpty(header.Value))
            {
                headers.AppendLine($"{header.Key}: {string.Join(", ", header.Value!)}");
            }
        }

        return headers.ToString();
    }

    private async Task<string> GetLoanApplicationToken(Guid? financialInstitutionMemberId, string memberId, string baseAccount)
    {
        var availableToken = await tokenService.GetAvailableApplicationTokenAsync();

        var languageCode = await loanApplicationService.GetLanguageCodeFromLastLoanApplicationAsync(baseAccount, memberId);
        try
        {
            await tokenService.InsertWebApiTokenAsync(financialInstitutionMemberId, availableToken, languageCode);
        }
        catch (Exception)
        {
            return string.Empty;
        }

        var tokenData = Encoding.UTF8.GetBytes(availableToken);
        return Convert.ToBase64String(tokenData);
    }
}
