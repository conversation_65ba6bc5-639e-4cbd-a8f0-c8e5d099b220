using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QCash.Models.Api;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.Interfaces;

namespace QCash.WebApi.Controllers;

[ApiController]
[Route("{fiSlug}/api/financial-institution")]
public class FinancialInstitutionController(
    IFiSettingService settingService,
    IApplicationKeyService applicationKeyService,
    IFinancialInstitutionConfigurationService financialInstitutionConfigurationService) : ControllerBase
{
    [HttpGet("settings")]
    [AllowAnonymous]
    public async Task<ActionResult<FinancialInstitutionSettingsModel>> GetFinancialInstitutionSettings()
    {
        var result = await settingService.GetSettingsResponseAsync();

        return result == null ? NotFound("Settings not found for FI.") : result;
    }

    [HttpGet("application-keys")]
    [AllowAnonymous]
    public async Task<ActionResult<ApplicationKeysModel>> GetApplicationKeys()
    {
        var applicationKeys = await applicationKeyService.GetPageDataAsync();

        if (applicationKeys == null)
            return NotFound("Application keys not found for FI.");

        var result = new ApplicationKeysModel
        {
            ApplicationSsoKey = applicationKeys.ApplicationSsoKey,
            SharedKeyWeb = applicationKeys.SharedKeyWeb!,
        };
        return result;
    }

    [HttpGet("configurations")]
    [AllowAnonymous]
    public async Task<List<FinancialInstitutionConfigurationModel>> GetAllFinancialInstitutionConfigurations()
    {
        var result = await financialInstitutionConfigurationService.GetAllFinancialInstitutionConfigurationsAsync();
        return result;
    }
}
