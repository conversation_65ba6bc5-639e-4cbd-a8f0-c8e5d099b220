using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using QCash.Common.Enums;
using QCash.Data.Extensions;
using QCash.Models.Api;
using QCash.Models.Api.Get;
using QCash.Service.Models.LoanApplication;
using QCash.Service.Services.FIConfiguration.Interfaces;
using QCash.Service.Services.General.Interfaces;
using QCash.Service.Services.Interfaces;
using QCash.Service.Services.LoanApplication.Interfaces;
using QCash.Service.Services.MemberInterface.Interfaces;
using QCash.WebApi.Extensions;
using QCash.WebApi.Filters;
// ReSharper disable RouteTemplates.MethodMissingRouteParameters

namespace QCash.WebApi.Controllers;

[Route("{fiSlug}/api/member-interface")]
[ApiController]
public class MemberInterfaceController(
    IMemberInterfacePresentationService memberInterfacePresentationService,
    IMemberProcessInterfaceService memberProcessInterfaceService,
    ILanguageService languageService,
    ILoanApplicationStepsService loanApplicationStepsService,
    ITokenService tokenService,
    IFiSettingService settingService,
    IQCashConnectService qCashConnectSettingService,
    ILogger<MemberInterfaceController> logger) : ControllerBase
{
    [HttpGet("available-languages-extended")]
    public async Task<ActionResult<List<LanguageApiModel>>> GetAvailableLanguagesExtended()
    {
        var languages = await languageService.GetLanguagesAsync();

        var result = languages.Select(language =>
            new LanguageApiModel { Id = language.Id, Code = language.LanguageCode, Name = language.Name, });

        return Ok(result);
    }

    [HttpGet("general-error")]
    public async Task<IEnumerable<DisplayTextApiModel>> GetGeneralError(Guid? languageId)
    {
        var language = await languageService.GetLanguageByIdAsync(languageId);
        var formatted = await memberProcessInterfaceService.GetGeneralErrorInterfaceTextAsync(language.Id, Enums.OriginApp.Api);

        return [.. formatted.Select(x => new DisplayTextApiModel { Key = x.Key, Value = x.Value })];
    }

    [HttpGet("initiate-error")]
    public async Task<ActionResult<InitiateErrorApiModelGet>> GetInitiateError(Guid? languageId)
    {
        var language = await languageService.GetLanguageByIdAsync(languageId);
        var formattedInterfaceTexts =
            await memberProcessInterfaceService.GetFormattedInterfaceTextsForInitiateErrorAsync(language.Id);
        var model = new InitiateErrorApiModelGet
        {
            Languages = [],
            SelectedLanguageCode = language.LanguageCode,
            DisplayTexts = [.. formattedInterfaceTexts.Select(text => new DisplayTextApiModel { Key = text.Key, Value = text.Value })]
        };

        var languages = await languageService.GetLanguagesAsync();
        var filteredLanguages = languages.Where(a => a.LanguageCode != language.LanguageCode);

        foreach (var lang in filteredLanguages)
        {
            model.Languages.Add(new AvailableLanguageApiModel { Key = lang.Id.ToString(), Value = lang.Name });
        }

        return Ok(model);
    }

    /// <summary>
    /// Gets member's interface presentation.
    /// </summary>
    [HttpGet("presentation")]
    [AllowAnonymous]
    public async Task<WizardMemberInterfacePresentation> GetMemberInterfacePresentationAsync()
    {
        var presentation = await memberInterfacePresentationService.GetMemberInterfacePresentationAsync();
        return presentation ?? new WizardMemberInterfacePresentation();
    }

    [HttpGet("maintenance")]
    public async Task<ActionResult<MaintenanceApiModelGet>> GetMaintenance(Guid? languageId)
    {
        var language = await languageService.GetLanguageByIdAsync(languageId);
        var formattedInterfaceTexts = await memberProcessInterfaceService.GetFormattedInterfaceTextsForStepAsync(
            null, language.Id, Service.Models.Core.Enums.LoanApplicationStep.Maintenance);

        var maintenanceModel = new MaintenanceApiModelGet
        {
            Step = nameof(LoanApplicationStep.Maintenance),
            SelectedLanguageCode = !string.IsNullOrEmpty(language.LanguageCode) ? language.LanguageCode : "en",
            DisplayValues = [.. formattedInterfaceTexts.Select(text => new DisplayTextApiModel { Key = text.Key, Value = text.Value })],
        };

        return Ok(maintenanceModel);
    }

    [HttpGet("default-texts")]
    [AllowAnonymous]
    public async Task<List<DisplayTextApiModel>> GetDefaultTextsAsync(Guid? languageId)
    {
        var language = await languageService.GetLanguageByIdAsync(languageId);
        var texts = await memberProcessInterfaceService.GetFirebirdWizardDefaultTextsAsync(language.Id);
        return [.. texts.Select(text => new DisplayTextApiModel
        {
            Key = text.Key,
            Value = text.Value,
        })];
    }

    [HttpGet("loan-landing")]
    [SsoAuthorization]
    public async Task<ActionResult<MaskWaitApiModelGet>> GetLoanLanding(Guid? languageId)
    {
        var correlationId = Request.HttpContext.GetOrCreateCorrelationId();
        try
        {
            var loanApplicationToken = await Request.GetLoanApplicationTokenAsync(tokenService);

            logger.LogInformation("Getting loan landing data. CorrelationId: {CorrelationId}", correlationId);

            var loanLandingModel = await loanApplicationStepsService.GetLoanLandingInterfaceTextsWithLanguageAsync(
                loanApplicationToken.Token, languageId, false);

            logger.LogInformation("Loan landing data retrieved successfully. CorrelationId: {CorrelationId}", correlationId);

            return Ok(loanLandingModel);
        }
        catch (ArgumentException ex)
        {
            logger.LogWarning(ex, "Invalid request for loan landing. CorrelationId: {CorrelationId}", correlationId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving loan landing data. CorrelationId: {CorrelationId}", correlationId);
            return StatusCode(500, "An error occurred while retrieving loan landing data");
        }
    }

    [HttpGet("login-texts")]
    [AllowAnonymous]
    public async Task<LoginPresentationModelGet> GetLoginInterfaceTextsAsync(Guid? languageId)
    {
        var loginFraudControlSettings = await qCashConnectSettingService.GetSettingsAsync();
        var language = await languageService.GetLanguageByIdAsync(languageId);

        if (!loginFraudControlSettings.IsFraudControlEnabled)
            throw new ApplicationException("Feature is disabled.");

        var requiredDigits = await settingService.GetRequiredDigitsAsync();
        var interfaceTexts = await memberProcessInterfaceService.GetFormattedInterfaceTextsForLoginAsync(language.Id);
        var presentation = await memberInterfacePresentationService.GetMemberInterfacePresentationAsync();

        var result = new LoginPresentationModelGet
        {
            LogoUrl = presentation?.LogoFilePath,
            DisplayValues = [.. interfaceTexts.Select(text => new DisplayTextApiModel
            {
                Key = text.Key,
                Value = text.Value,
            })],
            AccountIdRequiredDigits = requiredDigits,
            PreferredDelivery = loginFraudControlSettings.PreferredDelivery,
        };

        return result;
    }
}
