<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <UserSecretsId>6eac9df2-d5ab-4623-b50c-3782cfd596de</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.AzureKeyVault" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.System" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.Uris" Version="9.0.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.8.0" />
    <PackageReference Include="Azure.Identity" Version="1.14.0" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.SqlServer" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.17" />
    <PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
    <PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.DataProtection" Version="8.0.17" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="AspNetCore.HealthChecks.AzureApplicationInsights" Version="9.0.0" />
    <PackageReference Include="Log4Net.Appenders" Version="1.0.5" />
    <PackageReference Include="Microsoft.ApplicationInsights.Log4NetAppender" Version="2.23.0" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.Development.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
    <Content Update="log4net.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="log4net.dev.config">
      <DependentUpon>log4net.config</DependentUpon>
    </Content>
    <Content Update="log4net.Development.config">
      <DependentUpon>log4net.config</DependentUpon>
    </Content>
    <Content Update="log4net.Production.config">
      <DependentUpon>log4net.config</DependentUpon>
    </Content>
    <Content Update="log4net.QA.config">
      <DependentUpon>log4net.config</DependentUpon>
    </Content>
    <Content Update="log4net.Stage.config">
      <DependentUpon>log4net.config</DependentUpon>
    </Content>
    <Content Update="log4net.Train.config">
      <DependentUpon>log4net.config</DependentUpon>
    </Content>
    <Content Update="appsettings.dev.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
    <Content Update="appsettings.Production.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
    <Content Update="appsettings.QA.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
    <Content Update="appsettings.Stage.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
    <Content Update="appsettings.Train.json">
      <DependentUpon>appsettings.json</DependentUpon>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\QCash.Data\QCash.Data.csproj" />
    <ProjectReference Include="..\QCash.Models.Api\QCash.Models.Api.csproj" />
    <ProjectReference Include="..\QCash.Service\QCash.Service.csproj" />
    <ProjectReference Include="..\QCash.Utils\QCash.Utils.csproj" />
  </ItemGroup>

</Project>
