using Microsoft.ApplicationInsights.Extensibility.PerfCounterCollector.QuickPulse;
using QCash.WebApi.HealthChecks;
using QCash.Service.Utils;

namespace QCash.WebApi;

public class AppConfiguration
{
    public WebApplication ConfigureApp(WebApplication app)
    {
        if (app.Environment.IsDevelopment() || app.Environment.IsDev() || app.Environment.IsQa())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        app.UseHttpsRedirection();

        app.MapControllerRoute(
            name: "default with tenant and area",
            pattern: "{FiSlug}/api/{area:exists}/{controller=Home}/{action=Index}/{id?}");

        app.MapControllerRoute(
            name: "default with tenant",
            pattern: "{FiSlug}/api/{controller=Home}/{action=Index}/{id?}");

        app.MapHealthChecks("/healthz").AllowAnonymous();
        app.UseMiddleware<HealthCheckCachingMiddleware>(TimeSpan.FromSeconds(10));

        app.UseMiddleware<MultiTenantMiddleware>();
        return app;
    }
}
