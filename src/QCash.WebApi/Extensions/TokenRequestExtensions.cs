using System.Text;
using QCash.Data.Models;
using QCash.Service.Services.Interfaces;

namespace QCash.WebApi.Extensions;

public static class TokenRequestExtensions
{
    public static async Task<LoanApplicationToken> GetLoanApplicationTokenAsync(this HttpRequest request, ITokenService tokenService)
    {
        var token = request.Headers["Token"].FirstOrDefault();
        ArgumentException.ThrowIfNullOrEmpty(token, "Token header is missing");
        var decodedSecurityToken = DecodeSecurityToken(token);
        var loanApplicationToken = await tokenService.GetLoanApplicationTokenAsync(decodedSecurityToken);
        ArgumentNullException.ThrowIfNull(loanApplicationToken, "Can't find Loan Application Token");

        return loanApplicationToken;
    }

    private static string DecodeSecurityToken(string token) => Encoding.UTF8.GetString(Convert.FromBase64String(token));
}