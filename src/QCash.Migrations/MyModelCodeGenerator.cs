using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Scaffolding;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore.Design.Internal;
using Microsoft.EntityFrameworkCore.Scaffolding.Internal;

namespace QCash.Migrations;

[SuppressMessage("Usage", "EF1001:Internal EF Core API usage.")]
public class MyModelCodeGenerator(
    ModelCodeGeneratorDependencies dependencies,
    IOperationReporter operationReporter,
    IServiceProvider serviceProvider
    ) : IModelCodeGenerator
{
    public ScaffoldedModel GenerateModel(IModel model, ModelCodeGenerationOptions options)
    {
        var defaultGenerator = new CSharpModelGenerator(dependencies, operationReporter, serviceProvider);
        var scaffoldedModel = defaultGenerator.GenerateModel(model, options);

        scaffoldedModel.ContextFile.Code = scaffoldedModel.ContextFile.Code
            .Replace(".HasDefaultValue(", ".HasDbDefaultValue(")
            .Replace($"namespace {options.ContextNamespace};", $$"""
                namespace {{options.ContextNamespace}};

                public static class PropertyBuilderExtensions
                {
                    public static Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder<TProperty> HasDbDefaultValue<TProperty>(
                        this Microsoft.EntityFrameworkCore.Metadata.Builders.PropertyBuilder<TProperty> propertyBuilder,
                        object? value)
                        => propertyBuilder.HasDefaultValue(value).HasSentinel(value);
                }
                """);

        return scaffoldedModel;
    }

    public string Language => "C#";
}




