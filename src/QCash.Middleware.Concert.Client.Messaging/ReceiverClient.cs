using Azure.Messaging.ServiceBus;
using Azure.Storage.Blobs;
using QCash.Middleware.Concert.Client.Messaging.MessagePack;
using System;
using System.IO;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;

namespace QCash.Middleware.Concert.Client.Messaging;

public class ReceiverClient : IAsyncDisposable
{
    #region Fields

    private readonly ServiceBusProcessor _processor;
    private readonly string? _queueName;
    private readonly TimeSpan? _queueTimeToLive;

    private readonly Aes? _symmetricAlgorithm;
    private readonly BlobContainerClient? _blobContainerClient;
    private readonly Action<Action> _backgroundTask;
    #endregion

    #region Constructors

    public ReceiverClient(ServiceBusClient serviceBusClient, string queueName, TimeSpan? queueTimeToLive, Action<Action> backgroundTask)
    {
        _processor = serviceBusClient.CreateProcessor(queueName, new ServiceBusProcessorOptions()
        {
            AutoCompleteMessages = true,
            MaxConcurrentCalls = Environment.ProcessorCount,
            PrefetchCount = Environment.ProcessorCount * 100
        });
        _queueTimeToLive = queueTimeToLive;
        _backgroundTask = backgroundTask;
    }

    public ReceiverClient(ServiceBusClient serviceBusClient, Aes symmetricAlgorithm, BlobContainerClient blobContainerClient, string queueName, TimeSpan? queueTimeToLive, Action<Action> backgroundTask)
    {
        _processor = serviceBusClient.CreateProcessor(queueName, new ServiceBusProcessorOptions()
        {
            AutoCompleteMessages = true,
            MaxConcurrentCalls = Environment.ProcessorCount,
            PrefetchCount = Environment.ProcessorCount * 100,
            ReceiveMode = ServiceBusReceiveMode.ReceiveAndDelete
        });
        _queueName = queueName;
        _queueTimeToLive = queueTimeToLive;
        _symmetricAlgorithm = symmetricAlgorithm;
        _blobContainerClient = blobContainerClient;
        _backgroundTask = backgroundTask;
    }

    #endregion

    #region Receive Methods

    public async Task<Task> OnMessageAsync(Func<ConcertMessage, Task> callback)
    {
        _processor.ProcessMessageAsync += (args) => OnMessageInternalAsync(args.Message, callback, args.CancellationToken);

        _processor.ProcessErrorAsync += OnMessageProcessingException;

        //TODO JM - figure out how to handle this properly with backgroundTask replacement
        //_backgroundTask.Invoke(async () => await _processor.StartProcessingAsync().ConfigureAwait(false));
        await _processor.StartProcessingAsync().ConfigureAwait(false);

        return Task.CompletedTask;
    }

    private async Task OnMessageProcessingException(ProcessErrorEventArgs? args)
    {
        if (args == null || args.CancellationToken.IsCancellationRequested)
        {
            return;
        }

        if (args.Exception is ServiceBusException serviceBusException
            && serviceBusException.Reason == ServiceBusFailureReason.MessagingEntityNotFound)
        {
            await ServiceBusFactory.CreateQueueAsync(_processor.FullyQualifiedNamespace, args.EntityPath, _queueTimeToLive, CancellationToken.None).ConfigureAwait(false);
        }
    }

    private async Task OnMessageInternalAsync(ServiceBusReceivedMessage message, Func<ConcertMessage, Task> callback, CancellationToken cancellationToken)
    {
        if (!cancellationToken.IsCancellationRequested)
        {
            ConcertMessage concertMessage;

            if (message.ApplicationProperties.TryGetValue(Strings.ContentEncodingTkn, out var value) && value is string contentEncoding)
            {
                //If stored in storage then get from storage.
                var stream = contentEncoding.Contains(Strings.StorageContentEncoding)
                             && message.ApplicationProperties.TryGetValue(Strings.StoredNameTkn, out value)
                             && value is string blobName
                    ? await GetFromStorageAsync(blobName).ConfigureAwait(false)
                    : message.Body.ToStream();

                //If encrypted then decrypt.
                if (contentEncoding.Contains(Strings.EncryptedContentEncoding))
                {
                    stream = Decrypt(stream);
                }

                try
                {
                    //If compressed then decompress.
                    if (contentEncoding.Contains(Strings.DeflateContentEncoding))
                    {
                        MemoryStream decompressedStream = ConcertMessage.StreamPool.GetStream(Strings.DeflateStream);

                        try
                        {
                            await using (var compressionStream = new DeflateStream(stream, CompressionMode.Decompress))
                            {
                                await compressionStream.CopyToAsync(decompressedStream, cancellationToken);
                            }

                            decompressedStream.Position = 0;
                        }
                        catch
                        {
                            await decompressedStream.DisposeAsync();

                            throw;
                        }

                        await stream.DisposeAsync();
                        stream = decompressedStream;
                    }

                    concertMessage = MessagePackContentSerializer.Deserialize(stream);
                }
                finally
                {
                    await stream.DisposeAsync();
                }
            }
            else
            {
                concertMessage = MessagePackContentSerializer.Deserialize(message.Body.ToMemory());
            }

            await callback(concertMessage).ConfigureAwait(false);
        }
    }


    private async Task<Stream> GetFromStorageAsync(string blobName)
    {
        var client = _blobContainerClient ?? throw new InvalidOperationException("No Azure Blob Storage has been configured (StorageAccountName).");
        var blobClient = client.GetBlobClient(blobName);

        //Initial size is 1mb
        var stream = ConcertMessage.StreamPool.GetStream(Strings.BlobStream);

        var response = await blobClient.DownloadToAsync(stream).ConfigureAwait(false);

        if (response?.IsError == true)
        {
            throw new Azure.RequestFailedException(response);
        }

        //We will ignore it if it fails, so may require manual clean up.
        _ = blobClient.DeleteAsync();

        //Skip passed most header fields
        stream.Seek(8, SeekOrigin.Begin);

        var buffer = new byte[4];

        //Content-Type
        MoveLength(stream, buffer);
        //Content-Encoding
        MoveLength(stream, buffer);

        //Move to start of content, skipping content length
        stream.Seek(8, SeekOrigin.Current);

        return stream;
    }

    private static unsafe void MoveLength(MemoryStream stream, byte[] buffer)
    {
        var read = stream.Read(buffer, 0, buffer.Length);

        int length;

        fixed (byte* ptr = buffer)
        {
            length = *(int*)ptr;
        }

        stream.Seek(length, SeekOrigin.Current);
    }

    private MemoryStream Decrypt(Stream content)
    {
        var symmetricAlgorithm = _symmetricAlgorithm ?? throw new InvalidOperationException("No Azure KeyVault has been configured (KeyVaultAccountName).");

        var length = Convert.ToInt32(content.Length);
        var blockSize = symmetricAlgorithm.BlockSize;

        length = ((length / blockSize) + 1) * blockSize;

        MemoryStream cipherStream = ConcertMessage.StreamPool.GetStream(Strings.EncryptedStream, length);

        try
        {
            using var cryptoTransformer = _symmetricAlgorithm.CreateDecryptor(symmetricAlgorithm.Key, symmetricAlgorithm.IV);
            using var cryptoStream = new CryptoStream(cipherStream, cryptoTransformer, CryptoStreamMode.Write, true);
            content.CopyTo(cryptoStream);

            if (!cryptoStream.HasFlushedFinalBlock)
            {
                cryptoStream.FlushFinalBlock();
            }

            cipherStream.Position = 0;
        }
        catch
        {
            cipherStream.Dispose();

            throw;
        }

        content.Dispose();

        cipherStream.Position = 0;

        return cipherStream;
    }

    public async ValueTask DisposeAsync()
    {
        await _processor.DisposeAsync();

        GC.SuppressFinalize(this);
    }


    #endregion
}
