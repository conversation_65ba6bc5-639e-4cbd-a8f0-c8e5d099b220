using MessagePack;
using MessagePack.Formatters;
using System;
using System.Collections.Generic;
using System.Buffers;

namespace QCash.Middleware.Concert.Client.Messaging;

[MessagePackFormatter(typeof(ConcertMessageFormatter))]

public partial class ConcertMessage
{
    public class ConcertMessageFormatter : IMessagePackFormatter<ConcertMessage>
    {
        private const string? NullString = null;

        public ConcertMessageFormatter() { }


        public ConcertMessage Deserialize(ref MessagePackReader reader, MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil())
            {
                return default;
            }

            var domainId = NativeGuidFormatter.Instance.Deserialize(ref reader, options);
            var agentId = NativeGuidFormatter.Instance.Deserialize(ref reader, options);
            var id = NativeGuidFormatter.Instance.Deserialize(ref reader, options);
            //PreviousId
            _ = NativeGuidFormatter.Instance.Deserialize(ref reader, options);
            var messageSetId = NativeGuidFormatter.Instance.Deserialize(ref reader, options);

            ConcertMessage message = new ConcertMessage
            {
                Id = id,
                MessageSetId = messageSetId,
                AgentId = agentId,
                DomainId = domainId,
            };

            //MatchingInfo
            reader.ReadString();
            //Hops
            MessagePackSerializer.Deserialize<Queue<string>>(ref reader, options);

            message.ServiceId = NativeGuidFormatter.Instance.Deserialize(ref reader, options);
            message.ServiceSpecificInfo = reader.ReadString();
            message.MessageDataTypeName = reader.ReadString();
            message.MessageDataTypeId = NativeGuidFormatter.Instance.Deserialize(ref reader, options);
            message.TransactionTypeName = reader.ReadString();
            message.ContentType = reader.ReadString();

            message.CreatedAtUtc = new DateTime(reader.ReadInt64(), DateTimeKind.Utc);
            //TimeoutAtUtc
            reader.ReadInt64();

            message.Data = reader.ReadBytes()?.ToArray();

            return message;
            ////ExtraHelperInfo
            //MessagePackSerializer.Deserialize<Dictionary<string, string>>(ref reader, options);
            //message.MessagingHelperInfo = MessagePackSerializer.Deserialize<Dictionary<string, string>>(ref reader, options) ?? new Dictionary<string, string>(5);

            ////Flags
            //reader.ReadUInt64();
            ////State
            //reader.ReadUInt64();

            //return message;
        }

        /// <summary>
        /// Serializes a value.
        /// </summary>
        /// <param name="writer">The writer to use when serializing the value.</param>
        /// <param name="value">The value to be serialized.</param>
        /// <param name="options">The serialization settings to use, including the resolver to use to obtain formatters for types that make up the composite type />.</param>
        /// <exception cref="NotImplementedException"></exception>
        public void Serialize(ref MessagePackWriter writer, ConcertMessage value, MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();

                return;
            }

            NativeGuidFormatter.Instance.Serialize(ref writer, value.DomainId, options);
            NativeGuidFormatter.Instance.Serialize(ref writer, value.AgentId, options);
            NativeGuidFormatter.Instance.Serialize(ref writer, value.Id, options);
            //PreviousId
            NativeGuidFormatter.Instance.Serialize(ref writer, Guid.Empty, options);
            NativeGuidFormatter.Instance.Serialize(ref writer, value.MessageSetId, options);
            //MatchingInfo
            writer.Write(NullString);
            //Hops
            writer.WriteNil();
            NativeGuidFormatter.Instance.Serialize(ref writer, value.ServiceId, options);

            writer.Write(value.ServiceSpecificInfo);
            writer.Write(value.MessageDataTypeName);

            NativeGuidFormatter.Instance.Serialize(ref writer, value.MessageDataTypeId, options);
            writer.Write(value.TransactionTypeName);
            writer.Write(value.ContentType);
            writer.Write(value.CreatedAtUtc.Ticks);
            //TimeoutAtUtc
            writer.Write(DateTime.MinValue.Ticks);
            writer.Write(value.Data);
            //ExtraHelperInfo
            writer.WriteNil();

            if (value.MessagingHelperInfo?.Count > 0)
            {
                MessagePackSerializer.Serialize(ref writer, value.MessagingHelperInfo, options);
            }
            else
            {
                writer.WriteNil();
            }

            //Flags
            writer.Write(0);
            //State
            writer.Write(0);
        }

    }
}
