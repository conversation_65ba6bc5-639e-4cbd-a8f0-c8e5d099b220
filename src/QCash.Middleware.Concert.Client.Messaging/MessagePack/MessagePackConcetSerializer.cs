using MessagePack;
using MessagePack.Resolvers;
using System;
using System.IO;

namespace QCash.Middleware.Concert.Client.Messaging.MessagePack;

internal static class MessagePackContentSerializer
{
    private static readonly MessagePackSerializerOptions DefaultOptions = ContractlessStandardResolverAllowPrivate.Options;


    #region Methods

    public static void Serialize(Stream stream, ConcertMessage message) => MessagePackSerializer.Serialize(stream, message, DefaultOptions);

    public static ConcertMessage Deserialize(ReadOnlyMemory<byte> content) => MessagePackSerializer.Deserialize<ConcertMessage>(content, DefaultOptions);

    public static ConcertMessage Deserialize(Stream stream) => MessagePackSerializer.Deserialize<ConcertMessage>(stream, DefaultOptions);

    #endregion
}
