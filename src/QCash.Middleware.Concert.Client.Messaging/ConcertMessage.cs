using Microsoft.IO;
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace QCash.Middleware.Concert.Client.Messaging;

public partial class ConcertMessage
{
    internal static readonly RecyclableMemoryStreamManager StreamPool = new();

    public ConcertMessage()
    { }

    public ConcertMessage(Guid messageId, Guid messageSetId)
    {
        Id = messageId;
        MessageSetId = messageSetId;
        CreatedAtUtc = DateTime.UtcNow;
        ContentType = Strings.XmlContentType;
    }

    /// <summary>
    /// The domain identifier.
    /// </summary>
    [DataMember]
    public Guid DomainId { get; set; }

    /// <summary>
    /// The agent identifier.
    /// </summary>
    [DataMember]
    public Guid AgentId { get; set; }

    /// <summary>
    /// Gets or sets the message identifier.
    /// </summary>
    /// <value>
    /// The identifier.
    /// </value>
    [DataMember]
    public Guid Id { get; protected set; }

    /// <summary>
    /// The message set identifier.
    /// </summary>
    [DataMember]
    public Guid MessageSetId { get; protected set; }

    /// <summary>
    /// Gets or sets the created at UTC date time.
    /// </summary>
    /// <value>
    /// The created at UTC date time.
    /// </value>
    [DataMember]
    public DateTime CreatedAtUtc { get; set; }

    /// <summary>
    /// The service identifier.
    /// </summary>
    [DataMember]
    public Guid ServiceId { get; set; }

    /// <summary>
    /// The service specific information.
    /// </summary>
    [DataMember]
    public string? ServiceSpecificInfo { get; set; }

    /// <summary>
    /// The message data type name.
    /// </summary>
    [DataMember]
    public string? MessageDataTypeName { get; set; }

    /// <summary>
    /// The message data type identifier.
    /// </summary>
    [DataMember]
    public Guid MessageDataTypeId { get; set; }

    /// <summary>
    /// The transaction type name.
    /// </summary>
    [DataMember]
    public string? TransactionTypeName { get; set; }

    /// <summary>
    /// The content type, we set this to MIME Media Type http://www.iana.org/assignments/media-types/media-types.xhtml
    /// </summary>
    [DataMember]
    public string? ContentType { get; protected set; }


    /// <summary>
    /// Gets or sets the data.
    /// </summary>
    /// <value>
    /// The data.
    /// </value>
    [DataMember]
    public byte[] Data { get; set; } = [];

    /// <summary>
    /// The messaging helper information.
    /// </summary>
    /// <remarks>
    /// This is for internal use.
    /// </remarks>
    [DataMember]
    public Dictionary<string, string>? MessagingHelperInfo { get; set; }
}
