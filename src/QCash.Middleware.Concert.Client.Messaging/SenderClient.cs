using Azure.Messaging.ServiceBus;
using Azure.Storage.Blobs;
using QCash.Middleware.Concert.Client.Messaging.MessagePack;
using System;
using System.IO;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.IO;

namespace QCash.Middleware.Concert.Client.Messaging;

public class SenderClient
{
    #region Fields
    //Break down of the header
    //private static readonly byte[] MaxExpireTime = new byte[] { 0xff, 0x3f, 0x37, 0xf4, 0x75, 0x28, 0xca, 0x2b };
    //private static readonly byte[] ContentTypeLength = new byte[] { 0x15, 0x00, 0x00, 0x00 };
    //private static readonly byte[] ContentType = new byte[] { 0x61, 0x70, 0x70, 0x6C, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6F, 0x6E, 0x2F, 0x78, 0x2D, 0x6D, 0x73, 0x67, 0x70, 0x61, 0x63, 0x6B };
    //private static readonly byte[] ContentEncodingLength = new byte[] { 0x0D, 0x00, 0x00, 0x00 };
    //private static readonly byte[] ContentEncoding = new byte[] { 0x46, 0x72, 0x6F, 0x6D, 0x4D, 0x65, 0x73, 0x73, 0x61, 0x67, 0x69, 0x6E, 0x67 };
#pragma warning disable IDE0055
    private static readonly byte[] BlobHeader = [0xff, 0x3f, 0x37, 0xf4, 0x75, 0x28, 0xca, 0x2b, 0x15, 0x00, 0x00, 0x00, 0x61, 0x70, 0x70, 0x6C, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6F, 0x6E, 0x2F, 0x78, 0x2D, 0x6D, 0x73, 0x67, 0x70, 0x61, 0x63, 0x6B, 0x0D, 0x00, 0x00, 0x00, 0x46, 0x72, 0x6F, 0x6D, 0x4D, 0x65, 0x73, 0x73, 0x61, 0x67, 0x69, 0x6E, 0x67
    ];
#pragma warning restore IDE0055
    private readonly ServiceBusSender _internalClient;
    private readonly string? _queueName;

    private readonly Aes? _symmetricAlgorithm;
    private readonly BlobContainerClient? _blobContainerClient;

    #endregion

    #region Contructors

    public SenderClient(ServiceBusClient serviceBusClient, string queueName) => _internalClient = serviceBusClient.CreateSender(queueName);

    public SenderClient(ServiceBusClient serviceBusClient, Aes symmetricAlgorithm, BlobContainerClient blobContainerClient, string queueName)
    {
        _internalClient = serviceBusClient.CreateSender(queueName);
        _queueName = queueName;
        _symmetricAlgorithm = symmetricAlgorithm;
        _blobContainerClient = blobContainerClient;
    }

    #endregion

    #region Send methods

    public Task SendAsync(ConcertMessage message, TimeSpan requestTimeout, CancellationToken cancellationToken)
    {
        var stream = ConcertMessage.StreamPool.GetStream();
        var messageId = message.Id.ToString();
        try
        {
            MessagePackContentSerializer.Serialize(stream, message);

            var contentEncoding = string.Empty;

            if (stream.Length > 1024)
            {
                var zippedStream = ConcertMessage.StreamPool.GetStream(Strings.DeflateStream);

                try
                {
                    stream.Position = 0;

                    using (var compressionStream = new DeflateStream(zippedStream, CompressionMode.Compress, true))
                    {
                        stream.WriteTo(compressionStream);
                    }

                    zippedStream.Flush();
                    zippedStream.Seek(0, SeekOrigin.Begin);
                }
                catch
                {
                    zippedStream.Dispose();

                    throw;
                }

                stream.Dispose();
                stream = zippedStream;

                contentEncoding = Strings.DeflateContentEncoding;
            }

            if (_symmetricAlgorithm != null)
            {
                stream = Encrypt(stream);

                if (string.IsNullOrEmpty(contentEncoding))
                {
                    contentEncoding = Strings.EncryptedContentEncoding;
                }
                else
                {
                    contentEncoding += $" {Strings.EncryptedContentEncoding}";
                }
            }

            if (stream.Length >= 256000)
            {
                return SendLargeMessageAsync(messageId, contentEncoding, stream, cancellationToken);
            }

            var serviceBusMessage = new ServiceBusMessage(stream.ToArray())
            {
                ContentType = Strings.MsgPackContentType,
                MessageId = messageId,
                TimeToLive = requestTimeout,
            };

            if (!string.IsNullOrEmpty(contentEncoding))
            {
                serviceBusMessage.ApplicationProperties.Add(Strings.ContentEncodingTkn, contentEncoding);
            }

            return _internalClient.SendMessageAsync(serviceBusMessage, cancellationToken);
        }
        finally
        {
            stream.Dispose();
        }
    }

    private async Task SendLargeMessageAsync(string messageId, string contentEncoding, MemoryStream stream, CancellationToken cancellationToken)
    {
        var blobName = _queueName + messageId;

        var client = _blobContainerClient ?? throw new InvalidOperationException("No Azure Blob Storage has been configured (StorageAccountName).");
        var blobClient = client.GetBlobClient(blobName);


        await using (var storageStream = ConcertMessage.StreamPool.GetStream(Strings.BlobStream))
        {
            storageStream.Write(BlobHeader, 0, BlobHeader.Length);
            storageStream.Write(BitConverter.GetBytes(stream.Length), 0, 8);

            stream.Seek(0, SeekOrigin.Begin);
            stream.WriteTo(storageStream);

            storageStream.Seek(0, SeekOrigin.Begin);

            await blobClient.UploadAsync(storageStream, cancellationToken).ConfigureAwait(false);
        }

        if (string.IsNullOrEmpty(contentEncoding))
        {
            contentEncoding = "stored";
        }
        else
        {
            contentEncoding += " stored";
        }

        var serviceBusMessage = new ServiceBusMessage(Array.Empty<byte>())
        {
            ContentType = Strings.MsgPackContentType,
            MessageId = messageId,
        };

        if (!string.IsNullOrEmpty(contentEncoding))
        {
            serviceBusMessage.ApplicationProperties.Add(Strings.ContentEncodingTkn, contentEncoding);
            serviceBusMessage.ApplicationProperties.Add(Strings.StoredNameTkn, blobName);
        }

        try
        {
            await _internalClient.SendMessageAsync(serviceBusMessage, cancellationToken).ConfigureAwait(false);
        }
        catch
        {
            //We will delete from storage if send fails, but not wait for it to complete.
            _ = blobClient.DeleteIfExistsAsync(cancellationToken: CancellationToken.None);

            throw;
        }
    }

    private RecyclableMemoryStream Encrypt(MemoryStream content)
    {
        var symmetricAlgorithm = _symmetricAlgorithm ?? throw new InvalidOperationException("No Azure KeyVault has been configured (KeyVaultAccountName).");

        //iv = FixIV(iv);

        var length = Convert.ToInt32(content.Length);
        var blockSize = symmetricAlgorithm.BlockSize;

        length = ((length / blockSize) + 1) * blockSize;

        RecyclableMemoryStream cipherStream = ConcertMessage.StreamPool.GetStream(Strings.EncryptedStream, length);

        try
        {
            using var cryptoTransformer = symmetricAlgorithm.CreateEncryptor(symmetricAlgorithm.Key, symmetricAlgorithm.IV);
            using var cryptoStream = new CryptoStream(cipherStream, cryptoTransformer, CryptoStreamMode.Write, true);
            content.WriteTo(cryptoStream);

            if (!cryptoStream.HasFlushedFinalBlock)
            {
                cryptoStream.FlushFinalBlock();
            }

            cipherStream.Position = 0;
        }
        catch
        {
            cipherStream.Dispose();

            throw;
        }

        content.Dispose();

        cipherStream.Position = 0;

        return cipherStream;
    }

    #endregion
}
