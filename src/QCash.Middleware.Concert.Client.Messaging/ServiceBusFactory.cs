using Azure;
using Azure.Core;
using Azure.Identity;
using Azure.Messaging.ServiceBus;
using Azure.Messaging.ServiceBus.Administration;
using Azure.Security.KeyVault.Secrets;
using Azure.Storage.Blobs;
using System;
using System.Collections.Concurrent;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;

namespace QCash.Middleware.Concert.Client.Messaging;

public static class ServiceBusFactory
{
    private static readonly ConcurrentDictionary<string, ServiceBusClient> _serviceBusClients = new(StringComparer.InvariantCultureIgnoreCase);
    private static readonly ConcurrentDictionary<string, BlobContainerClient> _blobClients = new(StringComparer.InvariantCultureIgnoreCase);
    private static readonly ConcurrentDictionary<string, SecretClient> _secretClients = new(StringComparer.InvariantCultureIgnoreCase);

    private static readonly Lazy<TokenCredential> _tokenCredential = new(() =>
    {
        return new DefaultAzureCredential(new DefaultAzureCredentialOptions()
        {
            ExcludeAzureCliCredential = false,
            ExcludeEnvironmentCredential = false,
            ExcludeManagedIdentityCredential = false,
            ExcludeSharedTokenCacheCredential = false,
            ExcludeInteractiveBrowserCredential = true,
            ExcludeVisualStudioCodeCredential = false,
            ExcludeVisualStudioCredential = false,
        });
    }, LazyThreadSafetyMode.ExecutionAndPublication);


    internal static TokenCredential GetCredential()
    {
        return _tokenCredential.Value;
    }

    internal static SecretClient GetOrCreateSecretClient(string keyValutName, int maxRetries)
    {
        return _secretClients.GetOrAdd(keyValutName, (accountName) =>
        {
            return new SecretClient(new Uri($"https://{accountName}.vault.azure.net"), credential: GetCredential(), new SecretClientOptions()
            {
                Retry =
                {
                    MaxRetries = maxRetries,	//The maximum number of retry attempts before giving up
					Mode = RetryMode.Exponential,        //The approach to use for calculating retry delays
					MaxDelay = TimeSpan.FromMilliseconds(90000)
                },
            });
        });
    }

    public static BlobContainerClient GetOrCreateBlobContainerClient(string accountName, string containerName, int maxRetries)
    {
        return _blobClients.GetOrAdd(containerName, (key) =>
        {
            var client = new BlobContainerClient(new Uri($"https://{accountName}.blob.core.windows.net/{containerName}"), GetCredential(), new BlobClientOptions
            {
                Retry =
                {
                    MaxRetries = maxRetries,	//The maximum number of retry attempts before giving up
					Mode = RetryMode.Exponential,        //The approach to use for calculating retry delays
					MaxDelay = TimeSpan.FromMilliseconds(90000)
                },
            });

            client.CreateIfNotExists();

            return client;
        });
    }

    internal static ServiceBusClient GetOrCreateServiceBusClient(string fullyQualifiedNamespace, int maxRetries)
    {
        return _serviceBusClients.GetOrAdd(fullyQualifiedNamespace, (fullyQualifiedNamespace) =>
        {
            return new ServiceBusClient(fullyQualifiedNamespace, GetCredential(), new ServiceBusClientOptions
            {
                RetryOptions = new ServiceBusRetryOptions
                {
                    MaxRetries = maxRetries,    //The maximum number of retry attempts before giving up
                    Mode = ServiceBusRetryMode.Exponential,        //The approach to use for calculating retry delays
                    MaxDelay = TimeSpan.FromMilliseconds(90000)
                }
            });
        });
    }

    internal static ServiceBusClient GetOrCreateServiceBusClient(string fullyQualifiedNamespace, string tenantId, string clientId, string clientSecret, int maxRetries)
    {
        return _serviceBusClients.GetOrAdd(fullyQualifiedNamespace, (fullyQualifiedNamespace) =>
        {
            return new ServiceBusClient(fullyQualifiedNamespace,
                new ClientSecretCredential(tenantId, clientId, clientSecret),
                new ServiceBusClientOptions
                {
                    RetryOptions = new ServiceBusRetryOptions
                    {
                        MaxRetries = maxRetries,
                        Mode = ServiceBusRetryMode.Exponential,
                    }
                });
        });
    }

    public static async Task<SenderClient> CreateSenderAsync(string fullyQualifiedNamespace, string queueName, TimeSpan? queueTimeToLive, int maxRetries)
    {
        await CreateQueueAsync(fullyQualifiedNamespace, queueName, queueTimeToLive, CancellationToken.None).ConfigureAwait(false);

        return new SenderClient(GetOrCreateServiceBusClient(fullyQualifiedNamespace, maxRetries), queueName);
    }

    public static async Task<SenderClient> CreateSenderAsync(string fullyQualifiedNamespace, Aes symmetricAlgorithm, BlobContainerClient containerClient,
        string queueName, int maxRetries, CancellationToken cancellationToken)
    {
        await CreateQueueAsync(fullyQualifiedNamespace, queueName, null, cancellationToken).ConfigureAwait(false);

        return new SenderClient(GetOrCreateServiceBusClient(fullyQualifiedNamespace, maxRetries), symmetricAlgorithm, containerClient, queueName);
    }

    public static async Task<ReceiverClient> CreateReceiverAsync(string fullyQualifiedNamespace,
        Aes symmetricAlgorithm, BlobContainerClient containerClient, string queueName,
        TimeSpan? queueTimeToLive, int maxRetries, Action<Action> backgroundTask, CancellationToken cancellationToken)
    {
        await CreateQueueAsync(fullyQualifiedNamespace, queueName, queueTimeToLive, cancellationToken).ConfigureAwait(false);

        return new ReceiverClient(GetOrCreateServiceBusClient(fullyQualifiedNamespace, maxRetries), symmetricAlgorithm, containerClient, queueName, queueTimeToLive, backgroundTask);
    }

    private static ServiceBusAdministrationClient CreateAdministrationClient(string fullyQualifiedNamespace)
    {
        return new ServiceBusAdministrationClient(fullyQualifiedNamespace, GetCredential());
    }

    public static async Task CreateQueueAsync(string fullyQualifiedNamespace, string queueName, TimeSpan? timeToLive, CancellationToken cancellationToken)
    {
        if (timeToLive.HasValue && timeToLive < TimeSpan.FromMinutes(5))
        {
            throw new ArgumentOutOfRangeException(nameof(timeToLive), "Time to live should at least 5 minutes");
        }

        try
        {
            var administrationClient = CreateAdministrationClient(fullyQualifiedNamespace);

            if (!await administrationClient.QueueExistsAsync(queueName, cancellationToken).ConfigureAwait(false))
            {
                var queueOptions = new CreateQueueOptions(queueName)
                {
                    DeadLetteringOnMessageExpiration = true,
                    EnableBatchedOperations = true,
                    EnablePartitioning = false, //TODO: Review with Q-Cash if they want I don't think it's needed
                    MaxSizeInMegabytes = 1024,
                    RequiresDuplicateDetection = false,
                    RequiresSession = false,
                };

                if (timeToLive.HasValue)
                {
                    queueOptions.AutoDeleteOnIdle = timeToLive.Value;
                }

                _ = await administrationClient.CreateQueueAsync(queueOptions, cancellationToken).ConfigureAwait(false);
            }
        }
        // TODO: remove as it would not be catched
        catch (ServiceBusException exception) when (exception.Reason == ServiceBusFailureReason.MessagingEntityAlreadyExists)
        {
            //queue already exists
        }

    }

    public static async Task<Aes> GetSymmetricKeyAsync(string keyVaultName, string keyName, CancellationToken cancellationToken)
    {
        Response<KeyVaultSecret>? result = null;

        var secretClient = GetOrCreateSecretClient(keyVaultName, 5);

        if (!cancellationToken.IsCancellationRequested)
        {
            Pageable<SecretProperties>? versions = null;

            try
            {
                versions = secretClient.GetPropertiesOfSecretVersions(keyName, cancellationToken: cancellationToken);
            }
            catch (RequestFailedException exception)
            {
                //If we don't have permission to list the secrets
                if (exception.Status == 403)
                {
                    result = await secretClient.GetSecretAsync(keyName, cancellationToken: cancellationToken).ConfigureAwait(false);
                }
                else
                {
                    throw;
                }
            }

            if (result == null && versions != null && versions.Any())
            {
                //Get the oldest secret version.
                var secretProperties = versions.OrderBy(a => a.CreatedOn).First();

                result = await secretClient.GetSecretAsync(secretProperties.Name, secretProperties.Version, cancellationToken).ConfigureAwait(false);
            }

        }

        cancellationToken.ThrowIfCancellationRequested();

        if (result == null)
        {
            //Failed to get secret. In practice this doesn't seem to have come up, but to please the scanner -
            throw new InvalidOperationException("Failed to obtain Secret from Azure Key Vault.");
        }

        byte[] buffer = Convert.FromBase64String(result.Value.Value);
        byte[] key, iv;

        using (MemoryStream stream = new(buffer))
        using (BinaryReader reader = new(stream))
        {
            int length = reader.ReadInt32();
            key = reader.ReadBytes(length);
            length = reader.ReadInt32();
            iv = reader.ReadBytes(length);
        }

        var aes = Aes.Create();

        aes.Key = key;
        aes.IV = iv;

        return aes;
    }
}
