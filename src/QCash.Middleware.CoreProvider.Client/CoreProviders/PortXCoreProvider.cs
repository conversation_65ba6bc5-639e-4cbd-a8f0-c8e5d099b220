using Irony.Parsing;
using QCash.Middleware.Messaging.Helpers;
using QCash.Middleware.CoreProvider.Client.DSL;
using QCash.Middleware.CoreProvider.Client.Helpers;
using QCash.Middleware.CoreProvider.Client.Services;
using QCash.LoanApplication;
using QCash.LoanApplication.ResponseMessages;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using System.Net.Http;
using QCash.Middleware.CoreProvider.Client.Models;
using Account = QCash.LoanApplication.Account;
using AccountTransactionHistory = QCash.LoanApplication.AccountTransactionHistory;
using GetTransactionHistoryResponse = QCash.LoanApplication.ResponseMessages.GetTransactionHistoryResponse;
using Member = QCash.LoanApplication.Member;
using OwnerShipType = QCash.LoanApplication.OwnerShipType;
using TransferRequest = QCash.LoanApplication.TransferRequest;
using Newtonsoft.Json;
using System.Security.Cryptography;
using System.Text;
using QCash.Middleware.Interfaces.Internal.Interfaces;
using QCash.LoanApplication.Models;

namespace QCash.Middleware.CoreProvider.Client.CoreProviders;

public class PortXCoreProvider(
    IHttpClientFactory httpClientFactory,
    ILogger logger,
    IPortXCoreProviderBase coreProviderBase,
    ICoreProviderRetryService retryService,
    ICacheProvider cacheProvider,
    IDecisionEngineHelperService decisionEngineHelperService,
    bool propagateApplicationAndCorrelationId) : IMiddlewareCoreProvider, IMiddlewareDecisionEngineParametersProvider
{
    protected IPortXCoreProviderBase CoreProviderBase { get; } = coreProviderBase;
    private readonly ICacheProvider _cacheProvider = cacheProvider;
    private readonly IDecisionEngineHelperService _decisionEngineHelperService = decisionEngineHelperService;
    private readonly bool _propagateApplicationAndCorrelationId = propagateApplicationAndCorrelationId;
    private readonly ICoreProviderRetryService _retryService = retryService;

    private readonly IHttpClientFactory _httpClientFactory = httpClientFactory;
    private readonly ILogger _logger = logger;

    public async Task<CreateAndFundLoanResult> CreateAndFundLoanAsync(CreateAndFundLoanRequest request)
    {
        var originationFeeRequest = MapTransferRequest(request.LoanOriginationFeeRequest!, _propagateApplicationAndCorrelationId);
        if (originationFeeRequest != null)
        {
            originationFeeRequest.CoreId = null;
            originationFeeRequest.LoanApplicationId = null;
        }

        var baseRequest = new CreateAndFundLoanRequestPortX
        {
            BaseAccountId = request.BaseAccount?.Id,
            Comment = request.Comment,
            CoreId = request.CoreId,
            Frequency = (int)request.Frequency,
            NewLoanAppId = request.NewLoanAppId,
            SetAutoPayment = request.SetAutoPayment,
            ToAccountId = request.ToAccount?.AccountId,
            TransferType = (int)request.TransferType,
            Amount = request.Amount,
            NewLoan = new Models.NewLoan
            {
                Abrv = request.NewLoan.Abrv,
                Amount = request.NewLoan.Amount,
                ApplicationDate = request.NewLoan.ApplicationDate,
                Description = request.NewLoan.Description,
                GLCode = request.NewLoan.GLCode,
                Id = request.NewLoan.Id,
                InterestRate = request.NewLoan.InterestRate,
                AdjustedApr = request.NewLoan.AdjustedApr,
                IsOpenEndedLoan = request.NewLoan.IsOpenEndedLoan,
                MaturityDate = request.NewLoan.MaturityDate,
                PaymentAmount = request.NewLoan.PaymentAmount,
                PaymentCounts = request.NewLoan.PaymentCounts,
                PaymentDate = request.NewLoan.PaymentDate,
                PaymentDueDay = request.NewLoan.PaymentDueDay,
                PaymentLoanAmount = request.NewLoan.PaymentLoanAmount,
                Type = (int)request.NewLoan.Type,
                CollateralCode = request.NewLoan.CollateralCode,
                Purpose = (request.ExtraTransferInfo?.Infoes.TryGetValue("LoanPurpose", out var info)).HasValue ? request.ExtraTransferInfo?.Infoes.GetValueOrDefault("LoanPurpose") : null,
                OriginationFeeTransfer = originationFeeRequest,
            },
        };

        if (_propagateApplicationAndCorrelationId)
        {
            baseRequest.CorrelationId = request.CorrelationId;
            baseRequest.ApplicationId = request.ApplicationId;
        }

        //[old qc6 comment] TODO: add duplication detection
        return (await CoreProviderBase.CreateAndFundLoanAsync(baseRequest)).Result;

    }

    public async Task<decimal> GetAverageMonthlyCheckingDepositBalanceAsync(
        Guid coreId,
        AverageCheckingDepositBalanceParameter parameters,
        string correlationId,
        int applicationId,
        string additionalHeaders,
        string clientTimeZone)
    {
        var memberParameters = new GetMemberParameter
        {
            BaseAccountId = parameters.BaseAccountId,
            ExcludedSubAccountTypes = parameters.ExcludedSubAccountTypes,
            ExcludedBaseAccountTypes = parameters.ExcludedBaseAccountTypes,
            AllowAllAccountStatesForExclusion = true
        };

        var member = await GetMemberAsync(coreId, memberParameters, correlationId, applicationId, takeFromCache: true);

        var dateDiff = parameters.EndDate.Subtract(parameters.StartDate).TotalDays;
        var daysToSearch = Convert.ToInt64(Math.Floor(dateDiff));

        await PopulateMemberTransactionHistoryAsync(
            member,
            additionalHeaders,
            daysToSearch,
            coreId,
            parameters.FinancialInstitutionMemberId,
            parameters.CustomCoreDate,
            correlationId,
            applicationId,
            takeFromCache: true);

        var includeRulesParseTreeNodes =
            _decisionEngineHelperService.PrepareParseTreeNodes(parameters.DepositsToBeIncludedForAggregateDepositTotal);
        var excludeRulesParseTreeNodes =
            _decisionEngineHelperService.PrepareParseTreeNodes(parameters.DepositsToBeExcludedForAggregateDepositTotal);

        var monthDiff = ((parameters.EndDate.Year - parameters.StartDate.Year) * 12) + parameters.EndDate.Month - parameters.StartDate.Month;

        var monthAverages = new List<decimal>();

        Parallel.For(0, monthDiff + 1, i =>
        {
            var newMonth = parameters.StartDate.AddMonths(i);
            var startDate = i > 0
                ? newMonth.AddDays((newMonth.Day - 1) * -1)
                : parameters.StartDate;

            var endOfMonth =
                startDate.AddDays(DateTime.DaysInMonth(startDate.Year, startDate.Month) - startDate.Day);

            var endDate = endOfMonth > parameters.EndDate ? parameters.EndDate : endOfMonth;

            var averageMonthlyCheckingDepositBalance = member.BaseAccounts?
                .Where(ba => ba.OwnerShip == OwnerShipType.Primary)
                .DefaultIfEmpty()
                .Average(ba => ba?.Accounts == null || ba.Accounts.Length < 1
                    ? 0.0m
                    : ba.Accounts.Where(a =>
                            parameters.CheckingShareTypes != null
                            && a != null
                            && a.OpenDate >= parameters.StartDate
                            && !a.IsInactive
                            && a.Category == AccountCategory.Share
                            && !a.IsExcluded
                            && parameters.CheckingShareTypes.Contains(a.Type))
                        .DefaultIfEmpty()
                        .Sum(a =>
                            a?.TransactionHistory == null
                                ? 0.0m
                                : a.TransactionHistory.Where(h =>
                                        _decisionEngineHelperService.CheckRulesForDepositAggregate(
                                            includeRulesParseTreeNodes, h, true)
                                        && !_decisionEngineHelperService.CheckRulesForDepositAggregate(
                                            excludeRulesParseTreeNodes, h, false)
                                        && h.PostedDate != null
                                        && h.PostedDate.Value >= startDate
                                        && h.PostedDate.Value <= endDate)
                                    .DefaultIfEmpty().Sum(h => h?.Amount ?? 0.0m))) ?? 0.0m;

            lock (monthAverages)
            {
                monthAverages.Add(averageMonthlyCheckingDepositBalance);
            }
        });

        return monthAverages.Count == 0 ? 0.0m : monthAverages.Average();
    }

    public async Task<GetContextResponse> GetContextAsync(Guid coreId, string password, string username) =>
        await CoreProviderBase.GetContextAsync(new Models.GetContextRequestPortX
        {
            CoreId = coreId,
            Account = username,
            Password = password
        });

    public async Task<CoreConnectionResponse> GetCoreConnectionStatusAsync(Guid coreId, string? correlationId = null, CancellationToken cancellationToken = default)
    {
        var request = new Models.GetCoreConnectionStatusRequest
        {
            CoreId = coreId,
        };
        if (_propagateApplicationAndCorrelationId)
        {
            request.CorrelationId = correlationId;
        }
        return await CoreProviderBase.GetCoreConnectionStatusAsync(request, cancellationToken);
    }

    public async Task<Core[]> GetCoreProvidersAsync(string? correlationId = null, CancellationToken cancellationToken = default)
    {
        var coreProviderResponse = await CoreProviderBase.GetCoreProvidersAsync(correlationId, cancellationToken);
        if (coreProviderResponse.Cores == null)
        {
            return [];
        }
        return coreProviderResponse.Cores;
    }

    public async Task<Member> GetMemberAsync(Guid coreId, GetMemberParameter member, string correlationId, int? applicationId = null, bool takeFromCache = false)
    {
        var request = new GetMemberRequestPortX
        {
            CoreId = coreId,
            BaseAccountId = member.BaseAccountId,
            TaxId = member.TaxId,
            MemberId = member.MemberId,
        };
        if (_propagateApplicationAndCorrelationId)
        {
            request.CorrelationId = correlationId;
            request.ApplicationId = applicationId;
        }

        GetMemberResponse response;
        var key = $"Member-{coreId}-{member.BaseAccountId}";
        var offset = DateTimeOffset.Now.AddMinutes(5);
        if (takeFromCache)
        {
            response = await _cacheProvider.GetOrAddAsync(key, async _ => await CoreProviderBase.GetMemberAsync(request), offset);
        }
        else
        {
            response = await CoreProviderBase.GetMemberAsync(request);
            try
            {
                _cacheProvider.Remove(key);
            }
            catch
            {
                // ignored
            }

            await _cacheProvider.AddAsync(key, response, offset);
        }

        if (response.Member.BaseAccounts != null)
        {
            foreach (var baseAccount in response.Member.BaseAccounts)
            {
                if (member.ExcludedBaseAccountTypes != null)
                {
                    baseAccount.IsExcluded = member.ExcludedBaseAccountTypes.Contains(baseAccount.Classification);
                }

                if (member.ExcludedSubAccountTypes == null)
                {
                    continue;
                }

                var accounts = member.AllowAllAccountStatesForExclusion
                    ? baseAccount.Accounts
                    : baseAccount.Accounts?.Where(a => !a.IsClosed && !a.IsChargedOff);

                if (accounts != null)
                {
                    foreach (var subAcc in accounts)
                    {
                        subAcc.IsExcluded = member.ExcludedSubAccountTypes.Contains(subAcc.Type);
                    }
                }
            }
        }

        return response.Member;
    }

    public async Task<TransferResponse> TransferAsync(TransferRequest req)
    {
        var request = MapTransferRequest(req, _propagateApplicationAndCorrelationId);

        return await CoreProviderBase.TransferAsync(request);
    }

    public async Task<DecisionEngineParameters> GetDecisionEngineParametersAsync(Guid coreId, DecisionEngineSearchParameters searchParams, string correlationId, int applicationId, string additionalHeaders, string timeZone)
    {
        var deferredLogs = new ConcurrentQueue<(string message, DateTime time)>();

        var memberParameters = new GetMemberParameter
        {
            BaseAccountId = searchParams.BaseAccountId,
            ExcludedSubAccountTypes = searchParams.ExcludedSubAccountTypes,
            ExcludedBaseAccountTypes = searchParams.ExcludedBaseAccountTypes,
            AllowAllAccountStatesForExclusion = true,
            MemberId = searchParams.MemberId
        };

        var member = await GetMemberAsync(coreId, memberParameters, correlationId, applicationId, takeFromCache: true);
        var daysToSearch = searchParams.Products?
            .Select(product => Math.Max(
                product.AggregateDepositTotalHistoryTimePeriod,
                Math.Max(product.DirectDepositTransactionCountHistoryPeriod, product.NumberOfElectronicTransactionsHistoryPeriod)))
            .Prepend(0)
            .Max();

        var accountTransactionHistory = await PopulateMemberTransactionHistoryAsync(
            member,
            additionalHeaders,
            daysToSearch ?? 365,
            coreId,
            searchParams.FinancialInstitutionMemberId,
            searchParams.CustomCoreDate,
            correlationId,
            applicationId,
            takeFromCache: true);

        var accTranHistory = new List<AccountTransactionHistory>();
        if (searchParams.NSFCountRuleSet.Rules?.Count() > 0)
        {
            var tranHistoryRequestsIncludeNSFCount = member.BaseAccounts?.SelectMany(x => x.Accounts).Where(acct => !acct.IsExcluded).Select(acct => new TransactionHistoryRequestPortX
            {
                AccountId = acct.AccountId,
                BaseAccountId = acct.BaseAccountId,
                Option = (int)GetHistoryQueryOption(acct.Category ?? string.Empty),
                StartDate = (searchParams.CustomCoreDate ?? DateTime.UtcNow).AddMonths(-1 * searchParams.NSFCountMonthsThreshold),
                EndDate = searchParams.CustomCoreDate ?? DateTime.UtcNow,
            }).ToList();

            var getTransactionHistoryRequestIncludeNSFCount = new GetTransactionHistoryRequestPortX
            {
                CoreId = coreId,
                FinancialInstitutionMemberId = searchParams.FinancialInstitutionMemberId,
                TransactionHistoryRequests = tranHistoryRequestsIncludeNSFCount,
            };
            if (_propagateApplicationAndCorrelationId)
            {
                getTransactionHistoryRequestIncludeNSFCount.CorrelationId = correlationId;
                getTransactionHistoryRequestIncludeNSFCount.ApplicationId = applicationId;
            }

            if (tranHistoryRequestsIncludeNSFCount?.Count > 0)
            {
                //Note - this is as designed in QCash6, but it makes what would appear to be an unnecessary extra request for transaction history.
                //Couldn't we take this information from the transaction history we already pulled down in PopulateMemberTransactionHistoryAsync?

                var accountTransactionHistories = (await GetTransactionHistoryAsync(getTransactionHistoryRequestIncludeNSFCount, additionalHeaders)).History;
                if (accountTransactionHistories != null)
                {
                    accTranHistory = accountTransactionHistories.ToList();
                }
                //was GetTransactionHistoryResponseAsync. Replaced with GetTransactionHistoryAsync implementation here that will feed cache
            }
        }

        try
        {
            var result = new DecisionEngineParameters();
            var productResults = new ConcurrentBag<ProductDecisionEngineParameters>();

            var context = System.Web.HttpContext.Current;
            if (searchParams.Products != null)
            {
                Parallel.ForEach(searchParams.Products, product =>
                {
                    System.Web.HttpContext.Current = context;
                    var productResult = new ProductDecisionEngineParameters
                    {
                        Code = product.Code,
                        Name = product.Name,
                        Type = product.Type,
                        DecisionModelId = product.DecisionModelId,
                        FIHandle = product.FIHandle,
                    };
                    productResults.Add(productResult);

                    _decisionEngineHelperService.CalculateMemberInGoodStanding(member, searchParams, product, productResult);
                    _decisionEngineHelperService.CalculateProductEligibilityRequirements(member, searchParams, product, productResult,
                        accTranHistory);
                    _decisionEngineHelperService.CalculateLoanAmount(member, product, productResult, searchParams, LogMessage);
                    _decisionEngineHelperService.CalculateScores(member, product, productResult, searchParams);
                    _decisionEngineHelperService.CalculateFilterQueries(member, product, productResult);
                });
            }

            //foreach (var message in deferredLogs.AsEnumerable())
            //{
            //    LogAppMessage(searchParams.FinancialInstitutionMemberId, message.message, message.time);
            //}

            result.LogEntries = new DecisionEngineLogEntry[deferredLogs.Count];
            var i = 0;
            foreach (var message in deferredLogs.AsEnumerable())
            {
                result.LogEntries[i] = new DecisionEngineLogEntry()
                {
                    Message = message.message,
                    Time = message.time,
                    FinancialInstitutionMemberId = searchParams.FinancialInstitutionMemberId
                };
                i++;
                //LogAppMessage(searchParams.FinancialInstitutionMemberId, message.message, message.time);
            }

            result.ProductResults = productResults.ToArray();

            result.History = accountTransactionHistory;
            //how does this compare/relate to accTranHistory?

            return result;
        }
        catch (Exception exception)
        {
            var decisionEngineLogEntry = new DecisionEngineLogEntry()
            {
                Message = $"WebRole DE calculation error: {exception.GetFullMessage()}",
                Time = DateTime.UtcNow,
                FinancialInstitutionMemberId = searchParams.FinancialInstitutionMemberId
            };
            //To allow us to pass back a DecisionEngineLogEntry, so that this can be put in the db just as before after passing back from MWR
            exception.Data["DecisionEngineLogEntry"] = decisionEngineLogEntry;
            //LogAppMessage(searchParams.FinancialInstitutionMemberId, $"WebRole DE calculation error: {exception.GetFullMessage()}");
            throw;
        }

        void LogMessage(string message) => deferredLogs.Enqueue((message, DateTime.UtcNow));
    }

    public Task<ProductTypeFilterExpressionParsingResult> ParseProductTypeFilterExpressionAsync(Guid coreId, string expression, string businessRuleSlug)
    {
        expression = expression.Replace("‘", "'").Replace("’", "'");
        var grammar = new ProductExpressionGrammar();
        var languageData = new LanguageData(grammar);
        var parser = new Parser(languageData);
        var parseResult = parser.Parse(expression);
        var isValid = parseResult.Root != null;
        var errorMessage = string.Empty;
        if (parseResult.Root == null && parseResult.ParserMessages is { Count: > 0 })
        {
            errorMessage = parseResult.ParserMessages[0].Message + ". ";
        }

        if (isValid && (businessRuleSlug == "deposits_to_be_included_for_aggregate_deposit_total" || businessRuleSlug == "deposits_to_be_excluded_for_aggregate_deposit_total"))
        {
            var depositParser = new AggregateDepositsDSLProcessor(new ExpressionHelper());
            var message = depositParser.Validate(expression);
            if (!string.IsNullOrWhiteSpace(message))
            {
                errorMessage += message + ".";
                isValid = false;
            }
        }

        var result = new ProductTypeFilterExpressionParsingResult { ErrorMessage = errorMessage, IsValid = isValid };

        return Task.FromResult(result);
    }

    #region Not Implemented

    public Task<Account> CreateShareAsync(
        Guid coreId,
        CreateShareAccountParameters parameters,
        string correlationId,
        int applicationId,
        string clientTimeZone) => throw new NotImplementedException();

    public Task<GatewayHealthCheckResponse> GatewayHealthCheckAsync(Guid coreId) => throw new NotImplementedException();

    public Task GetTransactionHistoryAsync(
        Guid coreId,
        Account account,
        HistoryQueryOption option,
        string transactionTypeFilter,
        DateTime startDate,
        DateTime endDate,
        ExtraInfo extraFilters) => throw new NotImplementedException();

    public Task<InsertTrackingRecordResponse> InsertTrackingRecordAsync(InsertTrackingRecordRequest request) =>
        throw new NotImplementedException();

    public Task<OrderCreditCardResponse> OrderCreditCardAsync(OrderCreditCardRequest req) =>
        throw new NotImplementedException();

    #endregion

    private static Models.TransferRequest? MapTransferRequest(TransferRequest? req, bool propagateApplicationAndCorrelationId)
    {
        if (req == null)
        {
            return null;
        }

        var request = new Models.TransferRequest
        {
            Amount = req.Amount,
            Comment = req.Comment,
            FromAccount = new Models.MemberAccount
            {
                AccountId = req.FromAccount.AccountId,
                BaseAccountId = req.FromAccount.BaseAccountId
            },
            TransferType = (int)req.TransferType,
            CoreId = req.CoreId,
            LoanApplicationId = req.LoanApplicationId,
        };
        if (propagateApplicationAndCorrelationId)
        {
            request.CorrelationId = req.CorrelationId;
            request.ApplicationId = req.ApplicationId;
        }

        if (req.TransferType is TransferType.Fee or TransferType.GLTransfer)
        {
            request.ToGLAccount = new Models.GLAccount
            {
                AccountNumber = req.ToAccount.GLAccountNumber,
                Branch = "000", //TODO, can not be mapped from Transfer request, add it as a setting in Admin Portal?
                StatementCode = "02" //TODO
            };
        }
        else
        {
            request.ToAccount = new Models.MemberAccount
            {
                AccountId = req.ToAccount.AccountId,
                BaseAccountId = req.ToAccount.BaseAccountId
            };
        }

        return request;
    }

    private static HistoryQueryOption GetHistoryQueryOption(string category) =>
        category.ToUpper() switch
        {
            "LOAN" => HistoryQueryOption.LoanOnly,
            "SHARE" => HistoryQueryOption.ShareOnly,
            _ => HistoryQueryOption.Everything
        };

    private async Task<AccountTransactionHistory[]> PopulateMemberTransactionHistoryAsync(
        Member member,
        string additionalHeaders,
        long daysToSearch,
        Guid coreId,
        Guid fiMemberId,
        DateTime? customCoreDate,
        string correlationId,
        int applicationId,
        bool takeFromCache = false)
    {
        var accounts = member.BaseAccounts?
            .Where(x => !x.IsExcluded)
            .SelectMany(x => x.Accounts)
            .Where(acct => !acct.IsExcluded)
            .ToList();

        var tranHistoryRequests = accounts?.Select(acct => new Models.TransactionHistoryRequestPortX
        {
            AccountId = acct.AccountId,
            BaseAccountId = acct.BaseAccountId,
            Option = (int)GetHistoryQueryOption(acct.Category ?? string.Empty),
            StartDate = (customCoreDate ?? DateTime.UtcNow).AddDays(-1 * daysToSearch),
            EndDate = customCoreDate ?? DateTime.UtcNow,
        }).ToList();

        GetTransactionHistoryResponse? getTranHistoryResponse = null;
        if (tranHistoryRequests != null && tranHistoryRequests.Count > 0)
        {
            var getTransactionHistoryRequest = new Models.GetTransactionHistoryRequestPortX
            {
                CoreId = coreId,
                FinancialInstitutionMemberId = fiMemberId,
                TransactionHistoryRequests = tranHistoryRequests,
            };
            if (_propagateApplicationAndCorrelationId)
            {
                getTransactionHistoryRequest.CorrelationId = correlationId;
                getTransactionHistoryRequest.ApplicationId = applicationId;
            }
            getTranHistoryResponse = await GetTransactionHistoryAsync(getTransactionHistoryRequest, additionalHeaders, takeFromCache);
            //was GetTransactionHistoryResponseAsync. Replaced with GetTransactionHistoryAsync implementation here that will feed cache
        }

        if (accounts != null)
        {
            foreach (var account in accounts)
            {
                if (getTranHistoryResponse?.History != null)
                {
                    account.TransactionHistory = getTranHistoryResponse.History
                        .Where(c => c.AccountId == account.AccountId && c.BaseAccountId == account.BaseAccountId)
                        .ToArray();
                }
                else
                {
                    account.TransactionHistory = [];
                }
            }
        }

        return getTranHistoryResponse?.History ?? new AccountTransactionHistory[] { };
    }

    private string GetTransactionHistoryRequestKeyForCache(GetTransactionHistoryRequestPortX req)
    {
        //key needs to be more specific than just baseAccountID because the accounts/dates requested could change
        //we json stringify getTransactionHistoryRequestPortX (making startdate and enddate just dates, not momentary datetimes)
        //then hash it and use that in the key

        var copyOfTransactionHistoryRequest = new GetTransactionHistoryRequestPortX();
        copyOfTransactionHistoryRequest.ApplicationId = req.ApplicationId;
        copyOfTransactionHistoryRequest.CoreId = req.CoreId;
        copyOfTransactionHistoryRequest.CorrelationId = req.CorrelationId;
        copyOfTransactionHistoryRequest.FinancialInstitutionMemberId = req.FinancialInstitutionMemberId;

        if (req.TransactionHistoryRequests != null)
        {
            var tranHistoryRequests = req.TransactionHistoryRequests.Select(acct => new TransactionHistoryRequestPortX
            {
                AccountId = acct.AccountId,
                BaseAccountId = acct.BaseAccountId,
                Option = acct.Option,
                StartDate = acct.StartDate,
                EndDate = acct.EndDate
            }).ToList();
            copyOfTransactionHistoryRequest.TransactionHistoryRequests = new List<TransactionHistoryRequestPortX>(tranHistoryRequests);
        }

        if (copyOfTransactionHistoryRequest.TransactionHistoryRequests != null)
        {
            for (var i = 0; i < copyOfTransactionHistoryRequest.TransactionHistoryRequests.Count; i++)
            {
                copyOfTransactionHistoryRequest.TransactionHistoryRequests[i].StartDate = copyOfTransactionHistoryRequest.TransactionHistoryRequests[i].StartDate.Date;
                copyOfTransactionHistoryRequest.TransactionHistoryRequests[i].EndDate = copyOfTransactionHistoryRequest.TransactionHistoryRequests[i].EndDate.Date;
            }
        }
        var stringedForm = JsonConvert.SerializeObject(copyOfTransactionHistoryRequest);

        StringBuilder sb = new StringBuilder();

        using (var hash = SHA256.Create())
        {
            Encoding enc = Encoding.UTF8;
            var result = hash.ComputeHash(enc.GetBytes(stringedForm));

            foreach (var b in result)
            {
                sb.Append(b.ToString("x2"));
            }
        }

        return sb.ToString();
    }


    public async Task<GetTransactionHistoryResponse> GetTransactionHistoryAsync(Guid coreId, Guid? financialInstitutionMemberId,
        List<TransactionHistoryParameter>? transactionHistoryParametersList, string? correlationId, int? applicationId = null,
        bool takeFromCache = false)
    {
        var getTransactionHistoryRequestPortX = new GetTransactionHistoryRequestPortX
        {
            FinancialInstitutionMemberId = financialInstitutionMemberId,
            TransactionHistoryRequests = new(),
            CoreId = coreId,
            //ApplicationId = applicationId,
            //CorrelationId = correlationId
        };

        if (transactionHistoryParametersList != null)
        {
            var tranHistoryRequests = transactionHistoryParametersList.Select(acct => new TransactionHistoryRequestPortX
            {
                AccountId = acct.AccountId,
                BaseAccountId = acct.BaseAccountId,
                Option = acct.Option,
                StartDate = acct.StartDate,
                EndDate = acct.EndDate
            }).ToList();
            getTransactionHistoryRequestPortX.TransactionHistoryRequests = new List<TransactionHistoryRequestPortX>(tranHistoryRequests);
        }

        //no current need for this
        var additionalHeaders = string.Empty;

        return await GetTransactionHistoryAsync(getTransactionHistoryRequestPortX, additionalHeaders, takeFromCache);
    }

    public async Task<GetTransactionHistoryResponse> GetTransactionHistoryAsync(GetTransactionHistoryRequestPortX getTransactionHistoryRequestPortX, string additionalHeaders, bool takeFromCache = false)
    {
        var coreId = getTransactionHistoryRequestPortX.CoreId;

        //only keep for 5 minutes; the primary use case for this is just for the qcash client to be able to request these in order to insert
        //into the DB
        var keyhash = GetTransactionHistoryRequestKeyForCache(getTransactionHistoryRequestPortX);
        var key = $"Transactions-{coreId}-{getTransactionHistoryRequestPortX.FinancialInstitutionMemberId}-{keyhash}";
        var offset = DateTimeOffset.Now.AddMinutes(5);

        GetTransactionHistoryResponse response;
        if (takeFromCache)
        {
            response = await _cacheProvider.GetOrAddAsync(key, async _ => await CoreProviderBase.GetTransactionHistoryAsync(getTransactionHistoryRequestPortX, additionalHeaders), offset);
        }
        else
        {
            response = await CoreProviderBase.GetTransactionHistoryAsync(getTransactionHistoryRequestPortX, additionalHeaders);
            try
            {
                _cacheProvider.Remove(key);
            }
            catch
            {
                // ignored
            }

            await _cacheProvider.AddAsync(key, response, offset);
        }
        return response;
    }

    protected virtual async Task<GetTransactionHistoryResponse> GetTransactionHistoryResponseAsync(GetTransactionHistoryRequestPortX getTransactionHistoryRequest, string additionalHeaders)
    {
        return await CoreProviderBase.GetTransactionHistoryAsync(getTransactionHistoryRequest, additionalHeaders);
    }

    //protected virtual void LogAppMessage(Guid financialInstitutionMemberId, string message, DateTime? time = null)
    //{
    //    //No longer stored this way; these are passed back in the response object as DecisionEngineLogEntries
    //}

    public Task<SupportedOperations> GetSupportedOperations(Guid coreId)
    {
        //currently no logic based on specific coreId, but we want to pass that in because future implementation may be more specific
        //at this time, these features are based on the type of middleware and the operations it supports
        SupportedOperations supportedOperations = new SupportedOperations
        {
            SupportsCreateShare = false,
            SupportsGetContext = true,
            SupportsGetTransactionHistory = true,
            SupportsInsertTrackingRecord = false,
            SupportsOrderCreditCard = false,
            SupportsAutomaticRetry = false,
            SupportsGatewayHealthCheck = false
        };

        return Task.FromResult(supportedOperations);
    }
}
