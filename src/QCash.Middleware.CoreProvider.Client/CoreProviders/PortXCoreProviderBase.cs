using Newtonsoft.Json;
using QCash.LoanApplication;
using QCash.LoanApplication.ResponseMessages;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace QCash.Middleware.CoreProvider.Client.CoreProviders;

public class PortXCoreProviderBase : IPortXCoreProviderBase
{
    private Token? _token;

    public string? ClientId { get; set; }
    public string? ClientSecret { get; set; }
    public string? AccessTokenUrl { get; set; }
    public string? BaseAddress { get; set; }
    private HttpClient? HttpClient { get; set; }
    public string DateFormat { get; set; } = "yyyy'-'MM'-'dd'T'HH':'mm':'ss";

    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger _logger;

    public PortXCoreProviderBase(IHttpClientFactory httpclientfactory, ILogger logger, string baseAddress, string accessTokenUrl, string clientSecret, string clientId)
    {
        _logger = logger;
        _httpClientFactory = httpclientfactory;

        BaseAddress = baseAddress;
        AccessTokenUrl = accessTokenUrl;
        ClientSecret = clientSecret;
        ClientId = clientId;

        HttpClient = _httpClientFactory.CreateClient("PortX");
        HttpClient.BaseAddress = new Uri(BaseAddress ?? string.Empty);
    }

    public async Task<GetCoreProvidersResponse> GetCoreProvidersAsync(string? correlationId = null, CancellationToken cancellationToken = default)
    {
        var path = "info/getCoreProviders";
        if (!string.IsNullOrWhiteSpace(correlationId))
        {
            path += $"?CorrelationId={correlationId}";
        }
        return await GetRequestAsync<GetCoreProvidersResponse>(path, cancellationToken);
    }

    public async Task<CoreConnectionResponse> GetCoreConnectionStatusAsync(Models.GetCoreConnectionStatusRequest request, CancellationToken cancellationToken = default)
    {
        var path = $"info/getCoreConnectionStatus/{request.CoreId}";
        if (!string.IsNullOrWhiteSpace(request.CorrelationId))
        {
            path += $"?CorrelationId={request.CorrelationId}";
        }
        return await GetRequestAsync<CoreConnectionResponse>(path, cancellationToken);
    }

    public async Task<GetMemberResponse> GetMemberAsync(Models.GetMemberRequestPortX request) =>
        await PostRequestAsync<Models.GetMemberRequestPortX, GetMemberResponse>(request, "members/getMember");

    public async Task<GetContextResponse> GetContextAsync(Models.GetContextRequestPortX request) =>
        await PostRequestAsync<Models.GetContextRequestPortX, GetContextResponse>(request, "members/getContextId");

    public async Task<GetTransactionHistoryResponse> GetTransactionHistoryAsync(Models.GetTransactionHistoryRequestPortX request, string additionalHeaders)
    {
        var headers = JsonConvert.DeserializeObject<(string, string)[]>(additionalHeaders);

        //this operation transforms the response from Client.Models.GetTransactionHistoryResponse to LoanApplication.ResponseMessages.GetTransactionHistoryResponse
        //LoanApplication.ResponseMessages.GetTransactionHistoryResponse is "oldModel"
        //Client.Models.GetTransactionHistoryResponse is "newModel"
        //purpose: TellerID needs to be in ExtraInfo rather than in the base class TellerID field in order to match the universal format
        var response = await PostRequestAsync<Models.GetTransactionHistoryRequestPortX, Models.GetTransactionHistoryResponse>(
            request, "transactions/getTransactionHistory", headers ?? []);
        var serialized = JsonConvert.SerializeObject(response);
        var rsp = JsonConvert.DeserializeObject<GetTransactionHistoryResponse>(serialized);

        if (rsp == null)
        {
            //note that this should not occur since a failure to parse should result in a different exception at DeserializeObject
            throw new InvalidOperationException("Deserialized transaction history response was null");
        }

        if (rsp.History != null)
        {
            for (var i = 0; i < rsp.History.Length; i++)
            {
                var newModel =
                    response.History!
                        [i]; //ok to do this because they match; if they didn't, a failure would have occurred above in JsonConvert operations
                var oldModel = rsp.History[i];

                oldModel.ExtraInfo = new ExtraInfo
                {
                    Infoes = new Dictionary<string, string>
                    {
                        { "USERNUMBER", newModel.TellerId ?? string.Empty },
                        { nameof(Models.AccountTransactionHistory.IsDeposit), newModel.IsDeposit.ToString() }
                    }
                };
                oldModel.TranType = new TransactionType { Original = newModel.TransactionType };
            }
        }

        return rsp;
    }

    public async Task<CreateAndFundLoanResponse> CreateAndFundLoanAsync(Models.CreateAndFundLoanRequestPortX request) =>
        await PostRequestAsync<Models.CreateAndFundLoanRequestPortX, CreateAndFundLoanResponse>(request,
            "accounts/createAndFundLoan");

    public async Task<TransferResponse> TransferAsync(Models.TransferRequest? request) =>
        await PostRequestAsync<Models.TransferRequest, TransferResponse>(request, "transactions/transfer");

    private async Task<TResponse> GetRequestAsync<TResponse>(string path, CancellationToken cancellationToken = default) where TResponse : QCashResponseBase
    {
        var message = new HttpRequestMessage(HttpMethod.Get, path);
        return await SendMessageAsync<TResponse>(message, cancellationToken);
    }

    private async Task<TResponse> PostRequestAsync<TRequest, TResponse>(TRequest? request, string path, params (string key, string value)[] headers)
        where TResponse : QCashResponseBase
    {
        var message = new HttpRequestMessage(HttpMethod.Post, path)
        {
            Content = new StringContent(JsonConvert.SerializeObject(request, new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
                DateFormatString = DateFormat,
            }), Encoding.UTF8, "application/json")
        };
        return await SendMessageAsync<TResponse>(message, headers: headers);
    }

    private async Task<TResponse> SendMessageAsync<TResponse>(HttpRequestMessage message, CancellationToken cancellationToken = default, params (string key, string value)[] headers) where TResponse : QCashResponseBase
    {
        if (!string.IsNullOrWhiteSpace(AccessTokenUrl))
        {
            await RefreshTokenAsync();
            message.Headers.Add("Authorization", $"Bearer {_token!.AccessToken}");
        }

        foreach (var header in headers)
        {
            message.Headers.Add(header.key, header.value);
        }

        var response = await HttpClient!.SendAsync(message, cancellationToken);

        TResponse rsp;
        try
        {
            rsp = await GetResponseAsync<TResponse>(response);
            if (response.IsSuccessStatusCode && rsp.Fault == null)
            {
                return rsp;
            }
            _logger.LogDebug("Failure response from PortX, received response: {Response}", await response.Content.ReadAsStringAsync());
        }
        catch (Exception e)
        {
            throw new Models.CoreProviderPortXException(
                new CoreProviderFault { ErrorMessage = e.Message },
                $"Error receiving response. (Http status code: {response.StatusCode})",
            e);
        }

        throw new Models.CoreProviderPortXException(rsp, $"Error receiving response. (Http status code: {response.StatusCode})");
    }

    private static async Task<TResponse> GetResponseAsync<TResponse>(HttpResponseMessage response)
        where TResponse : QCashResponseBase
    {
        var stringResponse = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
        try
        {
            TResponse? tresponse = JsonConvert.DeserializeObject<TResponse>(stringResponse);
            if (tresponse != null)
            {
                return tresponse;
            }
            else
            {
                throw new Exception($"Deserialized response was null: {stringResponse}");
            }
        }
        catch (Exception e)
        {
            throw new Exception($"Failed to deserialize: {stringResponse}", e);
        }
    }

    private async Task RefreshTokenAsync()
    {
        if (_token is { IsExpired: false })
            return;

        var form = new Dictionary<string, string>
        {
            { "grant_type", "client_credentials" },
            { "client_id", ClientId ?? string.Empty },
            { "client_secret", ClientSecret ?? string.Empty }
        };

        var tokenResponse = await HttpClient!.PostAsync(AccessTokenUrl, new FormUrlEncodedContent(form));
        var jsonContent = await tokenResponse.Content.ReadAsStringAsync();
        _token = JsonConvert.DeserializeObject<Token>(jsonContent);
        _token!.DateCreated = DateTime.UtcNow;
    }

    internal class Token
    {
        [JsonProperty("access_token")]
        public string? AccessToken { get; set; }

        [JsonProperty("token_type")]
        public string? TokenType { get; set; }

        [JsonProperty("expires_in")]
        public int ExpiresIn { get; set; }

        [JsonProperty("refresh_token")]
        public string? RefreshToken { get; set; }

        public DateTime DateCreated { get; set; }

        public bool IsExpired => DateTime.UtcNow.Subtract(DateCreated).TotalSeconds >= ExpiresIn + 60;
    }
}
