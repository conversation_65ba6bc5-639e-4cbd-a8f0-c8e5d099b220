using QCash.Middleware.Concert.Client;
using QCash.LoanApplication;
using QCash.LoanApplication.ResponseMessages;
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using QCash.Middleware.Interfaces.Internal.Interfaces;
using QCash.LoanApplication.Models;

namespace QCash.Middleware.CoreProvider.Client.CoreProviders;

public class ConcertCoreProvider : IMiddlewareCoreProvider, IMiddlewareDecisionEngineParametersProvider
{
    private readonly IQCashConcertClient _client;
    private readonly ILogger _logger;

    public ConcertCoreProvider(IQCashConcertClient client, ILogger logger)
    {
        _client = client;
        _logger = logger;
    }

    public async Task<CreateAndFundLoanResult> CreateAndFundLoanAsync(CreateAndFundLoanRequest request)
    {
        return await _client.CreateAndFundLoanAsync(request, CancellationToken.None);
    }

    public async Task<Account> CreateShareAsync(Guid coreId, CreateShareAccountParameters parameters, string correlationId, int applicationId, string clientTimeZone)
    {
        return await _client.CreateShareAsync(coreId, parameters, correlationId, applicationId, clientTimeZone, CancellationToken.None);
    }

    public Task<GatewayHealthCheckResponse> GatewayHealthCheckAsync(Guid coreId)
    {
        throw new NotImplementedException();
    }

    public async Task<decimal> GetAverageMonthlyCheckingDepositBalanceAsync(Guid coreId, AverageCheckingDepositBalanceParameter parameters, string correlationId, int applicationId, string additionalHeaders, string clientTimeZone)
    {
        return await _client.GetAverageMonthlyCheckingDepositBalanceAsync(coreId, parameters, correlationId, applicationId, clientTimeZone, CancellationToken.None);
    }

    public Task<GetContextResponse> GetContextAsync(Guid coreId, string password, string username)
    {
        throw new NotImplementedException();
    }

    public async Task<CoreConnectionResponse> GetCoreConnectionStatusAsync(Guid coreId, string? correlationId = null, CancellationToken cancellationToken = default) =>
        await _client.GetCoreConnectionStatusAsync(coreId, cancellationToken);

    public async Task<LoanApplication.Core[]> GetCoreProvidersAsync(string? correlationId = null, CancellationToken cancellationToken = default) =>
        await _client.GetCoreProvidersAsync(cancellationToken);

    public async Task<DecisionEngineParameters> GetDecisionEngineParametersAsync(Guid coreId, DecisionEngineSearchParameters searchParams, string correlationId, int appId, string additionalHeaders, string timeZone)
    {
        return await _client.GetDecisionEngineParametersAsync(coreId, searchParams, correlationId, appId, timeZone, CancellationToken.None);
    }

    public async Task<Member> GetMemberAsync(Guid coreId, GetMemberParameter member, string correlationId, int? applicationId = null, bool takeFromCache = false)
    {
        return await _client.GetMemberAsync(coreId, member, correlationId, CancellationToken.None);
    }

    public Task GetTransactionHistoryAsync(Guid coreId, Account account, HistoryQueryOption option, string transactionTypeFilter, DateTime startDate, DateTime endDate, ExtraInfo extraFilters)
    {
        throw new NotImplementedException();
        //_client.GetTransactionHistoryAsync(coreId, account, option, transactionTypeFilter, startDate, endDate, extraFilters).Wait();
    }

    public async Task<InsertTrackingRecordResponse> InsertTrackingRecordAsync(InsertTrackingRecordRequest request)
    {
        return await _client.InsertTrackingRecordAsync(request, CancellationToken.None);
    }

    public async Task<OrderCreditCardResponse> OrderCreditCardAsync(OrderCreditCardRequest req)
    {
        return await _client.OrderCreditCardAsync(req, CancellationToken.None);
    }

    public async Task<ProductTypeFilterExpressionParsingResult> ParseProductTypeFilterExpressionAsync(Guid coreId, string expression, string businessRuleSlug)
    {
        return await _client.ParseProductTypeFilterExpressionAsync(coreId, expression, CancellationToken.None);
    }

    public async Task<TransferResponse> TransferAsync(TransferRequest req)
    {
        return await _client.TransferAsync(req, CancellationToken.None);
    }

    public Task<SupportedOperations> GetSupportedOperations(Guid coreId)
    {
        //currently no logic based on specific coreId, but we want to pass that in because future implementation may be more specific
        //at this time, these features are based on the type of middleware and the operations it supports
        SupportedOperations supportedOperations = new SupportedOperations
        {
            SupportsCreateShare = true,
            SupportsGetContext = false,
            SupportsGetTransactionHistory = false,
            SupportsInsertTrackingRecord = true,
            SupportsOrderCreditCard = true,
            SupportsAutomaticRetry = true,
            SupportsGatewayHealthCheck = false
        };

        return Task.FromResult(supportedOperations);
    }
}
