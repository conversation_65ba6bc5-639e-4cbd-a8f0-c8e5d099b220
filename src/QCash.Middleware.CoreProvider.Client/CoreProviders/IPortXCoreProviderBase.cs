using QCash.LoanApplication.ResponseMessages;
using System.Threading;
using System.Threading.Tasks;

namespace QCash.Middleware.CoreProvider.Client.CoreProviders;

public interface IPortXCoreProviderBase
{
    Task<GetCoreProvidersResponse> GetCoreProvidersAsync(string? correlationId = null, CancellationToken cancellationToken = default);

    Task<CoreConnectionResponse> GetCoreConnectionStatusAsync(Models.GetCoreConnectionStatusRequest request, CancellationToken cancellationToken = default);

    Task<GetMemberResponse> GetMemberAsync(Models.GetMemberRequestPortX request);

    Task<GetContextResponse> GetContextAsync(Models.GetContextRequestPortX request);

    Task<GetTransactionHistoryResponse> GetTransactionHistoryAsync(Models.GetTransactionHistoryRequestPortX request, string additionalHeaders);

    Task<CreateAndFundLoanResponse> CreateAndFundLoanAsync(Models.CreateAndFundLoanRequestPortX request);

    Task<TransferResponse> TransferAsync(Models.TransferRequest? request);
}
