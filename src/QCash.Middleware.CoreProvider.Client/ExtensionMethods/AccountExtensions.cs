using QCash.LoanApplication;
using System.Collections.Generic;

namespace QCash.Middleware.CoreProvider.Client.ExtensionMethods;

public static class AccountExtensions
{
    public static void AddExtraInfo(this Account account, string key, string value)
    {
        if (account.ExtraInfo == null)
            account.ExtraInfo = new LoanApplication.ExtraInfo { Infoes = new Dictionary<string, string>() };

        if (account.ExtraInfo.Infoes.ContainsKey(key) == false)
            account.ExtraInfo.Infoes.Add(key, value);
    }

    public static string GetFormattedAccountType(this Account account)
    {
        if (string.IsNullOrEmpty(account.SubType))
        {
            return account.Type ?? string.Empty;
        }
        return $"{account.Type}.{account.SubType}";
    }

    public static bool IsValidLoanAccount(this Account account)
    {
        return account != null && !account.IsClosed && !account.IsInactive &&
               account.Category == AccountCategory.Loan && !account.IsExcluded;
    }

    public static bool IsValidLoanAccountIgnoreClosed(this Account account)
    {
        return account != null && account.Category == AccountCategory.Loan && !account.IsExcluded;
    }

    public static bool IsValidShareAccount(this Account account)
    {
        return account != null && !account.IsClosed && !account.IsInactive &&
               account.Category == AccountCategory.Share && !account.IsExcluded;
    }
}
