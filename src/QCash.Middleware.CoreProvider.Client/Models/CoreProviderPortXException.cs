using QCash.LoanApplication;
using QCash.LoanApplication.ResponseMessages;

namespace QCash.Middleware.CoreProvider.Client.Models;

public class CoreProviderPortXException : CoreProviderException
{
    public CoreProviderPortXException(
        QCashResponseBase response,
        string message,
        Exception? inner = null) : base(response.Fault, message, inner) => Response = response;

    public CoreProviderPortXException(
        CoreProviderFault fault,
        string message,
        Exception? inner = null) : base(fault, message, inner)
    {
    }

    public QCashResponseBase? Response { get; set; }
}
