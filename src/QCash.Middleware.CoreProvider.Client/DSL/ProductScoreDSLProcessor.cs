using System.ComponentModel;
using Irony.Parsing;
using QCash.LoanApplication;
using System;
using System.Collections.Generic;
using System.Linq;
using QCash.Middleware.CoreProvider.Client.DSL.Interfaces;
using QCash.Middleware.CoreProvider.Client.ExtensionMethods;
using QCash.Middleware.CoreProvider.Client.DSL.Constants;

namespace QCash.Middleware.CoreProvider.Client.DSL;

public class ProductScoreDSLProcessor : IProductScoreDSLProcessor
{
    private readonly Parser _parser;
    private static readonly HashSet<string> LegalShareField = ["CODE", "TYPE", "SUBTYPE"];
    private static readonly HashSet<string> LegalLoanField = ["CODE", "TYPE", "SUBTYPE"];
    private static readonly HashSet<string> LegalAccountField = ["TYPE", "SUBTYPE"];
    internal IExpressionHelper ExpressionHelper { get; set; }

    private enum AccountCategory
    {
        /// <summary>
        /// Base account
        /// </summary>
        [Description("BASE")]
        BaseAccount,

        /// <summary>
        /// Loan
        /// </summary>
        [Description("LOAN")]
        Loan,

        /// <summary>
        /// Share
        /// </summary>
        [Description("SHARE")]
        Share,
    }

    public ProductScoreDSLProcessor(IExpressionHelper expressionHelper)
    {
        var grammar = new ProductExpressionGrammar();
        var languageData = new LanguageData(grammar);
        _parser = new Parser(languageData);
        ExpressionHelper = expressionHelper;
    }

    public int GetNumberOfFiringOnMemberData(string expression, Member? memberReference, bool allowMultipleFiring)
    {
        if (memberReference?.BaseAccounts == null || memberReference.BaseAccounts?.Length == 0)
        {
            return 0;
        }

        expression = ExpressionHelper.GetCleanExpression(expression);

        var parseTree = _parser.Parse(expression);
        if (parseTree.Root == null)
        {
            return 0;
        }

        var result = TraverseParseTreeCalculation(parseTree.Root, memberReference);
        if (!allowMultipleFiring && result > 0)
        {
            return 1;
        }
        return result;
    }

    public int GetNumberOfFiringOnMemberData(string expression, Account? account, bool allowMultipleFiring)
    {
        if (account == null)
        {
            return 0;
        }

        expression = ExpressionHelper.GetCleanExpression(expression);

        var parseTree = _parser.Parse(expression);
        if (parseTree.Root == null)
        {
            return 0;
        }

        var result = TraverseParseTreeCalculation(parseTree.Root, account);
        if (!allowMultipleFiring && result > 0)
        {
            return 1;
        }
        return result;
    }

    public int GetNumberOfFiringOnMemberData(string expression, Member? member) =>
        GetNumberOfFiringOnMemberData(expression, member, true);

    private int CalculateMemberAccountsType(Member? member, string type, AccountCategory acctCategoryType)
    {
        var acctCategory = acctCategoryType.ToDescriptionString() ?? "SHARE";
        type = type.Substring(1, type.Length - 2);
        if (member?.BaseAccounts == null)
        {
            throw new Exception("member.BaseAccounts cannot be null");
        }

        foreach (var theBaseAcct in member.BaseAccounts.Where(a => a.Accounts != null))
        {
            var accts = GetAccountsByCategory(theBaseAcct, acctCategory);
            if (accts.Any(a => type.Equals(a?.Type)))
            {
                return 1;
            }
        }
        return 0;
    }

    private int CalculateAccountType(Account? account, string type)
    {
        type = type.Substring(1, type.Length - 2);
        return (type.Equals(account?.Type)) ? 1 : 0;
    }

    private int CalculateAccountSubType(Account? account, string subtype)
    {
        subtype = subtype.Substring(1, subtype.Length - 2);
        return (subtype.Equals(account?.SubType)) ? 1 : 0;
    }

    private int CalculateMemberAccountsCode(Member? member, string code, AccountCategory acctCategoryType)
    {
        var acctCategory = acctCategoryType.ToDescriptionString() ?? "SHARE";
        code = code.Substring(1, code.Length - 2);
        if (member?.BaseAccounts == null)
        {
            throw new Exception("member.BaseAccounts cannot be null");
        }
        foreach (var theBaseAcct in member.BaseAccounts.Where(a => a.Accounts != null))
        {
            var accts = GetAccountsByCategory(theBaseAcct, acctCategory);
            if (accts.Any(acct => acct?.ExtraInfo != null
                                  && acct.ExtraInfo.Infoes != null
                                  && acct.ExtraInfo.Infoes.ContainsKey(DSLConstants.ExtraInfoCodeKey)
                                  && code.Equals(acct.ExtraInfo.Infoes[DSLConstants.ExtraInfoCodeKey])))
            {
                return 1;
            }
        }
        return 0;
    }

    private IEnumerable<Account?> GetAccountsByCategory(BaseAccount? theBaseAcct, string acctCategory)
    {
        var result = theBaseAcct?.Accounts?
            .Where(a => !(a?.IsClosed ?? false)
                    && (acctCategory.Equals("BASE")
                    || acctCategory.Equals(a?.Category, StringComparison.CurrentCultureIgnoreCase)));
        return result ?? [];
    }

    private int CalculateMemberAccountsSubType(Member? member, string subtype, AccountCategory acctCategoryType)
    {
        var acctCategory = acctCategoryType.ToDescriptionString() ?? "SHARE";
        subtype = subtype.Substring(1, subtype.Length - 2);
        if (member?.BaseAccounts == null)
        {
            throw new Exception("member.BaseAccounts cannot be null");
        }
        foreach (var theBaseAcct in member.BaseAccounts.Where(a => a.Accounts != null))
        {
            var accts = GetAccountsByCategory(theBaseAcct, acctCategory);
            if (accts.Any(a => subtype.Equals(a?.SubType, StringComparison.CurrentCultureIgnoreCase)))
            {
                return 1;
            }
        }

        return 0;
    }

    private int CalculateMemberAttribute(Member? member, string attribute, string? attraValue, string operand)
    {
        if (operand.ToLower().Equals("has"))
        {
            attribute = attribute.Substring(1, attribute.Length - 2);
            if ((member?.ExtraInfo != null && member.ExtraInfo.Infoes != null) && member.ExtraInfo.Infoes.ContainsKey(attribute.ToUpper()))
            {
                return 1;
            }

            if (member?.GetType().GetProperty(attribute) != null)
            {
                return 1;
            }
        }
        else if (operand.ToLower().Equals("contains") && attraValue != null)
        {
            // Compare target is wrapped in single quotes, replace with (.)
            attraValue = attraValue.Replace("'", ".");

            if ((member?.ExtraInfo != null && member.ExtraInfo.Infoes != null) &&
                member.ExtraInfo.Infoes.ContainsKey(attribute.ToUpper()) && member.ExtraInfo.Infoes[attribute.ToUpper()].Contains(attraValue))
            {
                return 1;
            }
            return 0;
        }
        else
        {
            if (attraValue != null)
            {
                attraValue = attraValue.Substring(1, attraValue.Length - 2);
                if ((member?.ExtraInfo != null && member.ExtraInfo.Infoes != null) && member.ExtraInfo.Infoes.ContainsKey(attribute.ToUpper()) && member.ExtraInfo.Infoes[attribute.ToUpper()].Equals(attraValue))
                {
                    return 1;
                }
            }
            // Possible property of Member?
            var memberProp = member?.GetType().GetProperty(attribute);
            if (memberProp != null && $"{memberProp.GetValue(member, null)}".Equals(attraValue))
            {
                return 1;
            }
        }
        return 0;
    }
    private int CalculateAccountAttribute(Account? account, string attribute, string? attraValue, string operand)
    {
        if (operand.Equals("has", StringComparison.CurrentCultureIgnoreCase))
        {
            attribute = attribute.Substring(1, attribute.Length - 2);
            if ((account?.ExtraInfo != null && account.ExtraInfo.Infoes != null) && account.ExtraInfo.Infoes.ContainsKey(attribute.ToUpper()))
            {
                return 1;
            }

            if (account?.GetType().GetProperty(attribute) != null)
            {
                return 1;
            }
        }
        else if (operand.Equals("contains", StringComparison.CurrentCultureIgnoreCase))
        {
            // Compare target is wrapped in single quotes, replace with (.)
            attraValue = attraValue?.Replace("'", ".");

            if ((account?.ExtraInfo != null && account.ExtraInfo.Infoes != null)
                && account.ExtraInfo.Infoes.ContainsKey(attribute.ToUpper())
                && attraValue != null
                && account.ExtraInfo.Infoes[attribute.ToUpper()].Contains(attraValue))
            {
                return 1;
            }

            return 0;
        }
        else   // would be default 'is'
        {
            attraValue = attraValue?.Substring(1, attraValue.Length - 2);
            if ((account?.ExtraInfo != null && account.ExtraInfo.Infoes != null) && account.ExtraInfo.Infoes.ContainsKey(attribute.ToUpper()) && account.ExtraInfo.Infoes[attribute.ToUpper()].Equals(attraValue))
            {
                return 1;
            }

            // Possible property of Member?
            var memberProp = account?.GetType().GetProperty(attribute);
            if (memberProp != null && $"{memberProp.GetValue(account, null)}".Equals(attraValue))
            {
                return 1;
            }
        }
        return 0;
    }
    private int CalculateMemberAccountsAttribute(Member? member, string attribute, string attraValue, string operand, AccountCategory acctCategoryType)
    {
        var acctCategory = acctCategoryType.ToDescriptionString() ?? "SHARE";
        var score = 0;

        if (operand.ToLower().Equals("has"))
        {
            attribute = attribute.Substring(1, attribute.Length - 2);
        }
        else
        {
            attraValue = attraValue.Substring(1, attraValue.Length - 2);
        }

        foreach (var theBaseAcct in member?.BaseAccounts?.Where(a => a.Accounts != null) ?? [])
        {
            var accounts = GetAccountsByCategory(theBaseAcct, acctCategory);
            foreach (var acct in accounts)
            {
                if (acct is { ExtraInfo.Infoes: not null })
                {
                    if (acct.ExtraInfo.Infoes.ContainsKey(attribute.ToUpper()))
                    {
                        if (attraValue == null)
                        {
                            score = 1;
                            break;
                        }

                        if (acct.ExtraInfo.Infoes[attribute.ToUpper()].Equals(attraValue))
                        {
                            score = 1;
                            break;
                        }
                    }
                }
                // Possible property of Account?
                var acctProp = acct?.GetType().GetProperty(attribute);

                if (acctProp == null)
                {
                    continue;
                }

                if (attraValue != null)
                {
                    score = 1;
                    break;
                }

                if ($"{acctProp.GetValue(acct, null)}".Equals(attraValue))
                {
                    score = 1;
                    break;
                }
            }
        }
        return score;
    }

    private int TraverseParseTreeCalculation(ParseTreeNode node, Member? member)
    {
        if (node.Term.Name.Equals("Term"))
        {
            var token = node.ChildNodes[0].FindTokenAndGetText();
            var parts = ExpressionHelper.GetTermParts(token);

            return TraverseParseTreeCalculationForMember(node, parts, member);
        }

        var calculations = node.ChildNodes
            .Select(c => TraverseParseTreeCalculation(c, member)).ToArray();

        return TraverseParseTreeCalculationForNonTerm(node, calculations);
    }

    private int TraverseParseTreeCalculation(ParseTreeNode node, Account? account)
    {
        if (node.Term.Name.Equals("Term"))
        {
            var token = node.ChildNodes[0].FindTokenAndGetText();
            var parts = ExpressionHelper.GetTermParts(token);

            return TraverseParseTreeCalculationForAccount(node, parts, account);
        }

        var calculations = node.ChildNodes
            .Select(c => TraverseParseTreeCalculation(c, account)).ToArray();

        return TraverseParseTreeCalculationForNonTerm(node, calculations);
    }

    private int TraverseParseTreeCalculationForNonTerm(ParseTreeNode node, int[] calculations)
    {
        if (!string.Equals(node.Term.Name, DSLConstants.TermBinaryExpression))
        {
            return calculations.Sum();
        }

        if (string.Equals(node.ChildNodes[1].FindTokenAndGetText().ToUpper(), "AND"))
        {
            return Math.Min(calculations[0], calculations[2]);
        }
        return calculations.Sum();
    }

    private int TraverseParseTreeCalculationForAccount(ParseTreeNode node, TermParts parts, Account? account)
    {
        // Run through individual account (share, loan, or base)
        if (parts.Components == TermComponentType.WithProperty) // && account != null)
        {
            if (parts.Base != TermBaseTypeEnum.Account
                && parts.Base != TermBaseTypeEnum.Loan
                && parts.Base != TermBaseTypeEnum.Share)
            {
                return 0;
            }

            if (parts.Field == TermFieldTypeEnum.Type)
            {
                return CalculateAccountType(account, node.ChildNodes[2].FindTokenAndGetText());
            }

            if (parts.Field == TermFieldTypeEnum.SubType)
            {
                return CalculateAccountSubType(account, node.ChildNodes[2].FindTokenAndGetText());
            }

            return CalculateAccountAttribute(account, parts.FieldFullValue!, node.ChildNodes[2].FindTokenAndGetText(), node.ChildNodes[1].FindTokenAndGetText());
        }

        if (parts.Components == TermComponentType.SimpleBase)
        {
            if (parts.Base is TermBaseTypeEnum.Account or TermBaseTypeEnum.Loan or TermBaseTypeEnum.Share)
            {
                return CalculateAccountAttribute(account, node.ChildNodes[2].FindTokenAndGetText(), null, node.ChildNodes[1].FindTokenAndGetText());
            }

            return 0;
        }

        return 0;
    }

    private int TraverseParseTreeCalculationForMember(ParseTreeNode node, TermParts parts, Member? member)
    {
        if (parts.Components == TermComponentType.WithProperty)
        {
            // will be something like "SHARE.TYPE is 'xxxx'"
            if (parts.Base == TermBaseTypeEnum.Share)      // Check all member share accounts
            {
                if (parts.Field == TermFieldTypeEnum.Type)
                {
                    return CalculateMemberAccountsType(member, node.ChildNodes[2].FindTokenAndGetText(), AccountCategory.Share);
                }
                if (parts.Field == TermFieldTypeEnum.SubType)
                {
                    return CalculateMemberAccountsSubType(member, node.ChildNodes[2].FindTokenAndGetText(), AccountCategory.Share);
                }
                if (parts.Field == TermFieldTypeEnum.Code)
                {
                    return CalculateMemberAccountsCode(member, node.ChildNodes[2].FindTokenAndGetText(), AccountCategory.Share);
                }

                return CalculateMemberAccountsAttribute(member, parts.FieldFullValue!, node.ChildNodes[2].FindTokenAndGetText(), node.ChildNodes[1].FindTokenAndGetText(), AccountCategory.Share);
            }
            if (parts.Base == TermBaseTypeEnum.Loan)      // Check all member loan accounts
            {
                if (parts.Field == TermFieldTypeEnum.Type)
                {
                    return CalculateMemberAccountsType(member, node.ChildNodes[2].FindTokenAndGetText(), AccountCategory.Loan);
                }
                if (parts.Field == TermFieldTypeEnum.Code)
                {
                    return CalculateMemberAccountsCode(member, node.ChildNodes[2].FindTokenAndGetText(), AccountCategory.Loan);
                }
                if (parts.Field == TermFieldTypeEnum.SubType)
                {
                    return CalculateMemberAccountsSubType(member, node.ChildNodes[2].FindTokenAndGetText(), AccountCategory.Loan);
                }

                return CalculateMemberAccountsAttribute(member, parts.FieldFullValue!, node.ChildNodes[2].FindTokenAndGetText(), node.ChildNodes[1].FindTokenAndGetText(), AccountCategory.Loan);
            }
            if (parts.Base == TermBaseTypeEnum.Member)      // Check member itself
            {
                return CalculateMemberAttribute(member, parts.FieldFullValue!, node.ChildNodes[2].FindTokenAndGetText(), node.ChildNodes[1].FindTokenAndGetText());
            }
            if (parts.Base == TermBaseTypeEnum.Account)      // Check all member Base accounts
            {
                if (parts.Field == TermFieldTypeEnum.Type)
                {
                    return CalculateMemberAccountsType(member, node.ChildNodes[2].FindTokenAndGetText(), AccountCategory.BaseAccount);
                }
                if (parts.Field == TermFieldTypeEnum.Code)
                {
                    return CalculateMemberAccountsCode(member, node.ChildNodes[2].FindTokenAndGetText(), AccountCategory.BaseAccount);
                }
                if (parts.Field == TermFieldTypeEnum.SubType)
                {
                    return CalculateMemberAccountsSubType(member, node.ChildNodes[2].FindTokenAndGetText(), AccountCategory.BaseAccount);
                }

                return CalculateMemberAccountsAttribute(member, parts.FieldFullValue!, node.ChildNodes[2].FindTokenAndGetText(), node.ChildNodes[1].FindTokenAndGetText(), AccountCategory.BaseAccount);
            }
        }
        else if (parts.Components == TermComponentType.SimpleBase)
        {
            // will be something like "Member Has 'xxxx'"

            if (parts.Base == TermBaseTypeEnum.Member)
            {
                return CalculateMemberAttribute(member, node.ChildNodes[2].FindTokenAndGetText(), null, node.ChildNodes[1].FindTokenAndGetText());
            }

            return 0;
        }

        return 0;
    }

    public bool IsExpressionValid(string expression, out string? errorMessage)
    {
        // Syntax checking
        var parseTree = _parser.Parse(expression);
        if (parseTree.Root == null)
        {
            errorMessage = parseTree.ParserMessages[0].Message;
            return false;
        }

        _isTrackingAsked = false;
        _trackingTypeAskedCnt = 0;
        if (!TraverseParseTreeValidation(parseTree.Root, out errorMessage))
        {
            return false;
        }

        if (_isTrackingAsked)
        {
            if (_trackingTypeAskedCnt == 0)
            {
                errorMessage = "If tracking is asked, then tracking type must be provided";
                return false;
            }

            if (_trackingTypeAskedCnt > 1)
            {
                errorMessage = "In each expression only one tracking type is allowed";
                return false;
            }
        }

        return true;
    }

    private bool _isTrackingAsked;

    private int _trackingTypeAskedCnt;

    public bool TraverseParseTreeValidation(ParseTreeNode node, out string? errorMessage)
    {
        if (string.Equals(node.Term.Name, "Term", StringComparison.Ordinal))
        {
            var token = node.ChildNodes[0].FindTokenAndGetText();
            var parts = ExpressionHelper.GetTermParts(token);
            var isValid = false;

            if (parts.Components == TermComponentType.WithProperty)
            {
                if (parts.Base == TermBaseTypeEnum.Share)
                {
                    isValid = LegalShareField.Contains(parts.FieldFullValue!);
                }
                else if (parts.Base == TermBaseTypeEnum.Loan)
                {
                    isValid = LegalLoanField.Contains(parts.FieldFullValue!);
                }
                else if (parts.Base == TermBaseTypeEnum.Account)
                {
                    isValid = LegalAccountField.Contains(parts.FieldFullValue!);
                }
                else if (parts.Base == TermBaseTypeEnum.Member)
                {
                    isValid = true;         // this.legalMemberField.Contains(fieldName);
                }
            }
            else if (parts.Components == TermComponentType.WithSubProperty)
            {
                isValid = parts.Base == TermBaseTypeEnum.Account
                          && parts.Field == TermFieldTypeEnum.Tracking;
                if (isValid)
                {
                    if (!_isTrackingAsked)
                    {
                        _isTrackingAsked = true;
                    }

                    if (parts.SubField == TermSubFieldTypeEnum.Type)
                    {
                        ++_trackingTypeAskedCnt;
                    }
                }
            }
            else
            {
                errorMessage = $"{token} is invalid";
                return false;
            }
        }

        foreach (var child in node.ChildNodes)
        {
            if (!TraverseParseTreeValidation(child, out errorMessage))
            {
                return false;
            }
        }

        errorMessage = null;
        return true;
    }
}
