using QCash.LoanApplication;
using System;
using System.Collections.Generic;
using System.Linq;

namespace QCash.Middleware.CoreProvider.Client;

internal class ParametersSetHelpers
{
    public static void Update<T>(
        ParametersSet responseParameters,
        ParametersSet requestParameters,
        string parameterName,
        Func<T> valueCalculation)
    {
        if (responseParameters == null ||
            requestParameters?.Parameters?.Any(r => r.Name == parameterName) != true)
            return;

        // Remove the param from the requests array
        requestParameters.Parameters = requestParameters.Parameters.Where(p => p.Name != parameterName).ToArray();

        var rawParameters = responseParameters.Parameters == null ? new List<Parameters>() : responseParameters.Parameters.ToList();
        var responseParam = rawParameters.Where(r => r.Name == parameterName).FirstOrDefault();
        if (responseParam != null)
        {
            responseParam.Value = valueCalculation()!.ToString();
        }
        else
        {
            rawParameters.Add(new Parameters
            {
                Name = parameterName,
                Value = valueCalculation()!.ToString()
            });
        }

        responseParameters.Parameters = rawParameters.ToArray();
    }

    public static void UpdateAll(
        ParametersSet responseParameters,
        ParametersSet requestParameters,
        Func<Parameters, double> parametersProcessing)
    {
        if (responseParameters == null || requestParameters?.Parameters == null || requestParameters.Parameters.Length == 0)
            return;

        var rawParameters = responseParameters.Parameters == null ? new List<Parameters>() : responseParameters.Parameters.ToList();
        foreach (var param in requestParameters.Parameters)
        {
            rawParameters.Add(new Parameters
            {
                Name = param.Name,
                Value = parametersProcessing(param).ToString()
            });
        }

        responseParameters.Parameters = rawParameters.ToArray();
    }
}
