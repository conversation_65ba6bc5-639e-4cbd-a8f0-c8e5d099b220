using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Irony.Parsing;
using Newtonsoft.Json;
using QCash.Middleware.CoreProvider.Client.DSL;
using QCash.Middleware.CoreProvider.Client.ExtensionMethods;
using QCash.LoanApplication;
using QCash.Middleware.Messaging.Helpers;

namespace QCash.Middleware.CoreProvider.Client.Helpers;

/// <summary>
/// Decision engine helper service.
/// </summary>
public class DecisionEngineHelperService : IDecisionEngineHelperService
{
    /// <inheritdoc />
    public void CalculateMemberInGoodStanding(
        Member member,
        DecisionEngineSearchParameters searchParams,
        Product product,
        ProductDecisionEngineParameters productResult)
    {
        productResult.ParametersSet ??= new ParametersSet
        {
            Parameters = new Parameters[] { }
        };

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.HaveSSN, () => string.IsNullOrWhiteSpace(member.TaxId) ? 0 : 1);

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.IsPrimaryAccountHolder,
            () => member.BaseAccounts?.Any(ba => ba.OwnerShip == OwnerShipType.Primary) == true ? 1 : 0);

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.IsEighteenYearsOrGreater,
            () => member.DateOfBirth.HasValue && member.DateOfBirth.Value.Age() >= 18 ? 1 : 0);

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.IsOfAgeOfMajority,
            () => member.DateOfBirth.HasValue && member.DateOfBirth.Value.Age() >= searchParams.AgeOfMajority ? 1 : 0);

        // Note: excluded accounts are in web GUI Exclusion Account Types which in Symitar case applies to Base Accounts 
        // Note: !excludedAccountTypes.Contains(ba.Classification) (Can SKIP check on excluded base accounts

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.HaveCreditUnionAccountThatIsNotAnExclusionAccountType,
            () => member.BaseAccounts != null && member.BaseAccounts
                .Any(ba => ba.Accounts != null && ba.Accounts
                    .Any(a => a is { IsClosed: false, IsInactive: false, IsExcluded: false })) ? 1 : 0);

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.HaveNoChargeOffAccountWithBalance,
            () => member.BaseAccounts == null || !member.BaseAccounts
                .Any(ba => ba.Accounts != null && ba.Accounts
                    .Any(a => a.IsValidLoanAccount() && a.IsChargedOff
                                                     && (string.IsNullOrEmpty(searchParams.OverrideChargeOffDescription)
                                                         || !searchParams.OverrideChargeOffDescription.Equals(
                                                             a.Description, StringComparison.OrdinalIgnoreCase))
                                                     && a.LedgerBalance > 0.0m)) ? 1 : 0);

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.HaveOpenShareAccount,
            () => member.BaseAccounts != null && member.BaseAccounts
                .Any(ba => ba.Accounts != null && ba.Accounts
                    .Any(a => a.IsValidShareAccount())) ? 1 : 0);


        if (searchParams.ExcludedLoans != null)
        {
            CheckForExcludedLoanAccounts(member, searchParams.ExcludedLoans); // This is actually global, not per product.  future change
        }

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.HaveExclusionLoans,
            () => member.BaseAccounts != null && member.BaseAccounts
                .Any(ba => ba.Accounts != null && ba.Accounts
                    .Any(a => a is { IsClosed: false, IsInactive: false, Category: AccountCategory.Loan } &&
                              IsExcludedLoanAccount(a))) ? 1 : 0);

        var excludedLoanPurposes = new HashSet<string>(searchParams.ExcludedLoansByPurpose ?? new string[] { });
        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.HaveExclusionLoansByPurpose,
            () => member.BaseAccounts != null && member.BaseAccounts
                .Any(ba => ba.Accounts != null && ba.Accounts
                    .Any(a => a is { IsClosed: false, Category: AccountCategory.Loan }
                              && excludedLoanPurposes.Contains(a.LoanPurpose ?? string.Empty))) ? 1 : 0);
    }

    /// <inheritdoc />
    public void CalculateProductEligibilityRequirements(
        Member member,
        DecisionEngineSearchParameters searchParams,
        Product product,
        ProductDecisionEngineParameters productResult,
        List<AccountTransactionHistory>? nsfHistList = null)
    {
        if (member.BaseAccounts == null)
        {
            return;
        }

        productResult.ParametersSet ??= new ParametersSet
        {
            Parameters = new Parameters[] { }
        };

        var time = searchParams.CustomCoreDate ?? DateTime.UtcNow;
        CalculateFeeBasedOrInterestBasedLoans(member, product, productResult, searchParams);

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.HavePastDueLoans,
            () => member.BaseAccounts
                .Where(baseAccount => baseAccount.Accounts != null && !baseAccount.IsExcluded)
                .Any(baseAccount => baseAccount.Accounts.Any(p => p.IsValidLoanAccount()
                                                                  && p.LedgerBalance != 0.0m
                                                                  && p.PaymentDueDate != null
                                                                  && time
                                                                  .AddDays(-1 * product.PastDueLoanDays) > p.PaymentDueDate)) ? 1 : 0);

        nsfHistList ??= new List<AccountTransactionHistory>();
        var dslParser = new AggregateDepositsDSLProcessor(new ExpressionHelper());

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.NSFCount,
            () => nsfHistList.Sum(h => searchParams.NSFCountRuleSet.Rules
                .Sum(r => dslParser.GetNumberOfTransactionHistoryFiring(r.Expression ?? string.Empty, h))));

        ParametersSetHelpers.Update(
           productResult.ParametersSet,
           product.ParametersSet,
           CoreParameters.BalanceCheckAny,
           () => GetValidShareAccounts(member, time)
               .Where(a => a.AvailableBalance.HasValue)
               .Any(a => a.AvailableBalance < product.BalanceBase) ? 1 : 0);

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.AvailableBalanceAll,
            () => GetValidShareAccounts(member, time)
                .Sum(a => a.AvailableBalance ?? 0.0m));

        var directDepositSourceTypes = new HashSet<string>(searchParams.DirectDepositSourceTypes ?? new string[] { });
        var endTime = searchParams.CustomCoreDate ?? DateTime.UtcNow;
        var dateTimeNowClient = endTime.FromUTC(searchParams.ClientTimeZone.GetTimeZoneInfo());
        var startDateTimeClient = dateTimeNowClient.AddDays(-1 * product.AggregateDepositTotalHistoryTimePeriod);
        startDateTimeClient = new DateTime(startDateTimeClient.Year, startDateTimeClient.Month, startDateTimeClient.Day, 0, 0, 0, 0);
        var startDateTimeUTC = startDateTimeClient.ToUTC(searchParams.ClientTimeZone.GetTimeZoneInfo());

        var waitTimeNowClient = DateTime.UtcNow.FromUTC(searchParams.ClientTimeZone.GetTimeZoneInfo());
        var startWaitDateTimeClient = waitTimeNowClient.AddDays(-1 * product.AggregateDepositWaitPeriodInDays);
        startWaitDateTimeClient = new DateTime(startWaitDateTimeClient.Year, startWaitDateTimeClient.Month, startWaitDateTimeClient.Day, 0, 0, 0, 0);
        var startWaitDateTimeUTC = startWaitDateTimeClient.ToUTC(searchParams.ClientTimeZone.GetTimeZoneInfo());

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.ExcludedDirectDepositTotal,
            () => (double)member.BaseAccounts.Sum(ba =>
                ba.Accounts == null || ba.Accounts.Length == 0 || ba.IsExcluded
                    ? 0.0m
                    : ba.Accounts
                        .Where(a => a.Category == AccountCategory.Share && !a.IsClosed && !a.IsExcluded)
                        .Sum(a => a.TransactionHistory == null
                            ? 0
                            : a.TransactionHistory
                                .Where(h => h.ExtraInfo?.Infoes != null
                                            && h.ExtraInfo.Infoes.TryGetValue(nameof(Models.AccountTransactionHistory.IsDeposit), out var isDepositValue)
                                            && isDepositValue.ToUpper() == "TRUE"
                                            && h.TranType != null
                                            && directDepositSourceTypes.Contains(h.TranType.Original ?? string.Empty)
                                            && h.PostedDate != null
                                            && h.PostedDate.Value >= startDateTimeUTC
                                            && (product.AggregateDepositWaitPeriodInDays == 0
                                                || h.PostedDate.Value < startWaitDateTimeUTC)
                                            && h.Amount < product.MinimumDirectDepositAmount)
                                .Sum(h => h.Amount))));
    }

    /// <inheritdoc />
    public void CalculateLoanAmount(
        Member member,
        Product product,
        ProductDecisionEngineParameters productResult,
        DecisionEngineSearchParameters searchParams,
        Action<string> logMessage)
    {
        if (member.BaseAccounts == null)
            return;

        productResult.ParametersSet ??= new ParametersSet
        {
            Parameters = new Parameters[] { }
        };

        var endTime = searchParams.CustomCoreDate ?? DateTime.UtcNow;
        var dateTimeNowClient = endTime.FromUTC(searchParams.ClientTimeZone.GetTimeZoneInfo());
        var startDateTimeClient = dateTimeNowClient.AddDays(-1 * product.AggregateDepositTotalHistoryTimePeriod);
        startDateTimeClient = new DateTime(startDateTimeClient.Year, startDateTimeClient.Month, startDateTimeClient.Day, 0, 0, 0, 0);
        var startDateTimeUTC = startDateTimeClient.ToUTC(searchParams.ClientTimeZone.GetTimeZoneInfo());

        var waitTimeNowClient = DateTime.UtcNow.FromUTC(searchParams.ClientTimeZone.GetTimeZoneInfo());
        var startWaitDateTimeClient = waitTimeNowClient.AddDays(-1 * product.AggregateDepositWaitPeriodInDays);
        startWaitDateTimeClient = new DateTime(startWaitDateTimeClient.Year, startWaitDateTimeClient.Month, startWaitDateTimeClient.Day, 0, 0, 0, 0);
        var startWaitDateTimeUTC = startWaitDateTimeClient.ToUTC(searchParams.ClientTimeZone.GetTimeZoneInfo());

        searchParams.ExcludeDirectDepositTransactionDescription ??= new string[] { };

        var excludedDirectDepositTransactionDescription = new HashSet<string?>(
            searchParams.ExcludeDirectDepositTransactionDescription.Select(s => s?.ToUpper()).ToArray());

        var dslParser = new AggregateDepositsDSLProcessor(new ExpressionHelper());

        var checkDepositsToBeIncluded = searchParams.DepositsToBeIncludedForAggregateDepositTotal?.Rules is
        {
            Length: > 0
        };
        var checkDepositsToBeExcluded = searchParams.DepositsToBeExcludedForAggregateDepositTotal?.Rules is
        {
            Length: > 0
        };

        ParametersSetHelpers.Update(productResult.ParametersSet, product.ParametersSet, CoreParameters.AggregateAccountDeposit, () =>
        {
            var accountTransactions = member.BaseAccounts
                .Where(ba => ba.Accounts is { Length: > 0 } && !ba.IsExcluded)
                .SelectMany(ba => ba.Accounts)
                .Where(a => a is { IsClosed: false, Category: AccountCategory.Share, IsExcluded: false })
                .Where(a => a.TransactionHistory is { Length: > 0 })
                .SelectMany(a => a.TransactionHistory)
                .Where(h => !excludedDirectDepositTransactionDescription.Contains(h.Description?.ToUpper())
                            && h.ExtraInfo?.Infoes != null
                            && (!checkDepositsToBeIncluded
                                || searchParams.DepositsToBeIncludedForAggregateDepositTotal!.Rules
                                    .Any(r => dslParser.CheckIfTransactionHistoryFiring(r.Expression ?? string.Empty, h)))
                            && (!checkDepositsToBeExcluded
                                || !searchParams.DepositsToBeExcludedForAggregateDepositTotal!.Rules
                                    .Any(r => dslParser.CheckIfTransactionHistoryFiring(r.Expression ?? string.Empty, h)))
                            && h.ExtraInfo.Infoes.TryGetValue(nameof(Models.AccountTransactionHistory.IsDeposit), out var isDepositValue)
                            && isDepositValue.ToUpper() == "TRUE"
                            && h.PostedDate != null
                            && h.PostedDate.Value >= startDateTimeUTC
                            && (product.AggregateDepositWaitPeriodInDays == 0
                                || h.PostedDate.Value < startWaitDateTimeUTC))
                .ToList();

            if (accountTransactions.Count == 0)
            {
                logMessage($"{product.Name} Aggregate Account Deposit List: there are no deposits");
                return 0.0;
            }

            var aggregateDeposits = accountTransactions
                .Select(h => $"BaseAccountId: {FormattingHelpers.GetMaskedNumber(h.BaseAccountId ?? string.Empty)}, " +
                             $"PostedDate: {h.PostedDate}, Description: {h.Description}, Type: {h.TranType.Original}, " +
                             $"Amount: {h.Amount}")
                .Aggregate(new StringBuilder(), (x, y) => x.Append($" | {y}"));
            logMessage($"{product.Name} Aggregate Account Deposit List: {aggregateDeposits}");

            return (double)accountTransactions.Sum(h => h.Amount);
        });

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.PrincipleBalanceOfOpenLoans,
            () => (double)member.BaseAccounts.Sum(
                ba =>
                    ba.Accounts == null || ba.Accounts.Length == 0 || ba.IsExcluded
                        ? 0.0m
                        : ba.Accounts.Where(
                                a =>
                                    a is { IsClosed: false, Category: AccountCategory.Loan, IsExcluded: false })
                            .Sum(a => a.LedgerBalance ?? 0.0m)));
    }

    /// <inheritdoc />
    public void CalculateScores(
        Member member,
        Product product,
        ProductDecisionEngineParameters productResult,
        DecisionEngineSearchParameters searchParams)
    {
        if (member.BaseAccounts == null)
            return;

        productResult.ParametersSet ??= new ParametersSet
        {
            Parameters = new Parameters[] { }
        };

        if (member.OpenDate != null)
            ParametersSetHelpers.Update(
                productResult.ParametersSet,
                product.ParametersSet,
                CoreParameters.LengthOfRelationship,
                () => ((searchParams.CustomCoreDate ?? DateTime.UtcNow).Year - member.OpenDate.Value.Year) * 12 +
                      (searchParams.CustomCoreDate ?? DateTime.UtcNow).Month
                      - member.OpenDate.Value.Month);

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.OverrideLOR,
            () => 0);

        // Loan/Share/Tracking Record types pulled from core, then filter with CreditUnionProductTypes passed in from mono and then count(*) of all accounts after filtering? Need clarification, matt will do it.

        var includedNumberOfElectronicTransactionsTellerIds =
            new HashSet<string>(searchParams.IncludedNumberOfElectronicTransactionsTellerIds ?? new string[] { });

        var endTime = searchParams.CustomCoreDate ?? DateTime.UtcNow;
        var dateTimeNowClient = endTime.FromUTC(searchParams.ClientTimeZone.GetTimeZoneInfo());
        var startDateTimeClient = dateTimeNowClient.AddDays(-1 * product.NumberOfElectronicTransactionsHistoryPeriod);
        startDateTimeClient = new DateTime(startDateTimeClient.Year, startDateTimeClient.Month, startDateTimeClient.Day, 0, 0, 0, 0);
        var startDateTimeUTC = startDateTimeClient.ToUTC(searchParams.ClientTimeZone.GetTimeZoneInfo());

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.NumberOfElectronicTransactions,
            () => member.BaseAccounts.Sum(
                ba => ba.Accounts == null ? 0 :
                    ba.Accounts.Where(a => a is { IsClosed: false, IsInactive: false, IsExcluded: false })
                        .Sum(a => a.TransactionHistory
                            ?.Count(h => h.ExtraInfo is { Infoes: not null }
                                         && h.ExtraInfo.Infoes.TryGetValue("USERNUMBER", out var userNumberValue)
                                         && includedNumberOfElectronicTransactionsTellerIds.Contains(userNumberValue)
                                         && h.PostedDate != null
                                         && h.PostedDate.Value >= startDateTimeUTC) ?? 0)));

        var excludeLoanTypesFromPaymentHistoryCalculation =
            new HashSet<string>(searchParams.ExcludeLoanTypesFromPaymentHistoryCalculation ?? new string[] { });
        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.TotalLoanPayments,
            () => member.BaseAccounts.Sum(
                ba =>
                    ba.Accounts == null
                        ? 0
                        : ba.Accounts.Where(a => a.Category == AccountCategory.Loan
                                                 && !excludeLoanTypesFromPaymentHistoryCalculation.Contains(a.GetFormattedAccountType()))
                            .Sum(a => a.NumberOfPaymentMade)));

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.LateLoanPayments,
            () => member.BaseAccounts?.Sum(
                ba =>
                    ba.Accounts == null
                        ? 0
                        : ba.Accounts.Where(a =>
                                a.Category == AccountCategory.Loan
                                && !excludeLoanTypesFromPaymentHistoryCalculation
                                    .Contains(a.GetFormattedAccountType()))
                            .Sum(a =>
                                a.LatePayments?.Sum(lp => lp.NumberOfLatePayments) ?? 0)) ?? 0);

        // Loan/Share/Tracking Record types pulled from core, then filter with CreditUnionProductTypes passed in from mono and then count(*) of all accounts after filtering? Need clarification, matt will do it.           

        var directDepositSourceTypes = new HashSet<string>(searchParams.DirectDepositSourceTypes ?? new string[] { });

        var endTimeDirectDeposit = searchParams.CustomCoreDate ?? DateTime.UtcNow;
        var dateTimeNowClientDirectDeposit = endTimeDirectDeposit.FromUTC(searchParams.ClientTimeZone.GetTimeZoneInfo());
        var startDateTimeClientDirectDeposit =
            dateTimeNowClientDirectDeposit.AddDays(-1 * product.DirectDepositTransactionCountHistoryPeriod);
        startDateTimeClientDirectDeposit =
            new DateTime(startDateTimeClientDirectDeposit.Year, startDateTimeClientDirectDeposit.Month, startDateTimeClientDirectDeposit.Day, 0, 0, 0, 0);
        var startDateTimeUTCDirectDeposit = startDateTimeClientDirectDeposit.ToUTC(searchParams.ClientTimeZone.GetTimeZoneInfo());

        ParametersSetHelpers.Update(productResult.ParametersSet, product.ParametersSet, CoreParameters.NumberOfDirectDeposit, () =>
        {
            return member.BaseAccounts?.Sum(ba => ba.Accounts == null || ba.IsExcluded ? 0
                : ba.Accounts
                    .Where(a => a.Category == AccountCategory.Share && !a.IsClosed && !a.IsExcluded)
                    .Sum(a => a.TransactionHistory
                        ?.Count(h => h.ExtraInfo?.Infoes != null
                                     && h.ExtraInfo.Infoes.TryGetValue(nameof(Models.AccountTransactionHistory.IsDeposit), out var isDepositValue)
                                     && isDepositValue.ToUpper() == "TRUE"
                                     && h.TranType != null
                                     && directDepositSourceTypes.Contains(h.TranType.Original ?? string.Empty)
                                     && h.PostedDate != null
                                     && h.PostedDate.Value >= startDateTimeUTCDirectDeposit) ?? 0)) ?? 0;
        });
    }

    /// <inheritdoc />
    public void CalculateFilterQueries(
        Member member,
        Product product,
        ProductDecisionEngineParameters productResult)
    {
        productResult.ParametersSet ??= new ParametersSet
        {
            Parameters = new Parameters[] { }
        };

        var dslParser = new ProductScoreDSLProcessor(new ExpressionHelper());
        var dslMember = GetDslMember(member);

        ParametersSetHelpers.UpdateAll(productResult.ParametersSet, product.ParametersSet, param =>
        {
            if (string.IsNullOrWhiteSpace(param.Expression))
                throw new ArgumentNullException(nameof(param.Expression), $@"Expression {param.Expression} for parameter {param.Name} is null.");

            param.Expression = param.Expression.Replace("‘", "'").Replace("’", "'");
            if (!dslParser.IsExpressionValid(param.Expression, out var errorMessage))
            {
                throw new ArgumentException(nameof(param.Expression), $"Expression {param.Expression} with name {param.Name} is not a valid expression for this FI. {errorMessage}");
            }

            return dslParser.GetNumberOfFiringOnMemberData(param.Expression, dslMember);
        });
    }

    /// <inheritdoc />
    public bool CheckRulesForDepositAggregate(
        ParseTreeNode[]? ruleNodes,
        AccountTransactionHistory histItem,
        bool valueIfNoRulesGiven)
    {
        //Previously, there was an extra condition below:
        // || histItem.ExtraInfo.Infoes.ContainsKey(Constants.ActionCode) == false 
        //this was probably specific to a certain Core.
        //This is currently unused and could possibly be considered deprecated.
        //Leaving intact here for now as it is functional.

        if (histItem.ExtraInfo?.Infoes == null ||
            histItem.ExtraInfo.Infoes.Count == 0)
        {
            return false;
        }

        // If there are no rules specified, we presume that the deposit is included
        if (ruleNodes == null || ruleNodes.Length == 0)
        {
            return valueIfNoRulesGiven;
        }

        return ruleNodes.Any(rule => AggregateDepositsDSLProcessor.TraverseParseTreeCalculation(rule, histItem));
    }

    /// <inheritdoc />
    public ParseTreeNode[]? PrepareParseTreeNodes(RuleSet includedRules)
    {
        var processor = new AggregateDepositsDSLProcessor(new ExpressionHelper());
        ParseTreeNode[]? returnNodes = includedRules?.Rules
            .Where(r => !string.IsNullOrEmpty(r.Expression))
            .Select(r => processor.GetParseTreeNode((r.Expression ?? string.Empty).Replace("‘", "'").Replace("’", "'")))
            .Where(ptn => ptn != null)
            .Select(ptn => ptn!)
            .ToArray();
        return returnNodes ?? [];
    }

    private static void CheckForExcludedLoanAccounts(Member member, IReadOnlyList<string> excludedAccounts)
    {
        if (excludedAccounts == null || excludedAccounts.Count == 0 || member.BaseAccounts == null || member.BaseAccounts.Length == 0)
            return;

        var dslAcctParser = new ProductScoreDSLProcessor(new ExpressionHelper());

        var isExpression = dslAcctParser.IsExpressionValid(excludedAccounts[0], out _);

        foreach (var account in member.BaseAccounts.SelectMany(x => x.Accounts))
        {
            if (isExpression)
            {
                var expressions = new HashSet<string>(excludedAccounts)
                    .Where(expression => dslAcctParser.GetNumberOfFiringOnMemberData(expression, account, false) > 0);
                foreach (var _ in expressions)
                    account.AddExtraInfo("ExcludedLoan", "true");
            }
            else if (excludedAccounts.Contains(account.Type))
                account.AddExtraInfo("ExcludedLoan", "true");
        }
    }

    private static bool IsExcludedLoanAccount(Account account) =>
        account.ExtraInfo is { Infoes: not null } && account.ExtraInfo.Infoes.ContainsKey("ExcludedLoan");

    private static void CalculateFeeBasedOrInterestBasedLoans(
        Member member,
        Product product,
        ProductDecisionEngineParameters productResult,
        DecisionEngineSearchParameters searchParams)
    {
        productResult.ParametersSet ??= new ParametersSet
        {
            Parameters = new Parameters[] { }
        };

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.NumberOfOpenLoans,
            () => member.BaseAccounts.Sum(
                ba => ba.Accounts == null || ba.IsExcluded
                    ? 0
                    : ba.Accounts.Count(a =>
                        a.IsValidLoanAccount() && a.GetFormattedAccountType() == product.Code)));

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.LoanBalance,
            () => (double)member.BaseAccounts.Sum(ba =>
                ba.Accounts == null
                    ? 0.0m
                    : ba.Accounts.Where(a => a.IsValidLoanAccount() && a.GetFormattedAccountType() == product.Code)
                        .Sum(a => a.LedgerBalance ?? 0.0m)));

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.UnsecuredBalance,
            () => (double)member.BaseAccounts.Sum(ba => ba.Accounts == null
                ? 0.0m
                : ba.Accounts.Where(a => a.IsValidLoanAccount())
                    .Sum(a => searchParams.OpenEndLoanTypes.Contains(a.GetFormattedAccountType())
                        ? a.CreditLimit
                        : searchParams.CloseEndLoanType.Contains(a.GetFormattedAccountType())
                            ? a.PayoffAmount : 0.0m) ?? 0.0m));

        ParametersSetHelpers.Update(
            productResult.ParametersSet,
            product.ParametersSet,
            CoreParameters.ReceivedLoans,
            () => member.BaseAccounts.Sum(ba => ba.Accounts
                ?.Count(a => a.IsValidLoanAccountIgnoreClosed()
                             && IsLoanWithinHistoryPeriod(a, product, searchParams.CustomCoreDate)
                             && a.GetFormattedAccountType() == product.Code) ?? 0));
    }

    private static bool IsLoanWithinHistoryPeriod(Account loanAccount, Product product, DateTime? customCoreDate)
    {
        if (loanAccount.OpenDate == null || product.LoanHistoryPeriod == 0)
            return true;

        return loanAccount.OpenDate.Value.AddMonths(product.LoanHistoryPeriod) > (customCoreDate ?? DateTime.UtcNow);
    }

    private static Member GetDslMember(Member member)
    {
        //TODO - find out why this serialize-deserialize process exists here
        var serialized = JsonConvert.SerializeObject(member);

        var dslMember = JsonConvert.DeserializeObject<Member>(serialized);

        if (dslMember != null && dslMember.BaseAccounts != null)
        {
            foreach (var baseAccount in dslMember.BaseAccounts)
                baseAccount.Accounts = baseAccount.Accounts.Where(a => !a.IsExcluded).ToArray();
        }

        return dslMember!;
    }

    private static IEnumerable<Account> GetValidShareAccounts(Member member, DateTime time) => member.BaseAccounts
                .Where(ba => !ba.IsExcluded && (!ba.CloseDate.HasValue || ba.CloseDate.Value > time))
                .SelectMany(ba => ba.Accounts)
                .Where(a => a.IsValidShareAccount());
}
