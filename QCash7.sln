
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.35201.131
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{D547449C-4459-4B7E-9B48-0AE0F5CC7082}"
	ProjectSection(SolutionItems) = preProject
		src\.editorconfig = src\.editorconfig
		.gitignore = .gitignore
		azure-pipelines-nightly.yml = azure-pipelines-nightly.yml
		azure-pipelines-release.yml = azure-pipelines-release.yml
		azure-pipelines.yml = azure-pipelines.yml
		AzureServiceBusEmulatorDockerConfig.json = AzureServiceBusEmulatorDockerConfig.json
		BannedSymbols.txt = BannedSymbols.txt
		Directory.Build.props = Directory.Build.props
		docker-compose.yaml = docker-compose.yaml
		nuget.config = nuget.config
		polaris.yml = polaris.yml
		README.md = README.md
		SastCheckPullRequestForNewIssues.ps1 = SastCheckPullRequestForNewIssues.ps1
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Data", "src\QCash.Data\QCash.Data.csproj", "{E9D60307-7438-48BC-B845-C10C24282ABC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Migrations", "src\QCash.Migrations\QCash.Migrations.csproj", "{EBE56B45-69C9-4582-A02B-AEBBB96B6DE2}"
	ProjectSection(ProjectDependencies) = postProject
		{E9D60307-7438-48BC-B845-C10C24282ABC} = {E9D60307-7438-48BC-B845-C10C24282ABC}
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Service", "src\QCash.Service\QCash.Service.csproj", "{1E90CA87-61C7-429D-85AE-9006B75CB679}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Web", "src\QCash.Web\QCash.Web.csproj", "{DE9AD68D-68C7-4F60-A899-A1A2075FDA63}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Service.UnitTest", "test\QCash.Service.UnitTest\QCash.Service.UnitTest.csproj", "{AE9DCDFB-0CF9-46CC-B65D-50ACF9BE8ED6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Utils", "src\QCash.Utils\QCash.Utils.csproj", "{D2441EAF-97CB-439C-8C67-31F4A027E4D8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Middleware", "Middleware", "{7AF5B919-CD01-40B7-BBA9-35C50425E4BB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Middleware.Interfaces.Shared", "src\QCash.Middleware.Interfaces\QCash.Middleware.Interfaces.Shared.csproj", "{373BCC16-A9D4-496A-8AA6-9629F2E9DA58}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Middleware.Concert.Client", "src\QCash.Middleware.Concert.Client\QCash.Middleware.Concert.Client.csproj", "{54EFEB11-8D59-4068-9A46-DF80572D5F43}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Middleware.Concert.Client.Messaging", "src\QCash.Middleware.Concert.Client.Messaging\QCash.Middleware.Concert.Client.Messaging.csproj", "{6466822B-EBFA-48FC-90C5-58773437F4CC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Middleware.CoreProvider.Client", "src\QCash.Middleware.CoreProvider.Client\QCash.Middleware.CoreProvider.Client.csproj", "{94658238-F99D-4959-8994-113A8E84084C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Middleware.Messaging.Helpers", "src\QCash.Middleware.Messaging\QCash.Middleware.Messaging.Helpers\QCash.Middleware.Messaging.Helpers.csproj", "{2B65EEF4-C54A-4BCB-B87A-F29BA9B662A2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Middleware.UnitTest", "test\QCash.Middleware.UnitTest\QCash.Middleware.UnitTest.csproj", "{********-95FC-4B69-9789-33F165BFC540}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Middleware.Router", "src\QCash.Middleware.Router\QCash.Middleware.Router.csproj", "{134AB032-88D7-4CA8-9E72-228AF4332EFB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Seed", "src\QCash.Seed\QCash.Seed.csproj", "{D5DFE3C4-C034-4340-943D-B9E6D984406C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "QCash.Web.UnitTest", "test\QCash.Web.UnitTest\QCash.Web.UnitTest.csproj", "{B34DE196-00FF-4178-B474-2EE051C3CA00}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QCash.Middleware.Interfaces.Internal", "src\QCash.Middleware.Interfaces.Internal\QCash.Middleware.Interfaces.Internal.csproj", "{4E4F0F99-FDE5-48F3-A0E4-D874BE830EA5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{8B1EC767-B8F9-47D7-832A-8361876C4019}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{4B3558D0-815A-43C3-8A7B-F8DD646CDA4B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QCash.Data.UnitTest", "test\QCash.Data.UnitTest\QCash.Data.UnitTest.csproj", "{0CC5D320-214E-44BA-9589-63E93E4B0008}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QCash.ReportDesigner", "src\QCash.ReportDesigner\QCash.ReportDesigner.csproj", "{1E2440B6-C366-4E86-B57A-7C4BAAB7A50D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Member", "Member", "{********-5060-49C1-A9EB-BB4B068AE798}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QCash.Blizard.Web", "src\QCash.Blizard.Web\QCash.Blizard.Web.csproj", "{A4518D55-F209-4F84-9877-AC450FF4AA89}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QCash.Common", "src\QCash.Common\QCash.Common.csproj", "{F0312FCB-504E-455A-BDDB-42D86B55F5EE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QCash.WebApi", "src\QCash.WebApi\QCash.WebApi.csproj", "{77519EBE-2526-4DED-9770-55302AA5FCC8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QCash.Models.Api", "src\QCash.Models.Api\QCash.Models.Api.csproj", "{5A6661AD-B97C-4F41-BDB8-8E9FAB35DFFE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "QCash.Utils.UnitTest", "test\QCash.Utils.UnitTest\QCash.Utils.UnitTest.csproj", "{F8AE7C54-82DA-4715-BC21-B0A0CBF8847F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E9D60307-7438-48BC-B845-C10C24282ABC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9D60307-7438-48BC-B845-C10C24282ABC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9D60307-7438-48BC-B845-C10C24282ABC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9D60307-7438-48BC-B845-C10C24282ABC}.Release|Any CPU.Build.0 = Release|Any CPU
		{EBE56B45-69C9-4582-A02B-AEBBB96B6DE2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EBE56B45-69C9-4582-A02B-AEBBB96B6DE2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EBE56B45-69C9-4582-A02B-AEBBB96B6DE2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EBE56B45-69C9-4582-A02B-AEBBB96B6DE2}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E90CA87-61C7-429D-85AE-9006B75CB679}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E90CA87-61C7-429D-85AE-9006B75CB679}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E90CA87-61C7-429D-85AE-9006B75CB679}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E90CA87-61C7-429D-85AE-9006B75CB679}.Release|Any CPU.Build.0 = Release|Any CPU
		{DE9AD68D-68C7-4F60-A899-A1A2075FDA63}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DE9AD68D-68C7-4F60-A899-A1A2075FDA63}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DE9AD68D-68C7-4F60-A899-A1A2075FDA63}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DE9AD68D-68C7-4F60-A899-A1A2075FDA63}.Release|Any CPU.Build.0 = Release|Any CPU
		{AE9DCDFB-0CF9-46CC-B65D-50ACF9BE8ED6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AE9DCDFB-0CF9-46CC-B65D-50ACF9BE8ED6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AE9DCDFB-0CF9-46CC-B65D-50ACF9BE8ED6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AE9DCDFB-0CF9-46CC-B65D-50ACF9BE8ED6}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2441EAF-97CB-439C-8C67-31F4A027E4D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2441EAF-97CB-439C-8C67-31F4A027E4D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2441EAF-97CB-439C-8C67-31F4A027E4D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2441EAF-97CB-439C-8C67-31F4A027E4D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{373BCC16-A9D4-496A-8AA6-9629F2E9DA58}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{373BCC16-A9D4-496A-8AA6-9629F2E9DA58}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{373BCC16-A9D4-496A-8AA6-9629F2E9DA58}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{373BCC16-A9D4-496A-8AA6-9629F2E9DA58}.Release|Any CPU.Build.0 = Release|Any CPU
		{54EFEB11-8D59-4068-9A46-DF80572D5F43}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{54EFEB11-8D59-4068-9A46-DF80572D5F43}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{54EFEB11-8D59-4068-9A46-DF80572D5F43}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{54EFEB11-8D59-4068-9A46-DF80572D5F43}.Release|Any CPU.Build.0 = Release|Any CPU
		{6466822B-EBFA-48FC-90C5-58773437F4CC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6466822B-EBFA-48FC-90C5-58773437F4CC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6466822B-EBFA-48FC-90C5-58773437F4CC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6466822B-EBFA-48FC-90C5-58773437F4CC}.Release|Any CPU.Build.0 = Release|Any CPU
		{94658238-F99D-4959-8994-113A8E84084C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{94658238-F99D-4959-8994-113A8E84084C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{94658238-F99D-4959-8994-113A8E84084C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{94658238-F99D-4959-8994-113A8E84084C}.Release|Any CPU.Build.0 = Release|Any CPU
		{2B65EEF4-C54A-4BCB-B87A-F29BA9B662A2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2B65EEF4-C54A-4BCB-B87A-F29BA9B662A2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2B65EEF4-C54A-4BCB-B87A-F29BA9B662A2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2B65EEF4-C54A-4BCB-B87A-F29BA9B662A2}.Release|Any CPU.Build.0 = Release|Any CPU
		{********-95FC-4B69-9789-33F165BFC540}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{********-95FC-4B69-9789-33F165BFC540}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{********-95FC-4B69-9789-33F165BFC540}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{********-95FC-4B69-9789-33F165BFC540}.Release|Any CPU.Build.0 = Release|Any CPU
		{134AB032-88D7-4CA8-9E72-228AF4332EFB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{134AB032-88D7-4CA8-9E72-228AF4332EFB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{134AB032-88D7-4CA8-9E72-228AF4332EFB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{134AB032-88D7-4CA8-9E72-228AF4332EFB}.Release|Any CPU.Build.0 = Release|Any CPU
		{D5DFE3C4-C034-4340-943D-B9E6D984406C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D5DFE3C4-C034-4340-943D-B9E6D984406C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D5DFE3C4-C034-4340-943D-B9E6D984406C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D5DFE3C4-C034-4340-943D-B9E6D984406C}.Release|Any CPU.Build.0 = Release|Any CPU
		{B34DE196-00FF-4178-B474-2EE051C3CA00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B34DE196-00FF-4178-B474-2EE051C3CA00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B34DE196-00FF-4178-B474-2EE051C3CA00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B34DE196-00FF-4178-B474-2EE051C3CA00}.Release|Any CPU.Build.0 = Release|Any CPU
		{4E4F0F99-FDE5-48F3-A0E4-D874BE830EA5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4E4F0F99-FDE5-48F3-A0E4-D874BE830EA5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4E4F0F99-FDE5-48F3-A0E4-D874BE830EA5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4E4F0F99-FDE5-48F3-A0E4-D874BE830EA5}.Release|Any CPU.Build.0 = Release|Any CPU
		{0CC5D320-214E-44BA-9589-63E93E4B0008}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0CC5D320-214E-44BA-9589-63E93E4B0008}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0CC5D320-214E-44BA-9589-63E93E4B0008}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0CC5D320-214E-44BA-9589-63E93E4B0008}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E2440B6-C366-4E86-B57A-7C4BAAB7A50D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E2440B6-C366-4E86-B57A-7C4BAAB7A50D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E2440B6-C366-4E86-B57A-7C4BAAB7A50D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E2440B6-C366-4E86-B57A-7C4BAAB7A50D}.Release|Any CPU.Build.0 = Release|Any CPU
		{A4518D55-F209-4F84-9877-AC450FF4AA89}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A4518D55-F209-4F84-9877-AC450FF4AA89}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A4518D55-F209-4F84-9877-AC450FF4AA89}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A4518D55-F209-4F84-9877-AC450FF4AA89}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0312FCB-504E-455A-BDDB-42D86B55F5EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0312FCB-504E-455A-BDDB-42D86B55F5EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0312FCB-504E-455A-BDDB-42D86B55F5EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0312FCB-504E-455A-BDDB-42D86B55F5EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{77519EBE-2526-4DED-9770-55302AA5FCC8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{77519EBE-2526-4DED-9770-55302AA5FCC8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{77519EBE-2526-4DED-9770-55302AA5FCC8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{77519EBE-2526-4DED-9770-55302AA5FCC8}.Release|Any CPU.Build.0 = Release|Any CPU
		{5A6661AD-B97C-4F41-BDB8-8E9FAB35DFFE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5A6661AD-B97C-4F41-BDB8-8E9FAB35DFFE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5A6661AD-B97C-4F41-BDB8-8E9FAB35DFFE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5A6661AD-B97C-4F41-BDB8-8E9FAB35DFFE}.Release|Any CPU.Build.0 = Release|Any CPU
		{F8AE7C54-82DA-4715-BC21-B0A0CBF8847F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F8AE7C54-82DA-4715-BC21-B0A0CBF8847F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F8AE7C54-82DA-4715-BC21-B0A0CBF8847F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F8AE7C54-82DA-4715-BC21-B0A0CBF8847F}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{E9D60307-7438-48BC-B845-C10C24282ABC} = {8B1EC767-B8F9-47D7-832A-8361876C4019}
		{EBE56B45-69C9-4582-A02B-AEBBB96B6DE2} = {8B1EC767-B8F9-47D7-832A-8361876C4019}
		{1E90CA87-61C7-429D-85AE-9006B75CB679} = {8B1EC767-B8F9-47D7-832A-8361876C4019}
		{DE9AD68D-68C7-4F60-A899-A1A2075FDA63} = {8B1EC767-B8F9-47D7-832A-8361876C4019}
		{AE9DCDFB-0CF9-46CC-B65D-50ACF9BE8ED6} = {4B3558D0-815A-43C3-8A7B-F8DD646CDA4B}
		{D2441EAF-97CB-439C-8C67-31F4A027E4D8} = {8B1EC767-B8F9-47D7-832A-8361876C4019}
		{7AF5B919-CD01-40B7-BBA9-35C50425E4BB} = {8B1EC767-B8F9-47D7-832A-8361876C4019}
		{373BCC16-A9D4-496A-8AA6-9629F2E9DA58} = {7AF5B919-CD01-40B7-BBA9-35C50425E4BB}
		{54EFEB11-8D59-4068-9A46-DF80572D5F43} = {7AF5B919-CD01-40B7-BBA9-35C50425E4BB}
		{6466822B-EBFA-48FC-90C5-58773437F4CC} = {7AF5B919-CD01-40B7-BBA9-35C50425E4BB}
		{94658238-F99D-4959-8994-113A8E84084C} = {7AF5B919-CD01-40B7-BBA9-35C50425E4BB}
		{2B65EEF4-C54A-4BCB-B87A-F29BA9B662A2} = {7AF5B919-CD01-40B7-BBA9-35C50425E4BB}
		{********-95FC-4B69-9789-33F165BFC540} = {4B3558D0-815A-43C3-8A7B-F8DD646CDA4B}
		{134AB032-88D7-4CA8-9E72-228AF4332EFB} = {7AF5B919-CD01-40B7-BBA9-35C50425E4BB}
		{D5DFE3C4-C034-4340-943D-B9E6D984406C} = {8B1EC767-B8F9-47D7-832A-8361876C4019}
		{B34DE196-00FF-4178-B474-2EE051C3CA00} = {4B3558D0-815A-43C3-8A7B-F8DD646CDA4B}
		{4E4F0F99-FDE5-48F3-A0E4-D874BE830EA5} = {7AF5B919-CD01-40B7-BBA9-35C50425E4BB}
		{0CC5D320-214E-44BA-9589-63E93E4B0008} = {4B3558D0-815A-43C3-8A7B-F8DD646CDA4B}
		{1E2440B6-C366-4E86-B57A-7C4BAAB7A50D} = {8B1EC767-B8F9-47D7-832A-8361876C4019}
		{********-5060-49C1-A9EB-BB4B068AE798} = {8B1EC767-B8F9-47D7-832A-8361876C4019}
		{A4518D55-F209-4F84-9877-AC450FF4AA89} = {********-5060-49C1-A9EB-BB4B068AE798}
		{F0312FCB-504E-455A-BDDB-42D86B55F5EE} = {********-5060-49C1-A9EB-BB4B068AE798}
		{77519EBE-2526-4DED-9770-55302AA5FCC8} = {********-5060-49C1-A9EB-BB4B068AE798}
		{5A6661AD-B97C-4F41-BDB8-8E9FAB35DFFE} = {********-5060-49C1-A9EB-BB4B068AE798}
		{F8AE7C54-82DA-4715-BC21-B0A0CBF8847F} = {4B3558D0-815A-43C3-8A7B-F8DD646CDA4B}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A855A56E-172E-46B9-900E-06231CAD7984}
	EndGlobalSection
EndGlobal
