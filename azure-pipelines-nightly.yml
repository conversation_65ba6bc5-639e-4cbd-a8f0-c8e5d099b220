# PR CI build

trigger:
  - none
    
schedules:
  - cron: "0 9 * * *"  # 3 AM CST corresponds to 9 AM UTC
    displayName: Daily 3AM CST Scan
    branches:
      include:
        - main
    always: false  # Only scan if there have been any changes made
    batch: true  # Only scan the latest changes if multiple changes have been made since last run

variables:
  - group: 'POLARIS'
  - name: 'buildConfiguration'
    value: 'Release'

name: $(date:yyyyMMdd) Daily SAST
pool:
  vmImage: 'ubuntu-latest'

jobs:
  - job: SASTScan
    timeoutInMinutes: 120
    
    steps:
      - pwsh: |
          # Read the file content
          $content = Get-Content polaris.yml -Raw
          
          # Replace the specific variable within the branch line
          $updatedContent = $content -replace '\$\{env.BUILD_REASON} \$\{env.SYSTEM_PULLREQUEST_SOURCEBRANCH}', '${scm.git.branch}'
          
          # Clean up any extra spaces or trailing spaces left after the replacement
          $updatedContent = $updatedContent -replace 'branch: [ ]+', 'branch: '
          
          # Write the updated content back to the file
          Set-Content polaris.yml -Value $updatedContent
          
          # Display the contents of 'polaris.yml'
          Get-Content polaris.yml
        displayName: 'Update polaris.yml'
        condition: 'false'

      - pwsh: |
          # Get the Artifact Staging Directory from the environment variable
          $baseDirectory = $env:BUILD_SOURCESDIRECTORY
          
          # Find all directories in glob src\**\Migrations
          $targetDirectories = Get-ChildItem -Path (Join-Path $baseDirectory "src") -Recurse -Directory | Where-Object { $_.Name -eq "Migrations" }
          
          foreach ($targetDirectory in $targetDirectories) {
            # Check if the directory path is valid
            if ([string]::IsNullOrWhiteSpace($targetDirectory)) {
              Write-Error "Target directory path is not set or invalid."
              exit 1
            }
            
            # Check if the target directory exists
            if (-not (Test-Path -Path $targetDirectory)) {
              Write-Error "Target directory does not exist: $targetDirectory"
              exit 1
            }
            
            # Get all .cs files in the target directory
            $csFiles = Get-ChildItem -Path $targetDirectory -Filter "*.cs" | Where-Object { $_.Name -notlike "*Snapshot.cs" }
            
            # Loop through each file
            foreach ($file in $csFiles) {
              # Output the filename
              Write-Output "Deleting file: $($file.FullName)"
            
              # Delete the file
              Remove-Item $file.FullName
            }
          }
        displayName: 'Remove Migration Files'

      - task: UseDotNet@2
        displayName: 'dotnet version'
        inputs:
          packageType: 'sdk'
          version: '9.0.x'
          includePreviewVersions: false

      - task: DotNetCoreCLI@2
        displayName: 'dotnet restore'
        inputs:
          command: 'restore'
          projects: '**/*.csproj'
          feedsToUse: 'select'
          vstsFeed: '71388218-5b5d-49f0-b0de-c191e394ed3b'
          verbosityRestore: 'Minimal'

      - task: BlackduckCoverityOnPolaris@2
        displayName: Black Duck Coverity on Polaris
        inputs:
          polarisService: 'Black Duck to Polaris'
          polarisCommand: 'analyze -w --coverity-ignore-capture-failure'

      - pwsh: |
          $srcPath = $env:BUILD_REPOSITORY_LOCALPATH
          $results = Join-Path -Path $srcPath -ChildPath ".blackduck/polaris/data/coverity/2024.6.0/idir/build-log.txt"
          Get-Content $results
        displayName: 'Display build log'
        condition: failed()

      - pwsh: |
          $srcPath = $env:BUILD_REPOSITORY_LOCALPATH
          $results = Join-Path -Path $srcPath -ChildPath ".blackduck\polaris\cli-scan.json"
          Get-Content $results
        displayName: 'Display scan results'
        condition: succeededOrFailed()

      - task: PowerShell@2
        displayName: 'Create new DevOps work items'
        inputs:
          targetType: filePath
          filePath: ./SastCreateWorkItemsForBranch.ps1

      - task: PowerShell@2
        displayName: 'Sync Coverity Finding Status to DevOps'
        inputs:
          targetType: filePath
          filePath: ./SastSyncFindingsAndDevOps.ps1

#      - pwsh: |
#          $branches = ("$env:BUILD_REASON $env:SYSTEM_PULLREQUEST_SOURCEBRANCH").Trim()
#          Write-Host "##vso[task.setvariable variable=POLARIS_BRANCH]$branches"
#        displayName: 'Combine variables'
#
#      - task: PythonScript@0
#        displayName: 'Check build for new Coverity findings'
#        inputs:
#          scriptSource: 'filePath'
#          scriptPath: 'azure-polaris-issues-tool.py'
#          failOnStderr: false
#          arguments: '--compare "$(POLARIS_COMPAREBRANCH)" --project "$(POLARIS_PROJECTNAME)" --branch "$(POLARIS_BRANCH)" --new --debug "$(POLARIS_DEBUGLEVEL)" --exit1-if-issues --spec "path,checker,name,severity,first_detected,status,comment,closed_date,url" --pr'
#      
#      - task: PythonScript@0
#        displayName: 'Check build for new Coverity findings'
#        inputs:
#          scriptSource: 'filePath'
#          scriptPath: 'check-for-new-issues.py'
#          failOnStderr: false
#        condition: 'false'          

#  - job: DebuggingInfo
#    displayName: 'Debugging Info'
#    pool:
#      vmImage: 'ubuntu-latest'
#    steps:
#      - checkout: none
#      - pwsh: |
#          Get-ChildItem Env: | Format-Table -AutoSize | Out-String -Width 4096 | Write-Host
#        displayName: 'Dump Environment Variables'
