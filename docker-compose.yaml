name: 'qcash7'
services:
  mssql:
    image: mcr.microsoft.com/mssql/server:2019-latest
    environment:
      ACCEPT_EULA: Y
      SA_PASSWORD: QCashDev!
    ports: 
      - "127.0.0.1:1536:1433"
    deploy:
      mode: global
      resources:
        limits:
          cpus: '1.00'
          memory: 2G
    volumes:
      - mssql-data:/var/opt/mssql
  redis:
    image: redis:6.2.6
    ports:
      - "6380:6379"
    deploy:
      mode: global
      resources:
        limits:
          cpus: '1.00'
          memory: 1G
    volumes:
      - redis-data:/data
  emulator:
    container_name: "servicebus-emulator"
    image: mcr.microsoft.com/azure-messaging/servicebus-emulator:latest
    pull_policy: always
    volumes:
      - ".\\AzureServiceBusEmulatorDockerConfig.json:/ServiceBus_Emulator/ConfigFiles/Config.json"
    ports:
      - "5672:5672"
      - "5300:5300"
    environment:
      SQL_SERVER: sqledge
      MSSQL_SA_PASSWORD: "QCashDev123!"  # Password should be same as what is set for SQL Edge  
      ACCEPT_EULA: Y
      # SQL_WAIT_INTERVAL: ${SQL_WAIT_INTERVAL} # Optional: Time in seconds to wait for SQL to be ready (default is 15 seconds)
    depends_on:
      - sqledge
    networks:
      sb-emulator:
        aliases:
          - "sb-emulator"
  sqledge:
    container_name: "sqledge"
    image: mcr.microsoft.com/azure-sql-edge:latest
    networks:
      sb-emulator:
        aliases:
          - "sqledge"
    environment:
      ACCEPT_EULA: Y
      MSSQL_SA_PASSWORD: "QCashDev123!" # To be filled by user as per policy : https://learn.microsoft.com/en-us/sql/relational-databases/security/strong-passwords?view=sql-server-linux-ver16
    volumes:
      - sqledge-data:/var/opt/mssql
  azurite:
    image: mcr.microsoft.com/azure-storage/azurite:latest
    container_name: "azurite"
    hostname: azurite
    restart: always
    ports:
      - "10000:10000"
      - "10001:10001"
      - "10002:10002"
    volumes:
      - azurite-data:/data

networks:
  sb-emulator:

volumes:
  mssql-data:
  redis-data:
  sqledge-data:
  azurite-data:
